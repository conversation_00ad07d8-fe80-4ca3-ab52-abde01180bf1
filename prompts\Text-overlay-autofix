# 🛠️ Fix: Text Overlay Sync for YouTube Thumbnail Generator

## Problem
When the "Text Overlay" feature is enabled, the system auto-generates a headline (e.g., "TRANSFORM NOW!") based on the video topic or prompt. However, when generating the image, the output sometimes uses a different headline (e.g., "CREATE THUMBNAIL!"), causing a mismatch between the overlay box, live preview, and the final image.

## Requirements
- The overlay text box (manual or auto-generated) must always be the **single source of truth** for the headline text in the generated image.
- When auto-generating overlay text, update the overlay box state and the live preview in real-time.
- The prompt sent to the image generator (OpenAI, etc.) must always use the current overlay text box value as the headline—never re-infer or re-generate it.
- The live preview must always reflect the overlay text box value, whether auto-generated or user-edited.

## Implementation Steps
1. When auto-generating overlay text:
    - Set the overlay text box value and update the state.
    - Trigger a live preview update.
2. When building the prompt for image generation:
    - If overlay is enabled and overlay text is non-empty, use it as the only headline.
    - Do **not** let the backend or LLM re-infer a headline from the original prompt.
3. Ensure the live preview component listens to overlay text state changes and updates in real-time.
4. Test both auto-generated and manually edited overlay text to ensure the overlay box, live preview, and generated image always match.

## Reference
- Use [OpenAI documentation](https://platform.openai.com/docs/) for prompt construction best practices.
- Use context7 MCP for additional documentation and implementation guidance.

---

**Acceptance Criteria:**
- The headline in the overlay box, live preview, and generated image are always identical, regardless of whether the overlay text was auto-generated or manually entered.