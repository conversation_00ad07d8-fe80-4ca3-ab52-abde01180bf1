---
description: 
globs: 
alwaysApply: false
---
# 🎮 Context-Aware Background Generation for YouTube Thumbnails

type: always

## 🧠 Objective
Ensure that the background of every generated YouTube thumbnail is visually relevant, dynamic, and contextually tied to the user’s video topic or prompt—never generic or “solid” unless explicitly requested.

---

## 📋 Rule Details

- **Default Behavior:**  
  If the user does not specify a background, the AI should infer a visually engaging, topic-relevant background based on the video’s subject, genre, or keywords.  
  - *Example:*
    - For a gaming video about “Fortnite,” use iconic Fortnite landscapes, colors, or props (e.g., Battle Bus, loot llama, in-game environments).
    - For “Battle Royale” games (Warzone, Call of Duty, Valorant, CS:GO, Apex Legends), use backgrounds that evoke the style, maps, or atmosphere of those games.
    - For mobile games, use UI elements, touch controls, or mobile device frames as part of the background.
    - For tech reviews, use circuit patterns, device close-ups, or digital gradients.
    - For unboxing or “mystery box” videos, use packaging, surprise effects, or product silhouettes as the background.
    - For vlogs, use lifestyle, travel, or daily environment scenes matching the topic.
    - For tutorials, use whiteboards, code editors, or relevant workspace imagery.

- **Specific Background Selection:**  
  If the user selects a specific background style (e.g., “template,” “upload,” “solid color”), the AI must use that style but still adapt it to the video topic.  
  - *Example:*
    - If “template” is chosen for a “Minecraft” video, use blocky, pixel-art backgrounds in Minecraft style.
    - If “upload” is chosen, use the uploaded image as the main background, but blend it with topic-relevant overlays or effects.
    - If “solid color” is chosen, add subtle overlays, gradients, or icons that relate to the topic, unless the user requests pure solid.

- **Never Generate:**  
  - Plain, contextless, or repetitive backgrounds.
  - Generic “solid” backgrounds unless explicitly requested.
  - Backgrounds that do not visually connect to the video’s main subject.

---

## 💡 Example Video Topics & Background Guidance

- **Gaming:**  
  - Fortnite: Use in-game scenery, iconic props, or color palettes.
  - Battle Royale: Use map overviews, weapon silhouettes, or action effects.
  - Mobile Games: Use phone frames, touch icons, or app UI elements.

- **Tech/Unboxing:**  
  - Use product packaging, device close-ups, or digital grids.

- **Mystery/Unusual Content:**  
  - Use question marks, glowing boxes, or “surprise” visual cues.

- **Vlogs/Lifestyle:**  
  - Use real-world scenes, travel locations, or daily life environments.

- **Tutorials/Education:**  
  - Use whiteboards, code editors, or classroom/workspace imagery.

---

## 👨‍💻 Developer Notes

- This feature does not override **user manual selections**
- Use simple NLP keyword matching for prototype (e.g., `if includes("fortnite")`)
- Future enhancement: plug in semantic classification via lightweight model
- Ensure results render inside `<canvas>` background layer before subject is composited

---

## 🧪 Test Scenarios

- Input: `"Unboxing the weirdest gadgets from Amazon"`  
  → Background = mystery tech box + cinematic blur

- Input: `"Best sniper plays in Call of Duty"`  
  → Background = battlefield gradient + smoke burst

- Input: `"Reacting to my old Facebook photos"`  
  → Background = pop-art halftone + blush emoji texture

  

## 🛡️ Universal Quality Rule

- The background must always enhance the thumbnail’s click appeal and make the video’s topic instantly recognizable—even if the user provides no background details.

---

**Apply this rule to all prompt generation logic for YouTube thumbnails.**
