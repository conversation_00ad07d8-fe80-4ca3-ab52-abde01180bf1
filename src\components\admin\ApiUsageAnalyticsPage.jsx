const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;
import { AdminTable } from './AdminTable.jsx';

export const ApiUsageAnalyticsPage = () => {
    const [timeRange, setTimeRange] = useState('30d');

    // Mock API usage data
    const [apiUsageData] = useState([
        {
            id: 'user-1',
            email: '<EMAIL>',
            apiCalls24h: 45,
            apiCalls7d: 312,
            apiCalls30d: 1247,
            totalApiCalls: 3456,
            lastCallTimestamp: '2024-01-20T14:30:00Z'
        },
        {
            id: 'user-2',
            email: '<EMAIL>',
            apiCalls24h: 23,
            apiCalls7d: 189,
            apiCalls30d: 892,
            totalApiCalls: 2134,
            lastCallTimestamp: '2024-01-20T12:15:00Z'
        },
        {
            id: 'user-3',
            email: '<EMAIL>',
            apiCalls24h: 67,
            apiCalls7d: 445,
            apiCalls30d: 2156,
            totalApiCalls: 5678,
            lastCallTimestamp: '2024-01-20T16:45:00Z'
        },
        {
            id: 'user-4',
            email: '<EMAIL>',
            apiCalls24h: 12,
            apiCalls7d: 78,
            apiCalls30d: 456,
            totalApiCalls: 1234,
            lastCallTimestamp: '2024-01-19T09:20:00Z'
        }
    ]);

    const timeRanges = [
        { value: '24h', label: 'Last 24 Hours' },
        { value: '7d', label: 'Last 7 Days' },
        { value: '30d', label: 'Last 30 Days' },
        { value: '90d', label: 'Last 90 Days' }
    ];

    const createChartPlaceholder = (title, type, id) => {
        return React.createElement('div', {
            className: 'admin-api-chart-placeholder bg-gray-800 border border-gray-700 rounded-lg p-6',
            id: `api-chart-placeholder-${id}`
        },
            React.createElement('h3', {
                className: 'admin-api-chart-title text-lg font-semibold text-white mb-4'
            }, title),
            React.createElement('div', {
                className: 'admin-api-chart-content h-64 bg-gray-700 rounded-lg flex items-center justify-center',
                id: `api-chart-content-${id}`
            },
                React.createElement('div', {
                    className: 'admin-api-chart-placeholder-content text-center'
                },
                    React.createElement('svg', {
                        className: 'admin-api-chart-placeholder-icon w-16 h-16 text-gray-500 mx-auto mb-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: type === 'line' 
                                ? 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                                : 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'
                        })
                    ),
                    React.createElement('p', {
                        className: 'admin-api-chart-placeholder-text text-gray-500 text-sm'
                    }, `${title} Chart Coming Soon`)
                )
            )
        );
    };

    const tableColumns = [
        {
            key: 'email',
            label: 'User Email',
            render: (user) => React.createElement('div', {
                className: 'api-user-email-cell'
            },
                React.createElement('span', {
                    className: 'api-user-email-text font-medium text-white'
                }, user.email)
            )
        },
        {
            key: 'apiCalls24h',
            label: 'API Calls (24h)',
            render: (user) => React.createElement('span', {
                className: 'api-calls-24h-count font-medium text-blue-400'
            }, user.apiCalls24h.toLocaleString())
        },
        {
            key: 'apiCalls7d',
            label: 'API Calls (7d)',
            render: (user) => React.createElement('span', {
                className: 'api-calls-7d-count font-medium text-green-400'
            }, user.apiCalls7d.toLocaleString())
        },
        {
            key: 'apiCalls30d',
            label: 'API Calls (30d)',
            render: (user) => React.createElement('span', {
                className: 'api-calls-30d-count font-medium text-purple-400'
            }, user.apiCalls30d.toLocaleString())
        },
        {
            key: 'totalApiCalls',
            label: 'Total API Calls',
            render: (user) => React.createElement('span', {
                className: 'api-calls-total-count font-bold text-yellow-400'
            }, user.totalApiCalls.toLocaleString())
        },
        {
            key: 'lastCallTimestamp',
            label: 'Last Call',
            render: (user) => React.createElement('span', {
                className: 'api-last-call-time text-gray-400 text-sm'
            }, new Date(user.lastCallTimestamp).toLocaleString())
        }
    ];

    // Calculate summary stats
    const totalApiCalls = apiUsageData.reduce((sum, user) => sum + user.totalApiCalls, 0);
    const totalCalls24h = apiUsageData.reduce((sum, user) => sum + user.apiCalls24h, 0);
    const totalCalls7d = apiUsageData.reduce((sum, user) => sum + user.apiCalls7d, 0);
    const totalCalls30d = apiUsageData.reduce((sum, user) => sum + user.apiCalls30d, 0);

    return React.createElement('div', {
        className: 'admin-api-usage-analytics-page',
        id: 'admin-api-usage-analytics-content'
    },
        // Page Header
        React.createElement('div', {
            className: 'admin-api-analytics-header mb-8',
            id: 'admin-api-analytics-header'
        },
            React.createElement('h1', {
                className: 'admin-api-analytics-title text-3xl font-bold text-white mb-2'
            }, 'API Usage Analytics'),
            React.createElement('p', {
                className: 'admin-api-analytics-subtitle text-gray-400'
            }, 'Monitor API usage patterns and user activity')
        ),

        // Summary Stats
        React.createElement('div', {
            className: 'admin-api-summary-stats-section mb-8',
            id: 'admin-api-summary-stats-section'
        },
            React.createElement('div', {
                className: 'admin-api-stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'
            },
                React.createElement('div', {
                    className: 'admin-api-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-api-stat-title text-sm font-medium text-gray-400 mb-2'
                    }, 'Total API Calls'),
                    React.createElement('p', {
                        className: 'admin-api-stat-value text-3xl font-bold text-white'
                    }, totalApiCalls.toLocaleString())
                ),
                React.createElement('div', {
                    className: 'admin-api-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-api-stat-title text-sm font-medium text-gray-400 mb-2'
                    }, 'Last 24 Hours'),
                    React.createElement('p', {
                        className: 'admin-api-stat-value text-3xl font-bold text-blue-400'
                    }, totalCalls24h.toLocaleString())
                ),
                React.createElement('div', {
                    className: 'admin-api-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-api-stat-title text-sm font-medium text-gray-400 mb-2'
                    }, 'Last 7 Days'),
                    React.createElement('p', {
                        className: 'admin-api-stat-value text-3xl font-bold text-green-400'
                    }, totalCalls7d.toLocaleString())
                ),
                React.createElement('div', {
                    className: 'admin-api-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-api-stat-title text-sm font-medium text-gray-400 mb-2'
                    }, 'Last 30 Days'),
                    React.createElement('p', {
                        className: 'admin-api-stat-value text-3xl font-bold text-purple-400'
                    }, totalCalls30d.toLocaleString())
                )
            )
        ),

        // Charts Section
        React.createElement('div', {
            className: 'admin-api-analytics-charts-area mb-8',
            id: 'admin-api-charts-section'
        },
            React.createElement('div', {
                className: 'admin-api-charts-header flex justify-between items-center mb-6'
            },
                React.createElement('h2', {
                    className: 'admin-api-charts-title text-xl font-semibold text-white'
                }, 'Usage Trends'),
                React.createElement('select', {
                    className: 'admin-api-time-range-select px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                    id: 'admin-api-time-range-select',
                    value: timeRange,
                    onChange: (e) => setTimeRange(e.target.value)
                }, timeRanges.map(range => 
                    React.createElement('option', {
                        key: range.value,
                        value: range.value
                    }, range.label)
                ))
            ),
            React.createElement('div', {
                className: 'admin-api-charts-grid grid grid-cols-1 lg:grid-cols-2 gap-6',
                id: 'admin-api-charts-grid'
            },
                createChartPlaceholder('API Calls Over Time', 'line', 'api-calls-over-time'),
                createChartPlaceholder('Top Users by API Usage', 'bar', 'top-users-api-usage')
            )
        ),

        // Per-User API Usage Table
        React.createElement('div', {
            className: 'admin-api-usage-table-wrapper',
            id: 'admin-api-usage-table-section'
        },
            React.createElement('h2', {
                className: 'admin-api-table-title text-xl font-semibold text-white mb-4'
            }, 'Per-User API Usage'),
            React.createElement(AdminTable, {
                columns: tableColumns,
                data: apiUsageData,
                emptyMessage: 'No API usage data available.',
                tableId: 'admin-api-usage-table'
            })
        )
    );
}; 