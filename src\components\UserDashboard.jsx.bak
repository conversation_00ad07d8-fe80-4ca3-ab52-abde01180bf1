const React = window.React;
const { useState, useEffect } = React;

// Import dashboard-specific styles
import '../styles/dashboard.css';
import '../styles/dashboard-premium-transitions.css'; // Add new premium transitions
import '../styles/confirmation-modal.css';
import '../styles/generation-details-modal.css';
import { ConfirmationModal } from './ui/ConfirmationModal.jsx';
import { GenerationDetailsModal } from './ui/GenerationDetailsModal.jsx';
import { ThumbnailPreviewModal } from './ui/ThumbnailPreviewModal.jsx';
import { PasswordChangeModal } from './ui/PasswordChangeModal.jsx';
import { authAPI } from '../utils/supabase.mjs';
import { PasswordRateLimiter, getRateLimitMessage } from '../utils/passwordRateLimit.js';

export const UserDashboard = ({ user, onExitDashboard = null, onUpdateUser = null }) => {
    const [activeTab, setActiveTab] = useState('overview');
    const [isEditing, setIsEditing] = useState(false);
    const [editableUser, setEditableUser] = useState({});
    const [previousTab, setPreviousTab] = useState('overview');
    const [isTabTransitioning, setIsTabTransitioning] = useState(false);
    
    // Generation History filter states
    const [sortOrder, setSortOrder] = useState('newest'); // 'newest' or 'oldest'
    const [qualityFilter, setQualityFilter] = useState('all'); // 'all', 'hd', 'medium', 'low'
    
    // Confirmation modal state
    const [confirmModal, setConfirmModal] = useState({
        isOpen: false,
        title: '',
        message: '',
        onConfirm: null,
        itemToDelete: null
    });
    
    // Generation details modal state
    const [detailsModal, setDetailsModal] = useState({
        isOpen: false,
        details: null
    });
    
    // Thumbnail preview modal state
    const [previewModal, setPreviewModal] = useState({
        isOpen: false,
        thumbnail: null,
        title: null,
        itemId: null
    });
    
    // Mobile portrait detection state
    const [isMobilePortrait, setIsMobilePortrait] = useState(false);
    
    // Password change modal state
    const [passwordModal, setPasswordModal] = useState({
        isOpen: false,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        showCurrentPassword: false,
        showNewPassword: false,
        showConfirmPassword: false,
        errors: {},
        isLoading: false,
        successMessage: ''
    });

    // Password rate limiting state
    const [passwordRateLimit, setPasswordRateLimit] = useState({
        canChange: true,
        eligibility: null,
        message: null,
        isChecking: false
    });

    // Check password change eligibility on mount and user change
    useEffect(() => {
        const checkPasswordEligibility = async () => {
            if (!user) return;

            setPasswordRateLimit(prev => ({ ...prev, isChecking: true }));

            try {
                const { supabase } = await import('../utils/supabase.mjs');
                const rateLimiter = new PasswordRateLimiter(supabase);
                const eligibility = await rateLimiter.checkPasswordChangeEligibility(user.id);
                const message = getRateLimitMessage(eligibility);

                setPasswordRateLimit({
                    canChange: eligibility.canChange,
                    eligibility,
                    message,
                    isChecking: false
                });
            } catch (error) {
                console.error('Error checking password eligibility:', error);
                // Fail open - allow password change on error
                setPasswordRateLimit({
                    canChange: true,
                    eligibility: null,
                    message: null,
                    isChecking: false
                });
            }
        };

        checkPasswordEligibility();
    }, [user]);
    
    // Detect mobile portrait mode
    useEffect(() => {
        const checkMobilePortrait = () => {
            // Check if screen width is <= 697px AND in portrait orientation
            const isPortrait = window.innerHeight > window.innerWidth;
            const isMobile = window.innerWidth <= 697;
            setIsMobilePortrait(isMobile && isPortrait);
        };
        
        // Check on mount
        checkMobilePortrait();
        
        // Add event listeners for resize and orientation change
        window.addEventListener('resize', checkMobilePortrait);
        window.addEventListener('orientationchange', checkMobilePortrait);
        
        // Cleanup
        return () => {
            window.removeEventListener('resize', checkMobilePortrait);
            window.removeEventListener('orientationchange', checkMobilePortrait);
        };
    }, []);

    // Initialize editable user data
    useEffect(() => {
        if (user) {
            // Calculate actual last login
            const getLastLogin = () => {
                if (user.last_sign_in_at) {
                    return new Date(user.last_sign_in_at).toLocaleString();
                }
                // If no last sign in, use current time as first login
                return new Date().toLocaleString();
            };

            // Calculate next billing date (30 days from now for monthly plans)
            const getNextBilling = () => {
                const nextMonth = new Date();
                nextMonth.setMonth(nextMonth.getMonth() + 1);
                return nextMonth.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
            };

            setEditableUser({
                fullName: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
                email: user.email || '<EMAIL>',
                plan: user.app_metadata?.plan || 'free',
                credits: user.app_metadata?.credits || 750,
                maxCredits: user.app_metadata?.max_credits || 1000,
                nextBilling: user.app_metadata?.next_billing || getNextBilling(),
                lastLogin: getLastLogin(),
                memberSince: user.created_at ? new Date(user.created_at).toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                }) : new Date().toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                })
            });
        }
    }, [user]);

    // Smooth tab switching animation
    const handleTabChange = (newTab) => {
        if (newTab !== activeTab) {
            setIsTabTransitioning(true);
            setPreviousTab(activeTab);
            
            setTimeout(() => {
                setActiveTab(newTab);
                setIsTabTransitioning(false);
            }, 150); // Quick transition
        }
    };

    const [generationHistory, setGenerationHistory] = useState([]);
    
    // Load generation history on component mount and when user changes
    useEffect(() => {
        const loadHistory = () => {
            try {
                // First try to get from user metadata
                if (user?.app_metadata?.generation_history) {
                    setGenerationHistory(user.app_metadata.generation_history);
                    return;
                }
                
                // Fallback to localStorage for local storage of generations
                const stored = localStorage.getItem('user_generation_history');
                if (stored) {
                    setGenerationHistory(JSON.parse(stored));
                    return;
                }
                
                // Return empty array if no data found
                setGenerationHistory([]);
            } catch (error) {
                console.error('Error loading generation history:', error);
                setGenerationHistory([]);
            }
        };
        
        loadHistory();
    }, [user]);

    // Close dropdowns when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            // If click is not on dropdown or button, close all dropdowns
            if (!event.target.closest('.show-more-container') && !event.target.closest('.dropdown-menu')) {
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    if (menu.style.display === 'flex') {
                        menu.style.display = 'none';
                    }
                });
            }
        };
    
        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, []);

    // Generation History helper functions
    const handleSortToggle = () => {
        setSortOrder(prev => prev === 'newest' ? 'oldest' : 'newest');
    };

    const handleQualityFilterToggle = () => {
        const qualityOptions = ['all', 'hd', 'medium', 'low'];
        const currentIndex = qualityOptions.indexOf(qualityFilter);
        const nextIndex = (currentIndex + 1) % qualityOptions.length;
        setQualityFilter(qualityOptions[nextIndex]);
    };

    const getQualityFilterLabel = () => {
        switch (qualityFilter) {
            case 'hd': return 'HD Only';
            case 'medium': return 'Medium Only';
            case 'low': return 'Low Only';
            default: return 'All Qualities';
        }
    };

    // Delete generation function
    const handleDeleteGeneration = (itemToDelete) => {
        console.log('Delete clicked for item:', itemToDelete);
        setConfirmModal({
            isOpen: true,
            title: 'Delete Generation',
            message: 'Are you sure you want to delete this generation? This action cannot be undone.',
            onConfirm: () => performDeleteGeneration(itemToDelete),
            itemToDelete: itemToDelete
        });
        console.log('Modal state set to:', { isOpen: true });
    };

    // Perform actual deletion
    const performDeleteGeneration = (itemToDelete) => {
        try {
            // Remove from current state
            const updatedHistory = generationHistory.filter(h => 
                (h.id || h.timestamp) !== (itemToDelete.id || itemToDelete.timestamp)
            );
            
            // Update state immediately
            setGenerationHistory(updatedHistory);
            
            // Update localStorage
            localStorage.setItem('user_generation_history', JSON.stringify(updatedHistory));
            
            // Close the modal
            closeConfirmModal();
            
            console.log('Generation deleted successfully');
        } catch (error) {
            console.error('Error deleting generation:', error);
            // Reload the history in case of error
            const stored = localStorage.getItem('user_generation_history');
            if (stored) {
                setGenerationHistory(JSON.parse(stored));
            }
            // Close the modal even on error
            closeConfirmModal();
        }
    };

    // Close confirmation modal
    const closeConfirmModal = () => {
        setConfirmModal({
            isOpen: false,
            title: '',
            message: '',
            onConfirm: null,
            itemToDelete: null
        });
    };

    // Handle delete account confirmation
    const handleDeleteAccount = () => {
        setConfirmModal({
            isOpen: true,
            title: 'Delete Account',
            message: 'This will permanently delete your account and all data. This cannot be undone. Continue?',
            onConfirm: performDeleteAccount,
            itemToDelete: null
        });
    };

    // Perform actual account deletion
    const performDeleteAccount = () => {
        try {
            // Here you would typically call your backend API to delete the account
            console.log('Account deletion initiated for user:', user?.email);
            
            // Clear all local data
            localStorage.removeItem('user_generation_history');
            localStorage.clear();
            
            // Close the modal
            closeConfirmModal();
            
            // Redirect or sign out user
            if (onExitDashboard) {
                onExitDashboard();
            }
            
            // In a real app, you would:
            // 1. Call your backend API to delete the account
            // 2. Handle the response
            // 3. Sign out the user
            // 4. Redirect to a confirmation page
            
            console.log('Account deletion process completed');
        } catch (error) {
            console.error('Error deleting account:', error);
            // In a real app, you would show an error message to the user
            closeConfirmModal();
        }
    };

    // Handle password change
    const handlePasswordChange = async ({ currentPassword, newPassword }) => {
        try {
            console.log('Password change requested for user:', user?.email);
            
            // Check rate limiting before proceeding
            if (!passwordRateLimit.canChange) {
                throw new Error(passwordRateLimit.message?.message || 'Password change not allowed at this time.');
            }
            
            // For password changes in Supabase, we need to verify the current password first
            const { supabase } = await import('../utils/supabase.mjs');
            
            // Store current session to restore later if needed
            const { data: currentSession } = await supabase.auth.getSession();
            // CRITICAL: Verify current password by attempting to sign in with it
            // This is the ONLY way to verify the current password in Supabase
            console.log("🔐 Verifying current password for user:", user.email);
            const { data: verifyData, error: verifyError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: currentPassword,
            });
            
            if (verifyError) {
                console.error("�� SECURITY: Current password verification FAILED:", verifyError.message);
                // Restore the original session if verification failed
                if (currentSession?.session) {
                    await supabase.auth.setSession(currentSession.session);
                }
                // NEVER proceed with password change if verification fails
                throw new Error("Current password is incorrect. Please verify your current password and try again.");
            }
            
            // Double-check that the verification actually succeeded
            if (!verifyData?.user || verifyData.user.id !== user.id) {
                console.error("🚨 SECURITY: Password verification returned unexpected user data");
                if (currentSession?.session) {
                    await supabase.auth.setSession(currentSession.session);
                }
                throw new Error("Authentication verification failed. Please try again.");
            }
            
            console.log("✅ Current password verification successful");            // Use the updateUser method which works for authenticated users
            const { data: updateData, error: updateError } = await supabase.auth.updateUser({
                password: newPassword
            });
            
            if (updateError) {
                console.error('Password update failed:', updateError);
                throw new Error(updateError.message || 'Failed to update password. Please try again.');
            }
            
            // Record the successful password change for rate limiting
            try {
                const rateLimiter = new PasswordRateLimiter(supabase);
                await rateLimiter.recordPasswordChange(user.id, {
                    ip: 'browser', // Could be enhanced to get real IP
                    userAgent: navigator.userAgent,
                    source: 'dashboard'
                });
                
                // Refresh eligibility status
                const newEligibility = await rateLimiter.checkPasswordChangeEligibility(user.id);
                const newMessage = getRateLimitMessage(newEligibility);
                
                setPasswordRateLimit({
                    canChange: newEligibility.canChange,
                    eligibility: newEligibility,
                    message: newMessage,
                    isChecking: false
                });
            } catch (recordError) {
                console.error('Error recording password change:', recordError);
                // Don't fail the password change if recording fails
            }
            
            console.log('Password changed successfully');
            return true;
        } catch (error) {
            console.error('Error changing password:', error);
            // Re-throw with a user-friendly message if it's not already one
            if (error.message.includes('Current password is incorrect') || 
                error.message.includes('Failed to update password') ||
                error.message.includes('Password change not allowed')) {
                throw error;
            }
            throw new Error('An unexpected error occurred. Please try again.');
        }
    };

    // Filter and sort generation history
    const filteredAndSortedHistory = generationHistory
        .filter(item => {
            if (qualityFilter === 'all') return true;
            const itemQuality = (item.quality || 'Medium').toLowerCase();
            return itemQuality === qualityFilter;
        })
        .sort((a, b) => {
            const dateA = new Date(a.date || a.timestamp);
            const dateB = new Date(b.date || b.timestamp);
            return sortOrder === 'newest' ? dateB - dateA : dateA - dateB;
        });

    const tabs = [
        { 
            id: 'overview', 
            label: 'Overview', 
            icon: 'solar:widget-2-bold-duotone' 
        },
        { 
            id: 'account', 
            label: 'Account', 
            icon: 'solar:user-circle-bold-duotone' 
        },
        { 
            id: 'billing', 
            label: isMobilePortrait ? 'Billing' : 'Billing & Subscription', 
            icon: 'solar:card-bold-duotone' 
        },
        { 
            id: 'history', 
            label: isMobilePortrait ? 'History' : 'Generation History', 
            icon: 'solar:history-bold-duotone' 
        }
    ];

    const handleSaveProfile = () => {
        if (onUpdateUser) {
            onUpdateUser(editableUser);
        }
        setIsEditing(false);
    };

    const handleCancelEdit = () => {
        // Reset to original user data
        const getLastLogin = () => {
            if (user.last_sign_in_at) {
                return new Date(user.last_sign_in_at).toLocaleString();
            }
            return new Date().toLocaleString();
        };

        const getNextBilling = () => {
            const nextMonth = new Date();
            nextMonth.setMonth(nextMonth.getMonth() + 1);
            return nextMonth.toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        };

        setEditableUser({
            fullName: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
            email: user.email || '<EMAIL>',
            plan: user.app_metadata?.plan || 'free',
            credits: user.app_metadata?.credits || 750,
            maxCredits: user.app_metadata?.max_credits || 1000,
            nextBilling: user.app_metadata?.next_billing || getNextBilling(),
            lastLogin: getLastLogin(),
            memberSince: user.created_at ? new Date(user.created_at).toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            }) : new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            })
        });
        setIsEditing(false);
    };

    // Overview Tab Content
    const renderOverviewTab = () => {
        const creditsPercentage = (editableUser.credits / editableUser.maxCredits) * 100;
        
        // Calculate real statistics from actual generation history
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();
        
        const generationsThisMonth = generationHistory.filter(item => {
            if (!item.date) return false;
            try {
                const itemDate = new Date(item.date);
                return itemDate.getMonth() === currentMonth && itemDate.getFullYear() === currentYear;
            } catch {
                return false;
            }
        }).length;

        const last30DaysCount = generationHistory.filter(item => {
            if (!item.date) return false;
            try {
                const itemDate = new Date(item.date);
                const thirtyDaysAgo = new Date(currentDate.getTime() - (30 * 24 * 60 * 60 * 1000));
                return itemDate >= thirtyDaysAgo;
            } catch {
                return false;
            }
        }).length;

        const recentActivity = generationHistory
            .sort((a, b) => new Date(b.date || 0) - new Date(a.date || 0))
            .slice(0, 4);

        return React.createElement('div', { className: 'dashboard-overview p-6' },
            // Welcome Section
            React.createElement('div', { className: 'welcome-section mb-8' },
                React.createElement('h1', { className: 'text-3xl font-bold text-white mb-2' }, 
                    `Welcome back, ${editableUser.fullName}!`
                ),
                React.createElement('p', { className: 'text-gray-400' }, 
                    `Last login: ${editableUser.lastLogin}`
                )
            ),

            // Stats Cards
            React.createElement('div', { className: 'stats-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-8' },
                // Monthly Credits Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Monthly Credits'),
                        React.createElement('span', {
                            className: 'iconify text-purple-400',
                            'data-icon': 'solar:star-bold-duotone',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-3xl font-bold text-white' }, editableUser.credits),
                        React.createElement('span', { className: 'text-gray-400 text-lg' }, ` / ${editableUser.maxCredits}`)
                    ),
                    React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-2 mb-2' },
                        React.createElement('div', {
                            className: 'bg-purple-500 h-2 rounded-full transition-all duration-300',
                            style: { width: `${creditsPercentage}%` }
                        })
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, 'Resets monthly')
                ),

                // Generations Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Generations'),
                        React.createElement('span', {
                            className: 'iconify text-blue-400',
                            'data-icon': 'solar:image-bold-duotone',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-3xl font-bold text-white' }, generationsThisMonth)
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, `${last30DaysCount} in the last 30 days`)
                ),

                // Subscription Card
                React.createElement('div', { className: 'stat-card bg-gray-800 border border-gray-700 rounded-lg p-6' },
                    React.createElement('div', { className: 'flex items-center justify-between mb-4' },
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Subscription'),
                        React.createElement('span', {
                            className: 'iconify text-green-400',
                            'data-icon': 'solar:crown-bold-duotone',
                            style: { fontSize: '24px' }
                        })
                    ),
                    React.createElement('div', { className: 'mb-3' },
                        React.createElement('span', { className: 'text-2xl font-bold text-white capitalize' }, editableUser.plan)
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400' }, `Renews ${editableUser.nextBilling}`)
                )
            ),

            // Recent Activity Section
            React.createElement('div', { className: 'recent-activity' },
                React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                    React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Recent Activity'),
                    React.createElement('button', { 
                        className: 'text-purple-400 hover:text-purple-300 text-sm font-medium',
                        onClick: () => setActiveTab('history')
                    }, 'View All Activity →')
                ),
                React.createElement('div', { className: 'activity-list space-y-4' },
                    recentActivity.length > 0 ? recentActivity.map((item, index) => 
                        React.createElement('div', {
                            key: item.id,
                            className: 'activity-item flex items-center gap-4 p-4 bg-gray-800 border border-gray-700 rounded-lg',
                            style: { '--item-index': index }
                        },
                            React.createElement('img', {
                                src: item.thumbnail || item.imageURL || '/dist/assets/placeholder-thumbnail.png',
                                alt: item.title || item.prompt || 'Generated thumbnail',
                                className: 'w-16 h-10 object-cover rounded'
                            }),
                            React.createElement('div', { className: 'flex-1' },
                                React.createElement('p', { className: 'text-white text-sm font-medium line-clamp-2' }, 
                                    item.title || item.prompt || 'Untitled Generation'
                                ),
                                React.createElement('p', { className: 'text-gray-400 text-xs' }, 
                                    `${new Date(item.date || item.timestamp).toLocaleDateString()}, ${item.quality || 'Medium'} quality`
                                )
                            ),
                            React.createElement('button', {
                                className: 'text-gray-400 hover:text-white p-2',
                                'aria-label': 'Share'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': 'solar:share-bold',
                                    style: { fontSize: '18px' }
                                })
                            )
                        )
                    ) : React.createElement('div', { 
                        className: 'text-center py-8 text-gray-400' 
                    },
                        React.createElement('span', {
                            className: 'iconify mb-4',
                            'data-icon': 'solar:image-bold-duotone',
                            style: { fontSize: '48px', display: 'block', marginBottom: '16px' }
                        }),
                        React.createElement('h3', { className: 'text-lg font-medium text-gray-300 mb-2' }, 'No recent activity'),
                        React.createElement('p', { className: 'text-sm' }, 'Start generating thumbnails to see your activity here!')
                    )
                )
            )
        );
    };

    // Account Tab Content
    const renderAccountTab = () => {
        return React.createElement('div', { className: 'dashboard-account p-6' },
            // Personal Information Section
            React.createElement('div', { className: 'personal-info-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6' },
                React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                    React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Personal Information'),
                    React.createElement('button', {
                        className: 'text-purple-400 hover:text-purple-300 text-sm font-medium flex items-center gap-2',
                        onClick: () => setIsEditing(!isEditing)
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:pen-bold',
                            style: { fontSize: '16px' }
                        }),
                        isEditing ? 'Cancel' : 'Edit'
                    )
                ),
                React.createElement('div', { className: 'flex items-center gap-6' },
                    // Avatar
                    React.createElement('div', { className: 'user-avatar w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center' },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:user-bold',
                            style: { fontSize: '32px', color: 'white' }
                        })
                    ),
                    // User Info
                    React.createElement('div', { className: 'flex-1' },
                        React.createElement('div', { className: 'mb-4' },
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Full Name'),
                            isEditing ? React.createElement('input', {
                                type: 'text',
                                value: editableUser.fullName,
                                onChange: (e) => setEditableUser({...editableUser, fullName: e.target.value}),
                                className: 'w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white edit-transition'
                            }) : React.createElement('p', { className: 'text-white font-medium edit-transition' }, editableUser.fullName)
                        ),
                        React.createElement('div', { className: 'mb-4' },
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Email Address'),
                            isEditing ? React.createElement('input', {
                                type: 'email',
                                value: editableUser.email,
                                onChange: (e) => setEditableUser({...editableUser, email: e.target.value}),
                                className: 'w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white edit-transition'
                            }) : React.createElement('p', { className: 'text-gray-400 edit-transition' }, editableUser.email)
                        ),
                        React.createElement('div', { className: 'mb-4' },
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Member Since'),
                            React.createElement('p', { className: 'text-gray-400' }, editableUser.memberSince)
                        ),
                        React.createElement('div', { className: 'mb-4' },
                            React.createElement('label', { className: 'block text-sm font-medium text-gray-300 mb-2' }, 'Last Login'),
                            React.createElement('p', { className: 'text-gray-400' }, editableUser.lastLogin)
                        )
                    )
                ),
                isEditing && React.createElement('div', { className: 'flex gap-3 mt-6' },
                    React.createElement('button', {
                        onClick: handleSaveProfile,
                        className: 'bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-medium'
                    }, 'Save Changes'),
                    React.createElement('button', {
                        onClick: handleCancelEdit,
                        className: 'bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium'
                    }, 'Cancel')
                )
            ),

            // Security Section
            React.createElement('div', { className: 'security-section bg-gray-800 border border-gray-700 rounded-lg p-6' },
                React.createElement('h2', { className: 'text-xl font-bold text-white mb-6' }, 'Security'),
                
                // Password Change Section with Rate Limiting Info
                React.createElement('div', { className: 'security-item py-4 border-b border-gray-700' },
                    React.createElement('div', { className: 'flex items-start justify-between gap-4' },
                        React.createElement('div', { className: 'flex-1' },
                            React.createElement('h3', { className: 'text-white font-medium mb-2' }, 'Password'),
                            
                            // Rate limiting status display
                            passwordRateLimit.isChecking ? (
                                React.createElement('div', { className: 'flex items-center gap-2 text-gray-400 text-sm mb-2' },
                                    React.createElement('span', {
                                        className: 'iconify animate-spin',
                                        'data-icon': 'solar:refresh-bold',
                                        style: { fontSize: '14px' }
                                    }),
                                    'Checking availability...'
                                )
                            ) : passwordRateLimit.message ? (
                                React.createElement('div', { 
                                    className: `flex items-start gap-2 text-sm mb-2 ${
                                        passwordRateLimit.message.type === 'success' ? 'text-green-400' :
                                        passwordRateLimit.message.type === 'warning' ? 'text-yellow-400' :
                                        passwordRateLimit.message.type === 'info' ? 'text-blue-400' :
                                        'text-red-400'
                                    }`
                                },
                                    React.createElement('span', {
                                        className: 'iconify mt-0.5',
                                        'data-icon': 
                                            passwordRateLimit.message.type === 'success' ? 'solar:check-circle-linear' :
                                            passwordRateLimit.message.type === 'warning' ? 'solar:clock-circle-linear' :
                                            passwordRateLimit.message.type === 'info' ? 'solar:info-circle-linear' :
                                            'solar:danger-triangle-linear',
                                        style: { fontSize: '16px' }
                                    }),
                                    React.createElement('div', {},
                                        React.createElement('p', { className: 'font-medium' }, passwordRateLimit.message.title),
                                        React.createElement('p', { className: 'text-xs opacity-90 mt-1' }, passwordRateLimit.message.message)
                                    )
                                )
                            ) : null,
                            
                            // Last password change info
                            passwordRateLimit.eligibility?.lastChange ? (
                                React.createElement('p', { className: 'text-gray-400 text-sm' }, 
                                    `Last changed ${new Date(passwordRateLimit.eligibility.lastChange).toLocaleDateString()}`
                                )
                            ) : (
                                React.createElement('p', { className: 'text-gray-400 text-sm' }, 'Password security settings')
                            )
                        ),
                        
                        // Password change button with conditional styling
                        React.createElement('button', {
                            className: `px-4 py-2 rounded-lg font-medium change-password-btn transition-all duration-200 ${
                                passwordRateLimit.canChange && !passwordRateLimit.isChecking
                                    ? 'bg-gray-700 hover:bg-gray-600 text-white hover:shadow-lg'
                                    : 'bg-gray-800 text-gray-500 cursor-not-allowed border border-gray-600'
                            }`,
                            'data-testid': 'change-password-btn',
                            'data-cy': 'change-password-btn',
                            id: 'change-password-btn',
                            disabled: !passwordRateLimit.canChange || passwordRateLimit.isChecking,
                            onClick: () => {
                                if (passwordRateLimit.canChange && !passwordRateLimit.isChecking) {
                                    setPasswordModal(prev => ({ ...prev, isOpen: true }));
                                }
                            },
                            title: passwordRateLimit.canChange 
                                ? 'Change your password' 
                                : passwordRateLimit.message?.message || 'Password change not available'
                        }, 
                            passwordRateLimit.isChecking ? 'Checking...' : 'Change Password'
                        )
                    )
                ),
                React.createElement('div', { className: 'security-item mt-6 bg-red-900/20 border border-red-700/50 rounded-lg p-4' },
                    React.createElement('div', { className: 'flex items-start gap-3' },
                        React.createElement('span', {
                            className: 'iconify text-red-400 mt-1',
                            'data-icon': 'solar:danger-triangle-linear',
                            style: { fontSize: '20px' }
                        }),
                        React.createElement('div', { className: 'flex-1' },
                            React.createElement('h3', { className: 'text-red-400 font-medium mb-2' }, 'Delete Account'),
                            React.createElement('p', { className: 'text-gray-400 text-sm mb-4' }, 'Permanently delete your account and all associated data. This action cannot be undone.'),
                            React.createElement('button', {
                                className: 'border border-red-600 hover:border-red-500 text-red-400 hover:text-red-300 hover:bg-red-600/10 px-4 py-2 rounded-lg font-medium transition-colors',
                                onClick: handleDeleteAccount
                            }, 'Delete Account')
                        )
                    )
                )
            )
        );
    };

    // Billing Tab Content
    const renderBillingTab = () => {
        const creditsPercentage = (editableUser.credits / editableUser.maxCredits) * 100;

        return React.createElement('div', { className: 'dashboard-billing p-6' },
            // Subscription Section
            React.createElement('div', { className: 'subscription-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6' },
                React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                    React.createElement('div', { className: 'flex items-center gap-3' },
                        React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Subscription'),
                        React.createElement('span', { className: 'bg-green-600 text-white px-2 py-1 rounded text-xs font-medium' }, 'Active')
                    ),
                    React.createElement('div', { className: 'flex gap-3' },
                        React.createElement('button', { className: 'text-purple-400 hover:text-purple-300 text-sm font-medium' }, 'Upgrade Plan'),
                        React.createElement('button', { className: 'text-gray-400 hover:text-gray-300 text-sm font-medium' }, 'Cancel Renewal')
                    )
                ),
                React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-4 gap-6' },
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Current Plan'),
                        React.createElement('p', { className: 'text-white font-semibold capitalize' }, editableUser.plan)
                    ),
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Price'),
                        React.createElement('p', { className: 'text-white font-semibold' }, '12 CAD/month')
                    ),
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Billing Cycle'),
                        React.createElement('p', { className: 'text-white font-semibold' }, 'Monthly'),
                        React.createElement('button', { className: 'text-purple-400 text-sm' }, 'Change to Annual')
                    ),
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Next Payment'),
                        React.createElement('p', { className: 'text-white font-semibold' }, editableUser.nextBilling)
                    )
                )
            ),

            // Credits Section
            React.createElement('div', { className: 'credits-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6' },
                React.createElement('h2', { className: 'text-xl font-bold text-white mb-6' }, 'Credits'),
                React.createElement('div', { className: 'mb-6' },
                    React.createElement('div', { className: 'flex items-baseline gap-2 mb-2' },
                        React.createElement('span', { className: 'text-3xl font-bold text-white' }, editableUser.credits),
                        React.createElement('span', { className: 'text-gray-400' }, `/ ${editableUser.maxCredits} credits remaining`)
                    ),
                    React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-3 mb-2' },
                        React.createElement('div', {
                            className: 'bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-300',
                            style: { width: `${creditsPercentage}%` }
                        })
                    ),
                    React.createElement('p', { className: 'text-sm text-gray-400 flex items-center gap-2' },
                        React.createElement('span', {
                            className: 'iconify text-yellow-400',
                            'data-icon': 'solar:lightning-bold',
                            style: { fontSize: '16px' }
                        }),
                        'Credits reset monthly on the 1st of each month'
                    )
                ),
                React.createElement('div', { className: 'credit-usage' },
                    React.createElement('div', { className: 'flex items-center gap-3 mb-4' },
                        React.createElement('span', {
                            className: 'iconify text-purple-400',
                            'data-icon': 'solar:pie-chart-bold-duotone',
                            style: { fontSize: '20px' }
                        }),
                        React.createElement('h3', { className: 'text-lg font-semibold text-white' }, 'Credit Usage')
                    ),
                    React.createElement('div', { className: 'space-y-3' },
                        React.createElement('div', { className: 'flex items-center justify-between' },
                            React.createElement('span', { className: 'text-gray-300' }, 'Low Quality Generation'),
                            React.createElement('span', { className: 'text-white font-medium' }, '1 credit')
                        ),
                        React.createElement('div', { className: 'flex items-center justify-between' },
                            React.createElement('span', { className: 'text-gray-300' }, 'Medium Quality Generation'),
                            React.createElement('span', { className: 'text-white font-medium' }, '3 credits')
                        ),
                        React.createElement('div', { className: 'flex items-center justify-between' },
                            React.createElement('span', { className: 'text-gray-300' }, 'HD Quality Generation'),
                            React.createElement('span', { className: 'text-white font-medium' }, '5 credits')
                        )
                    )
                )
            ),

            // Billing Information Section
            React.createElement('div', { className: 'billing-info-section bg-gray-800 border border-gray-700 rounded-lg p-6' },
                React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                    React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Billing Information'),
                    React.createElement('button', { className: 'text-purple-400 hover:text-purple-300 text-sm font-medium' }, 'Edit')
                ),
                React.createElement('div', { className: 'grid grid-cols-1 md:grid-cols-2 gap-6' },
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Billing Name'),
                        React.createElement('p', { className: 'text-white' }, editableUser.fullName)
                    ),
                    React.createElement('div', {},
                        React.createElement('p', { className: 'text-gray-400 text-sm mb-1' }, 'Billing Email'),
                        React.createElement('p', { className: 'text-white' }, editableUser.email)
                    )
                )
            )
        );
    };

    // Generation History Tab Content
    const renderHistoryTab = () => {
        // Helper function to properly close a dropdown
        const closeDropdown = (element) => {
            const dropdown = element.closest('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        };

        return React.createElement('div', { className: 'dashboard-history p-6' },
            React.createElement('div', { className: 'flex items-center justify-between mb-6' },
                React.createElement('h1', { className: 'text-2xl font-bold text-white' }, 'Generation History'),
                React.createElement('div', { 
                    className: 'flex gap-3',
                    id: 'generation-history-filter-controls'
                },
                    React.createElement('button', { 
                        className: 'bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2',
                        id: 'generation-history-sort-btn',
                        onClick: handleSortToggle
                    },
                        sortOrder === 'newest' ? 'Newest First' : 'Oldest First',
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': sortOrder === 'newest' ? 'solar:sort-vertical-bold' : 'solar:sort-vertical-bold',
                            style: { fontSize: '16px' }
                        })
                    ),
                    React.createElement('button', { 
                        className: 'bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium flex items-center gap-2',
                        id: 'generation-history-quality-filter-btn',
                        onClick: handleQualityFilterToggle
                    },
                        getQualityFilterLabel(),
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:filter-bold',
                            style: { fontSize: '16px' }
                        })
                    )
                )
            ),
            filteredAndSortedHistory.length > 0 ? React.createElement('div', { className: 'history-grid' },
                filteredAndSortedHistory.map(item => 
                    React.createElement('div', {
                        key: item.id || item.timestamp,
                        className: 'history-item',
                        onMouseLeave: (e) => {
                            // Close dropdown when mouse leaves the history item
                            const dropdown = e.currentTarget.querySelector('.dropdown-menu');
                            if (dropdown && dropdown.style.display === 'flex') {
                                dropdown.style.display = 'none';
                            }
                        },

                    },
                        React.createElement('div', { className: 'relative' },
                            item.thumbnail ? 
                                // Display the actual thumbnail covering entire card
                                React.createElement('img', {
                                    src: item.thumbnail,
                                    alt: item.title || item.prompt || 'Generated thumbnail',
                                    className: 'history-item-thumbnail'
                                }) :
                                // Fallback placeholder for old entries without thumbnails
                                React.createElement('div', { className: 'history-item-placeholder' },
                                    React.createElement('span', { className: 'iconify text-4xl mb-2 text-purple-400', 'data-icon': 'solar:gallery-bold-duotone' }),
                                    React.createElement('div', { className: 'text-sm text-center' },
                                        React.createElement('div', { className: 'font-medium text-white mb-1' }, (item.quality || 'Medium') + ' Quality'),
                                        React.createElement('div', { className: 'text-gray-400' }, (item.credits || 3) + ' credits used')
                                    )
                                ),
                            
                            // Dark feather gradient overlay at bottom
                            React.createElement('div', { className: 'history-item-gradient' }),
                            
                            // Bottom Actions Container - revealed on hover
                            React.createElement('div', { className: 'bottom-actions' },
                                // Left button - View Details
                                React.createElement('button', { 
                                    className: 'view-details-btn',
                                    id: `view-details-${item.id || item.timestamp}`,
                                    onClick: () => {
                                        setDetailsModal({
                                            isOpen: true,
                                            details: item
                                        });
                                    }
                                },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:clipboard-text-linear',
                                        style: { fontSize: '16px' }
                                    }),
                                    'View Details'
                                ),
                                
                                // Right button container - Show More with dropdown
                                React.createElement('div', { className: 'show-more-container' },
                                    React.createElement('button', { 
                                        className: 'show-more-btn',
                                        id: `show-more-${item.id || item.timestamp}`,
                                        onClick: (e) => {
                                            e.stopPropagation();
                                            const dropdown = e.currentTarget.nextElementSibling;
                                            const isVisible = dropdown.style.display === 'flex';
                                            
                                            // Close all other dropdowns
                                            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                                                if (menu !== dropdown) {
                                                    menu.style.display = 'none';
                                                }
                                            });
                                            
                                            // Toggle current dropdown
                                            dropdown.style.display = isVisible ? 'none' : 'flex';
                                        }
                                    },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:menu-dots-bold',
                                            style: { fontSize: '16px' }
                                        })
                                    ),
                                    
                                    // Dropdown Menu
                                    React.createElement('div', { 
                                        id: `dropdown-menu-${item.id || item.timestamp}`,
                                        className: 'dropdown-menu',
                                        onClick: (e) => e.stopPropagation(), // Prevent click from bubbling to document
                                        style: { display: 'none' }
                                    },
                                        // Download option
                                        React.createElement('button', {
                                            className: 'dropdown-item',
                                            onClick: (e) => {
                                                e.stopPropagation();
                                                const link = document.createElement('a');
                                                link.href = item.thumbnail;
                                                link.download = `thumbnail-${item.id || Date.now()}.jpg`;
                                                document.body.appendChild(link);
                                                link.click();
                                                document.body.removeChild(link);
                                                closeDropdown(e.currentTarget);
                                            }
                                        },
                                            React.createElement('span', {
                                                className: 'iconify',
                                                'data-icon': 'solar:download-minimalistic-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Download'
                                        ),
                                        
                                        // Preview option
                                        React.createElement('button', {
                                            className: 'dropdown-item',
                                            onClick: (e) => {
                                                e.stopPropagation();
                                                if (item.thumbnail) {
                                                    setPreviewModal({
                                                        isOpen: true,
                                                        thumbnail: item.thumbnail,
                                                        title: item.title || item.prompt || 'Generated Thumbnail',
                                                        itemId: item.id || item.timestamp
                                                    });
                                                }
                                                closeDropdown(e.currentTarget);
                                            }
                                        },
                                            React.createElement('span', {
                                                className: 'iconify',
                                                'data-icon': 'solar:eye-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Preview'
                                        ),
                                        
                                        // Delete option
                                        React.createElement('button', {
                                            className: 'dropdown-item delete-item',
                                            onClick: (e) => {
                                                e.stopPropagation();
                                                console.log('Delete button clicked for item:', item);
                                                // Close dropdown
                                                const dropdown = e.currentTarget.closest('.dropdown-menu');
                                                if (dropdown) {
                                                    dropdown.style.display = 'none';
                                                }
                                                // Call delete handler
                                                handleDeleteGeneration(item);
                                            }
                                        },
                                            React.createElement('span', {
                                                className: 'iconify',
                                                'data-icon': 'solar:trash-bin-2-linear',
                                                style: { fontSize: '16px' }
                                            }),
                                            'Delete'
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            ) : React.createElement('div', { className: 'text-center py-16 text-gray-400' },
                React.createElement('span', {
                    className: 'iconify mb-6',
                    'data-icon': 'solar:gallery-bold-duotone',
                    style: { fontSize: '64px', display: 'block', marginBottom: '24px' }
                }),
                React.createElement('h3', { className: 'text-xl font-medium text-gray-300 mb-4' }, 
                    generationHistory.length === 0 ? 'No generations yet' : 'No matching generations'
                ),
                React.createElement('p', { className: 'text-gray-400 mb-6' }, 
                    generationHistory.length === 0 
                        ? 'Start creating amazing thumbnails to build your generation history!'
                        : 'Try adjusting your filters to see more results.'
                ),
                generationHistory.length === 0 && React.createElement('button', {
                    onClick: onExitDashboard,
                    className: 'bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium'
                }, 'Create Your First Thumbnail')
            )
        );
    };

    return React.createElement('div', { className: 'user-dashboard min-h-screen bg-gray-900 flex flex-col' },
        // Header - Keep full width
        React.createElement('header', { className: 'dashboard-header border-b border-gray-700 bg-gray-800 w-full' },
            React.createElement('div', { className: 'flex items-center justify-between px-6 py-4 max-w-[960px] mx-auto' },
                React.createElement('div', { className: 'flex items-center gap-3' },
                    React.createElement('img', {
                        src: '/assets/main-logo.svg',
                        alt: 'ThumbSpark Logo',
                        style: { height: '56px' }
                    })
                ),
                React.createElement('button', {
                    onClick: onExitDashboard,
                    className: 'text-gray-400 hover:text-white p-2 rounded-lg transition-colors',
                    'aria-label': 'Exit Dashboard'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:close-circle-bold',
                        style: { fontSize: '24px' }
                    })
                )
            )
        ),

        // Navigation Tabs - Keep full width background but center content
        React.createElement('nav', { className: 'dashboard-nav border-b border-gray-700 bg-gray-800/50 w-full' },
            React.createElement('div', { className: 'flex px-6 max-w-[960px] mx-auto' },
                tabs.map(tab =>
                    React.createElement('button', {
                        key: tab.id,
                        onClick: () => handleTabChange(tab.id),
                        className: `flex items-center gap-2 px-4 py-3 border-b-2 transition-all duration-250 ${
                            activeTab === tab.id
                                ? 'border-purple-400 text-purple-400'
                                : 'border-transparent text-gray-400 hover:text-white'
                        }`
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': tab.icon,
                            style: { fontSize: '18px' }
                        }),
                        tab.label
                    )
                )
            )
        ),

        // Main Content Container - Centered with 960px max width
        React.createElement('div', { className: 'dashboard-outer-wrapper w-full flex-1 flex justify-center bg-gray-900' },
            React.createElement('div', { className: 'dashboard-container w-full max-w-[960px] px-6' },
                // Tab Content with animation wrapper
                React.createElement('main', { 
                    className: 'dashboard-content flex-1 py-6 tab-content-wrapper'
                },
                    React.createElement('div', { 
                        key: activeTab,
                        className: 'tab-content' 
                    },
                        activeTab === 'overview' && renderOverviewTab(),
                        activeTab === 'account' && renderAccountTab(),
                        activeTab === 'billing' && renderBillingTab(),
                        activeTab === 'history' && renderHistoryTab()
                    )
                )
            )
        ),
        
        // Confirmation Modal
        React.createElement(ConfirmationModal, {
            isOpen: confirmModal.isOpen,
            onClose: closeConfirmModal,
            onConfirm: confirmModal.onConfirm,
            title: confirmModal.title,
            message: confirmModal.message,
            confirmText: 'Delete',
            cancelText: 'Cancel',
            confirmButtonId: 'delete-confirm-btn',
            cancelButtonId: 'delete-cancel-btn'
        }),
        
        // Generation Details Modal
        React.createElement(GenerationDetailsModal, {
            isOpen: detailsModal.isOpen,
            onClose: () => {
                setDetailsModal({ isOpen: false, details: null });
            },
            details: detailsModal.details,
            host: 'localhost:3001'
        }),
        
        // Thumbnail Preview Modal
        React.createElement(ThumbnailPreviewModal, {
            isOpen: previewModal.isOpen,
            onClose: () => {
                setPreviewModal({ isOpen: false, thumbnail: null, title: null, itemId: null });
            },
            thumbnail: previewModal.thumbnail,
            title: previewModal.title,
            itemId: previewModal.itemId
        }),
        
        // Password Change Modal
        React.createElement(PasswordChangeModal, {
            isOpen: passwordModal.isOpen,
            onClose: () => {
                setPasswordModal(prev => ({ ...prev, isOpen: false }));
            },
            onPasswordChange: handlePasswordChange,
            passwordState: passwordModal,
            setPasswordState: setPasswordModal,
            rateLimitInfo: passwordRateLimit
        })
    );
}; 