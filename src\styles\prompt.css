/* Styles specific to the prompt input component */

.prompt-section {
  transition: all 0.3s ease;
}

.prompt-label {
  transition: all 0.2s ease;
}

/* Status badges */
.prompt-enhancing-badge,
.prompt-auto-enhanced-badge {
  transition: all 0.2s ease;
}

/* Suggestion chips */
.prompt-suggestion-chip {
  transition: all 0.2s ease;
  font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
  font-size: 0.75rem;
  letter-spacing: 0.025em;
}

.prompt-suggestion-chip:hover {
  transform: scale(1.0);
}

.prompt-suggestion-chip:active {
  transform: scale(1.0);
}

/* Preview block */
.prompt-preview-block {
  transition: all 0.2s ease;
}

/* Animation for the enhancing spinner */
@keyframes spinner {
  to {transform: rotate(360deg);}
}

.animate-spin {
  animation: spinner 1s linear infinite;
}

/* Enhanced spinner for variations button - smoother animation */
.variations-btn-icon.animate-spin {
  animation: spinner 0.8s linear infinite;
}

/* Enhanced loading spinner for variations modal */
.variations-loading-spinner {
  position: relative;
  width: 64px;
  height: 64px;
}

.variations-loading-spinner div {
  position: absolute;
  border: 4px solid transparent;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.variations-loading-spinner div:nth-child(1) {
  border-top-color: #a855f7;
  animation-delay: 0s;
}

.variations-loading-spinner div:nth-child(2) {
  border-right-color: #3b82f6;
  animation-delay: -0.6s;
  animation-direction: reverse;
}

/* Smooth fade-in for variations modal content */
.variations-modal-content {
  animation: fadeInUp 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced loading message animation */
.variations-loading-message {
  animation: pulseGlow 2s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
  from {
    opacity: 0.8;
    text-shadow: 0 0 10px rgba(168, 85, 247, 0.3);
  }
  to {
    opacity: 1;
    text-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
  }
}

/* Staggered bounce animation for loading dots */
.loading-dots {
  display: flex;
  gap: 4px;
  margin-top: 1rem;
}

.loading-dot {
  width: 8px;
  height: 8px;
  background: linear-gradient(45deg, #a855f7, #3b82f6);
  border-radius: 50%;
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Improved variations modal backdrop */
.prompt-variations-overlay {
  backdrop-filter: blur(12px) saturate(150%);
  -webkit-backdrop-filter: blur(12px) saturate(150%);
  background: rgba(0, 0, 0, 0.7);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.prompt-variations-overlay.closing {
  backdrop-filter: blur(0px);
  -webkit-backdrop-filter: blur(0px);
  background: rgba(0, 0, 0, 0);
}

/* Enhanced modal appearance during loading */
.variations-modal-loading {
  background: linear-gradient(145deg, 
    rgba(55, 65, 81, 0.95) 0%, 
    rgba(75, 85, 99, 0.95) 50%, 
    rgba(55, 65, 81, 0.95) 100%);
  border: 1px solid rgba(139, 92, 246, 0.3);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.9),
    0 0 0 1px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Locked state styling */
.prompt-lock-indicator {
  transition: all 0.2s ease;
}

.prompt-lock-tooltip {
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
}

/* Circular button styles for prompt actions */
.prompt-improve-btn,
.prompt-variations-btn {
  transition: all 0.2s ease;
  position: relative;
}

.prompt-improve-btn:hover,
.prompt-variations-btn:hover {
  transform: scale(1.0);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.prompt-improve-btn:active,
.prompt-variations-btn:active {
  transform: scale(1.0);
}

/* Special styling when generating variations (spinning state) */
.prompt-variations-btn.text-purple-400,
button[aria-label*="Generating"].text-purple-400 {
    background-color: rgba(139, 92, 246, 0.2) !important;
    border-color: rgba(139, 92, 246, 0.5) !important;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(139, 92, 246, 0.3) !important;
}

/* Tooltip styles for prompt action buttons - Updated to prevent conflicts */
.prompt-action-buttons .group:hover .absolute {
  /* Remove animation to prevent conflicts with inline styles */
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Enhanced icon styling */
.improve-btn-icon {
  transition: transform 0.2s ease;
}

.prompt-improve-btn:hover .improve-btn-icon {
  transform: scale(1.0);
}

/* Line clamp for text previews */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Character counter */
.prompt-char-count {
  transition: color 0.2s ease;
}

.prompt-char-count.warning {
  color: #F59E0B; /* amber-500 */
}

.prompt-char-count.error {
  color: #EF4444; /* red-500 */
}

/* Enhanced Prompt Input Styling for Cinematic Typewriter Effect */

.prompt-input-container {
    width: 100%;
    max-width: 652px;
    position: relative;
    margin: 0 auto;
}

.prompt-input-wrapper {
    position: relative;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: 5px; /* Minimal margin */
}

.prompt-textarea {
    width: 100%;
    height: 150px; /* Fixed height for consistency across all devices */
    min-height: 150px;
    max-height: 150px;
    background-color: #212936;
    border: 1px solid #4B5563;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace !important;
    border-radius: 8px;
    padding: 8px 9.18px; /* Reduced padding by 15% from original 10.8px (was 12px originally, now 15% less from 10.8px) */
    color: #F9FAFB;
    font-size: 0.875rem; /* Desktop/tablet font size */
    line-height: 1.4; /* Comfortable line height for readability */
    resize: none; /* Disable manual resizing */
    overflow-y: auto; /* Allow scrolling if content overflows */
    transition: border-color 0.15s cubic-bezier(0.4,0,0.2,1), box-shadow 0.15s cubic-bezier(0.4,0,0.2,1), font-weight 0.2s ease;
    letter-spacing: 0.025em;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Lighter font weight when user starts typing content */
.prompt-textarea:not(:placeholder-shown) {
    font-weight: 500 !important; /* Medium weight between 400-500 when content is present */
}

/* Placeholder styling for normal mode - use Geist Mono */
.prompt-textarea::placeholder {
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    color: #9CA3AF;
    opacity: 0.7;
    font-weight: 500 !important;
    letter-spacing: 0.025em;
}

/* Typewriter mode styling - only active during improve/typewriter effect */
.prompt-textarea.font-mono,
.prompt-textarea.improving-typewriter {
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    letter-spacing: 0.025em;
    line-height: 1.6;
    font-weight: 500 !important;
}

.prompt-textarea:focus {
    outline: none;
    border-color: #8B5CF6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Typewriter mode styling */
.prompt-textarea.font-mono {
    height: 150px; /* Consistent height with regular mode */
    min-height: 150px;
    max-height: 150px;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    letter-spacing: 0.025em;
    font-weight: 500 !important;
    line-height: 1.6;
    background-color: #212936; /* Slightly darker for terminal feel */
    border-color: #553C9A; /* Purple tint */
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.1);
}

/* Typewriter focus state */
.prompt-textarea.font-mono:focus {
    border-color: #A855F7;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;

    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2), 0 0 25px rgba(139, 92, 246, 0.15);
    
}

/* Placeholder styling for typewriter mode */
.prompt-textarea.font-mono::placeholder {
    color: #9CA3AF;
    opacity: 0.8;
    font-weight: 500 !important;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
}


/* Responsive width adjustments */
@media (min-width: 768px) {
    .prompt-input-container {
        max-width: 652px; /* Maintain consistent width with preview container */
    }
    
    .prompt-textarea {
        font-size: 0.975rem;
        padding: 1.10rem;
        font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
        letter-spacing: 0.025em;
        font-weight: 500 !important;
    }
    
    /* Lighter font weight when user starts typing content on tablet/desktop */
    .prompt-textarea:not(:placeholder-shown) {
        font-weight: 500 !important; /* Medium weight between 400-500 when content is present */
    }
    
    .prompt-textarea.font-mono {
        font-size: 0.95rem;
        padding: 1.148rem; /* Reduced by 15% from 1.35rem (was 1.5rem originally) */
    }
}

@media (min-width: 1024px) {
    .prompt-input-container {
        max-width: 652px; /* Maintain consistent width with preview container */
    }
    
    .prompt-textarea {
        height: 150px; /* Consistent height across all devices */
        min-height: 150px;
        max-height: 150px;
        line-height: 1.6; /* Maintain comfortable line height on desktop */
        font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
        letter-spacing: 0.025em;
        font-weight: 500 !important;
    }
    
    /* Lighter font weight when user starts typing content on desktop */
    .prompt-textarea:not(:placeholder-shown) {
        font-weight: 500 !important; /* Medium weight between 400-500 when content is present */
    }
    
    .prompt-textarea.font-mono,
    .prompt-textarea.improving-typewriter {
        height: 150px; /* Consistent height across all devices */
        min-height: 150px;
        max-height: 150px;
        line-height: 1.6; /* Consistent line height */
    }
}

/* Mobile responsive - match preview container behavior */
@media (max-width: 700px) {
    .prompt-input-container {
        width: 100%;
        max-width: 100%;
        margin: 0;
        margin-bottom: 1rem; /* Space between prompt and controls */
    }
    
    /* Mobile prompt section styling to match controls */
    .prompt-section {
        background: rgba(17, 24, 39, 0.8);
        border-radius: 16px;
        border: 1px solid rgba(55, 65, 81, 0.3);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 1rem;
        transition: all 0.3s ease;
    }
    
    .prompt-section:focus-within {
        border-color: rgba(139, 92, 246, 0.4);
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
    }
}

/* Enhanced Mobile/Tablet Responsive Styles for userPromptInput textarea */
@media (max-width: 767px) {
    .prompt-textarea {
        height: 150px !important; /* Consistent height across all devices */
        min-height: 150px !important;
        max-height: 150px !important;
        font-size: 0.96rem !important; /* 20% increase from 0.8rem (0.8 * 1.2 = 0.96rem) */
        padding: 0.765rem !important; /* Reduced by 15% from 0.9rem (was 1rem originally) */
        line-height: 1.6 !important; /* Adjusted line height for larger font */
        resize: none !important; /* Disable manual resizing */
        overflow-y: auto !important; /* Allow scrolling if content overflows */
        font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace !important;
        letter-spacing: 0.02em !important; /* Slightly reduced letter spacing for mobile */
        font-weight: 500 !important;
    }
    
    /* Lighter font weight when user starts typing content on mobile */
    .prompt-textarea:not(:placeholder-shown) {
        font-weight: 500 !important; /* Medium weight between 400-500 when content is present */
    }
    
    .prompt-textarea::placeholder {
        font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace !important;
        font-size: 0.96rem !important; /* 20% increase from 0.8rem to match input (0.8 * 1.2 = 0.96rem) */
        opacity: 0.7; /* Slightly more visible placeholder */
        letter-spacing: 0.02em !important; /* Match reduced letter spacing */
        font-weight: 500 !important; /* Reduced from 600 to 400 for mobile readability */
    }
    
    /* Typewriter mode adjustments for mobile */
    .prompt-textarea.font-mono,
    .prompt-textarea.improving-typewriter {
        font-size: 0.95rem !important;
        padding: 0.765rem !important; /* Reduced by 15% from 0.9rem to match mobile padding */
        line-height: 1.6 !important; /* Consistent line height */
        font-weight: 500 !important;
        letter-spacing: 0.025em;
    }
}

/* Cinematic glow animation */
@keyframes pulse {
    0%, 100% {
        opacity: 0.4;
    }
    50% {
        opacity: 0.8;
    }
}

/* Remove specific improve button overrides - let it use the same styles as variations button */

/* Remove specific improve button hover overrides - let it use the same styles as variations button */

/* Remove specific improve button icon overrides - let it use the same styles as variations button */

/* Remove specific improve button icon hover overrides - let it use the same styles as variations button */

/* Hover and active states removed for improve button - no effects applied */

/* Remove additional improve button positioning overrides - let it use the same styles as variations button */

/* Remove improve button hover state overrides - let it use the same styles as variations button */

/* Remove improve button active state overrides - let it use the same styles as variations button */

/* Special styling when improving (spinning state) */
.prompt-improve-btn.text-purple-400,
button[aria-label*="Improve"].text-purple-400,
button[aria-label*="improve"].text-purple-400 {
    background-color: rgba(139, 92, 246, 0.2) !important;
    border-color: rgba(139, 92, 246, 0.5) !important;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(139, 92, 246, 0.3) !important;
}

/* Lock tooltip enhanced styling */
#prompt-lock-tooltip {
    backdrop-filter: blur(12px);
    background-color: rgba(245, 158, 11, 0.95);
    border: 1px solid rgba(245, 158, 11, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Disabled state styling */
.prompt-textarea:disabled {
    background-color: #2D3748;
    border-color: #4A5568;
    color: #A0AEC0;
    cursor: not-allowed;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace !important;
    font-weight: 500 !important;
    letter-spacing: 0.025em;
}

/* Selection styling for better UX */
.prompt-textarea::selection {
    background-color: rgba(139, 92, 246, 0.3);
    color: #F9FAFB;
}

/* Typewriter effect for improve button output */
.prompt-textarea.improving-typewriter {
    background-color: #2A3441;
    border-color: #A855F7;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2), 0 0 25px rgba(139, 92, 246, 0.15);
    animation: subtle-pulse 2s ease-in-out infinite;
    transition: border-color 0.15s cubic-bezier(0.4,0,0.2,1), box-shadow 0.15s cubic-bezier(0.4,0,0.2,1);
}

/* Subtle glow animation during typewriter effect */
@keyframes subtle-pulse {
    0%, 100% {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15), 0 0 20px rgba(139, 92, 246, 0.1);
    }
    50% {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.25), 0 0 30px rgba(139, 92, 246, 0.2);
    }
}

/* Prompt variations dropdown styles */
#prompt-variations-dropdown {
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Variation card hover effects */
#prompt-variations-dropdown .group:hover {
    transform: scale(1.0);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Variations Modal Animations */
.prompt-variations-overlay {
    animation: fadeIn 200ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.prompt-variations-modal {
    animation: slideInUp 200ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

/* Custom scrollbar for variations modal */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.3);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.7);
    border-radius: 3px;
    transition: background 0.2s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.9);
}

/* Firefox scrollbar styling */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(139, 92, 246, 0.7) rgba(55, 65, 81, 0.3);
}

/* Enhanced animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.prompt-variation-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.prompt-variation-card:hover {
    transform: translateY(-2px);
}

.prompt-variation-implement-btn:hover {
    transform: translateX(2px);
}

/* Focus placeholder styling */
.prompt-focus-placeholder {
    position: absolute;
    top: 8px;
    left: 9.18px; /* Matches textarea padding for perfect alignment */
    color: #9CA3AF;
    font-size: 0.875rem;
    line-height: 1.4;
    font-family: 'Geist Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;
    font-weight: 500;
    letter-spacing: 0.025em;
    pointer-events: none;
    z-index: 1;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow: hidden;
    max-width: calc(100% - 18.36px); /* Account for left and right padding */
    max-height: calc(150px - 16px); /* Account for top and bottom padding */
    transition: opacity 0.2s ease;
}

/* Hide placeholder when textarea has content */
.prompt-textarea:not(:placeholder-shown) + .prompt-focus-placeholder {
    opacity: 0;
}

/* Responsive focus placeholder adjustments */
@media (min-width: 768px) {
    .prompt-focus-placeholder {
        font-size: 0.975rem;
        top: 1.10rem;
        left: 1.10rem;
        max-width: calc(100% - 2.20rem);
        max-height: calc(150px - 2.20rem);
    }
}

@media (min-width: 1024px) {
    .prompt-focus-placeholder {
        line-height: 1.6;
    }
}

@media (max-width: 767px) {
    .prompt-focus-placeholder {
        font-size: 0.96rem;
        top: 0.765rem;
        left: 0.765rem;
        max-width: calc(100% - 1.53rem);
        max-height: calc(150px - 1.53rem);
        line-height: 1.6;
        letter-spacing: 0.02em;
    }
}

/* Updated button styles for variations modal */
.prompt-variation-use-btn {
    transition: all 0.2s ease;
}

.prompt-variation-use-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
}

/* Enhanced variation styles for better readability */
.prompt-variation-use-btn {
    font-size: 0.75rem;
    padding: 4px 12px;
}

.prompt-variation-use-btn:hover {
    background-color: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.8);
}

/* Enhanced variation card text styling */
.prompt-variation-card p {
    line-height: 1.5;
    font-size: 0.875rem;
}

/* Responsive modal adjustments */
@media (min-width: 769px) and (max-width: 1399px) {
    .prompt-variations-modal {
        max-width: 90vw;
        margin: 0 auto;
    }
}

@media (max-width: 767px) {
    .prompt-variations-modal {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .prompt-variation-card {
        padding: 1rem;
    }
    
    .prompt-variation-card p {
        font-size: 0.8rem;
        padding-right: 3rem;
    }
}

/* Responsive modal for portrait mobile */
@media (max-width: 768px) and (orientation: portrait) {
  /* Ensure consistent positioning for glassmorphism containers */
  #welcome-form-content-container{
    max-height: 90vh;
    overflow-y: auto;
  }
}   