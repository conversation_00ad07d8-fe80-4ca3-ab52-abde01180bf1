// faceBlendEngine.js - ManualFaceBlendMatch2Stage implementation
// Stage 1: Local face detection & analysis using face-api.js (@vladmandic build)
// Stage 2: Alignment + color transfer + OpenCV seamlessClone for blending
// NOTE: This is an MVP implementation aimed at boosting success-rate for arbitrary faces.

/* global faceapi, cv */ // provided via CDN in index.html

let faceApiModelsLoaded = false;

async function loadFaceApiModels() {
  if (faceApiModelsLoaded) return;
  const modelPath = 'https://cdn.jsdelivr.net/npm/@vladmandic/face-api/model/';
  await Promise.all([
    faceapi.nets.tinyFaceDetector.loadFromUri(modelPath),
    faceapi.nets.faceLandmark68Net.loadFromUri(modelPath),
    faceapi.nets.faceExpressionNet.loadFromUri(modelPath)
  ]);
  faceApiModelsLoaded = true;
}

function toCvMat(image) {
  const mat = cv.imread(image);
  return mat;
}

function matchSkinTone(srcMat, destMat) {
  // Simple mean-shift color adjustment (placeholder)
  const srcMean = cv.mean(srcMat);
  const destMean = cv.mean(destMat);
  const alpha = destMean[0] / (srcMean[0] + 1e-6);
  const beta = destMean[1] / (srcMean[1] + 1e-6);
  const gamma = destMean[2] / (srcMean[2] + 1e-6);
  const adjusted = new cv.Mat();
  cv.multiply(srcMat, new cv.Mat(srcMat.rows, srcMat.cols, srcMat.type(), [alpha, beta, gamma, 1]), adjusted);
  return adjusted;
}

export async function blendFaces(sourceUrl, targetUrl) {
  await loadFaceApiModels();

  // Load images via HTMLImageElement
  const [srcImg, tgtImg] = await Promise.all([loadImage(sourceUrl), loadImage(targetUrl)]);

  // Detect faces & landmarks
  const detectionsSrc = await faceapi.detectSingleFace(srcImg, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks();
  const detectionsTgt = await faceapi.detectSingleFace(tgtImg, new faceapi.TinyFaceDetectorOptions()).withFaceLandmarks();

  if (!detectionsSrc || !detectionsTgt) {
    throw new Error('Face detection failed on source or target');
  }

  // Align source to target using landmarks (eyes & nose base)
  const alignedSrcCanvas = faceapi.createCanvasFromMedia(srcImg);
  const dims = faceapi.matchDimensions(alignedSrcCanvas, tgtImg, true);
  faceapi.resizeResults(detectionsSrc, dims);
  // For MVP, simply resize/crop source face bounding box to match target box
  const { box: srcBox } = detectionsSrc.detection;
  const { box: tgtBox } = detectionsTgt.detection;

  const ctx = alignedSrcCanvas.getContext('2d');
  const faceCrop = ctx.getImageData(srcBox.x, srcBox.y, srcBox.width, srcBox.height);
  const tmpCanvas = document.createElement('canvas');
  tmpCanvas.width = tgtBox.width;
  tmpCanvas.height = tgtBox.height;
  const tmpCtx = tmpCanvas.getContext('2d');
  tmpCtx.putImageData(faceCrop, 0, 0);
  // Resize to target face size
  const resizedFace = document.createElement('canvas');
  resizedFace.width = tgtBox.width;
  resizedFace.height = tgtBox.height;
  resizedFace.getContext('2d').drawImage(tmpCanvas, 0, 0, tgtBox.width, tgtBox.height);

  // Convert to cv.Mat for color adjustment
  const srcMat = toCvMat(resizedFace);
  const tgtMat = toCvMat(tgtImg);

  const adjustedSrc = matchSkinTone(srcMat, tgtMat);

  // Create mask (ellipse)
  const mask = new cv.Mat.zeros(adjustedSrc.rows, adjustedSrc.cols, cv.CV_8UC1);
  cv.ellipse(mask, new cv.Point(adjustedSrc.cols / 2, adjustedSrc.rows / 2), new cv.Size(adjustedSrc.cols / 2, adjustedSrc.rows / 2), 0, 0, 360, new cv.Scalar(255), -1);

  // Seamless clone onto target
  const center = new cv.Point(tgtBox.x + tgtBox.width / 2, tgtBox.y + tgtBox.height / 2);
  const output = new cv.Mat();
  cv.seamlessClone(adjustedSrc, tgtMat, mask, center, output, cv.NORMAL_CLONE);

  // Encode to dataURL via canvas
  const outCanvas = document.createElement('canvas');
  outCanvas.width = tgtMat.cols;
  outCanvas.height = tgtMat.rows;
  cv.imshow(outCanvas, output);

  // Cleanup mats
  srcMat.delete();
  tgtMat.delete();
  adjustedSrc.delete();
  mask.delete();
  output.delete();

  return outCanvas.toDataURL('image/jpeg');
}

async function loadImage(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
} 