import React from 'react';

const AuthThemeToggle = ({ isLightTheme, onToggle }) => {
    return (
        <button
            onClick={onToggle}
            className="auth-theme-toggle"
            title={`Switch to ${isLightTheme ? 'Dark' : 'Light'} Theme`}
            aria-label={`Switch to ${isLightTheme ? 'Dark' : 'Light'} Theme`}
            style={{ display: 'none' }}
        >
            <span className="toggle-icon">
                {isLightTheme ? '🌙' : '☀️'}
            </span>
            <span className="toggle-text">
                {isLightTheme ? 'Dark Theme' : 'Light Theme'}
            </span>
        </button>
    );
};

export { AuthThemeToggle }; 