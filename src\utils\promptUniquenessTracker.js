// Prompt Uniqueness Tracker - Ensures diverse AI enhancement variations
// Implements ai-enhanced-prompt-uniqueness-and-generate-lock system

/**
 * Tracks enhancement history and ensures diverse, unique prompt variations
 * Each re-enhancement should produce significantly different results with varied:
 * - Shot types (overhead, close-up, wide shot, dynamic angle)
 * - Position/placement (centered, off-center, split-screen, layered)
 * - LUT setup/color grading (warm tones, cool blues, high contrast, muted)
 * - Context ideas (action-focused, emotion-driven, technical, artistic)
 */

class PromptUniquenessTracker {
    constructor() {
        this.enhancementHistory = new Map(); // Maps original prompt to enhancement history
        this.variationStrategies = this.initializeVariationStrategies();
    }

    /**
     * Initialize diverse variation strategies for unique enhancements
     */
    initializeVariationStrategies() {
        return [
            {
                id: 'overhead_warm',
                name: 'Overhead Warm LUT',
                shotType: 'overhead aerial shot',
                positioning: 'centered with symmetrical layout',
                colorGrading: 'warm golden hour LUT with orange-amber tones',
                context: 'lifestyle and approachable atmosphere',
                lighting: 'soft natural lighting with warm fill',
                composition: 'rule of thirds with balanced visual weight'
            },
            {
                id: 'closeup_cool',
                name: 'Close-up <PERSON> Tones',
                shotType: 'tight close-up with shallow depth of field',
                positioning: 'off-center placement with negative space',
                colorGrading: 'cool blue-teal cinematic LUT with high contrast',
                context: 'technical precision and professional focus',
                lighting: 'dramatic side lighting with sharp shadows',
                composition: 'leading lines and dynamic asymmetry'
            },
            {
                id: 'wide_vibrant',
                name: 'Wide Shot Vibrant',
                shotType: 'wide establishing shot showing full context',
                positioning: 'split-screen or layered foreground-background',
                colorGrading: 'vibrant saturated colors with punchy contrast',
                context: 'energetic and action-oriented presentation',
                lighting: 'multi-point lighting setup with rim lights',
                composition: 'diagonal lines and movement-based flow'
            },
            {
                id: 'dynamic_moody',
                name: 'Dynamic Moody Angle',
                shotType: 'dynamic dutch angle with tilted perspective',
                positioning: 'asymmetrical placement with visual tension',
                colorGrading: 'moody dark LUT with selective color pops',
                context: 'dramatic storytelling with emotional depth',
                lighting: 'chiaroscuro lighting with strong contrast',
                composition: 'unconventional framing with visual interest'
            },
            {
                id: 'minimalist_clean',
                name: 'Minimalist Clean Aesthetic',
                shotType: 'medium shot with clean background separation',
                positioning: 'perfectly centered with generous white space',
                colorGrading: 'neutral LUT with subtle color grading',
                context: 'modern minimalist and sophisticated appeal',
                lighting: 'even studio lighting with soft shadows',
                composition: 'geometric balance and clean lines'
            },
            {
                id: 'action_burst',
                name: 'Action Burst Dynamic',
                shotType: 'action shot with motion blur and freeze frame',
                positioning: 'explosive radial composition from center outward',
                colorGrading: 'high-energy LUT with saturated primaries',
                context: 'intense action and high-impact energy',
                lighting: 'multiple strobe effects and dynamic highlights',
                composition: 'radial burst with converging action lines'
            },
            {
                id: 'artistic_abstract',
                name: 'Artistic Abstract Vision',
                shotType: 'creative angle with unconventional perspective',
                positioning: 'layered depth with multiple focal planes',
                colorGrading: 'artistic color grade with creative color shifts',
                context: 'creative expression and artistic interpretation',
                lighting: 'experimental lighting with colored gels',
                composition: 'abstract geometric shapes and negative space'
            },
            {
                id: 'retro_vintage',
                name: 'Retro Vintage Film',
                shotType: 'classic film-inspired framing',
                positioning: 'traditional composition with vintage styling',
                colorGrading: 'vintage film LUT with grain and slight desaturation',
                context: 'nostalgic and timeless aesthetic appeal',
                lighting: 'classic three-point lighting setup',
                composition: 'traditional film composition rules'
            }
        ];
    }

    /**
     * Get the next unique variation strategy for a given prompt
     * @param {string} originalPrompt - The original user prompt
     * @returns {Object} - The next variation strategy to use
     */
    getNextVariationStrategy(originalPrompt) {
        const promptKey = originalPrompt.toLowerCase().trim();
        
        if (!this.enhancementHistory.has(promptKey)) {
            this.enhancementHistory.set(promptKey, {
                attempts: 0,
                usedStrategies: [],
                lastEnhanced: Date.now()
            });
        }

        const history = this.enhancementHistory.get(promptKey);
        
        // Find strategies that haven't been used yet
        const availableStrategies = this.variationStrategies.filter(
            strategy => !history.usedStrategies.includes(strategy.id)
        );

        let selectedStrategy;
        
        if (availableStrategies.length > 0) {
            // Use a fresh strategy
            selectedStrategy = availableStrategies[
                Math.floor(Math.random() * availableStrategies.length)
            ];
        } else {
            // All strategies used - reset and pick randomly (but track for debugging)
            console.log(`[Prompt Uniqueness] All strategies used for "${originalPrompt}", resetting cycle`);
            history.usedStrategies = [];
            selectedStrategy = this.variationStrategies[
                Math.floor(Math.random() * this.variationStrategies.length)
            ];
        }

        // Update history
        history.attempts++;
        history.usedStrategies.push(selectedStrategy.id);
        history.lastEnhanced = Date.now();

        console.log(`[Prompt Uniqueness] Attempt ${history.attempts}: Using strategy "${selectedStrategy.name}" for variety`);
        
        return selectedStrategy;
    }

    /**
     * Build enhancement instructions based on the selected strategy
     * @param {string} originalPrompt - The original user prompt
     * @param {Object} strategy - The variation strategy to apply
     * @returns {string} - Enhanced prompt with unique variation applied
     */
    buildUniqueEnhancement(originalPrompt, strategy) {
        const basePrompt = originalPrompt.trim();
        
        return `Create a cinematic YouTube thumbnail using a ${strategy.shotType} perspective. 
        
COMPOSITION & POSITIONING:
${strategy.positioning} with ${strategy.composition}.

VISUAL STYLE & COLOR:
Apply ${strategy.colorGrading} for the overall mood and atmosphere.

LIGHTING SETUP:
Use ${strategy.lighting} to create depth and visual interest.

CONTEXT & THEME:
Focus on ${strategy.context} that enhances the subject matter: "${basePrompt}".

TECHNICAL EXECUTION:
- Render at 1280x720 resolution optimized for YouTube
- Ensure high contrast and readability at small sizes
- Create visual hierarchy that guides the viewer's eye
- Maintain consistency with the ${strategy.name} aesthetic approach

The result should feel distinctly different from previous iterations while maintaining professional quality and thumbnail effectiveness.`;
    }

    /**
     * Generate a unique enhanced prompt variation
     * @param {string} originalPrompt - The original user prompt
     * @param {string} category - The detected video category (optional)
     * @returns {Object} - Enhanced prompt result with strategy info
     */
    generateUniqueVariation(originalPrompt, category = 'general') {
        if (!originalPrompt || originalPrompt.trim().length < 3) {
            return {
                enhancedPrompt: originalPrompt,
                explanation: 'Prompt too short for enhancement',
                strategy: 'none',
                variation: null
            };
        }

        const strategy = this.getNextVariationStrategy(originalPrompt);
        const enhancedPrompt = this.buildUniqueEnhancement(originalPrompt, strategy);
        
        return {
            enhancedPrompt,
            explanation: `Applied ${strategy.name}: ${strategy.shotType} with ${strategy.colorGrading} and ${strategy.context}`,
            strategy: strategy.id,
            variation: strategy,
            isUnique: true
        };
    }

    /**
     * Clear history for a specific prompt (useful for testing)
     * @param {string} originalPrompt - The prompt to clear history for
     */
    clearPromptHistory(originalPrompt) {
        const promptKey = originalPrompt.toLowerCase().trim();
        this.enhancementHistory.delete(promptKey);
        console.log(`[Prompt Uniqueness] Cleared history for: "${originalPrompt}"`);
    }

    /**
     * Get enhancement statistics for debugging
     * @param {string} originalPrompt - The prompt to get stats for
     * @returns {Object} - Statistics about enhancement history
     */
    getEnhancementStats(originalPrompt) {
        const promptKey = originalPrompt.toLowerCase().trim();
        const history = this.enhancementHistory.get(promptKey);
        
        if (!history) {
            return {
                attempts: 0,
                usedStrategies: [],
                availableStrategies: this.variationStrategies.length,
                nextStrategy: 'First enhancement'
            };
        }

        const availableCount = this.variationStrategies.length - history.usedStrategies.length;
        const nextStrategy = availableCount > 0 ? 'New strategy available' : 'Will reset cycle';

        return {
            attempts: history.attempts,
            usedStrategies: history.usedStrategies,
            availableStrategies: availableCount,
            nextStrategy,
            lastEnhanced: new Date(history.lastEnhanced).toLocaleTimeString()
        };
    }
}

// Create singleton instance
const promptUniquenessTracker = new PromptUniquenessTracker();

export { promptUniquenessTracker };
export default promptUniquenessTracker; 