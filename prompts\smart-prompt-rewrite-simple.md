# Smart Prompt Rewrite (Simplified & Overlay-Free)

id: smart-prompt-rewrite-simple.md

🎯 Feature: Smart Prompt Rewriting (Clear, Clean, and Overlay-Free)

Objective

Improve generic or vague user prompts (e.g. "The truth about Apple devices") by rephrasing them into cleaner, more visually descriptive prompts that do not include unnecessary verbs, emotional expressions, or text instructions.

This is designed for users who rely on the Text Overlay toggle to add titles separately — so the rewritten prompts focus purely on scene setup and composition.

✅ Rewrite Strategy

| ✅ DO                      | 🚫 DO NOT                             |
|---------------------------|----------------------------------------|
| Focus on subject, object, and background  | Avoid verbs like "reacting," "holding," "explaining" |
| Describe the scene simply | Avoid emotional states like "shocked" or "angry" |
| Keep the prompt short     | Avoid telling the model to "add text" or "show overlay" |

✍️ Example Input → Output

| User Input                           | Enhanced Prompt |
|-------------------------------------|-----------------|
| The truth about Apple devices       | A clean cinematic shot of an Apple product on a dark tech background |
| My cringy old TikToks               | A retro-style background with video reels and subtle lighting |
| Best AI tools in 2025               | A modern tech workspace with glowing icons of various AI tools |
| Worst purchases I made              | A dramatic table setup with gadgets placed under spotlight |
| Unboxing iPhone 16                  | A glowing smartphone centered on a minimalist background |

🔒 Prompt Output Policy

Enhanced prompts should:

Be 1–2 sentences at most

Not include any uppercase title or text instructions

Not mention emotional tone or user reaction

Not instruct to "add glow", "show emoji", or "write overlay"

✅ Final Prompt Injection Example

A dark background with cinematic lighting, featuring a MacBook and iPhone in the center.

Ready for UI Use

Add toggle: ✨ Auto-Improve Prompt

If enabled: transforms user input before sending to generation

Keeps creative control lightweight and non-technical 