<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Overlay Sanitization Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #e5e5e5;
        }
        .test-container {
            background: #2d2d2d;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #404040;
        }
        .input-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #f0f0f0;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #1a1a1a;
            color: #e5e5e5;
            font-family: monospace;
        }
        .result {
            background: #1e3a8a;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            word-break: break-all;
        }
        .test-cases {
            display: grid;
            gap: 15px;
        }
        .test-case {
            background: #374151;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #10b981;
        }
        .original {
            background: #7f1d1d;
            color: #fecaca;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        .sanitized {
            background: #14532d;
            color: #bbf7d0;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
            font-family: monospace;
        }
        h1, h2 {
            color: #f9fafb;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        .status.pass {
            background: #059669;
            color: white;
        }
        .status.fail {
            background: #dc2626;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🛠️ Text Overlay Safe Character Sanitization Test</h1>
    <p>This page tests the sanitization system that removes problematic characters from YouTube thumbnail overlay text before image generation.</p>

    <div class="test-container">
        <h2>Interactive Test</h2>
        <div class="input-group">
            <label for="testInput">Enter text with special characters:</label>
            <textarea id="testInput" rows="3" placeholder="Try: Gaming Review (Epic) [MUST WATCH] {2024} @username #trending 🔥💯"></textarea>
        </div>
        <div class="result" id="result">
            <strong>Sanitized Result:</strong> <span id="sanitizedText">Type something above...</span>
        </div>
    </div>

    <div class="test-container">
        <h2>Predefined Test Cases</h2>
        <div class="test-cases" id="testCases">
            <!-- Test cases will be populated by JavaScript -->
        </div>
    </div>

    <script type="module">
        // Text Sanitization Functions (copied from textSanitizer.js)
        function sanitizeOverlayText(text, options = {}) {
            const {
                preserveSpaces = false,
                removeEmojis = true,
                removeNumbers = false,
                convertToUppercase = true
            } = options;

            if (!text || typeof text !== 'string') {
                return '';
            }

            let sanitized = text;

            // Remove problematic special characters that can interfere with image generation
            const specialCharsPattern = /[()[\]{}<>|\\`~@#$%^&*+=]/g;
            sanitized = sanitized.replace(specialCharsPattern, '');

            // Remove quotes that can break prompt parsing
            const quotesPattern = /["""''`]/g;
            sanitized = sanitized.replace(quotesPattern, '');

            // Remove mathematical and currency symbols
            const mathCurrencyPattern = /[÷×±∞€£¥₹$¢]/g;
            sanitized = sanitized.replace(mathCurrencyPattern, '');

            // Remove emoji characters if enabled
            if (removeEmojis) {
                const emojiPattern = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]|[\u{FE0F}]|[\u{200D}]|[\u{1F3FB}-\u{1F3FF}]/gu;
                sanitized = sanitized.replace(emojiPattern, '');
            }

            // Remove numbers if enabled
            if (removeNumbers) {
                sanitized = sanitized.replace(/[0-9]/g, '');
            }

            // Clean up punctuation - keep essential ones, remove problematic ones
            const problematicPunctuationPattern = /[^\w\s!?.,;:\-]/g;
            sanitized = sanitized.replace(problematicPunctuationPattern, '');

            // Normalize whitespace
            if (!preserveSpaces) {
                sanitized = sanitized.replace(/\s+/g, ' ');
            }

            // Remove leading/trailing whitespace
            sanitized = sanitized.trim();

            // Convert to uppercase if enabled
            if (convertToUppercase) {
                sanitized = sanitized.toUpperCase();
            }

            // Final cleanup
            const finalCleanupPattern = /[^\w\s!?.,;:\-]/g;
            sanitized = sanitized.replace(finalCleanupPattern, '');

            return sanitized;
        }

        // Interactive test
        const testInput = document.getElementById('testInput');
        const sanitizedText = document.getElementById('sanitizedText');

        testInput.addEventListener('input', (e) => {
            const original = e.target.value;
            const sanitized = sanitizeOverlayText(original);
            sanitizedText.textContent = sanitized || '(empty)';
        });

        // Predefined test cases
        const testCases = [
            {
                name: "Basic Parentheses and Brackets",
                input: "Gaming Review (Epic) [MUST WATCH]",
                expected: "GAMING REVIEW EPIC MUST WATCH"
            },
            {
                name: "Special Characters and Symbols",
                input: "Tech Tips & Tricks @2024 #trending",
                expected: "TECH TIPS  TRICKS  TRENDING"
            },
            {
                name: "Emojis and Unicode",
                input: "Amazing Video 🔥💯 Must See! 😱",
                expected: "AMAZING VIDEO  MUST SEE!"
            },
            {
                name: "Currency and Math Symbols",
                input: "Make $1000+ in 30 Days! (100% Proven)",
                expected: "MAKE  IN  DAYS!  PROVEN"
            },
            {
                name: "Multiple Quote Types",
                input: 'Review: "Amazing" vs \'Terrible\' Product',
                expected: "REVIEW: AMAZING VS TERRIBLE PRODUCT"
            },
            {
                name: "Complex Mixed Characters",
                input: "React.js Tutorial {2024} - Learn 100% [Free Course] @codecamp",
                expected: "REACT.JS TUTORIAL  - LEARN  FREE COURSE "
            },
            {
                name: "Excessive Punctuation",
                input: "OMG!!! This is CRAZY??? (No Way!!!)",
                expected: "OMG!!! THIS IS CRAZY??? NO WAY!!!"
            },
            {
                name: "YouTube-Style Title",
                input: "I Tried [EXTREME CHALLENGE] for 24 Hours! (Gone Wrong) 😳",
                expected: "I TRIED EXTREME CHALLENGE FOR  HOURS! GONE WRONG "
            }
        ];

        // Run test cases
        const testCasesContainer = document.getElementById('testCases');
        testCases.forEach((testCase, index) => {
            const result = sanitizeOverlayText(testCase.input);
            const passed = result === testCase.expected;
            
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            testDiv.innerHTML = `
                <h3>Test ${index + 1}: ${testCase.name} <span class="status ${passed ? 'pass' : 'fail'}">${passed ? 'PASS' : 'FAIL'}</span></h3>
                <div class="original"><strong>Original:</strong> ${testCase.input}</div>
                <div class="sanitized"><strong>Result:</strong> ${result}</div>
                <div style="margin-top: 8px; font-size: 12px; color: #9ca3af;">
                    <strong>Expected:</strong> ${testCase.expected}
                </div>
            `;
            testCasesContainer.appendChild(testDiv);
        });

        // Set initial test input
        testInput.value = "Gaming Review (Epic) [MUST WATCH] {2024} @username #trending 🔥💯";
        testInput.dispatchEvent(new Event('input'));
    </script>
</body>
</html> 