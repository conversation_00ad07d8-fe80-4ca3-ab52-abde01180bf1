---
description: 
globs: 
alwaysApply: false
---
# 📌 Face Upload Match Enhancement Rule

---
ruleId: face-upload-match-enhancement
description: >
  Enhance the "Custom Face Upload" feature to ensure the generated AI image
  accurately and consistently matches the face in the uploaded image or provided URL.
  This rule addresses exact face matching/swapping requirements and fixes upload functionality issues.
ruleType: always
appliesTo:
  - /src/utils/promptFormatter.js # For integrating facial matching instructions into prompts
  - /src/components/ControlPanel.jsx # Where face upload controls are handled
  - /src/App.jsx # Main app file with upload handlers
  - /src/components/person/FaceUploadSection.jsx # Face upload UI component
  - /src/styles/controls.css # Styling for face upload section

---

## 🎯 Problem Statement

### Current Issues:
1. **Inaccurate Face Matching**: Generated images show similar but not exact face matches
2. **Upload Functionality**: Local photo upload feature not working properly
3. **Face Swapping**: Need true face replacement/swap capability, not just similar faces

### User Expectations:
- Uploaded face should be exactly replicated in the generated thumbnail
- The face should look like the same person, not just similar
- Local file upload should work seamlessly

---

## 🛠️ Implementation Details

### 1. Enhanced Prompt Instructions

When a custom face is provided (uploaded or URL), the prompt now includes:

```javascript
**CRITICAL FACE MATCHING REQUIREMENTS**: 
The person's face in the generated image MUST be an EXACT match to the uploaded reference face. 
This is a face swap/replacement task - preserve the EXACT facial identity including:
- Bone structure, nose shape, eye shape and spacing
- Mouth shape, jawline, forehead proportions
- Ear shape if visible
- Any unique facial features (moles, freckles, scars)
- Exact skin tone and texture
- Hair color and style

The face should look like the SAME PERSON, not just similar. 
Think of this as placing the uploaded person's face onto the generated body/scene.
The final result must be indistinguishable from having the uploaded person actually in the scene.
```

### 2. Priority System

Added **HIGHEST PRIORITY** instruction when custom face is provided:
```javascript
thumbnailGuidance += `**HIGHEST PRIORITY**: Ensure the generated person's face is an EXACT match to the provided reference face. This is a face replacement/swap task that must preserve the person's exact identity. `;
```

### 3. File Upload Fixes

Fixed the file input element:
- Changed accept attribute from `.jpg, .jpeg, .png` to `image/jpeg,image/jpg,image/png`
- Added ID for debugging: `id: 'face-upload-input'`
- Enhanced error handling with detailed console logging

### 4. Improved Error Handling

```javascript
const handleFileUpload = (event) => {
    console.log('File upload triggered');
    const file = event.target.files[0];
    
    // Detailed logging for debugging
    console.log('File selected:', file.name, 'Type:', file.type, 'Size:', file.size);
    
    // Better error messages
    if (!allowedTypes.includes(file.type)) {
        console.error('Invalid file type:', file.type);
        setErrorMsg('Invalid file type. Please upload JPEG or PNG.');
    }
    
    // Try-catch for FileReader operations
    try {
        reader.readAsDataURL(file);
    } catch (error) {
        console.error('Error starting file read:', error);
        setErrorMsg('Error processing file. Please try again.');
    }
};
```

---

## 📋 Technical Approach

### Frontend Implementation (Current):
1. **File Upload**: Convert image to base64 data URL
2. **Prompt Building**: Include face matching instructions
3. **Placeholder**: Use `[uploaded face image]` in prompt when data URL detected
4. **URL Support**: Direct URL reference for web-hosted images

### Recommended Backend Enhancement (Future):
```javascript
// Pseudo-code for proper face swapping implementation
async function enhancedImageGeneration(prompt, faceImageData) {
    if (faceImageData) {
        // 1. Extract face embedding
        const faceEmbedding = await extractFaceEmbedding(faceImageData);
        
        // 2. Send to specialized face-aware model
        const result = await generateWithFaceControl({
            prompt: prompt,
            faceEmbedding: faceEmbedding,
            faceWeight: 0.9 // High weight for exact matching
        });
        
        return result;
    }
    
    // Regular generation without face control
    return await standardGeneration(prompt);
}
```

---

## 🚀 Usage Guide

### For Uploaded Images:
1. Click "Upload" tab in Custom Face Upload section
2. Click the upload area or drag & drop an image
3. Supported formats: JPEG, PNG (max 2MB)
4. Image converts to base64 and shows preview
5. Generate thumbnail with exact face matching

### For Image URLs:
1. Click "URL" tab
2. Paste image URL (must be publicly accessible)
3. Press Enter or click outside to load
4. Preview shows if URL is valid
5. Generate thumbnail with face from URL

### Debugging Upload Issues:
1. Open browser console (F12)
2. Try uploading a file
3. Check console for detailed logs:
   - "File upload triggered"
   - "File selected: [name] Type: [type] Size: [size]"
   - "File read successfully"
   - Any error messages

---

## 🔍 Testing Checklist

✅ **Upload Functionality**
- [ ] File input opens file picker when clicked
- [ ] Accepts JPEG/PNG files
- [ ] Rejects other file types with error
- [ ] Shows file size error for >2MB files
- [ ] Displays preview after successful upload
- [ ] Console logs show upload progress

✅ **Face Matching Quality**
- [ ] Generated face matches uploaded face exactly
- [ ] Facial features preserved (eyes, nose, mouth shape)
- [ ] Skin tone matches original
- [ ] Hair style/color preserved
- [ ] Unique features maintained (moles, scars, etc.)

✅ **URL Loading**
- [ ] Accepts valid image URLs
- [ ] Shows error for invalid URLs
- [ ] Displays preview for loaded images
- [ ] Face matching works with URL images

---

## 🎨 UI/UX Improvements

### Visual Feedback:
- Beta badge indicates feature is experimental
- Clear preview with border highlighting
- Remove button for easy image clearing
- Tab switching between Upload/URL modes

### Error States:
- Clear error messages for invalid files
- Console logging for debugging
- Graceful handling of read errors
- Warning about development status

---

## 📝 Notes & Limitations

### Current Limitations:
1. **API Constraints**: OpenAI's image generation models have inherent limitations in exact face replication
2. **No Face Embedding**: Current implementation uses text prompts only, not actual face data
3. **Privacy**: Uploaded images converted to base64 - ensure user awareness

### Future Enhancements:
1. **Face Detection**: Client-side face detection to validate uploads
2. **Multiple Faces**: Support for multiple face replacements
3. **Face Enhancement**: Pre-process faces for better quality
4. **Backend Integration**: Proper face embedding extraction and control

### Security Considerations:
- File size limit (2MB) prevents abuse
- Type checking prevents non-image uploads
- No server upload - all client-side processing
- URL images must be publicly accessible

---

## 🔧 Implementation Status

✅ **Completed**:
- Enhanced prompt instructions for exact face matching
- Fixed file upload accept attribute
- Added comprehensive error handling
- Improved logging for debugging
- Priority system for face matching

🚧 **In Progress**:
- Testing with various face types
- Optimizing prompt instructions
- Gathering user feedback

📋 **Planned**:
- Backend face embedding system
- Client-side face detection
- Advanced face preprocessing
- Multiple face support

---

This rule ensures that the face upload feature works correctly and provides the best possible face matching within the current technical constraints.
