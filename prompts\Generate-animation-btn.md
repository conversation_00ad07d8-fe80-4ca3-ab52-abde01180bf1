### ✨ Generate Button Shimmer Effect – Subtle, Looping, and On-Brand

**Requirements:**
- Reduce shimmer intensity: Use a softer, lower-opacity white gradient for the shimmer, similar to the Sign In and dashboard upgrade CTA buttons.
- Keep the current smooth hover transition and premium cubic-bezier easing.
- On click (when generating/loading), the shimmer animation should loop continuously until generation is complete.
- The shimmer should only loop during the loading state, not on hover.
- Ensure the shimmer is subtle, never visually overwhelming or distracting.
- Match the shimmer style and feel to the Sign In and upgrade CTA buttons for brand consistency.
- All transitions must be smooth, with no layout shift or flicker.
- Use only CSS for the shimmer animation and looping (no JavaScript).
- Maintain accessibility and responsiveness.

**Implementation Notes:**
- Use a lighter white gradient with reduced opacity (e.g., rgba(255,255,255,0.10) to rgba(255,255,255,0.22)).
- For looping, use a CSS keyframes animation (e.g., `@keyframes shimmer-move`) and apply it only when the button is in the loading state.
- On hover (when not loading), shimmer animates once as before.
- On loading, shimmer animates in a loop.
- Ensure the shimmer layer is always below the button content (z-index).
- Test on all breakpoints for visual consistency.

**Sample CSS:**
```css
/* Subtle shimmer effect */
.generate-btn-shimmer {
  content: '';
  position: absolute;
  top: 0; left: -60%;
  width: 60%; height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.10) 40%, rgba(255,255,255,0.22) 50%, rgba(255,255,255,0.10) 60%, transparent 100%);
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4,0,0.2,1);
}

/* Hover: single shimmer */
.generate-btn:not(.loading):hover .generate-btn-shimmer {
  animation: shimmer-move 0.7s cubic-bezier(0.4,0,0.2,1);
}

/* Loading: looped shimmer */
.generate-btn.loading .generate-btn-shimmer {
  animation: shimmer-move 1.2s cubic-bezier(0.4,0,0.2,1) infinite;
  opacity: 1;
}

@keyframes shimmer-move {
  0% { left: -60%; }
  100% { left: 120%; }
}
```

**Usage:**
```jsx
<button className={`generate-btn${isLoading ? ' loading' : ''}`}>
  <span className="generate-btn-shimmer"></span>
  {/* ...button content... */}
</button>
```

---

**Acceptance Criteria:**
- Shimmer is subtle and matches Sign In/upgrade CTA style.
- Shimmer animates once on hover, loops on loading.
- No JS required for animation.
- No layout shift or flicker.
- Fully responsive and accessible.