---
description: 
globs: 
alwaysApply: false
---
---
title: upload-personal-face
id: upload-personal-face.mdc
ruleType: manual
---

## Feature: Upload Personal Face (Headshot Replacement)

### Objective
Enable users to upload their own headshot image, which will replace the default AI-generated face in the thumbnail if the "Include Person" toggle is active. This supports greater personalization and professional branding in generated thumbnails.

---

## Applies To
- /src/components/PersonControls.jsx
- /src/components/ThumbnailGenerator.jsx
- /src/state/userPreferences.ts
- /public/uploads/faces/

---

## Rules

### 🧩 Behavior
- Only show face upload UI if `"Include Person"` toggle is ON.
- If no image is uploaded → fallback to AI-generated face.
- If image uploaded → inject image as subject’s face via prompt + JSON.

### 📤 Upload UI
- File Input Label: `🧑 Upload Your Face (Headshot)`
- Accepts: JPEG, PNG
- Max Size: 2MB
- Crop Tool: Optional (recommended)
- Live Preview with "Remove" option

### 🧠 Prompt Injection
> "Replace the AI-generated subject’s face with the uploaded headshot. Keep the same pose, lighting, and expression. Use the uploaded face as if it were naturally part of the scene."

### ✅ Output JSON Example
```json
"subject": {
  "person": {
    "use_custom_face": true,
    "custom_face_url": "https://yourapp.com/uploads/user123-face.jpg",
    "expression": "shocked",
    "pose": "pointing",
    "appearance": {
      "gender": "auto"
    }
  }
}

Notes for Developers

Store uploaded image URL temporarily in session or CDN.
Make sure image fits within existing aspect ratio and lighting rules.
Use consistent prompt phrasing to ensure GPT-image-1 includes face properly.
⚠️ Privacy Warning
Always show this below the upload input:

"Your uploaded face is used only temporarily for image generation and not stored after export unless saved."
✅ Done When

User can upload/remove face only when "Include Person" is active.
Image is injected into prompt and included in final render.
Preview and cleanup behavior works without reload.
Bonus UX Tip 💡

Use a small circular preview area with a tooltip: “Used to personalize the subject’s face in your thumbnail.”

✅ Ready to activate in Cursor chat using:

@upload-personal-face

