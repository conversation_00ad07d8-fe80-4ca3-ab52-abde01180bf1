/* ======================================
   User Profile Main Container Styles
   ====================================== */

.user-profile-main-container {
    position: relative;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
}

/* ======================================
   Profile Card Wrapper Styles
   ====================================== */

.user-profile-card {
    background: linear-gradient(145deg, rgba(31, 41, 55, 0.8), rgba(17, 24, 39, 0.9));
    box-shadow: 
        0 20px 40px -10px rgba(0, 0, 0, 0.4),
        0 10px 20px -5px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(75, 85, 99, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.user-profile-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 32px 64px -12px rgba(0, 0, 0, 0.5),
        0 20px 32px -8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(139, 92, 246, 0.4);
}

/* ======================================
   Welcome Banner Styles
   ====================================== */

.profile-welcome-banner {
    background: linear-gradient(135deg, 
        rgba(147, 51, 234, 0.2) 0%, 
        rgba(59, 130, 246, 0.2) 35%, 
        rgba(6, 182, 212, 0.2) 100%
    );
    position: relative;
    overflow: hidden;
}

.profile-welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent
    );
    transition: left 0.8s ease;
}

.profile-welcome-banner:hover::before {
    left: 100%;
}

.banner-pattern {
    animation: patternFloat 8s ease-in-out infinite;
}

@keyframes patternFloat {
    0%, 100% { transform: translateY(0px) scale(1); }
    50% { transform: translateY(-5px) scale(1.05); }
}

/* ======================================
   Profile Avatar Enhancements
   ====================================== */

.profile-avatar-container {
    background: linear-gradient(135deg, #3B82F6, #8B5CF6, #EC4899);
    box-shadow: 
        0 8px 16px rgba(59, 130, 246, 0.3),
        0 4px 8px rgba(139, 92, 246, 0.2);
    position: relative;
    transition: all 0.3s ease;
}

.profile-avatar-container::before {
    content: '';
    position: absolute;
    inset: -3px;
    background: linear-gradient(135deg, #3B82F6, #8B5CF6, #EC4899);
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
    animation: avatarGlow 3s ease-in-out infinite;
}

.profile-avatar-container:hover::before {
    opacity: 0.6;
}

@keyframes avatarGlow {
    0%, 100% { opacity: 0.2; }
    50% { opacity: 0.5; }
}

.profile-avatar {
    transition: all 0.3s ease;
}

.profile-avatar-container:hover .profile-avatar {
    transform: scale(1.05);
}

/* ======================================
   Plan Status Banner Styles
   ====================================== */

.plan-status-banner {
    background: linear-gradient(135deg, 
        rgba(5, 150, 105, 0.15), 
        rgba(59, 130, 246, 0.15)
    );
    border: 1px solid rgba(5, 150, 105, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.plan-status-banner:hover {
    border-color: rgba(5, 150, 105, 0.5);
    transform: translateY(-1px);
}

.plan-status-banner::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(5, 150, 105, 0.2) 0%, transparent 70%);
    border-radius: 50%;
    animation: planGlow 4s ease-in-out infinite;
}

@keyframes planGlow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

/* ======================================
   Upgrade Promotion Section Styles
   ====================================== */

.upgrade-promotion-section {
    background: linear-gradient(135deg, 
        rgba(147, 51, 234, 0.2), 
        rgba(59, 130, 246, 0.2),
        rgba(147, 51, 234, 0.15)
    );
    border: 1px solid rgba(147, 51, 234, 0.4);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px -3px rgba(139, 92, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.upgrade-promotion-section:hover {
    border-color: rgba(147, 51, 234, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px -3px rgba(139, 92, 246, 0.15), 0 4px 8px -2px rgba(59, 130, 246, 0.1);
}

.upgrade-promotion-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent
    );
    transition: left 0.6s ease;
}

.upgrade-promotion-section:hover::before {
    left: 100%;
}

.upgrade-glow {
    animation: upgradeGlow 6s ease-in-out infinite;
}

@keyframes upgradeGlow {
    0%, 100% { opacity: 0.1; transform: scale(1); }
    50% { opacity: 0.3; transform: scale(1.5); }
}

/* ======================================
   Upgrade Action Button Styles
   ====================================== */

.upgrade-action-btn {
    background: linear-gradient(135deg, #8B5CF6, #3B82F6);
    box-shadow: 
        0 4px 15px rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px -1px rgba(139, 92, 246, 0.2);
}

.upgrade-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent
    );
    transition: left 0.4s ease;
}

.upgrade-action-btn:hover::before {
    left: 100%;
}

.upgrade-action-btn:hover {
    background: linear-gradient(135deg, #7C3AED, #2563EB);
    box-shadow: 
        0 7px 15px -1px rgba(139, 92, 246, 0.3);
    transform: translateY(-1px);
}

/* ======================================
   Credits Tracking Section Styles
   ====================================== */

.credits-tracking-section {
    transition: all 0.3s ease;
}

.credits-progress-container {
    background: linear-gradient(135deg, rgba(55, 65, 81, 0.6), rgba(31, 41, 55, 0.8));
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(75, 85, 99, 0.4);
    transition: all 0.3s ease;
}

.credits-progress-container:hover {
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(139, 92, 246, 0.2);
}

.credits-progress-fill {
    background: linear-gradient(90deg, #06B6D4, #3B82F6, #8B5CF6);
    position: relative;
    overflow: hidden;
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.credits-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.4), 
        transparent
    );
    animation: creditsShimmer 3s infinite;
}

@keyframes creditsShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-shimmer {
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* ======================================
   Profile Action Buttons Styles
   ====================================== */

.profile-actions-section {
    margin-top: 1rem;
}

.profile-action-btn {
    background: linear-gradient(135deg, 
        rgba(55, 65, 81, 0.3), 
        rgba(31, 41, 55, 0.5)
    );
    border: 1px solid rgba(75, 85, 99, 0.4);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.08), 
        transparent
    );
    transition: left 0.4s ease;
}

.profile-action-btn:hover::before {
    left: 100%;
}

.profile-action-btn:hover {
    background: linear-gradient(135deg, 
        rgba(75, 85, 99, 0.4), 
        rgba(55, 65, 81, 0.6)
    );
    border-color: rgba(139, 92, 246, 0.4);
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Sign Out Button Special Styling */
#sign-out-button:hover {
    background: linear-gradient(135deg, 
        rgba(220, 38, 38, 0.2), 
        rgba(185, 28, 28, 0.3)
    );
    border-color: rgba(239, 68, 68, 0.4);
}

/* ======================================
   Responsive Design
   ====================================== */

@media (max-width: 768px) {
    .user-profile-main-container {
        width: 100%;
        max-width: 100%;
        padding: 0 1rem;
    }
    
    .user-profile-card {
        width: 100% !important;
        border-radius: 16px;
        margin: 0;
    }
    
    .profile-welcome-banner {
        padding: 1.5rem;
    }
    
    .profile-content-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .profile-welcome-banner {
        padding: 1rem;
    }
    
    .profile-content-section {
        padding: 1rem;
    }
    
    .profile-avatar-container {
        width: 3rem;
        height: 3rem;
    }
    
    .profile-user-name {
        font-size: 1rem;
    }
}

/* ======================================
   Dark Mode Enhancements
   ====================================== */

@media (prefers-color-scheme: dark) {
    .user-profile-card {
        background: linear-gradient(145deg, 
            rgba(17, 24, 39, 0.95), 
            rgba(31, 41, 55, 0.9)
        );
        border-color: rgba(75, 85, 99, 0.4);
    }
}

/* ======================================
   Accessibility Enhancements
   ====================================== */

.profile-action-btn:focus,
.upgrade-action-btn:focus {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

.profile-action-btn:focus-visible,
.upgrade-action-btn:focus-visible {
    outline: 2px solid #3B82F6;
    outline-offset: 2px;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ======================================
   Icon Specific Styles
   ====================================== */

.iconify[data-icon*="crown"] {
    filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.5));
    animation: crownGlow 3s ease-in-out infinite;
}

@keyframes crownGlow {
    0%, 100% { filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.3)); }
    50% { filter: drop-shadow(0 0 12px rgba(251, 191, 36, 0.8)); }
}

.iconify[data-icon*="star"] {
    animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* ======================================
   Hover Effects and Micro-interactions
   ====================================== */

.banner-greeting {
    transition: all 0.3s ease;
}

.profile-welcome-banner:hover .banner-greeting {
    transform: translateY(-1px);
}

.profile-user-details {
    transition: all 0.3s ease;
}

.profile-welcome-banner:hover .profile-user-details {
    transform: translateX(2px);
}

/* Smooth all transitions */
* {
    transition: all 0.2s ease;
}

/* Upgrade Promotion Section Styles (in User Profile Dropdown) */
.upgrade-promotion-section {
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px -3px rgba(139, 92, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05);
}

.upgrade-promotion-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px -3px rgba(139, 92, 246, 0.15), 0 4px 8px -2px rgba(59, 130, 246, 0.1);
}

.upgrade-action-btn {
    box-shadow: 0 4px 12px -1px rgba(139, 92, 246, 0.2);
}

.upgrade-action-btn:hover {
    box-shadow: 0 7px 15px -1px rgba(139, 92, 246, 0.3);
}

.upgrade-header .text-transparent {
    background-size: 200% auto;
    animation: gradient-text 3s linear infinite;
}

@keyframes gradient-text {
    to {
        background-position: 200% center;
    }
}

/* End of User Profile Section Styles */ 