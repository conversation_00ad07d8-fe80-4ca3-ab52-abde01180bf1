title: thumbspark-generator-config
type: universal-thumbnail-settings
version: 1.0.0

defaults:
  aspect_ratio: "16:9"
  composition: "tight on face and object"
  resolution_mode: "low"
  quality_upgrade_available: true
  text_overlay_enabled: true
  prompt_hidden_after_template: true

video_topics:
  smart_overlay_suggestions:
    enabled: true
    examples:
      - "iPhone 16 vs Samsung S25" : "The Ultimate Comparison!"
      - "Reacting to my old cringy videos 😳" : "Try Not to Laugh!"
      - "How I grew my channel from 0 to 100K" : "From Zero to Viral!"
      - "Worst purchases I regret" : "Do NOT Buy These!"

prompt_enhancer:
  active: true
  behavior:
    - general users input short titles
    - AI rewrites it as engaging but simple
    - Overlay text remains editable
  rules:
    avoid_complexity: true
    tone: professional, clear, accessible

background_logic:
  default_type: "auto-related"
  fallback: "solid-color"
  smart_mapping:
    gaming:
      - fortnite: "Fortnite build battle background"
      - warzone: "Military battlefield dusk setting"
      - csgo: "Counter Strike style urban ruins"
    tech:
      - apple review: "Sleek modern tech setup"
      - app comparison: "Split background of two UIs"
    reaction:
      - cringe: "Colorful radial comic burst"
      - scary: "Dark red/blue cinematic background"
    unboxing:
      - product: "Glowing box + dramatic lighting"

background_types:
  - Solid Color
  - Mesh Gradient
  - Sunburst
  - Cinematic
  - Thematic
  - Halftone/Comic
  - Neon/Glow
  - Bokeh/Leak
  - Doodle
  - Pattern/Texture
  - Upload Image

custom_background:
  upload:
    - from_device: true
    - import_url: true
  controls:
    - blur_slider:
        label: "Blur"
        range: [0, 10]
        default: 0
        realtime_preview: true

face_swap:
  enabled: true
  options:
    - upload_from_device: true
    - import_from_url: true
  processing:
    auto_crop_align: true
    detection_lib: "face-api.js (or MediaPipe fallback)"
    preview_face_on_canvas: true

color_moods:
  presets:
    - cinematic-warm: "Orange/Teal cinematic LUT"
    - cold-drama: "Cool shadowy tone"
    - gaming-glow: "Neon blue/purple contrast"
    - viral-energy: "High contrast saturation"
    - soft-calm: "Muted tones for relaxing vibes"
  ui:
    type: "picker-cards"
    hover_preview: true

logo_embedding:
  brandfetch_integration: true
  2d_3d_toggle: true
  quick_search_input: true
  use_cases:
    - app reviews
    - startup comparisons
    - software tutorials

UI_placeholder_titles:
  auto_typewriter_enabled: true
  max_titles: 4
  examples:
    - "Trying to live on $10 a day 💸"
    - "Reacting to my old cringy videos 😳"
    - "Epic win moments in Fortnite"
    - "How to edit videos for beginners"

privacy_control:
  hide_prompt_after_template_applied: true
  show_only_clean_title_text: true

image_generation_rules:
  prevent_overlay_crop: true
  prevent_empty-padding-canvas: true
  allow_overlay_toggle_off: true

output:
  save_to: "/public/generated/"
  allow_png_transparency: true
  export_hd_on_click: true
