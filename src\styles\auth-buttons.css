/* ================= STANDARDIZED AUTH CTA BUTTONS ================= */

/* Base standardized CTA button styles for all auth screens */
.auth-cta-btn {
    /* Size and spacing - matches Sign In button */
    width: 100% !important;
    padding: 1rem 1.5rem !important; /* 16px top/bottom, 24px left/right */
    min-height: 50px !important;
    
    /* Visual styling - matches Sign In button */
    background: #006FEE !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 0.75rem !important; /* 12px - matches rounded-xl */
    
    /* Typography - matches Sign In button */
    font-family: inherit !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    
    /* Layout and display */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important; /* 8px gap for icons/text */
    
    /* Effects and transitions */
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    cursor: pointer !important;
    
    /* Remove any background images */
    background-image: none !important;
}

/* Hover and focus states */
.auth-cta-btn:hover,
.auth-cta-btn:focus-visible {
    background: #1A8CFF !important;
    box-shadow: 0 8px 25px rgba(0, 111, 238, 0.3) !important;
    transform: translateY(-1px) !important;
}

/* Active state */
.auth-cta-btn:active {
    background: #0056CC !important;
    transform: scale(0.98) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Disabled state */
.auth-cta-btn:disabled {
    background: #6B7280 !important;
    color: #9CA3AF !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

/* Shimmer effect for hover - Matching glass version */
.auth-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
    z-index: 1;
}

.auth-cta-btn:hover::before {
    left: 100%;
}

.auth-cta-btn:disabled::before {
    display: none !important;
}

/* Ensure content stays above shimmer effect */
.auth-cta-btn > * {
    position: relative;
    z-index: 2;
}

/* Loading/submitting state */
.auth-cta-btn.submitting,
.auth-cta-btn[disabled] {
    background: #006FEE !important;
    cursor: wait !important;
    opacity: 0.8 !important;
}

.auth-cta-btn.submitting:hover,
.auth-cta-btn[disabled]:hover {
    background: #006FEE !important;
    transform: none !important;
    box-shadow: 0 4px 12px rgba(0, 111, 238, 0.2) !important;
}

.auth-google-btn.submitting,
.auth-google-btn[disabled] {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #252525 100%) !important;
    cursor: wait !important;
    opacity: 0.8 !important;
}

.auth-google-btn.submitting:hover,
.auth-google-btn[disabled]:hover {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #252525 100%) !important;
    transform: none !important;
}

/* ================= GOOGLE BUTTON STANDARDIZATION ================= */

/* Google button inherits from auth-cta-btn but with custom styling */
.auth-google-btn {
    /* Inherit all auth-cta-btn styles */
    width: 100% !important;
    padding: 1rem 1.5rem !important;
    min-height: 50px !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    border-radius: 0.75rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.75rem !important; /* Slightly larger gap for Google icon */
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
    cursor: pointer !important;
    border: none !important;
    
    /* Google-specific styling - Updated to lighter neutral shade */
    background: #F5F7FA !important;
    border: 1px solid #E3EAF3 !important;
    color: #1e293b !important;
    backdrop-filter: blur(8px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
}

.auth-google-btn:hover {
    background: #E3EAF3 !important; /* Slightly darker neutral on hover */
    border-color: #D1D9E6 !important; /* Darker border on hover */
    transform: translateY(-1px) !important;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.auth-google-btn:active {
    transform: translateY(0px) scale(0.98) !important;
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.1s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.auth-google-btn:focus {
    outline: none !important;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.5),
        0 0 0 3px rgba(80, 80, 80, 0.6) !important;
}

.auth-google-btn:disabled {
    background: #374151 !important;
    border-color: #4B5563 !important;
    color: #9CA3AF !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.6 !important;
}

/* Google button shimmer effect - Matching glass version */
.auth-google-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
    z-index: 1;
}

.auth-google-btn:hover::before {
    left: 100%;
}

.auth-google-btn:disabled::before {
    display: none !important;
}

.auth-google-btn > * {
    position: relative;
    z-index: 2;
}

.auth-google-btn svg {
    filter: brightness(1.1) !important;
    transition: filter 0.2s ease !important;
}

.auth-google-btn:hover svg {
    filter: brightness(1.2) drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)) !important;
}

.auth-google-btn:disabled svg {
    filter: grayscale(1) opacity(0.5) !important;
}

/* ================= ANIMATIONS ================= */

@keyframes auth-shimmer-move {
    0% { 
        left: -75%; 
        opacity: 1;
    }
    100% { 
        left: 125%; 
        opacity: 0;
    }
}

@keyframes google-shimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 150%;
        opacity: 0;
    }
}

/* ================= MOBILE RESPONSIVE STYLES ================= */

/* Tablet breakpoint */
@media (max-width: 1024px) {
    .auth-cta-btn,
    .auth-google-btn {
        padding: 0.875rem 1.25rem !important; /* 14px top/bottom, 20px left/right */
        font-size: 0.9rem !important;
        min-height: 48px !important;
    }
}

/* Mobile breakpoint - unified 50px height at 576px breakpoint */
@media (max-width: 576px) {
    .auth-cta-btn,
    .auth-google-btn {
        padding: 0.875rem 1rem !important; /* 14px top/bottom, 16px left/right */
        font-size: 0.875rem !important;
        min-height: 50px !important; /* Unified 50px height for mobile */
    }
    
    .auth-cta-btn::before,
    .auth-google-btn::before {
        width: 60%;
        left: -80%;
    }
    
    @keyframes auth-shimmer-move {
        0% { 
            left: -80%; 
            opacity: 1;
        }
        100% { 
            left: 130%; 
            opacity: 0;
        }
    }
}

/* Small mobile - maintains 50px height */
@media (max-width: 480px) {
    .auth-cta-btn,
    .auth-google-btn {
        padding: 0.8125rem 0.875rem !important; /* 13px top/bottom, 14px left/right */
        font-size: 0.8125rem !important; /* 13px */
        min-height: 50px !important; /* Keep 50px height */
    }
}

/* Very small mobile - still maintains 50px height */
@media (max-width: 360px) {
    .auth-cta-btn,
    .auth-google-btn {
        padding: 0.75rem 0.75rem !important; /* 12px top/bottom, 12px left/right */
        font-size: 0.75rem !important; /* 12px */
        min-height: 50px !important; /* Keep 50px height */
    }
}



/* ================= OVERRIDE EXISTING STYLES ================= */

/* Override any existing welcome-signin-btn styles */
.auth-cta-btn.welcome-signin-btn,
.auth-google-btn.google-signin-btn {
    /* Force override existing styles */
    background: #006FEE !important;
    padding: 1rem 1.5rem !important;
    min-height: 50px !important;
    font-size: 1rem !important;
    border-radius: 0.75rem !important;
}

.auth-google-btn.google-signin-btn {
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #252525 100%) !important;
    border: 1px solid rgba(80, 80, 80, 0.3) !important;
}

/* Mobile override to ensure 50px height at 576px breakpoint */
@media (max-width: 576px) {
    .auth-cta-btn.welcome-signin-btn,
    .auth-google-btn.google-signin-btn,
    .auth-cta-btn,
    .auth-google-btn,
    #welcome-signin-submit-btn,
    #google-signin-btn,
    #google-signup-btn {
        min-height: 50px !important;
        padding: 0.875rem 1rem !important;
        font-size: 1.00rem !important;
    }
    

}

/* Portrait orientation specific overrides - 25% larger font for better readability */
@media (max-width: 576px) and (orientation: portrait) {
    .auth-cta-btn,
    .auth-google-btn,
    #welcome-signin-submit-btn,
    #google-signin-btn,
    #google-signup-btn {
        min-height: 50px !important;
        padding: 0.875rem 1rem !important;

    }
    
    /* 25% larger font size for CTA button labels in mobile portrait */
    .auth-cta-btn .auth-cta-text,
    .auth-cta-btn .auth-loading-text,
    .auth-google-btn .auth-cta-text,
    .auth-google-btn .auth-loading-text,
    #welcome-signin-submit-btn .signin-button-text,
    #welcome-signin-submit-btn .signin-loading-text,
    #welcome-signin-submit-btn .auth-cta-text,
    #welcome-signin-submit-btn .auth-loading-text,
    #google-signin-btn .auth-cta-text,
    #google-signin-btn .auth-loading-text,
    #google-signin-btn span,
    #google-signup-btn .auth-cta-text,
    #google-signup-btn .auth-loading-text,
    #google-signup-btn span,
    .auth-cta-btn span:not(.iconify),
    .auth-google-btn span:not(.iconify),
    /* Additional specificity to override preview.css signin-button-text styles */
    .auth-cta-btn .signin-button-text,
    .auth-cta-btn .signin-loading-text,
    .signin-button-text,
    .signin-loading-text {
        font-size: 1.09375rem !important; /* 0.875rem * 1.25 = 1.09375rem (25% increase) */
        line-height: 1.3 !important; /* Slightly tighter line height for better button fit */
        font-weight: 500 !important; /* Ensure consistent font weight */
    }
}

/* ================= SPECIFIC TEXT STYLING ================= */

/* Button text styling to ensure consistency */
.auth-cta-text {
    font-weight: 500 !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Loading text styling */
.auth-loading-text {
    font-weight: 500 !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Icon styling within buttons */
.auth-cta-btn .iconify,
.auth-google-btn .iconify {
    flex-shrink: 0 !important;
    position: relative !important;
    z-index: 2 !important;
}

/* Responsive text sizing - matching button breakpoints (landscape only to avoid portrait conflict) */
@media (max-width: 576px) and (orientation: landscape) {
    .auth-cta-text,
    .auth-loading-text {
        font-size: 0.875rem !important;
    }
}

@media (max-width: 480px) {
    .auth-cta-text,
    .auth-loading-text {
        font-size: 0.8125rem !important;
    }
}

@media (max-width: 360px) {
    .auth-cta-text,
    .auth-loading-text {
        font-size: 0.75rem !important;
    }
}

/* ================= ENHANCED ACCESSIBILITY ================= */

/* Enhanced accessibility for mobile portrait - larger tap targets and better contrast */
@media (max-width: 576px) and (orientation: portrait) {
    .auth-cta-btn:focus,
    .auth-google-btn:focus {
        outline: 3px solid #60a5fa !important; /* Thicker outline for better visibility */
        outline-offset: 3px !important;
    }
    
    /* Ensure text remains readable with increased size */
    .auth-cta-btn .auth-cta-text,
    .auth-cta-btn .auth-loading-text,
    .auth-google-btn .auth-cta-text,
    .auth-google-btn .auth-loading-text {
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; /* Subtle shadow for better readability */
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .auth-cta-btn,
    .auth-google-btn {
        border: 2px solid currentColor !important;
    }
    
    @media (max-width: 576px) and (orientation: portrait) {
        .auth-cta-btn .auth-cta-text,
        .auth-cta-btn .auth-loading-text,
        .auth-google-btn .auth-cta-text,
        .auth-google-btn .auth-loading-text {
            font-weight: 600 !important; /* Bolder text for high contrast */
        }
    }
}

/* Reduced motion support for accessibility */
@media (prefers-reduced-motion: reduce) {
    .auth-cta-btn,
    .auth-google-btn,
    .auth-cta-btn::before,
    .auth-google-btn::before {
        animation: none !important;
        transition: none !important;
    }
}

/* ================= SIGNUP FORM SPACING ADJUSTMENTS ================= */

/* Reduce input field spacing by 15% on mobile (max-width: 767px) for signup page */
@media (max-width: 767px) {
    /* Target the signup form specifically */
    .space-y-6 > * + * {
        margin-top: 1.275rem !important; /* 15% reduction from 1.5rem (24px to ~20.4px) */
    }
    
    /* Also target any form containers in signup */
    form.space-y-6 > * + * {
        margin-top: 1.275rem !important; /* 15% reduction from 1.5rem */
    }
    
    /* Adjust google auth section spacing */
    .google-auth-section {
        margin-bottom: 1.275rem !important; /* 15% reduction from 1.5rem */
    }
    
    /* Adjust auth divider spacing */
    .auth-divider {
        margin-top: 1.275rem !important;
        margin-bottom: 1.275rem !important;
    }
    
    /* Ensure error messages don't add extra spacing */
    .text-red-400.flex.items-center {
        margin-top: 0.25rem !important; /* Keep error message spacing minimal */
    }
} 