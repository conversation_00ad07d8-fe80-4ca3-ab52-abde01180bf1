import { useState, useEffect } from 'react'

/**
 * Custom hook for managing prompt enhancement logic
 * Implements the person-headshot-enhancement rule
 */
export const usePromptEnhancer = () => {
    const [isHeadshotMode, setIsHeadshotMode] = useState(false);
    
    /**
     * Detects if the current configuration should trigger headshot enhancement
     */
    const detectHeadshotMode = (includePerson, selectedExpression, userPrompt) => {
        if (!includePerson) {
            return false;
        }
        
        // Check for headshot keywords in user prompt
        const headshotKeywords = [
            'headshot', 'portrait', 'face', 'facial', 'close-up', 'closeup',
            'profile', 'selfie', 'reaction', 'expression', 'looking at camera'
        ];
        
        const hasHeadshotKeywords = headshotKeywords.some(keyword => 
            userPrompt.toLowerCase().includes(keyword)
        );
        
        // Check for headshot-appropriate expressions
        const headshotExpressions = [
            'Shocked', 'Surprised', 'Thinking', 'Happy', 'Loved', 
            'Angry', 'Crying', 'Laughing', 'Neutral', 'Proud'
        ];
        
        const hasHeadshotExpression = headshotExpressions.includes(selectedExpression);
        
        return hasHeadshotKeywords || hasHeadshotExpression;
    };
    
    /**
     * Updates headshot mode based on current settings
     */
    const updateHeadshotMode = (includePerson, selectedExpression, userPrompt) => {
        const newHeadshotMode = detectHeadshotMode(includePerson, selectedExpression, userPrompt);
        setIsHeadshotMode(newHeadshotMode);
        return newHeadshotMode;
    };
    
    /**
     * Gets the enhanced prompt description for headshots
     */
    const getHeadshotEnhancementDescription = () => {
        return "Cinematic portrait or video-style headshot that emphasizes facial features with exceptional clarity and detail. Ensures lifelike, flattering skin tones with professional-grade LUT color grading and studio-quality lighting.";
    };
    
    return {
        isHeadshotMode,
        detectHeadshotMode,
        updateHeadshotMode,
        getHeadshotEnhancementDescription
    };
}; 