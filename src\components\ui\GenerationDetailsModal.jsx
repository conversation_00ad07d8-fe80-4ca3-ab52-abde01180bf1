import React, { useState, useEffect } from 'react'

export const GenerationDetailsModal = ({ 
    isOpen, 
    onClose, 
    details = {},
    host = 'localhost:3001'
}) => {
    const [isClosing, setIsClosing] = useState(false);
    const [copiedPrompt, setCopiedPrompt] = useState(false);

    useEffect(() => {
        if (!isOpen) {
            setIsClosing(false);
            setCopiedPrompt(false);
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const handleClose = () => {
        setIsClosing(true);
        // Delay the actual close to allow animation to complete
        setTimeout(() => {
            onClose();
        }, 200); // Match the animation duration
    };

    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            handleClose();
        }
    };

    const handleCopyPrompt = async () => {
        if (details.prompt) {
            try {
                await navigator.clipboard.writeText(details.prompt);
                setCopiedPrompt(true);
                setTimeout(() => setCopiedPrompt(false), 2000);
            } catch (err) {
                console.error('Failed to copy prompt:', err);
            }
        }
    };

    const formatDate = (date) => {
        if (!date) return 'Unknown';
        const dateObj = new Date(date);
        return dateObj.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return React.createElement('div', {
        className: `generation-details-modal-overlay fixed inset-0 z-50 flex items-center justify-center p-4${isClosing ? ' closing' : ''}`,
        onClick: handleBackdropClick,
        id: 'generation-details-modal-overlay'
    },
        // Backdrop
        React.createElement('div', {
            className: 'absolute inset-0 bg-black/50 backdrop-blur-sm'
        }),
        
        // Modal Container
        React.createElement('div', {
            className: 'generation-details-modal-container relative w-full max-w-lg mx-4 sm:mx-4',
            id: 'generation-details-modal-container'
        },
            // Modal Content
            React.createElement('div', {
                className: 'modal-composition bg-gray-800/95 border border-gray-700/50 rounded-2xl shadow-2xl backdrop-blur-md',
                id: 'modal-composition'
            },
                // Header
                React.createElement('div', {
                    className: 'modal-header flex items-center justify-between p-5 border-b border-gray-700/50',
                    id: 'modal-header'
                },
                    // Title
                    React.createElement('div', {
                        className: 'modal-title-container',
                        id: 'modal-title-container'
                    },
                        React.createElement('h3', {
                            className: 'modal-title text-gray-200 font-semibold text-base',
                            id: 'modal-title'
                        }, 'Thumb Details')
                    ),
                    
                    // Close Button
                    React.createElement('button', {
                        className: 'close-btn text-gray-400 hover:text-gray-200 transition-colors p-1.5 hover:bg-gray-700/50 rounded-lg',
                        onClick: handleClose,
                        'aria-label': 'Close modal',
                        id: 'modal-close-btn'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:close-circle-linear',
                            style: { fontSize: '24px' }
                        })
                    )
                ),
                
                // Body
                React.createElement('div', {
                    className: 'modal-body p-5 space-y-5',
                    id: 'modal-body'
                },
                    // Prompt Section
                    React.createElement('div', {
                        className: 'prompt-section',
                        id: 'prompt-section'
                    },
                        // Prompt Header
                        React.createElement('div', {
                            className: 'prompt-header flex items-center justify-between mb-2',
                            id: 'prompt-header'
                        },
                            React.createElement('div', {
                                className: 'prompt-label flex items-center gap-2',
                                id: 'prompt-label'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-gray-400',
                                    'data-icon': 'solar:document-text-linear',
                                    style: { fontSize: '18px' }
                                }),
                                React.createElement('span', {
                                    className: 'label-text text-gray-300 font-medium text-sm',
                                    id: 'label-text'
                                }, 'Prompt Text')
                            ),
                            // Copy Button
                            React.createElement('button', {
                                className: `copy-btn flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs font-medium transition-all ${
                                    copiedPrompt 
                                        ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                                        : 'bg-gray-700/50 text-gray-400 hover:text-gray-200 hover:bg-gray-700 border border-gray-600/50'
                                }`,
                                onClick: handleCopyPrompt,
                                id: 'copy-prompt-btn'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': copiedPrompt ? 'solar:check-circle-linear' : 'solar:copy-linear',
                                    style: { fontSize: '16px' }
                                }),
                                copiedPrompt ? 'Copied!' : 'Copy'
                            )
                        ),
                        // Prompt Content
                        React.createElement('div', {
                            className: 'prompt-content bg-gray-900/80 border border-gray-700/30 rounded-lg p-4 max-h-36 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent',
                            id: 'prompt-content'
                        },
                            React.createElement('pre', {
                                className: 'prompt-text text-gray-300 font-mono whitespace-pre-wrap break-words',
                                style: { 
                                    fontSize: '0.8125rem', // 13px (5% larger than 12px)
                                    lineHeight: '1.6'
                                },
                                id: 'prompt-text'
                            }, details.prompt || 'No prompt available')
                        )
                    ),
                    
                    // Details Section
                    React.createElement('div', {
                        className: 'details-section space-y-3',
                        id: 'details-section'
                    },
                        // Quality Row
                        React.createElement('div', {
                            className: 'detail-row flex items-center justify-between py-2',
                            id: 'quality-row'
                        },
                            React.createElement('div', {
                                className: 'detail-label flex items-center gap-2',
                                id: 'quality-label'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-gray-400',
                                    'data-icon': 'solar:star-linear',
                                    style: { fontSize: '18px' }
                                }),
                                React.createElement('span', {
                                    className: 'label-text text-gray-400 text-sm',
                                    id: 'quality-label-text'
                                }, 'Quality')
                            ),
                            React.createElement('span', {
                                className: 'detail-value text-gray-200 font-medium text-sm',
                                id: 'quality-value'
                            }, details.quality || 'Medium')
                        ),
                        
                        // Date Row
                        React.createElement('div', {
                            className: 'detail-row flex items-center justify-between py-2',
                            id: 'date-row'
                        },
                            React.createElement('div', {
                                className: 'detail-label flex items-center gap-2',
                                id: 'date-label'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-gray-400',
                                    'data-icon': 'solar:calendar-linear',
                                    style: { fontSize: '18px' }
                                }),
                                React.createElement('span', {
                                    className: 'label-text text-gray-400 text-sm',
                                    id: 'date-label-text'
                                }, 'Date Generated')
                            ),
                            React.createElement('span', {
                                className: 'detail-value text-gray-200 font-medium text-sm',
                                id: 'date-value'
                            }, formatDate(details.date || details.timestamp))
                        ),
                        
                        // Credits Row
                        React.createElement('div', {
                            className: 'detail-row flex items-center justify-between py-2',
                            id: 'credits-row'
                        },
                            React.createElement('div', {
                                className: 'detail-label flex items-center gap-2',
                                id: 'credits-label'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-gray-400',
                                    'data-icon': 'solar:wallet-linear',
                                    style: { fontSize: '18px' }
                                }),
                                React.createElement('span', {
                                    className: 'label-text text-gray-400 text-sm',
                                    id: 'credits-label-text'
                                }, 'Credits Used')
                            ),
                            React.createElement('span', {
                                className: 'detail-value text-gray-200 font-medium text-sm',
                                id: 'credits-value'
                            }, details.credits || 3)
                        )
                    )
                ),
                
                // Footer
                React.createElement('div', {
                    className: 'modal-footer p-5 pt-0',
                    id: 'modal-footer'
                },
                    // OK Button
                    React.createElement('div', {
                        className: 'button-container flex justify-end',
                        id: 'button-container'
                    },
                        React.createElement('button', {
                            className: 'ok-btn px-6 py-2.5 rounded-lg bg-purple-600 hover:bg-purple-700 text-white font-medium text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-800',
                            onClick: handleClose,
                            id: 'ok-btn'
                        }, 'OK')
                    )
                )
            )
        )
    );
}; 