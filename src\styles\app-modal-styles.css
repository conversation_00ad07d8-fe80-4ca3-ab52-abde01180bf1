/* Enhanced CSS for pricing modal */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.95);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
}

.pricing-modal-overlay {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    animation: modalFadeIn 0.3s ease-out;
}

.pricing-modal-content {
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.8),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    animation: modalSlideIn 0.3s ease-out;
}

.pricing-plan-card {
    position: relative;
    overflow: visible;
}

.pricing-plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pricing-plan-card:hover::before {
    opacity: 1;
}

.crown-icon {
    filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.4));
    animation: crownGlow 2s ease-in-out infinite alternate;
}

@keyframes crownGlow {
    from {
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.4));
    }
    to {
        filter: drop-shadow(0 0 16px rgba(255, 215, 0, 0.6));
    }
}

.plan-select-button {
    position: relative;
    overflow: hidden;
}

/* ================= MACOS LIQUID GLASS PRICING MODAL ================= */

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.85); /* Increased opacity from 0.6 to 0.85 */
    backdrop-filter: blur(20px) saturate(120%); /* Increased blur from 8px to 20px */
    -webkit-backdrop-filter: blur(20px) saturate(120%);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Modal Container */
.modal-liquid-glass-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    background: linear-gradient(145deg, 
        rgba(17, 24, 39, 0.95) 0%, /* Dark background with high opacity */
        rgba(31, 41, 55, 0.92) 50%, 
        rgba(17, 24, 39, 0.95) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2); /* Brighter border */
    border-radius: 24px;
    backdrop-filter: blur(40px) saturate(200%); /* Stronger blur */
    -webkit-backdrop-filter: blur(40px) saturate(200%);
    box-shadow: 
        0 32px 64px -12px rgba(0, 0, 0, 0.8), /* Darker shadow */
        0 0 0 1px rgba(255, 255, 255, 0.15), /* Brighter inner border */
        inset 0 1px 0 rgba(255, 255, 255, 0.2); /* Brighter top highlight */
    overflow: hidden;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    transform: translateZ(0); /* Force hardware acceleration */
}

/* Modal Header */
.modal-glass-header {
    position: relative;
    padding: 3rem 2rem 2rem 2rem;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.12) 0%, /* Brighter header background */
        rgba(255, 255, 255, 0.06) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* Brighter border */
}

.modal-glass-close-button {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: 10;
}

.modal-glass-close-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

.modal-glass-header-content {
    text-align: left;
    max-width: 100%;
    margin: 0;
}

.modal-glass-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%); /* Brighter gradient */
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

.modal-glass-subtitle {
    font-size: 1.125rem;
    font-weight: 400;
    color: #cbd5e1; /* Brighter subtitle color */
    line-height: 1.6;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Modal Body */
.modal-glass-body {
    padding: 3rem 2rem;
    overflow-y: visible;
    max-height: none;
}

/* Pricing Plans Grid - Force 3 columns in same row */
.pricing-plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    align-items: stretch; /* Ensure all cards have equal height */
}

/* Pricing Plan Cards */
.pricing-plan-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(31, 41, 55, 0.9) 0%, /* Darker, more opaque background */
        rgba(17, 24, 39, 0.85) 50%, 
        rgba(31, 41, 55, 0.9) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15); /* Brighter border */
    border-radius: 20px;
    padding: 1.75rem 1.5rem;
    backdrop-filter: blur(20px) saturate(180%); /* Stronger blur */
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 480px;
    box-shadow: 
        0 8px 32px -4px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1); /* Inner highlight */
}

.pricing-plan-card:hover {
    transform: translateY(-4px);
    box-shadow: 
        0 20px 40px -8px rgba(0, 0, 0, 0.5), /* Stronger shadow */
        0 0 0 1px rgba(255, 255, 255, 0.2), /* Brighter border on hover */
        inset 0 1px 0 rgba(255, 255, 255, 0.15); /* Brighter inner highlight */
    border-color: rgba(255, 255, 255, 0.25); /* Brighter hover border */
}

.pricing-plan-card.highlighted-plan {
    border-color: rgba(139, 92, 246, 0.5); /* Brighter purple border */
    background: linear-gradient(145deg, 
        rgba(139, 92, 246, 0.1) 0%, /* Purple tint */
        rgba(31, 41, 55, 0.9) 50%, 
        rgba(139, 92, 246, 0.05) 100%);
    box-shadow: 
        0 12px 24px -6px rgba(139, 92, 246, 0.3), /* Stronger purple shadow */
        0 0 0 1px rgba(139, 92, 246, 0.25), /* Brighter purple border */
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.pricing-plan-card.highlighted-plan:hover {
    transform: translateY(-6px);
    box-shadow: 
        0 24px 48px -12px rgba(139, 92, 246, 0.4), /* Stronger shadow */
        0 0 0 1px rgba(139, 92, 246, 0.35), /* Brighter border */
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.pricing-plan-card.current-plan {
    border-color: rgba(34, 197, 94, 0.5); /* Brighter green border */
    background: linear-gradient(145deg, 
        rgba(34, 197, 94, 0.1) 0%, /* Green tint */
        rgba(31, 41, 55, 0.9) 50%, 
        rgba(34, 197, 94, 0.05) 100%);
    box-shadow: 
        0 8px 32px -4px rgba(34, 197, 94, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Plan Badge */
.plan-badge {
    position: absolute;
    top: -10px;
    right: 1rem;
    transform: none;
    z-index: 10;
}

.plan-badge.most-popular {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    box-shadow: 0 3px 8px rgba(139, 92, 246, 0.35);
    white-space: nowrap;
}

.plan-badge.current-plan-badge {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: #ffffff;
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    box-shadow: 0 3px 8px rgba(34, 197, 94, 0.35);
    white-space: nowrap;
}

/* Plan Content */
.plan-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.plan-header {
    text-align: left;
}

/* Plan Name */
.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Better text shadow */
}

.plan-price-container {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.plan-price {
    font-size: 3.5rem; /* Increased from 3rem */
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
    letter-spacing: -0.02em;
}

.plan-period {
    font-size: 1.125rem;
    color: #94a3b8;
    font-weight: 500;
}

/* Plan Description */
.plan-description {
    font-size: 1rem;
    color: #e2e8f0; /* Brighter description text */
    line-height: 1.5;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Plan Features */
.plan-features {
    flex: 1;
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.plan-feature {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.25rem 0;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.plan-feature:hover {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.feature-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    color: #22c55e;
    margin-top: 0.125rem;
}

/* Feature Text */
.feature-text {
    color: #f1f5f9; /* Even brighter feature text */
    font-size: 0.95rem;
    line-height: 1.5;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Plan Action Button */
.plan-action-button {
    width: 100%;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    margin-top: auto;
}

.plan-action-button.highlighted {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.plan-action-button.highlighted:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}

.plan-action-button.standard {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.plan-action-button.standard:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.plan-action-button.disabled {
    background: rgba(55, 65, 81, 0.5);
    color: #9ca3af;
    cursor: not-allowed;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.plan-action-button.disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Modal Footer */
.modal-glass-footer {
    padding: 2rem 2rem 3rem 2rem;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.04) 0%, 
        rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-guarantees {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

/* Guarantee Text */
.guarantee-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #cbd5e1; /* Brighter guarantee text */
    font-size: 0.875rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.guarantee-icon {
    width: 16px;
    height: 16px;
    color: #22c55e;
}

.guarantee-text {
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .pricing-plans-grid {
        gap: 2rem;
    }
    
    .modal-liquid-glass-container {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .modal-glass-header {
        padding: 2.5rem 1.5rem 1.5rem 1.5rem;
    }
    
    .modal-glass-body {
        padding: 2.5rem 1.5rem;
    }
    
    .modal-glass-footer {
        padding: 1.5rem 1.5rem 2.5rem 1.5rem;
    }
}

@media (max-width: 768px) {
    .pricing-plans-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .modal-liquid-glass-container {
        max-height: 95vh;
        overflow-y: auto;
    }
    
    .modal-glass-header {
        padding: 1.5rem 1.5rem 1rem 1.5rem;
    }
    
    .modal-glass-body {
        padding: 1.5rem;
        overflow-y: visible;
        max-height: none;
    }
    
    .modal-glass-footer {
        padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
    
    .modal-glass-title {
        font-size: 2rem;
    }
    
    .modal-glass-subtitle {
        font-size: 1rem;
    }
    
    .plan-price {
        font-size: 2.6rem;
    }
    
    .pricing-plan-card {
        padding: 1.5rem 1.25rem;
        min-height: auto;
    }
    
    .footer-guarantees {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .pricing-plans-grid {
        gap: 1.5rem;
    }
    
    .modal-liquid-glass-container {
        margin: 0.25rem;
        border-radius: 16px;
        max-height: 98vh;
        overflow-y: auto;
    }
    
    .modal-glass-header {
        padding: 1rem 1rem 0.75rem 1rem;
    }
    
    .modal-glass-body {
        padding: 1rem;
        overflow-y: visible;
        max-height: none;
    }
    
    .modal-glass-footer {
        padding: 0.75rem 1rem 1rem 1rem;
    }
    
    .modal-glass-close-button {
        top: 0.75rem;
        right: 0.75rem;
        width: 36px;
        height: 36px;
    }
    
    .modal-glass-title {
        font-size: 1.5rem;
    }
    
    .modal-glass-subtitle {
        font-size: 0.875rem;
    }
    
    .pricing-plan-card {
        padding: 1rem 0.875rem;
        min-height: auto;
        margin-bottom: 0;
    }
    
    .plan-price {
        font-size: 2.3rem;
    }
    
    .plan-feature {
        padding: 0.2rem 0;
    }
    
    .feature-text {
        font-size: 0.9rem;
    }
}

/* Mobile landscape specific fixes */
@media (max-width: 768px) and (orientation: landscape) {
    .modal-liquid-glass-container {
        max-height: 95vh;
        overflow-y: auto;
    }
    
    .modal-glass-header {
        padding: 1rem 1.5rem 0.75rem 1.5rem;
    }
    
    .modal-glass-body {
        padding: 1rem 1.5rem;
        overflow-y: visible;
        max-height: none;
    }
    
    .modal-glass-footer {
        padding: 0.75rem 1.5rem 1rem 1.5rem;
    }
    
    .pricing-plans-grid {
        gap: 1.5rem;
    }
}

/* Responsive design improvements */
@media (max-width: 1024px) {
    .pricing-plans-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .pricing-modal-content {
        margin: 1rem;
        max-height: 95vh;
        border-radius: 1.5rem;
    }
    
    .pricing-plans-grid {
        gap: 2rem;
    }

    .pricing-plan-card {
        padding: 1.5rem;
    }
}

@media (max-width: 640px) {
    .pricing-modal-content {
        margin: 0.5rem;
        border-radius: 1rem;
    }
}

/* Smooth transitions for all interactive elements */
.pricing-plan-card,
.plan-select-button,
.check-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
.pricing-plan-card:hover {
    transform: translateY(-2px);
}

/* Badge animations */
@keyframes badgeFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-2px);
    }
}

.pricing-plan-badge {
    animation: badgeFloat 3s ease-in-out infinite;
}

/* Template Modal Scrollbar Styling */
.template-modal-body::-webkit-scrollbar,
.show-more-modal-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.template-modal-body::-webkit-scrollbar-track,
.show-more-modal-body::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 4px;
}

.template-modal-body::-webkit-scrollbar-thumb,
.show-more-modal-body::-webkit-scrollbar-thumb {
    background: #6B7280;
    border-radius: 4px;
    border: 1px solid #374151;
}

.template-modal-body::-webkit-scrollbar-thumb:hover,
.show-more-modal-body::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF;
}

.template-modal-body::-webkit-scrollbar-corner,
.show-more-modal-body::-webkit-scrollbar-corner {
    background: #374151;
}

/* Ensure scrollbars are always visible when content overflows */
.template-modal-body,
.show-more-modal-body {
    overflow-y: scroll !important;
} 

/* ========== Homepage Success Toast Liquid Glass Animations ========== */

/* Liquid Slide-In Animation with 3D effects and blur transitions */
@keyframes liquidSlideInRight {
    0% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(15deg);
        filter: blur(4px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(0) scale(1.02) rotateY(0deg);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
}

/* Liquid Slide-Out Animation with matching timing */
@keyframes liquidSlideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
    40% {
        opacity: 0.6;
        transform: translateX(0) scale(1.02) rotateY(-5deg);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(15deg);
        filter: blur(4px);
    }
}

/* Homepage Success Toast Styling */
.liquid-glass-toast-inverted {
    position: relative;
    animation-duration: 495ms;
    animation-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation-fill-mode: both;
    perspective: 1000px;
}

/* Icon container styling for liquid glass effect */
.liquid-toast-icon-container {
    transition: all 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Close button styling with hover effects */
.liquid-close-button {
    will-change: transform, background-color, box-shadow;
}

.liquid-close-button:focus {
    outline: none;
    ring: 2px solid rgba(255, 255, 255, 0.3);
    ring-offset: 2px;
    ring-offset-color: transparent;
}

/* Enhanced typography for content */
.liquid-toast-content {
    will-change: opacity;
}

/* Success toast specific backdrop filters for enhanced visual quality */
.liquid-glass-toast-inverted {
    backdrop-filter: blur(20px) saturate(180%) brightness(1.1) contrast(1.05);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(1.1) contrast(1.05);
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
    .liquid-glass-toast-inverted {
        min-width: 340px;
        max-width: calc(100vw - 2rem);
        margin: 0 1rem;
    }
    
    .liquid-toast-icon-container {
        width: 36px;
        height: 36px;
    }
    
    .liquid-close-button {
        width: 28px;
        height: 28px;
    }
}

/* Reduced motion support for accessibility */
@media (prefers-reduced-motion: reduce) {
    @keyframes liquidSlideInRight {
        0% {
            opacity: 0;
            transform: translateX(20px);
        }
        100% {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes liquidSlideOutRight {
        0% {
            opacity: 1;
            transform: translateX(0);
        }
        100% {
            opacity: 0;
            transform: translateX(20px);
        }
    }
    
    .liquid-glass-toast-inverted {
        animation-duration: 200ms;
    }
    
    .liquid-toast-icon-container,
    .liquid-close-button {
        transition-duration: 150ms;
    }
} 