/**
 * Direct image download utility - bypasses Canvas processing to avoid CORS issues
 * Use this as a fallback when CORS configuration is not yet working
 */

/**
 * Downloads an image directly without Canvas processing
 * @param {string} imageUrl - The source image URL
 * @param {string|number} itemId - Unique identifier for the thumbnail
 * @param {string} filename - Optional custom filename
 */
export const downloadImageDirect = async (imageUrl, itemId = Date.now(), filename = null) => {
    try {
        console.log(`[Direct Download] Starting direct download for: ${imageUrl}`);
        
        // Create a temporary link element for download
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = filename || `thumbnail-${itemId}.jpg`;
        link.setAttribute('target', '_blank'); // Fallback: open in new tab
        
        // Add to DOM, click, and remove
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log(`[Direct Download] Download initiated successfully`);
        return true;
        
    } catch (error) {
        console.error('[Direct Download] Download failed:', error);
        
        // Last resort: open in new tab
        try {
            window.open(imageUrl, '_blank');
            console.log(`[Direct Download] Opened in new tab as fallback`);
            return true;
        } catch (openError) {
            console.error('[Direct Download] Even opening in new tab failed:', openError);
            return false;
        }
    }
};

/**
 * Downloads image with fetch approach (handles some CORS scenarios better)
 * @param {string} imageUrl - The source image URL
 * @param {string|number} itemId - Unique identifier for the thumbnail
 */
export const downloadImageWithFetch = async (imageUrl, itemId = Date.now()) => {
    try {
        console.log(`[Fetch Download] Starting fetch download for: ${imageUrl}`);
        
        // Fetch the image
        const response = await fetch(imageUrl, {
            mode: 'cors',
            credentials: 'omit'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        // Get the blob
        const blob = await response.blob();
        
        // Create object URL
        const blobUrl = URL.createObjectURL(blob);
        
        // Create download link
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `thumbnail-${itemId}.jpg`;
        
        // Download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Clean up object URL
        URL.revokeObjectURL(blobUrl);
        
        console.log(`[Fetch Download] Download completed successfully`);
        return true;
        
    } catch (error) {
        console.error('[Fetch Download] Fetch download failed:', error);
        
        // Fallback to direct download
        return downloadImageDirect(imageUrl, itemId);
    }
};

/**
 * Universal download function that tries multiple approaches
 * @param {string} imageUrl - The source image URL
 * @param {string|number} itemId - Unique identifier for the thumbnail
 */
export const downloadImageUniversal = async (imageUrl, itemId = Date.now()) => {
    console.log(`[Universal Download] Attempting download with multiple strategies`);
    
    // Strategy 1: Try fetch approach (best for CORS-enabled storage)
    const fetchSuccess = await downloadImageWithFetch(imageUrl, itemId);
    if (fetchSuccess) {
        return true;
    }
    
    // Strategy 2: Direct download link (works for most browsers)
    console.log(`[Universal Download] Fetch failed, trying direct download`);
    const directSuccess = await downloadImageDirect(imageUrl, itemId);
    if (directSuccess) {
        return true;
    }
    
    // Strategy 3: Copy URL to clipboard as last resort
    try {
        await navigator.clipboard.writeText(imageUrl);
        alert('Download failed, but the image URL has been copied to your clipboard. You can paste it in a new tab to save the image.');
        console.log(`[Universal Download] Copied URL to clipboard as last resort`);
        return true;
    } catch (clipboardError) {
        console.error('[Universal Download] All download strategies failed:', clipboardError);
        alert('Download failed. Please right-click the image and select "Save Image As..." or open the image in a new tab.');
        return false;
    }
}; 