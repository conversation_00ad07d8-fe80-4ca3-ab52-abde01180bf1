export default [
  {
    id: "reaction-epic",
    name: "Epic [TOPIC] Reaction!",
    description: "For epic reaction videos.",
    promptBase: "Create a fun YouTube thumbnail for 'Epic [TOPIC] Reaction!'. Show a person with a shocked expression, emoji and explosion icons, and bold text overlay: 'EPIC REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "EPIC\nREACTION!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-epic.webp" }
  },
  {
    id: "reaction-meme",
    name: "[MEME] Meme Reaction!",
    description: "For meme reaction videos.",
    promptBase: "Design a meme reaction thumbnail for '[MEME] Meme Reaction!'. Show a person laughing, meme and laughing emoji icons, and text overlay: 'MEME REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Laughing', includeIcons: true, textOverlay: true, overlayText: "MEME\nREACTION!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-meme.webp" }
  },
  {
    id: "reaction-music",
    name: "[SONG] Music Reaction!",
    description: "For music reaction videos.",
    promptBase: "Create a music reaction thumbnail for '[SONG] Music Reaction!'. Show a person with headphones, music note and heart icons, and bold text overlay: 'MUSIC REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "MUSIC\nREACTION!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-music.webp" }
  },
  {
    id: "reaction-try-not-to-laugh",
    name: "Try Not To Laugh Challenge!",
    description: "For try not to laugh challenge videos.",
    promptBase: "Design a challenge thumbnail for 'Try Not To Laugh Challenge!'. Show two people, one laughing and one serious, with challenge and timer icons. Text overlay: 'TRY NOT TO LAUGH!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Laughing', includeIcons: true, textOverlay: true, overlayText: "TRY NOT TO LAUGH!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-try-not-to-laugh.webp" }
  },
  {
    id: "reaction-food",
    name: "[FOOD] Taste Test Reaction!",
    description: "For food taste test reaction videos.",
    promptBase: "Create a taste test reaction thumbnail for '[FOOD] Taste Test Reaction!'. Show a person with a surprised expression, food and star icons, and bold text overlay: 'TASTE TEST!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Surprised', includeIcons: true, textOverlay: true, overlayText: "TASTE TEST!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-food.webp" }
  },
  {
    id: "reaction-review",
    name: "[PRODUCT] First Impression!",
    description: "For first impression or review reactions.",
    promptBase: "Design a first impression thumbnail for '[PRODUCT] First Impression!'. Show a person with a curious expression, product and thumbs up/down icons, and text overlay: 'FIRST IMPRESSION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Curious', includeIcons: true, textOverlay: true, overlayText: "FIRST IMPRESSION!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-review.webp" }
  },
  {
    id: "reaction-old-videos",
    name: "Reacting to My Old Videos!",
    description: "For YouTubers reacting to their old videos.",
    promptBase: "Create a nostalgic YouTube thumbnail for 'Reacting to My Old Videos!'. Change character style to look like a YouTuber with headphones, and use a background of blurred videos of people. Text overlay: 'OLD VIDEOS REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Embarrassed', includeIcons: true, textOverlay: true, overlayText: "OLD VIDEOS\nREACTION!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-old-videos.webp" }
  },
  {
    id: "reaction-gaming",
    name: "Gaming Reaction Highlights!",
    description: "For gaming reaction highlight videos.",
    promptBase: "Design a thrilling thumbnail for 'Gaming Reaction Highlights!'. Show a gamer with an intense expression, game controller and lightning icons, and bold text overlay: 'GAMING HIGHLIGHTS!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Intense', includeIcons: true, textOverlay: true, overlayText: "GAMING\nHIGHLIGHTS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-gaming.webp" }
  },
  {
    id: "reaction-prank",
    name: "Prank Reaction Madness!",
    description: "For prank reaction videos.",
    promptBase: "Create a hilarious thumbnail for 'Prank Reaction Madness!'. Show a person with a surprised and laughing expression, prank and laughing icons, and bold text overlay: 'PRANK MADNESS!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Surprised', includeIcons: true, textOverlay: true, overlayText: "PRANK\nMADNESS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/reaction/reaction-prank.webp" }
  }
]; 