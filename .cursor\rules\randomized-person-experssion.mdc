---
description: 
globs: 
alwaysApply: false
---
---
title: randomized-person-expression-and-framing
id: randomized-person-expression-and-framing.mdc
ruleType: manual
---

## 🧠 Feature: Randomized Person Mood Expression & Camera Framing

### Objective
Prevent repetitive, formulaic character poses and expressions in AI-generated thumbnails by introducing controlled randomness to both mood expression and camera framing.

---

## Applies To
- /src/utils/promptFormatter.js
- /src/hooks/usePromptEnhancer.ts
- /src/components/PromptInput.jsx

---

## 🎯 Problem
Currently, when a user selects a mood (e.g., "thinking"), the generated character often appears in the same pose and facial expression 80–90% of the time. This leads to thumbnails that look repetitive and lack visual interest. Camera framing (zoom/angle) is also rarely varied.

---

## ✅ Solution: Randomized Expression & Framing Rules

- **Expression/Pose Randomization:**  
  - For each mood (e.g., thinking, happy, surprised), maintain a list of 3–5 possible poses and facial expressions that fit the mood.
  - When generating a thumbnail, randomly select one from the list, rather than always using the default.
  - Example for "thinking":  
    - Hand on chin, looking up  
    - Finger on temple, looking sideways  
    - Arms crossed, furrowed brow  
    - Looking at camera with a slight frown  
    - Head tilted, eyes upward

- **Camera Framing Randomization:**  
  - Randomly vary the camera framing for the character:
    - 60–70% of the time: standard upper-body or head-and-shoulders shot
    - 20–30% of the time: close-up on the face (for emotional impact)
    - 10–20% of the time: wider shot (showing more of the body or background)
  - Ensure the framing is always appropriate for the selected mood and maintains thumbnail clarity.

- **Prompt Construction:**  
  - When building the prompt, inject a randomly selected pose/expression and a randomly selected camera framing instruction.
  - Example:  
    - "A person in a 'thinking' mood, with hand on chin and eyes looking up, shown in a close-up face shot."

---

## 🛠️ Implementation Notes

- Add arrays of pose/expression variants for each mood in the prompt construction logic.
- Use a randomizer to select both the pose/expression and the camera framing for each thumbnail generation.
- Ensure the randomization is controlled (not purely random) to avoid jarring or inappropriate combinations.
- Update the UI or prompt preview to reflect the selected pose/framing if possible.

---

## ✅ Done When

- Thumbnails for the same mood show a variety of poses and expressions.
- Camera framing varies naturally between close-up, medium, and wide shots.
- Visual diversity and realism are noticeably improved in generated thumbnails.

---

To activate this in Cursor: @randomized-person-expression-and-framing