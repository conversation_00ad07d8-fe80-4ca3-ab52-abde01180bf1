ThumbSpark Mobile & Responsive UX Rule
Objective:
Ensure all main app (non-auth) screens in ThumbSpark are fully mobile-optimized, responsive, and provide a seamless, app-like user experience across all devices and breakpoints. Fix layout, spacing, button, modal, and grid issues, and standardize UI for touch and accessibility.
Scope
Applies to: All main app screens (dashboard, home, controls, modals, banners, preview)
Excludes: Authentication screens (Sign In, Sign Up, etc.)
Requirements
1. Modals
All modals (delete, thumb details, preview, confirmation) must be perfectly centered on all devices.
Use equal padding on all sides (min 20px on mobile).
Modal buttons must be full-width and at least 44px tall on mobile.
"Copy" button must work on all devices (use Clipboard API, support touch events).
Modal close buttons must be easily tappable (min 32x32px).
2. Banners & CTA Buttons
Banner patterns must not show unwanted icons or overlap at any breakpoint.
All CTA buttons in banners must match the style of auth buttons (size, color, font, hover/tap states).
Banners must always be fully visible and not clipped on mobile.
3. Layout & Spacing
Reduce empty space at the top of the home/dashboard so main actions are visible without scrolling.
All action buttons must be visible above the fold on mobile.
Sidebar and header logo: show logo and drawer button at the top on mobile, hide on desktop as needed.
Personal info section and avatar: adjust layout and size for ≤768px (stack vertically, avatar 64–96px).
Sidebar top space: fill with logo and drawer button on mobile.
4. Grids & Cards
Mood/expression picker and template card grids:
1 column at ≤599px
2 columns at 600–1023px
3 columns at 1024–1279px
4 columns at ≥1280px
Cards must have consistent padding, border-radius, and shadow at all breakpoints.
5. Controls Bottom Row
Generate button: 80–85% width on mobile, centered.
Download button: oval, 44x44px, positioned to the right of Generate.
Both buttons must be touch-friendly and have clear focus/active states.
6. Accessibility & Touch
All interactive elements (buttons, toggles, inputs) must be at least 44x44px on mobile.
Ensure full keyboard and screen reader accessibility for all controls and modals.
Use semantic HTML and ARIA attributes as needed.
Testing & Acceptance
Test on real devices and emulators at 320px, 375px, 414px, 600px, 768px, 1024px, 1280px+.
No horizontal scrolling or clipped content at any breakpoint.
All buttons, modals, and banners are fully visible and usable on mobile.
All touch targets are easy to tap and visually clear.
No visual regressions on desktop.
Acceptance Criteria:
All main app screens are fully responsive and mobile-optimized.
No clipped, hidden, or overlapping UI at any breakpoint.
All modals, banners, and controls are touch-friendly and accessible.
"Copy" button and all CTAs work on mobile.
Layout and spacing are visually balanced and app-like on all devices.