---
description: 
globs: 
alwaysApply: true
---
@gradient-text-auto-shade
ruleId: gradient-text-auto-shade
description: >
  Always render text in the live preview and output using a sharp, readable gradient based on the user's selected primary color. Automatically generate lighter and/or darker shades of the same color (using HSL or similar) for the gradient stops. Never render plain solid color text unless gradient text is not supported by the browser, in which case fall back to the primary color. The color swatch row must show both the primary and generated gradient color(s).

appliesTo:
  - /src/components/ControlPanel.jsx
  - /src/styles/controls.css
  - /src/utils/colorUtils.js

ruleType: always

implementationNotes: |
  - When a user selects a primary color, convert it to HSL.
  - Generate a secondary (and optionally tertiary) color by adjusting only the lightness and/or saturation of the primary color, keeping the hue constant.
  - Use these colors as gradient stops for the text (e.g., linear-gradient(90deg, primary, secondary)).
  - Apply the gradient to the text using background-clip: text and -webkit-background-clip: text.
  - Ensure the text remains sharp and readable (no blur, no blocky rendering).
  - If the browser does not support gradient text, fall back to the solid primary color.
  - In the color swatch row, display both the primary and generated gradient color(s) as circular swatches.
  - All color math and gradient logic should be in a utility (e.g., /src/utils/colorUtils.js) for reusability.

sampleTransformedUI: |
  - User picks #B89F00 (gold) as primary.
  - System generates #FFD84D (lighter gold) as secondary.
  - Live preview text shows a left-to-right gradient from #B89F00 to #FFD84D, always sharp and readable.
  - Color swatch row: 🟡 → 🟡 (primary → lighter gold).

acceptanceCriteria:
  - Gradient text is always used in preview/output if supported.
  - Gradient is always based on the selected color’s hue.
  - Text is never blurry or unreadable.
  - Color swatch row always shows both primary and generated gradient color(s).
  - Fallback to solid color only if gradient text is not supported.

---
