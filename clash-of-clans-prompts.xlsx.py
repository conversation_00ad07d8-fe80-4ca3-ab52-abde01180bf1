import openpyxl
from openpyxl import Workbook

# Data: (Main Base Prompt, Text Overlay Suggestion, Category)
prompts = [
    # Base Building & Strategy
    ("Ultimate Town Hall 16 Base Design", "Never Gets 3 Starred!", "Base Building"),
    ("Secret Base Layout", "Defends Against Everything!", "Base Building"),
    ("Pro Builder Reveals War Base Strategy", "Unbeatable Defense!", "Base Building"),
    ("This Base Design Made Me Legend League!", "Legend League Ready!", "Base Building"),
    # Attack Strategies & Raids
    ("Insane 3-Star Attack Strategy", "Works Every Time!", "Attack Strategy"),
    ("How I Destroyed Max Town Hall 16", "With This Trick!", "Attack Strategy"),
    ("Secret Attack Combo", "Pros Don't Want You to Know!", "Attack Strategy"),
    ("Perfect Queen Walk Strategy", "Easy 3 Stars!", "Attack Strategy"),
    # Clan Wars & Competition
    ("Epic Clan War Comeback", "From 0 to Victory!", "Clan Wars"),
    ("My Clan vs #1 Global Clan", "Legendary Battle!", "Clan Wars"),
    ("Revenge Attack Gone Wrong", "Epic Fail!", "Clan Wars"),
    ("Clutch War Attack", "Saves the Day!", "Clan Wars"),
    # Upgrades & Progress
    ("Max Town Hall 16 Upgrade Journey", "Time Lapse!", "Upgrades"),
    ("Free-to-Play vs Pay-to-Win", "Who Wins?", "Upgrades"),
    ("Fastest Way to Max Your Base", "In 2024!", "Upgrades"),
    ("1000 Days of Clash Progress", "Incredible Transformation!", "Upgrades"),
    # Competitive & Achievements
    ("Road to Legend League", "Epic Push!", "Competitive"),
    ("World Record Breaking Attack Strategy", "Unstoppable!", "Competitive"),
    ("From Noob to Pro", "My Clash Journey!", "Competitive"),
    ("Impossible Challenge", "Can It Be Done?", "Competitive"),
    # Loot & Resources
    ("Biggest Loot Raid Ever", "2 Million Gold!", "Loot"),
    ("Secret Farming Strategy", "Massive Loot!", "Loot"),
    ("Best Army for Dark Elixir Farming", "Maximize Gains!", "Loot"),
    ("How to Get Rich Fast", "In Clash of Clans!", "Loot"),
    # Trending & Meta
    ("New Update Changes Everything", "OP Strategy!", "Trending"),
    ("This Troop Combination is Broken!", "Game Changer!", "Trending"),
    ("Supercell Doesn't Want You to See This!", "Secret Revealed!", "Trending"),
    ("Meta-Breaking Attack Strategy", "Game Changer!", "Trending"),
    # Entertainment & Reactions
    ("Reacting to My Old Clash Base", "Cringe Alert!", "Entertainment"),
    ("Trolling Global Chat in Clash of Clans!", "Hilarious Moments!", "Entertainment"),
    ("Epic Fail Compilation", "Worst Attacks Ever!", "Entertainment"),
    ("Rating Your Clash Bases", "Roast Session!", "Entertainment"),
]

wb = Workbook()
ws = wb.active
ws.title = "Clash of Clans Prompts"

# Header
ws.append(["Main Base Prompt", "Text Overlay Suggestion", "Category"])

# Data rows
for row in prompts:
    ws.append(row)

# Set filters
ws.auto_filter.ref = ws.dimensions

# Save file
wb.save("clash-of-clans-prompts.xlsx")
print("Excel file 'clash-of-clans-prompts.xlsx' created successfully.") 