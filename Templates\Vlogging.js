export default [
  {
    id: "vlog-street-food",
    name: "Trying Indian Street Food!",
    description: "For vlogs about experiencing street foods for the first time.",
    promptBase: "Create a YouTube thumbnail for 'Trying Indian Street Food!'. Show a person with a disappointed expression, surrounded by unusual Indian food items. Ensure the expression and overall look are very realistic, avoiding warm LUTs and maintaining a soft, calm tone.",
    settingsToApply: { includePerson: true, selectedExpression: 'Disappointed', includeIcons: true, textOverlay: true, overlayText: "STREET\nFOOD!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/street-food-vlog.webp" }
  },
  {
    id: "vlog-travel",
    name: "North Pole Adventure!",
    description: "For vlogs about traveling and experiencing extreme cold for the first time.",
    promptBase: "Create a thrilling thumbnail for 'North Pole Adventure!'. Show a person bundled up in heavy winter clothing, shivering with visible signs of freezing. Include icons of snowflakes and a thermometer showing below zero temperatures, with a snowy background. Text overlay: 'FIRST TIME IN THE COLD!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Freezing', includeIcons: true, textOverlay: true, overlayText: "FIRST TIME\nIN THE COLD!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/north-pole-adventure.webp" }
  },
  {
    id: "vlog-morning-routine",
    name: "Morning Routine [DAY]!",
    description: "For morning routine vlogs.",
    promptBase: "Create a stylish thumbnail for 'Morning Routine [DAY]!'. Show a female character trying high-brand perfumes, expressing contemplation about the best brand for her. Include a big 3D realistic question mark over blurred perfume boxes.",
    settingsToApply: { includePerson: true, selectedExpression: 'Thinking', includeIcons: true, textOverlay: true, overlayText: "MORNING\nROUTINE!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/morning-routine.webp" }
  },
  {
    id: "vlog-haul",
    name: "[STORE] Haul!",
    description: "For shopping haul vlogs.",
    promptBase: "Design a fun thumbnail for '[STORE] Haul!'. Show a person with shopping bags, gift and tag icons, and bold text overlay: 'HAUL!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "HAUL!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/haul.webp" }
  },
  {
    id: "vlog-qna",
    name: "Q&A With [NAME]",
    description: "For Q&A or interview vlogs.",
    promptBase: "Create a Q&A thumbnail for 'Q&A With [NAME]'. Show two people talking, question and chat icons, and text overlay: 'Q&A'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Talking', includeIcons: true, textOverlay: true, overlayText: "Q&A" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/qna.webp" }
  },
  {
    id: "vlog-niagara-falls",
    name: "Niagara Falls Adventure!",
    description: "For vlogs about visiting natural wonders like Niagara Falls.",
    promptBase: "Create a breathtaking thumbnail for 'Niagara Falls Adventure!'. Highlight the majestic waterfalls with mist and rainbow effects, and include nature and travel icons. Use bold text overlay: 'NIAGARA FALLS!'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "NIAGARA\nFALLS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/vlogging/niagara-falls-adventure.webp" }
  }
]; 