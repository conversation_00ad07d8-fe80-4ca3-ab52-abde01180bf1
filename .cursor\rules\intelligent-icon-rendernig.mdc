---
description: 
globs: 
alwaysApply: true
---
# 🎨 Intelligent Icon Rendering System
ruleType: always
appliesTo:
  - /src/utils/promptFormatter.js # Core prompt building logic
  - /src/components/ControlPanel.jsx # Icon controls and toggles
  - /src/utils/iconClassifier.js # NEW: Icon classification utility
  - /src/hooks/useIconRenderingMode.js # NEW: Hook for rendering mode detection
  - /src/templates/ # All template files for category-specific icon rules
  - /src/App.jsx # Main state management for icon rendering preferences

---

## 🧠 Core Concept: Dual-Style Icon Rendering

### Purpose
Automatically detect and classify icons based on real-world tangibility to apply appropriate rendering styles:
- **Realistic Rendering (70-80% realism)**: For tangible, real-world objects
- **3D Cartoonish Rendering**: For abstract concepts, emotions, and intangible ideas

---

## 🔍 Object Classification System

### 🌍 Real-World Objects → Realistic Rendering (70-80%)
**Trigger Conditions**: Objects that exist physically in reality

#### Health & Fitness Category
```javascript
const healthFitnessRealistic = [
  'stethoscope', 'dumbbells', 'heart rate monitor', 'blood pressure cuff',
  'medical syringe', 'pills', 'thermometer', 'bandage', 'scales',
  'yoga mat', 'resistance bands', 'protein powder', 'water bottle',
  'running shoes', 'fitness tracker', 'massage gun'
];
```

#### Food & Nutrition Category
```javascript
const foodNutritionRealistic = [
  'apple', 'banana', 'broccoli', 'carrot', 'tomato', 'avocado',
  'chef knife', 'cutting board', 'mixing bowl', 'measuring cup',
  'frying pan', 'oven', 'blender', 'coffee mug', 'dinner plate',
  'fork', 'spoon', 'wine glass', 'grocery bag', 'lunch box'
];
```

#### Technology Category
```javascript
const technologyRealistic = [
  'smartphone', 'laptop', 'desktop computer', 'tablet', 'smartwatch',
  'wireless earbuds', 'camera', 'microphone', 'keyboard', 'mouse',
  'router', 'usb cable', 'hard drive', 'graphics card', 'monitor',
  'vr headset', 'drone', 'smart speaker', 'gaming controller'
];
```

#### Finance & Business Category
```javascript
const financeBusinessRealistic = [
  'coins', 'banknotes', 'credit card', 'wallet', 'briefcase',
  'calculator', 'pen', 'documents', 'folder', 'filing cabinet',
  'office chair', 'desk', 'whiteboard', 'marker', 'stapler',
  'business card', 'handshake', 'signature', 'contract', 'seal'
];
```

#### Education Category
```javascript
const educationRealistic = [
  'books', 'notebook', 'pencil', 'eraser', 'ruler', 'backpack',
  'chalkboard', 'graduation cap', 'diploma', 'globe', 'microscope',
  'calculator', 'highlighter', 'sticky notes', 'binder', 'desk lamp',
  'chair', 'classroom desk', 'library shelf', 'textbook'
];
```

#### Transportation & Travel Category
```javascript
const transportationRealistic = [
  'car', 'bicycle', 'airplane', 'train', 'bus', 'motorcycle',
  'suitcase', 'passport', 'ticket', 'map', 'compass', 'camera',
  'hotel key', 'luggage tag', 'travel pillow', 'sunglasses'
];
```

### 🎭 Abstract Concepts → 3D Cartoonish Rendering
**Trigger Conditions**: Intangible concepts, emotions, digital phenomena

#### Emotions & Feelings
```javascript
const emotionsCartoonish = [
  'joy', 'happiness', 'sadness', 'anger', 'surprise', 'fear',
  'love', 'excitement', 'disappointment', 'pride', 'confidence',
  'stress', 'relaxation', 'motivation', 'inspiration', 'determination'
];
```

#### Digital & Virtual Concepts
```javascript
const digitalConceptsCartoonish = [
  'cloud computing', 'data storage', 'wifi signal', 'download',
  'upload', 'sync', 'backup', 'security shield', 'firewall',
  'algorithm', 'artificial intelligence', 'machine learning',
  'blockchain', 'cryptocurrency', 'digital transformation'
];
```

#### Time & Abstract Ideas
```javascript
const abstractConceptsCartoonish = [
  'time', 'past', 'present', 'future', 'growth', 'success',
  'innovation', 'creativity', 'strategy', 'goal', 'target',
  'progress', 'achievement', 'vision', 'mission', 'values',
  'teamwork', 'leadership', 'communication', 'collaboration'
];
```

#### Weather & Natural Phenomena
```javascript
const weatherCartoonish = [
  'sunshine', 'rain', 'snow', 'lightning', 'wind', 'storm',
  'rainbow', 'cloud', 'hurricane', 'tornado', 'earthquake',
  'volcano', 'meteor', 'aurora', 'eclipse'
];
```

---

## 🛠 Implementation Logic

### Icon Classification Function
```javascript
// /src/utils/iconClassifier.js
export const classifyIconRenderingStyle = (iconKeyword, category = 'general') => {
  const keyword = iconKeyword.toLowerCase().trim();
  
  // Category-specific realistic objects
  const realisticObjects = [
    ...healthFitnessRealistic,
    ...foodNutritionRealistic,
    ...technologyRealistic,
    ...financeBusinessRealistic,
    ...educationRealistic,
    ...transportationRealistic
  ];
  
  // Abstract/cartoonish concepts
  const cartoonishConcepts = [
    ...emotionsCartoonish,
    ...digitalConceptsCartoonish,
    ...abstractConceptsCartoonish,
    ...weatherCartoonish
  ];
  
  // Check for realistic rendering
  if (realisticObjects.some(obj => keyword.includes(obj) || obj.includes(keyword))) {
    return {
      style: 'realistic',
      renderingLevel: '70-80% photorealistic',
      description: 'High-detail realistic rendering with natural lighting and textures'
    };
  }
  
  // Check for cartoonish rendering
  if (cartoonishConcepts.some(concept => keyword.includes(concept) || concept.includes(concept))) {
    return {
      style: 'cartoonish',
      renderingLevel: '3D stylized',
      description: 'Clean 3D cartoonish style with vibrant colors and smooth surfaces'
    };
  }
  
  // Default fallback based on category
  return getDefaultStyleByCategory(category);
};

const getDefaultStyleByCategory = (category) => {
  const realisticCategories = ['health-fitness', 'food', 'tech', 'finance', 'business', 'education'];
  const cartoonishCategories = ['emotions', 'abstract', 'digital', 'weather'];
  
  if (realisticCategories.includes(category)) {
    return {
      style: 'realistic',
      renderingLevel: '70-80% photorealistic',
      description: 'High-detail realistic rendering with natural lighting and textures'
    };
  }
  
  return {
    style: 'cartoonish',
    renderingLevel: '3D stylized',
    description: 'Clean 3D cartoonish style with vibrant colors and smooth surfaces'
  };
};
```

### Enhanced Prompt Building Integration
```javascript
// Update /src/utils/promptFormatter.js
import { classifyIconRenderingStyle } from './iconClassifier.js';

export const buildIconRenderingInstructions = (includeIcons, userPrompt, templateCategory) => {
  if (!includeIcons) return '';
  
  // Extract potential icon keywords from prompt
  const iconKeywords = extractIconKeywords(userPrompt);
  
  let realisticIcons = [];
  let cartoonishIcons = [];
  
  iconKeywords.forEach(keyword => {
    const classification = classifyIconRenderingStyle(keyword, templateCategory);
    if (classification.style === 'realistic') {
      realisticIcons.push(keyword);
    } else {
      cartoonishIcons.push(keyword);
    }
  });
  
  let instructions = '\n\nICON RENDERING INSTRUCTIONS:\n';
  
  if (realisticIcons.length > 0) {
    instructions += `- REALISTIC ICONS (70-80% photorealistic): ${realisticIcons.join(', ')}
      Render these with: Natural lighting, realistic textures, proper shadows, 
      material-accurate surfaces, subtle wear/aging effects, professional photography quality.\n`;
  }
  
  if (cartoonishIcons.length > 0) {
    instructions += `- 3D CARTOONISH ICONS: ${cartoonishIcons.join(', ')}
      Render these with: Clean 3D stylized appearance, vibrant saturated colors, 
      smooth gradient surfaces, simplified geometry, glossy/matte finish, playful proportions.\n`;
  }
  
  if (realisticIcons.length === 0 && cartoonishIcons.length === 0) {
    // Fallback based on category
    const defaultStyle = getDefaultStyleByCategory(templateCategory);
    instructions += `- DEFAULT STYLE: ${defaultStyle.description}\n`;
  }
  
  instructions += '\nEnsure visual consistency within each style category while maintaining clear distinction between realistic and cartoonish elements.';
  
  return instructions;
};

const extractIconKeywords = (prompt) => {
  // Enhanced keyword extraction logic
  const iconIndicators = [
    'icon', 'symbol', 'graphic', 'illustration', 'element',
    'with', 'showing', 'featuring', 'including', 'displaying'
  ];
  
  // Split prompt and look for potential icon references
  const words = prompt.toLowerCase().split(/[\s,.:;!?]+/);
  const iconKeywords = [];
  
  words.forEach((word, index) => {
    if (iconIndicators.some(indicator => words[index - 1] === indicator)) {
      iconKeywords.push(word);
    }
  });
  
  return iconKeywords;
};
```

### Template-Specific Icon Rules
```javascript
// Update template files to include icon rendering preferences
const businessTemplate = {
  id: "business-success",
  name: "Business Success",
  category: "business",
  iconRenderingRules: {
    preferRealistic: ['briefcase', 'handshake', 'documents', 'calculator'],
    preferCartoonish: ['success', 'growth', 'target', 'achievement'],
    defaultStyle: 'realistic'
  },
  promptBase: "Create a professional business success thumbnail...",
  // ... other properties
};
```

### UI Enhancement for Icon Controls
```javascript
// Update /src/components/ControlPanel.jsx
const IconRenderingModeSelector = ({ currentMode, onModeChange }) => {
  const modes = [
    { 
      id: 'auto', 
      name: 'Smart Auto', 
      description: 'Automatically choose realistic or cartoonish based on object type',
      icon: 'solar:magic-stick-3-bold-duotone'
    },
    { 
      id: 'realistic', 
      name: 'All Realistic', 
      description: 'Render all icons with 70-80% photorealistic quality',
      icon: 'solar:camera-bold-duotone'
    },
    { 
      id: 'cartoonish', 
      name: 'All Cartoonish', 
      description: 'Render all icons in 3D cartoonish style',
      icon: 'solar:pallete-2-bold-duotone'
    }
  ];
  
  return React.createElement('div', { className: 'icon-rendering-mode-selector' },
    React.createElement('label', { className: 'text-sm font-medium text-gray-300 mb-2' }, 
      'Icon Rendering Style:'
    ),
    React.createElement('div', { className: 'mode-grid grid grid-cols-1 gap-2' },
      modes.map(mode => 
        React.createElement('button', {
          key: mode.id,
          onClick: () => onModeChange(mode.id),
          className: `mode-option p-3 rounded-lg border transition-all ${
            currentMode === mode.id 
              ? 'border-purple-500 bg-purple-900/30' 
              : 'border-gray-600 bg-gray-700/50 hover:border-purple-400'
          }`,
          'aria-pressed': currentMode === mode.id
        },
          React.createElement('div', { className: 'flex items-center gap-2 mb-1' },
            React.createElement('span', { 
              className: 'iconify text-lg',
              'data-icon': mode.icon
            }),
            React.createElement('span', { className: 'font-medium text-white' }, mode.name)
          ),
          React.createElement('p', { className: 'text-xs text-gray-400' }, mode.description)
        )
      )
    )
  );
};
```

---

## 🎯 Advanced Features

### Context-Aware Classification
- **Business/Finance**: Default to realistic for professional credibility
- **Health/Fitness**: Mix realistic equipment with cartoonish emotions
- **Food**: Realistic ingredients with cartoonish taste expressions
- **Education**: Realistic materials with cartoonish learning concepts

### Quality Gradients
- **Ultra-Realistic (90-95%)**: Premium mode for hero objects
- **Standard Realistic (70-80%)**: Default for most real objects
- **Semi-Realistic (50-60%)**: Stylized but recognizable
- **Cartoonish (3D Styled)**: Clean, vibrant, simplified

### Smart Detection Keywords
```javascript
const enhancedKeywordDetection = {
  realistic: [
    'product', 'tool', 'equipment', 'device', 'machine', 'vehicle',
    'food', 'ingredient', 'material', 'object', 'item', 'gear'
  ],
  cartoonish: [
    'feeling', 'emotion', 'concept', 'idea', 'energy', 'vibe',
    'mood', 'spirit', 'essence', 'aura', 'flow', 'power'
  ]
};
```

---

## 📋 Implementation Checklist

✅ **Phase 1: Core Classification System**
- [ ] Create `iconClassifier.js` utility
- [ ] Implement keyword detection logic
- [ ] Add category-based defaults

✅ **Phase 2: Prompt Integration**
- [ ] Update `buildPrompt` function
- [ ] Add icon rendering instructions
- [ ] Test with existing templates

✅ **Phase 3: UI Controls**
- [ ] Add icon rendering mode selector
- [ ] Create preview indicators
- [ ] Implement user override options

✅ **Phase 4: Template Enhancement**
- [ ] Update all template files
- [ ] Add category-specific rules
- [ ] Test across all template types

✅ **Phase 5: Quality Assurance**
- [ ] A/B test rendering styles
- [ ] Gather user feedback
- [ ] Refine classification logic

---

This system will automatically enhance thumbnail generation by applying appropriate rendering styles based on object tangibility, creating more engaging and contextually appropriate visuals for your users.