---
description: 
globs: 
alwaysApply: false
---
signup-login-welcome
# 📌 Description
Hero UI-based Signup/Login Welcome Screen for MVP

# Type: manual

## Requirements

- Create `/src/pages/Welcome.jsx` (or similar) for the welcome/auth screen.
- Use **Hero UI Kit** (Tailwind CDN) for all styling and layout.
- The page should have two modes: **Signup** and **Login**.
- Each mode is a card/panel centered on the screen, with:
  - Headline and subtitle
  - Form fields (see above)
  - Primary action button (Sign Up / Log In)
  - Google auth button (with icon, no real auth logic)
  - Link to switch modes
  - “Skip” button (top-right or below form)
- All form fields must use Hero UI input styles, with clear focus/active states.
- Use Heroicons or Solar icons for input adornments and Google button.
- Responsive and dark mode by default.
- No backend/auth logic—just UI/UX.
- Placeholders and error states are optional, but keep the UI clean and modern.

## Deliverables

- `/src/pages/Welcome.jsx` (or `/components/Welcome.jsx`)
- All styles via Tailwind/hero-ui classes (no inline styles)
- Demo-ready, visually polished, and easy to extend for real auth later

# Example UI
- Centered card with “Welcome!” headline
- Tabs or links to switch between Signup/Login
- Google button with icon
- “Skip” button (text/ghost style)
- All fields and buttons styled with Hero UI Kit

# Notes
- No backend, no real auth—just front-end UI
- Use Hero UI Kit and Heroicons/Solar icons only
- Ready for future backend integration
