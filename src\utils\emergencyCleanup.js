/**
 * 🚨 Emergency localStorage Cleanup Utility
 * 
 * Call this function to immediately free up localStorage space
 * when facing "quota has been exceeded" errors.
 */

export const emergencyCleanup = () => {
    console.log('🚨 Starting emergency localStorage cleanup...');
    
    let freedSpace = 0;
    let removedItems = 0;
    
    // Keys to preserve (critical auth tokens)
    const criticalKeys = [
        'sb-csibhnfqpwqkhpnvdakz-auth-token',
        'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier',
        'supabase.auth.token',
        'remember_me_auth'
    ];
    
    try {
        // Get all localStorage keys
        const allKeys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) allKeys.push(key);
        }
        
        console.log(`📊 Found ${allKeys.length} items in localStorage`);
        
        // Remove items by priority (largest and least important first)
        const itemsToRemove = [];
        
        allKeys.forEach(key => {
            const value = localStorage.getItem(key);
            if (value && !criticalKeys.includes(key) && !criticalKeys.some(ck => key.startsWith(ck))) {
                const size = value.length;
                
                // Categorize items for removal
                let priority = 1;
                if (key.startsWith('avatar_')) priority = 5; // Large files
                else if (key.includes('cache')) priority = 2;
                else if (key.includes('temp')) priority = 1;
                else if (key.includes('openmoji')) priority = 2;
                else if (key.includes('demo_')) priority = 3;
                
                itemsToRemove.push({ key, size, priority });
            }
        });
        
        // Sort by priority (higher first) and size (larger first)
        itemsToRemove.sort((a, b) => {
            if (a.priority !== b.priority) return b.priority - a.priority;
            return b.size - a.size;
        });
        
        // Remove items until we've freed at least 2MB
        const targetSpace = 2 * 1024 * 1024; // 2MB
        
        for (const item of itemsToRemove) {
            try {
                localStorage.removeItem(item.key);
                freedSpace += item.size;
                removedItems++;
                console.log(`🗑️ Removed: ${item.key} (${formatBytes(item.size)})`);
                
                if (freedSpace >= targetSpace) {
                    break;
                }
            } catch (error) {
                console.warn(`Failed to remove ${item.key}:`, error);
            }
        }
        
        console.log(`✅ Emergency cleanup complete:`);
        console.log(`   - Removed ${removedItems} items`);
        console.log(`   - Freed ${formatBytes(freedSpace)} of space`);
        console.log(`   - Preserved ${criticalKeys.length} critical auth tokens`);
        
        return {
            success: true,
            removedItems,
            freedSpace: formatBytes(freedSpace),
            message: `Cleaned up ${removedItems} items, freed ${formatBytes(freedSpace)}`
        };
        
    } catch (error) {
        console.error('Emergency cleanup failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

// Helper function to format bytes
const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Global function for easy access in console
window.emergencyCleanup = emergencyCleanup;

// Also provide a simple function to check storage usage
export const checkStorageUsage = () => {
    let totalSize = 0;
    let itemCount = 0;
    
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        const value = localStorage.getItem(key);
        if (key && value) {
            totalSize += value.length;
            itemCount++;
        }
    }
    
    const estimatedQuota = 5 * 1024 * 1024; // 5MB typical limit
    const usagePercentage = (totalSize / estimatedQuota) * 100;
    
    const report = {
        totalItems: itemCount,
        totalSize: formatBytes(totalSize),
        estimatedQuota: formatBytes(estimatedQuota),
        usagePercentage: Math.min(usagePercentage, 100).toFixed(1) + '%',
        isNearLimit: usagePercentage > 80,
        isFull: usagePercentage > 95
    };
    
    console.log('📊 localStorage Usage Report:', report);
    return report;
};

window.checkStorageUsage = checkStorageUsage; 