---
description: 
globs: 
alwaysApply: false
---
@openmoji-emoji-standardization
ruleId: openmoji-emoji-standardization
description: >
  All emoji in the UI (including mood/expression and gender selectors) must use OpenMoji SVG or PNG assets for rendering, not system Unicode emoji. This ensures a consistent, cross-platform appearance regardless of OS or browser. Emoji must be loaded from local OpenMoji assets or CDN, with no fallback to system fonts. Accessibility: each emoji must have an appropriate aria-label or alt text. Remove all system emoji font usage from the codebase.

appliesTo:
  - /src/components/ControlPanel.jsx
  - /src/components/person/MoodAndExpressionPicker.jsx
  - /src/components/person/GenderSelector.jsx
  - /src/styles/controls.css

acceptanceCriteria:
  - All emoji are rendered using OpenMoji SVG/PNG, not Unicode.
  - Emoji appearance is identical on all platforms.
  - Accessibility labels are present for all emoji.
  - No system emoji font fallback.
  - No layout or spacing regressions.

references:
  - https://openmoji.org/
  - https://github.com/hfg-gmuend/openmoji
  - https://openmoji.org/library/#license