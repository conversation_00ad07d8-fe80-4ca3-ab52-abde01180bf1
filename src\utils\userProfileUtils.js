/**
 * User Profile Utilities
 * Handles username generation, validation, and profile management for Gmail authenticated users
 */

/**
 * Generates a unique username for new Gmail authenticated users
 * @param {string} email - User's email address
 * @param {string} fullName - User's full name from Gmail
 * @returns {string} Generated username
 */
export const generateUniqueUsername = (email, fullName) => {
    // Extract base username from email or name
    let baseUsername = '';
    
    if (email) {
        // Use email prefix as base
        baseUsername = email.split('@')[0].toLowerCase();
    } else if (fullName) {
        // Use first name as base
        baseUsername = fullName.split(' ')[0].toLowerCase();
    } else {
        baseUsername = 'user';
    }
    
    // Clean username - only allow alphanumeric and underscores
    baseUsername = baseUsername.replace(/[^a-z0-9]/g, '');
    
    // Ensure it's not too long
    if (baseUsername.length > 14) {
        baseUsername = baseUsername.substring(0, 14);
    }
    
    // Generate unique suffix (6-digit number)
    const uniqueId = Math.floor(Math.random() * 900000) + 100000;
    
    return `${baseUsername}${uniqueId}`;
};

/**
 * Validates username format and requirements
 * @param {string} username - Username to validate
 * @returns {Object} Validation result with isValid and error message
 */
export const validateUsername = (username) => {
    if (!username || username.length < 3) {
        return {
            isValid: false,
            error: 'Username must be at least 3 characters long'
        };
    }
    
    if (username.length > 20) {
        return {
            isValid: false,
            error: 'Username must be 20 characters or less'
        };
    }
    
    if (!/^[a-z0-9_]+$/.test(username)) {
        return {
            isValid: false,
            error: 'Username can only contain lowercase letters, numbers, and underscores'
        };
    }
    
    if (username.startsWith('_') || username.endsWith('_')) {
        return {
            isValid: false,
            error: 'Username cannot start or end with an underscore'
        };
    }
    
    // Check for reserved usernames
    const reservedUsernames = [
        'admin', 'root', 'user', 'test', 'api', 'www', 'mail', 'ftp',
        'thumbspark', 'support', 'help', 'info', 'contact', 'about',
        'login', 'signup', 'register', 'dashboard', 'profile', 'settings'
    ];
    
    if (reservedUsernames.includes(username.toLowerCase())) {
        return {
            isValid: false,
            error: 'This username is reserved. Please choose another one.'
        };
    }
    
    return {
        isValid: true,
        error: null
    };
};

/**
 * Formats display name for user profile
 * @param {Object} user - User object from Supabase
 * @returns {string} Formatted display name
 */
export const getDisplayName = (user) => {
    if (user?.user_metadata?.full_name) {
        return user.user_metadata.full_name;
    }
    
    if (user?.email) {
        // Capitalize first letter of email prefix
        const emailPrefix = user.email.split('@')[0];
        return emailPrefix.charAt(0).toUpperCase() + emailPrefix.slice(1);
    }
    
    return 'User';
};

/**
 * Gets or generates username for user
 * @param {Object} user - User object from Supabase
 * @returns {string} Username
 */
export const getUserUsername = (user) => {
    if (user?.user_metadata?.username) {
        return user.user_metadata.username;
    }
    
    // Generate username if not exists
    return generateUniqueUsername(user?.email, user?.user_metadata?.full_name);
};

/**
 * Checks if user profile needs initialization (for new Gmail users)
 * @param {Object} user - User object from Supabase
 * @returns {boolean} True if profile needs initialization
 */
export const needsProfileInitialization = (user) => {
    return !user?.user_metadata?.username || !user?.user_metadata?.profile_initialized;
};

/**
 * Initializes user profile for new Gmail authenticated users
 * @param {Object} supabaseClient - Supabase client instance
 * @param {Object} user - User object from Supabase
 * @returns {Promise<Object>} Updated user object
 */
export const initializeUserProfile = async (supabaseClient, user) => {
    try {
        if (!needsProfileInitialization(user)) {
            return user;
        }
        
        const username = generateUniqueUsername(user.email, user.user_metadata?.full_name);
        const displayName = getDisplayName(user);
        
        const { data, error } = await supabaseClient.auth.updateUser({
            data: {
                ...user.user_metadata,
                username: username,
                full_name: displayName,
                profile_initialized: true,
                profile_created_at: new Date().toISOString()
            }
        });
        
        if (error) {
            console.error('Failed to initialize user profile:', error);
            return user;
        }
        
        console.log('✅ User profile initialized with username:', username);
        return data.user;
    } catch (error) {
        console.error('Error initializing user profile:', error);
        return user;
    }
};

/**
 * Updates user profile information
 * @param {Object} supabaseClient - Supabase client instance
 * @param {Object} profileData - Profile data to update
 * @returns {Promise<Object>} Update result
 */
export const updateUserProfile = async (supabaseClient, profileData) => {
    try {
        // Validate username if provided
        if (profileData.username) {
            const validation = validateUsername(profileData.username);
            if (!validation.isValid) {
                return {
                    success: false,
                    error: validation.error
                };
            }
        }
        
        const { data, error } = await supabaseClient.auth.updateUser({
            data: {
                ...profileData,
                profile_updated_at: new Date().toISOString()
            }
        });
        
        if (error) {
            return {
                success: false,
                error: error.message
            };
        }
        
        return {
            success: true,
            user: data.user
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}; 