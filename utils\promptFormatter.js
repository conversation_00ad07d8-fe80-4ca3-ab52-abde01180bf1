// Utility to build a GPT-image-1 prompt following universal thumbnail-generation rules
// Docs: thumbnail-generation.mdc

import { extractContextualVisuals } from './src/utils/promptEnhancer.js';

/**
 * Build a prompt for GPT-image-1 based on UI state and universal rules.
 * @param {Object} params
 * @param {string} params.userPrompt - Raw user prompt describing the topic.
 * @param {boolean} params.includePerson - Whether to include a human figure.
 * @param {boolean} params.includeIcons - Whether to include icons/emojis.
 * @param {boolean} params.textOverlay - Whether to add text overlay.
 * @param {string} params.selectedExpression - Selected expression/mood for the person.
 * @param {boolean} params.fitFullCanvas - Whether to fit the image to the full canvas.
 * @param {string} params.overlayText - Custom text provided by the user for the overlay.
 * @param {string} params.textPosition - Position of the text overlay.
 * @param {string} params.selectedTextSize - Selected text size for the text overlay.
 * @param {string} params.selectedFontFamily - Selected font family for the text overlay.
 * @param {string} params.selectedGender - Selected gender for the person in the thumbnail.
 * @param {string} params.primaryTextColor - Primary color for the text overlay.
 * @param {string} params.secondaryTextColor - Secondary color for the text overlay.
 * @param {string} params.customFaceImageUrl - URL of the custom face image, if provided.
 * @returns {string} GPT-image-1 prompt string.
 */
export function buildThumbnailPrompt({ userPrompt, includePerson, includeIcons, textOverlay, selectedExpression, fitFullCanvas, overlayText, textPosition, selectedTextSize, selectedFontFamily, selectedGender, primaryTextColor, secondaryTextColor, customFaceImageUrl }) {

    // Define position descriptions
    const positionDescription = {
        "Top Left": "Position the title text block in the upper-left corner.",
        "Top Center": "Center the title text block horizontally at the top.",
        "Top Right": "Position the title text block in the upper-right corner.",
        "Center": "Place the title text block directly in the center of the image.",
        "Bottom Left": "Position the title text block in the bottom-left corner.",
        "Bottom Center": "Center the title text block horizontally at the bottom.",
        "Bottom Right": "Position the title text block in the bottom-right corner.",
    };

    // Define the edge safe zone reinforcement text
    const edgeSafeZoneInstruction = "Ensure the entire text overlay, including shadow and glow, is fully visible and **at least 100px away from the left and right edges**. If necessary to fit, reduce font size or wrap text to prevent cropping. Never allow text or effects to touch or cross the image border.";
    const topBottomSafeZoneInstruction = "Ensure at least 50px vertical margin from the top/bottom edges.";

    // Start with the base instruction
    let prompt = `Create a cinematic YouTube thumbnail image at 1280x720 resolution.\n\n`;

    // Subject Section
    prompt += `Subject:\n`;
    if (includePerson) {
        let genderDesc = "a human figure";
        if (selectedGender === "Male") {
            genderDesc = "a male human figure";
        } else if (selectedGender === "Female") {
            genderDesc = "a female human figure";
        } else if (selectedGender === "Non-binary") {
            genderDesc = "a non-binary human figure";
        }

        let expressionInstruction = "";
        switch (selectedExpression) {
            case "Happy":
                expressionInstruction = `with a happy facial expression featuring a genuine smile and bright open eyes (not closed or squinting).`;
                break;
            case "Shocked":
                expressionInstruction = `with a shocked or surprised facial emotion.`;
                break;
            case "Loved":
                expressionInstruction = `with an excited or loving facial emotion, possibly with heart motifs if appropriate.`;
                break;
            case "Thinking":
                expressionInstruction = `with a curious or thoughtful facial emotion, perhaps with a hand on chin.`;
                break;
            case "Focus":
                expressionInstruction = `appearing intently focused or concentrating on the main subject, with a serious or determined expression.`;
                break;
            case "Angry":
                expressionInstruction = `with an angry facial emotion.`;
                break;
            case "Crying":
                expressionInstruction = `with a sad or crying facial emotion (can be for 'cringe' or empathetic sadness).`;
                break;
            case "Laughing":
                expressionInstruction = `with a natural laughing expression featuring a joyful, pleasant smile or gentle laughter and bright open eyes (not squinting, closed, or overly crinkled). Avoid exaggerated or cartoonish laughter. The expression should be genuinely happy, friendly, approachable, and realistic with a sense of warmth and engagement.`;
                break;
            case "Thumbs Up":
                expressionInstruction = `with a positive, approving expression while making a clear thumbs up hand gesture. The face should show satisfaction, approval, or "feeling good" mood. The thumbs up gesture should be prominent and clearly visible, positioned naturally (typically at chest or shoulder level). This expresses positive feelings about recommendations, good things, or general approval.`;
                break;
            case "Thumbs Down":
                expressionInstruction = `with a disapproving or negative expression while making a clear thumbs down hand gesture. The face should show disappointment, disapproval, or "not recommended" mood. The thumbs down gesture should be prominent and clearly visible, positioned naturally. This expresses negative feelings about something not recommended or disliked.`;
                break;
            case "OK Hand":
                expressionInstruction = `with a satisfied, "all good" expression while making a clear OK hand sign (thumb and forefinger forming a circle with other fingers extended). The face should show contentment, perfection, or "everything is fine" mood. The OK hand gesture should be prominent and clearly visible, positioned naturally. This is similar to thumbs up in positive feeling but with a distinct hand movement expressing "perfect" or "all good".`;
                break;
            case "Neutral":
                expressionInstruction = `with a neutral facial emotion.`;
                break;
            case "Proud":
                expressionInstruction = `with a proud, confident, or cool facial emotion (e.g., smiling with sunglasses).`;
                break;
            case "Default":
            default:
                expressionInstruction = `with a neutral or context-appropriate facial emotion (based on the main prompt).`;
                break;
        }
        
        if (customFaceImageUrl && customFaceImageUrl.trim() !== '') {
            prompt += `- Replace the subject's face with the headshot image from the following URL: ${customFaceImageUrl.trim()}. Maintain the same pose, expression (${selectedExpression}), lighting, and context. Use the face from the provided URL accurately and naturally, as if it were part of the original thumbnail scene. The figure should be ${genderDesc}.\n`;
        } else {
            prompt += `- Include ${genderDesc} ${expressionInstruction}\n`;
        }
        prompt += `- Pose: facing camera, pointing, reacting, or holding a relevant object related to: ${userPrompt}.\n`;
    } else {
        prompt += `- Focus on the core topic: ${userPrompt}. Do not include any human figures or faces.\n`;
    }

    // Text Overlay Section - Updated Logic
    if (textOverlay) {
        let basePlacement = positionDescription[textPosition] || positionDescription["Top Right"];
        let finalPlacementInstruction = `${basePlacement} ${edgeSafeZoneInstruction} ${topBottomSafeZoneInstruction}`;

        // Add text size instruction based on selectedTextSize
        let textSizeInstruction = "";
        if (selectedTextSize === "Medium") {
            textSizeInstruction = `\\n- Text Size: Use moderately sized title text that fits comfortably without overpowering the thumbnail.`;
        } else if (selectedTextSize === "Large") {
            textSizeInstruction = `\\n- Text Size: Use large, bold title text for dramatic visual impact and mobile-first visibility.`;
        }

        // Shared styling instructions including the new gradient effect
        const sharedStyling = `\\n- Font: Use font family '${selectedFontFamily}'. Ensure it is bold, highly legible, and uppercase.${textSizeInstruction}\\n` +
                              `- Effects: Add BOTH a strong drop shadow AND a bright outer glow.\\n` +
                              `- **Style Enhancement:** Render text with a shiny/glossy finish. Apply a **gradient feather** using the main text color (lighter shade top, darker shade bottom). Add a subtle top highlight/shine.\\n` +
                              `- Colors: Use primary color '${primaryTextColor}' and secondary color '${secondaryTextColor}'. Create high contrast, for example, by using one for the main fill and the other for an outline or glow, or by creating a gradient between them.${selectedGender}\\n` +
                              `- Placement & Safe Zone: ${finalPlacementInstruction}\\n`;

        if (overlayText && overlayText.trim().length > 0) {
            // Use custom overlay text with enhanced placement & styling
            prompt += `\\nCustom Text Overlay (Exact - Pyramid Shape):\\n`;
            prompt += `- Use the following text **exactly as provided**...\\n... (exact text rules remain the same) ...\\n\`\`\`\\n${overlayText.trim()}\\n\`\`\`\\n`;
            prompt += `- Center and style the text in a bold, pyramid shape as written.\\n`;
            prompt += `- Do not add any other text, subtitles, or titles.\\n`;
            prompt += sharedStyling; // Apply shared styling with gradient

        } else {
            // Fallback to using userPrompt with enhanced placement & styling
            prompt += `\\nText Overlay (Strict Rules - Derived from Prompt):\\n`;
            prompt += `- Generate a bold, uppercase title (target: 3-6 words) based on the video topic: ${userPrompt}.\\n`;
            prompt += `- Make the title impactful, complete, and avoid ending with ellipses (...). \\n`;
            prompt += `- Use action-oriented phrasing or create curiosity where appropriate.\\n`;
            prompt += sharedStyling; // Apply shared styling with gradient
        }
    } else {
        prompt += `\nText Overlay: OFF\n`;
        prompt += `- Do NOT include any text, titles, or writing of any kind in the image.\n`;
        prompt += `- Focus only on visual/graphic elements, icons, and background composition.\n`;
    }

    // Visual Focus Section - Updated Icon Logic
    prompt += `\\nVisual Focus:\\n`;
    prompt += `- Keep the layout simple, clean, and high-impact.\\n`;
    if (includeIcons) {
        // Replace the old simple instruction with context-aware logic
        prompt += `- Analyze the main video topic (${userPrompt}) and select **2-4 icons or emojis** that directly represent the subject, action, or emotion.\\n`;
        prompt += `- Avoid generic or unrelated emojis (e.g., random food/smileys unless relevant).\\n`;
        prompt += `- Ensure all selected icons/emojis share a **consistent visual style** (e.g., all flat, all 3D, all outlined).\\n`;
        prompt += `- Arrange them thoughtfully to enhance the message without cluttering the thumbnail.\\n`;
    }
    prompt += `- Maintain strong center or rule-of-thirds composition for the main subject.\\n`;

    // Background Section - Updated with more diverse instructions
    prompt += `\nBackground & Color Grading:\n`;
    prompt += `- Use a fresh, visually striking color palette for the background. Do not repeat the same color scheme as previous thumbnails.\n`;
    prompt += `- Explore a range of vibrant gradients, bold color combinations, or cinematic lighting effects.\n`;
    prompt += `- Consider using blues, greens, purples, oranges, teals, or other high-contrast hues—not just reds or warm tones.\n`;
    prompt += `- If the main subject or theme suggests a mood, match the background color and lighting to enhance that emotion (e.g., cool blue for tech, energetic yellow for excitement, deep purple for mystery).\n`;
    prompt += `- Incorporate creative background elements:\n`;
    prompt += `  - Abstract shapes, soft gradients, or subtle patterns\n`;
    prompt += `  - Motion blur, light streaks, or bokeh effects\n`;
    prompt += `  - Thematic textures (e.g., digital grid for tech, paper for education, neon for gaming)\n`;
    prompt += `- Rotate and flip the color/gradient direction or lighting angle for each new thumbnail to avoid visual repetition.\n`;
    prompt += `- Ensure the background always provides strong contrast with the subject and overlay text for maximum readability and click appeal.\n`;

    // Output Format & Quality Rules Section
    prompt += `\nOutput Format & Quality Rules:\n`;
    prompt += `- Resolution MUST be exactly 1280x720 pixels (16:9 aspect ratio).\n`;
    prompt += `- Optimize for clear visibility on both mobile & desktop YouTube interfaces.\n`;
    prompt += `- Ensure high sharpness and saturation for a bold look.\n`;
    prompt += `- Always center the main subject or lead with clear visual hierarchy.\n`;
    if (textOverlay) {
        prompt += `- Bold text must be easily readable even on small screens.\n`;
    }
    if (includePerson) {
        prompt += `- Emotions and gestures should feel human and appropriate for the selected expression: ${selectedExpression}.\n`;
    }
    prompt += `- Use background contrast effectively to emphasize the subject ${includePerson && textOverlay ? 'and text' : includePerson ? '' : textOverlay ? 'and text' : ''}.\n`;
    prompt += `- Avoid visual clutter – prioritize clarity of the main subject ${includePerson ? '(especially the face)' : ''} ${textOverlay ? 'and the message' : ''}.\n`;

    // Full Width & No Pillarboxing Section
    prompt += `\nFull-Width & No Pillarboxing (Critical):\n`;
    prompt += `- The image background and all visual content must extend fully to the absolute left and right edges of the 1280x720 frame. There must be zero empty space, black bars, or dark gradients at the sides—no pillarboxing of any kind.\n`;
    prompt += `- The background should always fill the entire frame, even behind the safe zone. Use color, texture, or scene extension as needed.\n`;
    prompt += `- Only overlay text and essential visual elements (like faces or icons) must remain inside a 100px safe zone from all edges for mobile safety (especially left/right for text). But the background/art must always reach every edge.\n`;
    prompt += `- Do not add any borders, vignettes, or fade effects at the sides. The image must look full-bleed and cinematic, with no side gaps on any device or platform.\n`;
    prompt += `- If necessary, cleverly extend the background (e.g., blur-stretch, color smear, or mirrored edge) so it naturally bleeds to 0px from the left and right sides with zero visible seams, **while ensuring text stays 100px+ from the sides**.\n`;
    prompt += `- Foreground elements (icons, emojis, arrows, gestures) must be **fully visible** and kept at least 100px away from every edge so they are never cut off by the frame. No element should touch or cross the border.\n`;
    prompt += `- **Critically: Never position text too close to the left or right edge**, even if other rules are met. Maintain generous horizontal padding for text at all times.\n`;
    prompt += `- Avoid white space or dead zones on either side; ensure lighting, motion blur, or environmental assets extend the scene seamlessly across the horizontal space.\n`;

    // Fit Full Canvas logic
    if (fitFullCanvas) {
        prompt += `\nFit Full Canvas (ON):\n`;
        prompt += `Generate a YouTube thumbnail at exact 1280x720 pixels (16:9 aspect ratio). Fill the full width and height of the frame — no black bars or side padding.\n`;
        prompt += `Use background extensions, gradient fills, or cinematic framing to naturally span edge to edge. Preserve central focus and visual hierarchy. Do not allow important elements to be cropped or fall outside safe text zones (min 100px padding).\n`;
    } else {
        prompt += `\nFit Full Canvas (OFF):\n`;
        prompt += `You may center the visual content and allow minimal framing margin on left/right if necessary, but avoid shrinking the subject too much. Prioritize clarity and emotional focus.\n`;
    }

    // Call helper and append contextual instructions
    const contextualVisuals = extractContextualVisuals(userPrompt);
    prompt += contextualVisuals; // Append the contextual instructions

    return prompt;
}

