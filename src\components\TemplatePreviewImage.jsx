/**
 * TemplatePreviewImage.jsx
 * Universal component for displaying template preview images
 * Automatically falls back to solid color placeholders when images fail to load
 */

import React, { useState, useEffect } from 'react'

import { getTemplatePreviewPath, getCategoryFolder } from '../utils/templateImageUtils.js';

export const TemplatePreviewImage = ({ 
  template, 
  categoryId, 
  className = '', 
  onImageLoad = () => {}, 
  onImageError = () => {} 
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Generate image path - use backgroundImage if available, otherwise use generated path
  const previewPath = template.templateImagePlaceholder.backgroundImage || 
                      getTemplatePreviewPath(getCategoryFolder(categoryId), template.id);
  
  // Handle image load success
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
    setIsLoading(false);
    onImageLoad();
  };

  // Handle image load error
  const handleImageError = () => {
    setImageLoaded(false);
    setImageError(true);
    setIsLoading(false);
    onImageError();
  };

  // Reset state when template or category changes
  useEffect(() => {
    setImageLoaded(false);
    setImageError(false);
    setIsLoading(true);
  }, [template.id, categoryId]);

  // Render solid color fallback
  const renderFallback = () => {
    // Handle both old format (bgColor + text) and new format (backgroundImage)
    if (template.templateImagePlaceholder.backgroundImage) {
      // New format with backgroundImage - use a generic gradient fallback
      return React.createElement('div', {
        className: `template-item-thumbnail relative aspect-video bg-gradient-to-br from-gray-800 to-gray-600 flex items-center justify-center p-3 ${className}`,
        'data-fallback': 'true'
      },
        React.createElement('span', {
          className: 'template-item-preview-text text-lg font-bold text-white text-center drop-shadow-lg'
        }, template.name)
      );
    } else {
      // Old format with bgColor + text (legacy support)
      const colorClass = template.templateImagePlaceholder.bgColor;
      const gradientClass = `from-${colorClass.split('-')[1]}-800 to-${colorClass.split('-')[1]}-600`;
      
      return React.createElement('div', {
        className: `template-item-thumbnail relative aspect-video bg-gradient-to-br ${gradientClass} flex items-center justify-center p-3 ${className}`,
        'data-fallback': 'true'
      },
        React.createElement('span', {
          className: 'template-item-preview-text text-lg font-bold text-white text-center drop-shadow-lg'
        }, template.templateImagePlaceholder.text)
      );
    }
  };

  // Render image preview
  const renderImagePreview = () => {
    return React.createElement('div', {
      className: `template-item-thumbnail relative aspect-video overflow-hidden ${className}`,
      'data-image-preview': 'true'
    },
      // Preview Image
      React.createElement('img', {
        src: previewPath,
        alt: template.name,
        className: 'w-full h-full object-cover',
        onLoad: handleImageLoad,
        onError: handleImageError,
        loading: 'lazy'
      }),
      // Loading overlay
      isLoading && React.createElement('div', {
        className: 'absolute inset-0 bg-gray-800 animate-pulse flex items-center justify-center'
      },
        React.createElement('div', {
          className: 'w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin'
        })
      ),
      // Fallback overlay if image fails
      imageError && renderFallback()
    );
  };

  // Always try to load image first, fallback if it fails
  return imageError ? renderFallback() : renderImagePreview();
};

/**
 * Hook for batch loading template preview images
 * Useful for preloading images in template grids
 */
export const useTemplateImagePreloader = (templates, categoryId) => {
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [failedImages, setFailedImages] = useState(new Set());

  useEffect(() => {
    const categoryFolder = getCategoryFolder(categoryId);
    
    templates.forEach(template => {
      const previewPath = getTemplatePreviewPath(categoryFolder, template.id);
      const img = new Image();
      
      img.onload = () => {
        setLoadedImages(prev => new Set([...prev, template.id]));
      };
      
      img.onerror = () => {
        setFailedImages(prev => new Set([...prev, template.id]));
      };
      
      img.src = previewPath;
    });
  }, [templates, categoryId]);

  return { loadedImages, failedImages };
}; 