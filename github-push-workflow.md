---
title: github-push-workflow
id: github-push-workflow.md
feature: version-control
---

# 🚀 GitHub Push Workflow

## 📌 Overview
This guide ensures that your project—regardless of framework (Next.js, Python, Flutter, etc.)—is cleanly committed and synced to GitHub.

---

## 🛠️ Workflow Steps

### ✅ Step 1: Commit Changes

- Review staged changes.
- Ensure only **intentional files** (no temp/debug/junk) are included.
- Use clear and semantic commit messages (e.g., `feat: add thumbnail UI`, `fix: resolve layout bug`).

```bash
git add .
git commit -m "your message here"
```

### ✅ Step 2: Pull Before Push

- Always sync with remote before pushing to avoid conflicts.
- Resolve any merge conflicts locally.

```bash
git pull origin main
```

### ✅ Step 3: Push Changes

- Push your commits to the remote repository.
- Verify your changes appear on GitHub.

```bash
git push origin main
```

---

## 💡 Best Practices

### Commit Messages

- Use the format: `<type>: <description>`
- Types include:
  - `feat`: New feature
  - `fix`: Bug fix
  - `docs`: Documentation change
  - `style`: Formatting, no code change
  - `refactor`: Code change (no features/fixes)
  - `test`: Adding/updating tests
  - `chore`: Maintenance tasks

### Branching Strategy

- Create feature branches for substantial changes
  ```bash
  git checkout -b feature/new-thumbnail-generator
  ```
- Merge via pull request after code review
  ```bash
  git push origin feature/new-thumbnail-generator
  ```

### Handling Merge Conflicts

1. Pull the latest changes
2. Resolve conflicts in your editor
3. Commit the resolved changes
4. Push the merged result

```bash
git pull origin main
# Resolve conflicts in editor
git add .
git commit -m "fix: resolve merge conflicts"
git push origin main
```

---

## 🧪 Troubleshooting

### Common Issues

1. **Push rejected**: Pull first, resolve conflicts, then push again
2. **File too large**: Check `.gitignore` settings for large files
3. **Permission denied**: Verify SSH key setup or use HTTPS URL

### Quick Fixes

```bash
# Check repository status
git status

# View remote repository URL
git remote -v

# Undo last commit (keep changes staged)
git reset --soft HEAD~1

# Discard uncommitted changes
git checkout -- .
```

---

**Implementation Status:** Ready to use for all project types. 