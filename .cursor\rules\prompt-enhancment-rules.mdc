---
description: 
globs: 
alwaysApply: false
---
# ✨ Smart Prompt Enhancement – YouTube-Ready Titles

## 📌 Description
Upgrade the "Improve Prompt" feature to generate professional, YouTube-optimized video titles from user-submitted prompts.  
**Goal:** Ensure all suggestions are relevant, catchy, and directly reflect the user’s original video idea.

---

## 🛑 Current Issues
- Suggestions are often off-topic, generic, or not suitable as YouTube titles.
- Example of undesired output:  
  *A retro theatre marquee background with film reels and colourful light leaks in a nostalgic style.*

---

## ✅ Solution Requirements

- **Integrate OpenAI API** to rewrite prompts into engaging, YouTube-ready titles.
- Enhanced prompts must:
  - Be directly relevant to the user’s original idea.
  - Sound professional and compelling as a YouTube video title.
  - Never be generic, off-topic, or just a scene/background description.

---

## 🧠 Reference Examples

| **User Input (Raw Prompt)**             | **Suggested Smart Prompt**                          |
|----------------------------------------|-----------------------------------------------------|
| react to my cringe video               | Revisiting My Old Videos – What Was I Thinking?     |
| trying weird snacks                    | Trying Unusual Snacks from Around the World         |
| my daily routine                       | My Real Daily Routine You Didn’t Know About         |
| reacting to TikTok trends              | The Latest TikTok Trends – Are They Worth the Hype? |
| I built a website in 1 hour            | One-Hour Website Challenge – Can I Do It?           |
| gaming with my friends                 | Fun Multiplayer Game Night with Friends             |
| unboxing my new phone                  | Unboxing the Latest Smartphone – First Impressions  |
| my most embarrassing moment            | The Moment I’ll Never Forget (And Wish I Could)     |
| learning to cook for the first time    | My First Attempt at Cooking – Disaster or Success?  |
| reacting to viral memes                | Viral Memes That Broke the Internet – My Reaction   |
| cleaning my messy room                 | Transforming My Messy Room – Before & After         |
| AI is taking over everything           | Is AI Changing the World Faster Than We Think?      |
| top 5 AI tools                         | The 5 Best AI Tools I’m Using Right Now             |

> Use these as a reference for the desired output style.

---

## 🎯 Acceptance Criteria

- Output is always a **YouTube-ready video title**.
- Suggestion **faithfully reflects the user’s original intent**.
- No generic, irrelevant, or background/scene-based outputs.
- Feature is powered by the **OpenAI API**.

---

**Summary:**  
Update the "Improve Prompt" feature to use the OpenAI API for rewriting user prompts into professional, relevant, and engaging YouTube video titles, following the provided examples and criteria.