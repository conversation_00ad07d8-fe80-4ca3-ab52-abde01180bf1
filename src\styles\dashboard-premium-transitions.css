/* Dashboard Premium Transitions and Animations */

/* ===== Premium Animation Variables ===== */
:root {
    --transition-quick: 150ms;
    --transition-normal: 250ms;
    --transition-slow: 350ms;
    --easing-smooth: cubic-bezier(0.4, 0, 0.2, 1);
    --easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --easing-premium: cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* ===== Tab Switching Animations ===== */
.dashboard-nav button {
    position: relative;
    transition: all var(--transition-normal) var(--easing-smooth);
    transform-style: preserve-3d;
}

/* Active tab indicator animation */
.dashboard-nav button::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #a78bfa, #818cf8);
    transform: scaleX(0);
    transform-origin: center;
    transition: transform var(--transition-normal) var(--easing-smooth);
}

.dashboard-nav button.border-purple-400::after {
    transform: scaleX(1);
}

/* Tab hover effect - removed translateY */
.dashboard-nav button:hover {
    /* Removed translateY hover effect */
}

.dashboard-nav button:active {
    /* Removed translateY active effect */
}

/* Tab content transition wrapper */
.tab-content-wrapper {
    position: relative;
    overflow: hidden;
}

/* Tab content fade and slide animation */
.tab-content {
    animation: tabContentEnter var(--transition-slow) var(--easing-premium) forwards;
}

@keyframes tabContentEnter {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Alternative slide from bottom animation */
.tab-content-slide-up {
    animation: tabContentSlideUp var(--transition-slow) var(--easing-premium) forwards;
}

@keyframes tabContentSlideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== History Card Hover Animations ===== */
.history-item {
    transition: all var(--transition-normal) var(--easing-smooth);
    transform: translateZ(0); /* Enable GPU acceleration */
}

.history-item:hover {
    /* Removed scale effect - only image zooms now */
    border-color: #a78bfa;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3),
                0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Smooth thumbnail zoom on hover - zooms content without changing container size */
.history-item {
    overflow: hidden; /* Ensure zoomed image doesn't exceed container bounds */
}

.history-item-thumbnail {
    transition: transform var(--transition-slow) var(--easing-smooth);
    transform-origin: center center; /* Zoom from center */
}

.history-item:hover .history-item-thumbnail {
    transform: scale(1.1); /* Zoom the image content only, container stays same size */
}

/* Bottom actions smooth reveal */
.bottom-actions {
    transition: all var(--transition-normal) var(--easing-smooth);
    transform: translateY(10px);
}

.history-item:hover .bottom-actions {
    transform: translateY(0);
}

/* ===== Button Premium Effects ===== */
.view-details-btn,
.show-more-btn,
.dropdown-item {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-quick) var(--easing-smooth);
}

/* Ripple effect on click */
.view-details-btn::before,
.show-more-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width var(--transition-normal), height var(--transition-normal);
}

.view-details-btn:active::before,
.show-more-btn:active::before {
    width: 100px;
    height: 100px;
}

/* ===== Dropdown Menu Animations ===== */
.dropdown-menu {
    animation: dropdownEnter var(--transition-quick) var(--easing-bounce) forwards;
    transform-origin: top right;
}

/* Different animation for dropdowns that appear below */
.history-item:nth-child(-n+3) .dropdown-menu {
    animation: dropdownEnterBelow var(--transition-quick) var(--easing-bounce) forwards;
    transform-origin: top right;
}

@keyframes dropdownEnter {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes dropdownEnterBelow {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Dropdown item hover stagger effect */
.dropdown-item {
    transition: all var(--transition-quick) var(--easing-smooth);
}

.dropdown-item:hover {
    /* Removed translateX shifting effect */
}

/* ===== Edit Mode Transitions ===== */
.edit-transition {
    transition: all var(--transition-normal) var(--easing-smooth);
}

/* Input field focus animation */
input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
    outline: none;
    border-color: #a78bfa;
    box-shadow: 0 0 0 3px rgba(167, 139, 250, 0.1);
    transform: scale(1.01);
    transition: all var(--transition-quick) var(--easing-smooth);
}

/* ===== Modal Animations ===== */
.modal-backdrop {
    animation: fadeIn var(--transition-normal) var(--easing-smooth) forwards;
}

.modal-content {
    animation: modalEnter var(--transition-slow) var(--easing-premium) forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalEnter {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Modal exit animations */
.modal-backdrop.exiting {
    animation: fadeOut var(--transition-normal) var(--easing-smooth) forwards;
}

.modal-content.exiting {
    animation: modalExit var(--transition-normal) var(--easing-smooth) forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes modalExit {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.95) translateY(10px);
    }
}

/* ===== Stats Cards Animations ===== */
.stat-card {
    transition: all var(--transition-normal) var(--easing-smooth);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    /* Removed translateY hover effect */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2);
    border-color: #a78bfa;
}

/* Progress bar animation */
.stat-card .bg-purple-500 {
    position: relative;
    overflow: hidden;
}

.stat-card .bg-purple-500::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* ===== Activity List Animations ===== */
.activity-item {
    transition: all var(--transition-normal) var(--easing-smooth);
    animation: activityItemEnter var(--transition-slow) var(--easing-premium) forwards;
    animation-delay: calc(var(--item-index, 0) * 50ms);
    opacity: 0;
}

@keyframes activityItemEnter {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.activity-item:hover {
    transform: translateX(4px);
    background: #1f2937;
    border-color: #a78bfa;
}

/* ===== Smooth Page Transitions ===== */
.dashboard-outer-wrapper {
    animation: pageEnter var(--transition-slow) var(--easing-premium) forwards;
}

@keyframes pageEnter {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== Loading States ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== Premium Glow Effects ===== */
.glow-on-hover {
    position: relative;
    isolation: isolate;
}

.glow-on-hover::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #a78bfa, #818cf8, #a78bfa);
    border-radius: inherit;
    opacity: 0;
    filter: blur(10px);
    transition: opacity var(--transition-normal) var(--easing-smooth);
    z-index: -1;
}

.glow-on-hover:hover::before {
    opacity: 0.5;
}

/* ===== Responsive Adjustments ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Mobile-specific transitions */
@media (max-width: 768px) {
    /* Reduce animation distances on mobile */
    @keyframes tabContentEnter {
        from {
            opacity: 0;
            transform: translateX(10px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    /* Disable hover lift on touch devices */
    @media (hover: none) {
        .history-item:hover {
            transform: none;
        }
        
        .stat-card:hover {
            transform: none;
        }
    }
} 