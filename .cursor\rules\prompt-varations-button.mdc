---
description: 
globs: 
alwaysApply: true
---
@prompt-variations-button
ruleId: prompt-variations-suggestions
description: >
  Adds a “Prompt Variations” button next to the “improve” button in the prompt input area. When clicked, it generates 1–3 smart, diverse prompt suggestions based on the user’s current input and displays them in a modal or dropdown for easy selection.

appliesTo:
  - /src/components/PromptInput.jsx
  - /src/styles/prompt.css
  - /src/utils/promptVariations.js # New utility for generating variations

ruleType: always

implementationNotes: |
  - Place the new button beside the “improve” button, matching style and accessibility.
  - On click, call a function (local or API) to generate 1–3 alternative prompts.
  - Display suggestions in a modal or dropdown below the input, styled with Hero UI and dark mode.
  - Each suggestion is a clickable card/chip; clicking replaces the prompt input.
  - Limit to 3 suggestions; show a message if none are available.
  - Ensure full keyboard and screen reader accessibility.

sampleUI: |
  [ Variations ] [ improve ]
  ┌─────────────────────────────┐
  | Try a different prompt:     |
  | ┌─────────────┐             |
  | | Variation 1 | [Use]       |
  | └─────────────┘             |
  | ┌─────────────┐             |
  | | Variation 2 | [Use]       |
  | └─────────────┘             |
  | ┌─────────────┐             |
  | | Variation 3 | [Use]       |
  | └─────────────┘             |
  └─────────────────────────────┘

acceptanceCriteria:
  - <PERSON><PERSON> appears next to “improve” and matches style.
  - Clicking shows up to 3 prompt variations in a modal/dropdown.
  - User can insert a variation with one click/tap.
  - Fully accessible and mobile-friendly.
  - Follows Hero UI and dark mode styling.
