import React from 'react';
import { getDemoStatusMessage } from '../utils/demoLimitManager.js';

/**
 * Demo Limit Banner Component
 * Shows remaining generations and limit status for demo users
 */
export const DemoLimitBanner = ({ 
    demoUsageStats, 
    isLimitReached, 
    onUpgrade = null 
}) => {
    const { remainingGenerations, totalGenerations, limit, progressPercentage } = demoUsageStats;

    if (isLimitReached) {
        return (
            React.createElement('div', {
                className: 'bg-gradient-to-r from-red-900/30 to-purple-900/30 border border-red-700/50 rounded-xl p-4 mb-6 backdrop-blur-sm'
            },
                React.createElement('div', {
                    className: 'flex items-center justify-between'
                },
                    React.createElement('div', {
                        className: 'flex items-center gap-3'
                    },
                        React.createElement('div', {
                            className: 'flex-shrink-0'
                        },
                            React.createElement('span', {
                                className: 'iconify text-2xl text-red-400',
                                'data-icon': 'solar:fire-bold-duotone'
                            })
                        ),
                        React.createElement('div', null,
                            React.createElement('h3', {
                                className: 'text-lg font-semibold text-white mb-1'
                            }, '🎯 Demo Complete!'),
                            React.createElement('p', {
                                className: 'text-sm text-gray-300'
                            }, `You've generated all ${limit} demo thumbnails. Amazing work!`)
                        )
                    ),
                    onUpgrade && React.createElement('button', {
                        onClick: onUpgrade,
                        className: 'px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900'
                    }, 'Upgrade Now')
                ),
                React.createElement('div', {
                    className: 'mt-3'
                },
                    React.createElement('div', {
                        className: 'w-full bg-gray-700 rounded-full h-2'
                    },
                        React.createElement('div', {
                            className: 'bg-gradient-to-r from-red-500 to-purple-500 h-2 rounded-full transition-all duration-500',
                            style: { width: '100%' }
                        })
                    )
                )
            )
        );
    }

    // Show warning when getting close to limit
    const isWarningZone = remainingGenerations <= 5;
    const isCriticalZone = remainingGenerations <= 2;

    const getBorderColor = () => {
        if (isCriticalZone) return 'border-red-700/50';
        if (isWarningZone) return 'border-yellow-700/50';
        return 'border-purple-700/50';
    };

    const getGradientColor = () => {
        if (isCriticalZone) return 'from-red-900/20 to-purple-900/20';
        if (isWarningZone) return 'from-yellow-900/20 to-purple-900/20';
        return 'from-purple-900/20 to-blue-900/20';
    };

    const getProgressColor = () => {
        if (isCriticalZone) return 'from-red-500 to-purple-500';
        if (isWarningZone) return 'from-yellow-500 to-purple-500';
        return 'from-purple-500 to-blue-500';
    };

    const getIconColor = () => {
        if (isCriticalZone) return 'text-red-400';
        if (isWarningZone) return 'text-yellow-400';
        return 'text-purple-400';
    };

    const getIcon = () => {
        if (isCriticalZone) return 'solar:danger-triangle-bold-duotone';
        if (isWarningZone) return 'solar:thunder-bold-duotone';
        return 'solar:star-bold-duotone';
    };

    return (
        React.createElement('div', {
            className: `bg-gradient-to-r ${getGradientColor()} border ${getBorderColor()} rounded-xl p-4 mb-6 backdrop-blur-sm`
        },
            React.createElement('div', {
                className: 'flex items-center justify-between mb-3'
            },
                React.createElement('div', {
                    className: 'flex items-center gap-3'
                },
                    React.createElement('div', {
                        className: 'flex-shrink-0'
                    },
                        React.createElement('span', {
                            className: `iconify text-xl ${getIconColor()}`,
                            'data-icon': getIcon()
                        })
                    ),
                    React.createElement('div', null,
                        React.createElement('h3', {
                            className: 'text-sm font-semibold text-white mb-1'
                        }, getDemoStatusMessage()),
                        React.createElement('p', {
                            className: 'text-xs text-gray-400'
                        }, `${totalGenerations}/${limit} generations used`)
                    )
                ),
                onUpgrade && isWarningZone && React.createElement('button', {
                    onClick: onUpgrade,
                    className: 'px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-xs font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1 focus:ring-offset-gray-900'
                }, 'Upgrade')
            ),
            React.createElement('div', {
                className: 'space-y-2'
            },
                React.createElement('div', {
                    className: 'flex justify-between text-xs text-gray-400'
                },
                    React.createElement('span', null, 'Demo Progress'),
                    React.createElement('span', null, `${progressPercentage}%`)
                ),
                React.createElement('div', {
                    className: 'w-full bg-gray-700 rounded-full h-2'
                },
                    React.createElement('div', {
                        className: `bg-gradient-to-r ${getProgressColor()} h-2 rounded-full transition-all duration-500`,
                        style: { width: `${progressPercentage}%` }
                    })
                )
            )
        )
    );
};

export default DemoLimitBanner; 