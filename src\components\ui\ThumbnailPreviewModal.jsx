import React, { useEffect, useRef, useState } from 'react'
import { downloadThumbnailAt1280x720 } from '../../utils/imageUtils.js'

export const ThumbnailPreviewModal = ({ 
    isOpen, 
    onClose, 
    thumbnail, 
    title = 'Generated Thumbnail',
    itemId,
    fullImageUrl 
}) => {
    const modalRef = useRef(null);
    const previousFocusRef = useRef(null);
    const [isClosing, setIsClosing] = useState(false);

    useEffect(() => {
        if (isOpen && !isClosing) {
            // Store the currently focused element
            previousFocusRef.current = document.activeElement;
            
            // Focus the modal
            if (modalRef.current) {
                modalRef.current.focus();
            }

            // Handle ESC key press
            const handleEsc = (e) => {
                if (e.key === 'Escape') {
                    handleClose();
                }
            };

            // Add event listener
            document.addEventListener('keydown', handleEsc);

            // Prevent body scroll
            document.body.style.overflow = 'hidden';

            return () => {
                document.removeEventListener('keydown', handleEsc);
                document.body.style.overflow = '';
                
                // Restore focus to previous element
                if (previousFocusRef.current) {
                    previousFocusRef.current.focus();
                }
            };
        }
    }, [isOpen, isClosing]);

    // Reset closing state when modal opens
    useEffect(() => {
        if (isOpen) {
            setIsClosing(false);
        }
    }, [isOpen]);

    if (!isOpen) return null;

    const handleClose = () => {
        setIsClosing(true);
        // Wait for animation to complete before calling onClose
        setTimeout(() => {
            onClose();
        }, 250); // Match animation duration
    };

    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            handleClose();
        }
    };

    const handleDownload = async () => {
        if (!thumbnail) return;
        // Use full-quality image from Supabase if available, otherwise fallback to thumbnail
        const downloadUrl = fullImageUrl || thumbnail;
        await downloadThumbnailAt1280x720(downloadUrl, itemId, downloadUrl);
    };

    return React.createElement('div', {
        className: `thumbnail-preview-modal-backdrop ${isClosing ? 'closing' : ''}`,
        onClick: handleBackdropClick,
        'aria-modal': 'true',
        role: 'dialog',
        'aria-label': 'Thumbnail preview'
    },
        React.createElement('div', {
            ref: modalRef,
            className: `thumbnail-preview-modal-content ${isClosing ? 'closing' : ''}`,
            tabIndex: -1
        },
            // macOS-style title bar (simplified)
            React.createElement('div', { className: 'modal-title-bar' },
                // Traffic light buttons container (decorative only)
                React.createElement('div', { className: 'traffic-lights' },
                    // Red button (decorative)
                    React.createElement('div', {
                        className: 'traffic-light red'
                    }),
                    // Yellow button (decorative)
                    React.createElement('div', {
                        className: 'traffic-light yellow'
                    }),
                    // Green button (decorative)
                    React.createElement('div', {
                        className: 'traffic-light green'
                    })
                ),
                // Close button with Solar icon
                React.createElement('button', {
                    className: 'w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                    onClick: handleClose,
                    'aria-label': 'Close preview'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        viewBox: '0 0 24 24',
                        fill: 'none',
                        stroke: 'currentColor',
                        strokeWidth: '2',
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        className: 'w-5 h-5'
                    },
                        React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                        React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                    )
                )
            ),

            // Image container
            React.createElement('div', { className: 'modal-image-container' },
                React.createElement('img', {
                    src: fullImageUrl || thumbnail, // Use original quality for preview, fallback to thumbnail
                    alt: title,
                    className: 'modal-thumbnail-image'
                }),

                // Download button overlay
                React.createElement('button', {
                    className: 'modal-download-btn',
                    onClick: handleDownload,
                    'aria-label': 'Download thumbnail'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:download-minimalistic-linear',
                        style: { fontSize: '22px' } // Increased icon size by ~10%
                    })
                )
            )
        )
    );
}; 