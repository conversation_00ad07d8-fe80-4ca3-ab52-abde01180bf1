import React, { useState, useEffect, useRef } from 'react'
import { validateImageUrlComprehensive } from '../../utils/imageUrlValidator.js'

// Safe fallback functions
const getFaceQualityDescriptionFallback = (quality) => {
  const descriptions = {
    'excellent': 'Excellent quality - Perfect for face swapping',
    'good': 'Good quality - Should work well',
    'fair': 'Fair quality - May work with some limitations',
    'poor': 'Poor quality - May not work reliably',
    'very_poor': 'Very poor quality - Unlikely to work',
    'unknown': 'Quality unknown'
  };
  return descriptions[quality] || quality || 'Unknown';
};

// Dynamic validator function with fallback
const validateForFaceSwapSafe = async (url, options = {}) => {
  try {
    const advancedValidator = await import('../../utils/advancedFaceUploadValidator.js');
    return await advancedValidator.validateForFaceSwap(url, options);
  } catch (error) {
    console.warn('Advanced validator not available, using basic validation:', error);
    const result = await validateImageUrlComprehensive(url);
    return { 
      ...result, 
      faceQuality: 'unknown', 
      isFaceCompatible: true 
    };
  }
};

export const FaceUploadSection = ({
  imageSourceType,
  customFaceImageUrl,
  handleImageSourceTypeChange,
  setCustomFaceImageUrl,
  handleFileUpload,
  setErrorMsg,
  createTooltipIconProp, // Prop for the tooltip utility
  openImageRequirementsModal, // Function from App level to open modal
  onShowSuccessToast // Function to show success notification
}) => {
  const fileInputRef = useRef(null);
  const uploadZoneRef = useRef(null);
  // Initialize tempUrl based on whether customFaceImageUrl is a URL (not a data URI)
  const initialTempUrl = (imageSourceType === 'url' && customFaceImageUrl && !customFaceImageUrl.startsWith('data:image')) 
    ? customFaceImageUrl 
    : '';
  const [tempUrl, setTempUrl] = useState(initialTempUrl);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isValidatingUrl, setIsValidatingUrl] = useState(false);
  const [faceQualityInfo, setFaceQualityInfo] = useState(null);

  // Prevent default browser drag behavior over the component
  useEffect(() => {
    const preventDefaults = (e) => {
      if (imageSourceType === 'upload' && uploadZoneRef.current?.contains(e.target)) {
        e.preventDefault();
        e.stopPropagation();
      }
    };
    
    // Add listeners to prevent default drag behavior
    const events = ['dragenter', 'dragover', 'dragleave', 'drop'];
    events.forEach(eventName => {
      document.addEventListener(eventName, preventDefaults, false);
    });
    
    return () => {
      events.forEach(eventName => {
        document.removeEventListener(eventName, preventDefaults, false);
      });
    };
  }, [imageSourceType]);

  useEffect(() => {
    // Update tempUrl if the source type is URL and the actual customFaceImageUrl changes
    // or if customFaceImageUrl becomes a data URI (meaning an upload just happened)
    if (imageSourceType === 'url') {
      if (customFaceImageUrl && !customFaceImageUrl.startsWith('data:image')) {
        setTempUrl(customFaceImageUrl);
      } else {
        // If it's a data URI (from upload) or null/empty, clear tempUrl for URL input
        setTempUrl(''); 
      }
    }
  }, [imageSourceType, customFaceImageUrl]);

  const handleSetFaceImageFromUrl = async () => {
    if (tempUrl.trim() && imageSourceType === 'url') {
      setIsValidatingUrl(true);
      setErrorMsg('');
      setFaceQualityInfo(null);
      
      try {
        // Use advanced face swap validation with improved fallback
        const validation = await validateForFaceSwapSafe(tempUrl.trim(), {
          requireFaceDetection: false, // Make this less strict for better UX
          minQualityScore: 0.3, // Lower threshold to be more permissive
          skipAdvancedAnalysis: false
        });

        console.log('Validation result:', validation);
        
        if (validation.isValid && validation.isAccessible) {
          // Success: Use the transformed URL
          setCustomFaceImageUrl(validation.transformedUrl || validation.url || tempUrl.trim());
          setErrorMsg('');
          setFaceQualityInfo(validation);
          
          // Show success notification with quality info
          if (onShowSuccessToast) {
            const serviceInfo = validation.service === 'imgur' ? ' (Imgur)' : 
                              validation.service === 'googleDrive' ? ' (Google Drive)' : 
                              validation.service === 'dropbox' ? ' (Dropbox)' : 
                              validation.service === 'discord' ? ' (Discord)' : 
                              validation.service === 'github' ? ' (GitHub)' : '';
            
            // Handle different quality info structures
            let qualityInfo = '';
            if (validation.faceQuality && validation.faceQuality !== 'unknown') {
              qualityInfo = ` - ${getFaceQualityDescriptionFallback(validation.faceQuality)}`;
            } else if (validation.faceAnalysis?.analysis?.message) {
              qualityInfo = ` - ${validation.faceAnalysis.analysis.message}`;
            }
            
            onShowSuccessToast(`Image loaded successfully${serviceInfo}${qualityInfo}!`, 'success');
          }
        } else {
          // Validation failed - but try to provide helpful feedback
          let errorMessage = validation.error || 'Image validation failed';
          
          // Check if it's a face detection issue vs accessibility issue
          if (validation.isAccessible === false) {
            errorMessage = validation.error || 'Unable to access image from URL. Please check the URL and try again.';
          } else if (validation.faceAnalysis?.analysis?.message) {
            errorMessage = validation.faceAnalysis.analysis.message;
            
            // If it's just a face detection warning but image is accessible, allow it
            if (validation.isAccessible && validation.faceAnalysis.analysis.method === 'heuristic_fallback') {
              setCustomFaceImageUrl(validation.transformedUrl || validation.url || tempUrl.trim());
              setErrorMsg(''); // Clear error since we're allowing it
              setFaceQualityInfo(validation);
              
              if (onShowSuccessToast) {
                onShowSuccessToast(`Image loaded with warning: ${validation.faceAnalysis.analysis.message}`, 'warning');
              }
              setIsValidatingUrl(false);
              return;
            }
          }
          
          setErrorMsg(errorMessage);
          setFaceQualityInfo(validation);
        }
      } catch (error) {
        console.error('Face upload validation error:', error);
        
        // Emergency fallback: try basic URL validation
        try {
          const basicValidation = await validateImageUrlComprehensive(tempUrl.trim());
          if (basicValidation.isValid && basicValidation.isAccessible) {
            setCustomFaceImageUrl(basicValidation.transformedUrl || basicValidation.url || tempUrl.trim());
            setErrorMsg('');
            setFaceQualityInfo(null);
            if (onShowSuccessToast) {
              onShowSuccessToast('Image URL set (advanced validation unavailable)', 'warning');
            }
          } else {
            setErrorMsg(basicValidation.error || 'Unable to validate image URL');
          }
        } catch (fallbackError) {
          console.error('Even basic validation failed:', fallbackError);
          setErrorMsg('Unable to validate image URL. Please check the URL and try again.');
        }
      } finally {
        setIsValidatingUrl(false);
      }
    } else if (imageSourceType === 'url' && !tempUrl.trim()) {
      // If URL input is cleared, also clear the customFaceImageUrl if it was a URL
      if (customFaceImageUrl && !customFaceImageUrl.startsWith('data:image')) {
          setCustomFaceImageUrl('');
      }
       setErrorMsg('');
       setFaceQualityInfo(null);
    }
  };

  const handleRemoveFaceImage = () => {
    setCustomFaceImageUrl('');
    setTempUrl('');
    setFaceQualityInfo(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = null; // Clear the file input
    }
    setErrorMsg('');
    
    // Show success notification for face image deletion
    if (onShowSuccessToast) {
      onShowSuccessToast('Face image removed successfully!', 'success');
    }
  };

  const handleUrlInputChange = (e) => {
    setTempUrl(e.target.value);
    // Clear any existing errors when user starts typing
    if (e.target.value.trim() === '') {
      setErrorMsg('');
      setFaceQualityInfo(null);
    }
  };
  
  const onUrlInputBlur = () => {
    // Apply the URL when the input field loses focus, only if it's not empty.
    // If empty on blur, it effectively clears the URL if the user intended to remove it.
    if (tempUrl.trim()) {
        handleSetFaceImageFromUrl();
    } else if (imageSourceType === 'url') { 
        // If user clears the URL field and blurs, remove the image if it was a URL
         if (customFaceImageUrl && !customFaceImageUrl.startsWith('data:image')) {
            setCustomFaceImageUrl('');
        }
        setFaceQualityInfo(null);
    }
  };

  // Enhanced drag and drop handlers
  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Check if the dragged item is a file
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      // Check if it's an image file
      const item = e.dataTransfer.items[0];
      if (item.kind === 'file' && item.type.startsWith('image/')) {
        setIsDragOver(true);
        console.log('Valid image file being dragged');
      }
    } else {
      // Fallback for browsers that don't support dataTransfer.items
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set dragOver to false if we're leaving the upload zone entirely
    if (!uploadZoneRef.current?.contains(e.relatedTarget)) {
      setIsDragOver(false);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    // This is crucial - we need to set the dropEffect to indicate this is a valid drop target
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Drop event triggered');
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    console.log('Dropped files:', files);
    if (files && files.length > 0) {
      console.log('Processing dropped file:', files[0].name);
      // Create a synthetic event that matches the file input's onChange event structure
      const syntheticEvent = {
        target: {
          files: files,
          value: files[0]?.name || ''
        }
      };
      handleFileUpload(syntheticEvent);
    }
  };

  const tooltipText = "Replace AI face: Upload your headshot or paste an image URL (JPEG/PNG/WebP, supports Imgur, Google Drive, Dropbox, and more). For best results, use clear, front-facing photos with good lighting.";

  return React.createElement('div', { 
    className: `face-upload-section custom-face-image-block py-3 px-3 rounded-lg border border-gray-700 bg-gray-800/50 mb-5 overflow-hidden ${customFaceImageUrl ? 'has-preview' : ''}`, 
    id: 'custom-face-image-block' 
  },
    React.createElement('div', { className: 'flex items-center justify-between mb-3' },
      React.createElement('div', { className: 'flex items-center gap-2' },
        React.createElement('label', { className: 'text-sm font-medium text-gray-300', id: 'custom-face-image-label' }, 'Custom Face Upload'),
        React.createElement('span', { className: 'beta-label text-xs px-1.5 py-0.5 rounded bg-gray-700 text-gray-400 font-medium', id: 'custom-face-image-beta' }, 'beta')
      ),
      createTooltipIconProp(tooltipText, 'tooltip-face-source')
    ),
    

    
    // Custom Tab Buttons (similar to Editor/Templates)
    React.createElement('div', { 
      className: 'face-source-tab-navigation-container flex justify-center mt-1 mb-3', 
      id: 'face-source-tab-navigation'
    },
      React.createElement('div', { 
        className: `face-source-tab-group ${imageSourceType === 'url' ? 'url-active' : ''}`,
        id: 'upload-url-tab-group',
        role: 'tablist',
        'aria-label': 'Face source selection tabs'
      },
        // Upload Tab Button (Disabled)
        React.createElement('button', {
          id: 'upload-tab-button',
          className: `face-source-tab-button inactive-tab opacity-50 cursor-not-allowed`,
          onClick: (e) => {
            e.preventDefault();
            // Show tooltip or message that upload is under development
          },
          disabled: true,
          role: 'tab',
          'aria-selected': false,
          'aria-controls': 'upload-tab-content',
          'aria-disabled': true,
          title: 'Local file upload is currently under development'
        }, 
          React.createElement('span', {
            className: 'iconify upload-icon',
            'data-icon': 'solar:upload-line-duotone'
          }),
          React.createElement('span', null, 'Upload'),
          React.createElement('span', {
            className: 'ml-1 text-xs text-yellow-400',
            'aria-label': 'Under development'
          }, '(Dev)')
        ),
        // URL Tab Button
        React.createElement('button', {
          id: 'url-tab-button',
          className: `face-source-tab-button ${imageSourceType === 'url' ? 'active-tab' : 'inactive-tab'} focus:z-10`,
          onClick: () => handleImageSourceTypeChange('url'),
          role: 'tab',
          'aria-selected': imageSourceType === 'url',
          'aria-controls': 'url-tab-content'
        }, 
          React.createElement('span', {
            className: 'iconify url-icon',
            'data-icon': 'solar:link-circle-line-duotone'
          }),
          React.createElement('span', null, 'URL')
        )
      )
    ),
    
        // Tab Content Container
    React.createElement('div', {
      className: 'tab-content-wrapper mt-2'
    },
      false && React.createElement('div', { 
        className: 'upload-tab-content-section flex flex-col gap-4 items-center justify-center py-8', 
        id: 'upload-tab-content',
        role: 'tabpanel',
        'aria-labelledby': 'upload-tab-button'
      },
        // Under Development Message
        React.createElement('div', {
          className: 'text-center space-y-3'
        },
          React.createElement('div', {
            className: 'w-16 h-16 mx-auto rounded-full bg-yellow-500/10 border border-yellow-500/30 flex items-center justify-center'
          },
            React.createElement('span', {
              className: 'iconify text-yellow-400',
              'data-icon': 'solar:settings-minimalistic-bold-duotone',
              style: { fontSize: '24px' }
            })
          ),
          React.createElement('div', {
            className: 'space-y-2'
          },
            React.createElement('h3', {
              className: 'text-sm font-medium text-yellow-400'
            }, 'Local Upload Under Development'),
            React.createElement('p', {
              className: 'text-xs text-gray-400 max-w-xs mx-auto leading-relaxed'
            }, 'We\'re working on local file upload functionality. For now, please use the URL option to add custom face images.'),
            React.createElement('div', {
              className: 'flex items-center justify-center gap-1 text-xs text-gray-500 mt-3'
            },
              React.createElement('span', {
                className: 'iconify',
                'data-icon': 'solar:clock-circle-linear',
                style: { fontSize: '12px' }
              }),
              React.createElement('span', null, 'Coming soon')
            )
          )
        )
      ),
    
    imageSourceType === 'url' && React.createElement('div', { 
      className: 'url-tab-content-section flex flex-col gap-2', 
      id: 'url-tab-content',
      role: 'tabpanel',
      'aria-labelledby': 'url-tab-button'
    },
      // URL Input Area - Enhanced with validation feedback
      React.createElement('div', {
        className: 'url-input-wrapper rounded-lg h-14 flex items-center px-3 gap-1.5',
        style: {
          backgroundColor: 'rgb(28, 35, 48)',
          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
        }
    },
      React.createElement('input', {
          type: 'text', 
          id: 'customFaceUrlInput', 
          placeholder: 'Paste image URL here (supports Imgur, Google Drive, Dropbox, etc.)...',
          value: tempUrl, 
          onChange: handleUrlInputChange, 
          onBlur: onUrlInputBlur,
          onKeyDown: (e) => {
            if (e.key === 'Enter') {
              handleSetFaceImageFromUrl();
            }
          },
          className: 'flex-grow min-w-0 bg-transparent border-0 focus:ring-0 focus:outline-none text-gray-100 text-sm placeholder-gray-500',
        'aria-label': 'Custom face image URL input'
      }),
      React.createElement('button', {
          type: 'button', 
          onClick: handleSetFaceImageFromUrl,
          disabled: isValidatingUrl,
          className: `px-2 py-1.5 text-xs rounded-lg transition-all flex-shrink-0 ${isValidatingUrl ? 'opacity-50 cursor-not-allowed' : ''} ${
            tempUrl.trim() 
              ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600 ring-2 ring-purple-600/30 animate-pulse-once' 
              : ''
          }`,
          style: tempUrl.trim() && !isValidatingUrl ? {
            backgroundColor: 'rgb(147, 51, 234)',
            color: 'white',
            border: '1px solid rgb(147, 51, 234)',
            boxShadow: '0 0 12px rgba(147, 51, 234, 0.4)',
            animation: 'buttonAttention 0.5s ease-out'
          } : {
            backgroundColor: 'rgb(57, 65, 80)',
            color: 'rgb(113, 113, 122)',
            border: '1px solid rgb(57, 65, 80)',
            boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)'
          },
          id: 'custom-face-image-url-set-btn', 
          'aria-label': 'Set custom face image URL',
          onMouseEnter: (e) => {
            if (tempUrl.trim() && !isValidatingUrl) {
              e.target.style.backgroundColor = 'rgb(126, 34, 206)';
            } else {
              e.target.style.backgroundColor = 'rgb(63, 73, 90)';
            }
          },
          onMouseLeave: (e) => {
            if (tempUrl.trim() && !isValidatingUrl) {
              e.target.style.backgroundColor = 'rgb(147, 51, 234)';
            } else {
              e.target.style.backgroundColor = 'rgb(57, 65, 80)';
            }
          }
      }, 
        isValidatingUrl ? React.createElement('span', {
          className: 'iconify animate-spin',
          'data-icon': 'solar:spinner-linear',
          style: { fontSize: '12px' }
        }) : 'Set URL'
      )
      ),
      
      // Enhanced URL hint with supported services
      React.createElement('div', {
        className: 'flex items-center gap-2'
      },
        React.createElement('span', {
          className: 'iconify text-green-500',
          'data-icon': 'solar:check-circle-linear',
          style: { fontSize: '14px' }
        }),
        React.createElement('p', {
          className: 'text-xs text-gray-500'
        }, "Supports: Imgur, Google Drive, Dropbox, Discord, GitHub, and direct image URLs (.jpg, .png, .webp).")
      ),
      
      // Face Quality Indicator (if available) - Hidden when URL is successfully set
      false && faceQualityInfo && React.createElement('div', {
        className: 'face-quality-indicator mt-2 p-2 rounded-md bg-gray-700/30 border border-gray-600/50'
      },
        React.createElement('div', {
          className: 'flex items-center gap-2 mb-1'
        },
          React.createElement('span', {
            className: 'iconify text-blue-400',
            'data-icon': 'solar:shield-check-linear',
            style: { fontSize: '14px' }
          }),
          React.createElement('span', {
            className: 'text-xs font-medium text-gray-300'
          }, 'Face Quality Analysis:')
        ),
        React.createElement('p', {
          className: 'text-xs text-gray-400'
        }, getFaceQualityDescriptionFallback(faceQualityInfo.faceQuality)),
        faceQualityInfo.recommendations && faceQualityInfo.recommendations.length > 0 && React.createElement('div', {
          className: 'mt-1'
        },
          React.createElement('p', {
            className: 'text-xs text-gray-500 mb-1'
          }, 'Recommendations:'),
          React.createElement('ul', {
            className: 'text-xs text-gray-500 space-y-1'
          },
            faceQualityInfo.recommendations.slice(0, 3).map((rec, index) => 
              React.createElement('li', {
                key: index,
                className: 'flex items-start gap-1'
              },
                React.createElement('span', {
                  className: 'text-blue-400 mt-0.5',
                  style: { fontSize: '8px' }
                }, '•'),
                React.createElement('span', null, rec)
              )
            )
          )
        )
      ),
      
      // Disclaimer inside the URL tab
      React.createElement('p', { 
        className: 'text-xs text-gray-500 mt-2 italic', 
        id: 'custom-face-image-disclaimer-url' 
      }, "This feature is currently under development. Results may not be 100% accurate and could contain mistakes.")
    )
    ),
    
    // Preview Section Container
    customFaceImageUrl && React.createElement('div', {
      className: 'preview-section-container mt-3 mb-2',
      style: { 
        animation: 'fadeIn 0.3s ease-in-out'
      }
    },
      React.createElement('div', { className: 'rounded-md bg-gray-700/30', id: 'custom-face-image-preview-block' },
      React.createElement('p', { className: 'text-xs text-gray-400 self-start', id: 'custom-face-image-preview-label' }, 'Face Preview:'),
      React.createElement('img', { 
        src: customFaceImageUrl, alt: 'Custom Face Preview',
        className: 'rounded-full object-cover border-2 border-purple-500 shadow-md',
        id: 'custom-face-image-preview-img',
        onError: (e) => { 
          e.target.style.display='none';
          if (!document.getElementById('img-error-msg')){ // Prevent multiple error messages
            const errorP = document.createElement('p');
            errorP.id = 'img-error-msg';
            errorP.className = 'text-xs text-red-400';
            errorP.textContent = 'Preview unavailable. Check URL or file.';
            e.target.parentNode.appendChild(errorP);
          }
        },
        onLoad: (e) => {
          // Remove error message if image loads successfully
          const errorMsgEl = document.getElementById('img-error-msg');
          if (errorMsgEl) errorMsgEl.remove();
          e.target.style.display=''; // Ensure image is visible
        }
      }),
      React.createElement('button', {
        type: 'button', 
        onClick: handleRemoveFaceImage,
        className: 'custom-face-image-remove-btn w-6 h-6 bg-red-600 rounded-full flex items-center justify-center opacity-100 transition-all duration-200 cursor-pointer hover:bg-red-500 focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-gray-800',
        id: 'custom-face-image-remove-btn', 
        'aria-label': 'Remove custom face image',
        title: 'Remove image'
      },
        React.createElement('span', {
          className: 'iconify',
          'data-icon': 'solar:trash-bin-minimalistic-linear',
          style: { fontSize: '12px', color: 'white' }
        })
      )
      )
    ),
    
    // Image Requirements Button (moved to bottom as secondary action)
    React.createElement('div', {
      className: 'mt-auto pt-3 border-t border-gray-700/50',
      style: { marginTop: customFaceImageUrl ? '1rem' : 'auto' }
    },
      React.createElement('button', {
        type: 'button',
        onClick: () => {
          console.log('Image Requirements button clicked!');
          openImageRequirementsModal();
        },
        className: 'image-requirements-btn w-full py-2.5 px-3 bg-gray-700/30 hover:bg-gray-600/40 text-gray-400 hover:text-gray-200 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 text-xs font-medium border border-gray-600/50 hover:border-gray-500/70',
        'aria-label': 'View detailed image requirements and guidelines',
        id: 'image-requirements-btn'
      },
        React.createElement('span', {
          className: 'iconify',
          'data-icon': 'solar:document-text-linear',
          style: { fontSize: '14px' }
        }),
        'Image Requirements'
      )
    )
  );
}; 