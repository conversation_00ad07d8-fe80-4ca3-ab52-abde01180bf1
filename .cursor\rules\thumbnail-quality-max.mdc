---
description: 
globs: 
alwaysApply: true
---
@thumbnail-quality-max
ruleId: thumbnail-quality-max
description: >
  Enforces maximum image quality for all generated and displayed thumbnails and previews at the new lower resolution.
  Ensures thumbnails are always sharp, clear, and free from visible compression artifacts or blurring.

appliesTo:
  - /src/components/ThumbnailPreview.jsx
  - /src/utils/promptFormatter.js
  - /src/App.jsx
  - /public/assets/demo-thumbnail.jpg

ruleType: always

implementationNotes: |
  - All generated thumbnails must be exactly 720x360 pixels (horizontally stretched for Supabase storage), with no upscaling or downscaling in the UI.
  - When requesting images from OpenAI or any backend, always specify the highest available quality (e.g., JPEG quality 95+ or PNG).
  - Never use low-res proxies or base64 previews for the main preview or modal.
  - The preview modal must display the original, full-resolution image, not a compressed or resized version.
  - In the UI, use `object-fit: cover` and `image-rendering: auto` (not pixelated or smooth) for best browser quality.
  - Do not apply any CSS or JS that would blur, smooth, or compress the image further.
  - If the backend returns a compressed image, request a higher quality version or switch to PNG if possible.
  - Always test on both retina/high-DPI and standard displays to ensure sharpness.

acceptanceCriteria:
  - Thumbnails and previews are always sharp and clear, with no visible JPEG artifacts or blurring.
  - Clicking a thumbnail opens a modal with the full 720x360 image at original quality.
  - No upscaling, downscaling, or extra compression is applied in the browser.
  - All text and faces in the image are crisp and readable.

sampleUI: |
  - Thumbnail grid: All images are sharp, no visible blur or blockiness.
  - Preview modal: Image is full-size, sharp, and matches the original generation quality.
  - No “soft” or “pixelated” look at any step.