import React, { useState, useRef, useEffect } from 'react';

const SearchableCountrySelect = ({ 
    value, 
    onChange, 
    countries, 
    placeholder = "Select your country",
    className = "",
    error = false 
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [highlightedIndex, setHighlightedIndex] = useState(-1);
    const dropdownRef = useRef(null);
    const searchInputRef = useRef(null);
    const listRef = useRef(null);

    // Filter countries based on search term
    const filteredCountries = countries.filter(country =>
        country.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Handle input click to toggle dropdown
    const handleInputClick = () => {
        setIsOpen(!isOpen);
        if (!isOpen) {
            setSearchTerm('');
            setHighlightedIndex(-1);
        }
    };

    // Handle country selection
    const handleCountrySelect = (country) => {
        onChange(country);
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
    };

    // Handle search input change
    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setHighlightedIndex(-1);
    };

    // Handle keyboard navigation
    const handleKeyDown = (e) => {
        if (!isOpen) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                setIsOpen(true);
            }
            return;
        }

        switch (e.key) {
            case 'Escape':
                setIsOpen(false);
                setSearchTerm('');
                setHighlightedIndex(-1);
                break;
            case 'ArrowDown':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev < filteredCountries.length - 1 ? prev + 1 : 0
                );
                break;
            case 'ArrowUp':
                e.preventDefault();
                setHighlightedIndex(prev => 
                    prev > 0 ? prev - 1 : filteredCountries.length - 1
                );
                break;
            case 'Enter':
                e.preventDefault();
                if (highlightedIndex >= 0 && filteredCountries[highlightedIndex]) {
                    handleCountrySelect(filteredCountries[highlightedIndex]);
                }
                break;
            default:
                break;
        }
    };

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsOpen(false);
                setSearchTerm('');
                setHighlightedIndex(-1);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
        if (isOpen && searchInputRef.current) {
            searchInputRef.current.focus();
        }
    }, [isOpen]);

    // Scroll highlighted item into view
    useEffect(() => {
        if (highlightedIndex >= 0 && listRef.current) {
            const highlightedElement = listRef.current.children[highlightedIndex];
            if (highlightedElement) {
                highlightedElement.scrollIntoView({
                    block: 'nearest',
                    behavior: 'smooth'
                });
            }
        }
    }, [highlightedIndex]);

    // Highlight matching text
    const highlightMatch = (text, searchTerm) => {
        if (!searchTerm) return text;
        
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const parts = text.split(regex);
        
        return parts.map((part, index) =>
            regex.test(part) ? (
                React.createElement('mark', {
                    key: index,
                    className: 'bg-purple-200 text-purple-900 px-0.5 rounded'
                }, part)
            ) : part
        );
    };

    return React.createElement('div', {
        ref: dropdownRef,
        className: `relative w-full ${className}`,
        onKeyDown: handleKeyDown
    },
        // Main input field
        React.createElement('div', {
            onClick: handleInputClick,
            className: `w-full px-4 py-3 bg-gray-700 border rounded-lg text-white cursor-pointer transition-all flex items-center justify-between ${
                error ? 'border-red-500' : 'border-gray-600'
            } ${isOpen ? 'border-purple-500 ring-2 ring-purple-500' : 'hover:border-gray-500'}`,
            tabIndex: 0,
            role: 'combobox',
            'aria-expanded': isOpen,
            'aria-haspopup': 'listbox',
            'aria-label': 'Country selection'
        },
            React.createElement('span', {
                className: value ? 'text-white' : 'text-gray-400'
            }, value || placeholder),
            React.createElement('svg', {
                className: `w-5 h-5 text-gray-400 transition-transform duration-200 ${
                    isOpen ? 'transform rotate-180' : ''
                }`,
                fill: 'none',
                stroke: 'currentColor',
                viewBox: '0 0 24 24'
            },
                React.createElement('path', {
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round',
                    strokeWidth: 2,
                    d: 'M19 9l-7 7-7-7'
                })
            )
        ),
        // Dropdown menu (now opens upwards)
        isOpen && React.createElement('div', {
            className: 'absolute left-0 bottom-full mb-2 z-50 w-full bg-gray-700 border border-gray-600 rounded-lg shadow-xl max-h-60 overflow-hidden'
        },
            // Search input
            React.createElement('div', {
                className: 'p-3 border-b border-gray-600'
            },
                React.createElement('input', {
                    ref: searchInputRef,
                    type: 'text',
                    value: searchTerm,
                    onChange: handleSearchChange,
                    placeholder: 'Search countries...',
                    className: 'w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500',
                    autoComplete: 'off'
                })
            ),
            // Countries list
            React.createElement('div', {
                ref: listRef,
                className: 'max-h-48 overflow-y-auto',
                role: 'listbox'
            },
                filteredCountries.length > 0 ? (
                    filteredCountries.map((country, index) =>
                        React.createElement('div', {
                            key: country,
                            onClick: () => handleCountrySelect(country),
                            className: `px-4 py-3 cursor-pointer transition-colors ${
                                index === highlightedIndex
                                    ? 'bg-purple-600 text-white'
                                    : 'text-gray-200 hover:bg-gray-600'
                            }`,
                            role: 'option',
                            'aria-selected': value === country
                        }, highlightMatch(country, searchTerm))
                    )
                ) : (
                    React.createElement('div', {
                        className: 'px-4 py-3 text-gray-400 text-center'
                    }, 'No countries found')
                )
            )
        )
    );
};

export default SearchableCountrySelect; 