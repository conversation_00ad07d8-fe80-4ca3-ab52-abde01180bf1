const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;

export const TemplateModal = ({ isOpen, onClose, onSave, template, categories }) => {
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        category: '',
        basePrompt: '',
        settings: {
            includePerson: false,
            selectedExpression: 'Default',
            textOverlay: false,
            overlayText: '',
            includeIcons: false,
            userPromptFocus: ''
        },
        templateImagePlaceholder: {
            text: '',
            bgColor: 'bg-purple-600'
        }
    });

    const expressions = ['Default', 'Happy', 'Shocked', 'Loved', 'Thinking', 'Angry', 'Crying', 'Laughing', 'Neutral', 'Proud'];
    const bgColors = [
        { value: 'bg-purple-600', label: 'Purple' },
        { value: 'bg-blue-600', label: 'Blue' },
        { value: 'bg-green-600', label: 'Green' },
        { value: 'bg-red-600', label: 'Red' },
        { value: 'bg-yellow-600', label: 'Yellow' },
        { value: 'bg-pink-600', label: 'Pink' },
        { value: 'bg-indigo-600', label: 'Indigo' },
        { value: 'bg-gray-600', label: 'Gray' }
    ];

    useEffect(() => {
        if (template) {
            setFormData({
                name: template.name || '',
                description: template.description || '',
                category: template.category || '',
                basePrompt: template.basePrompt || '',
                settings: {
                    includePerson: template.settings?.includePerson || false,
                    selectedExpression: template.settings?.selectedExpression || 'Default',
                    textOverlay: template.settings?.textOverlay || false,
                    overlayText: template.settings?.overlayText || '',
                    includeIcons: template.settings?.includeIcons || false,
                    userPromptFocus: template.settings?.userPromptFocus || ''
                },
                templateImagePlaceholder: {
                    text: template.templateImagePlaceholder?.text || '',
                    bgColor: template.templateImagePlaceholder?.bgColor || 'bg-purple-600'
                }
            });
        } else {
            // Reset form for new template
            setFormData({
                name: '',
                description: '',
                category: categories[0] || '',
                basePrompt: '',
                settings: {
                    includePerson: false,
                    selectedExpression: 'Default',
                    textOverlay: false,
                    overlayText: '',
                    includeIcons: false,
                    userPromptFocus: ''
                },
                templateImagePlaceholder: {
                    text: '',
                    bgColor: 'bg-purple-600'
                }
            });
        }
    }, [template, categories]);

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSettingsChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            settings: {
                ...prev.settings,
                [field]: value
            }
        }));
    };

    const handlePlaceholderChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            templateImagePlaceholder: {
                ...prev.templateImagePlaceholder,
                [field]: value
            }
        }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        onSave(formData);
    };

    if (!isOpen) return null;

    return React.createElement('div', {
        className: 'template-modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50',
        id: 'template-editor-modal-container',
        onClick: (e) => {
            if (e.target === e.currentTarget) {
                onClose();
            }
        }
    },
        React.createElement('div', {
            className: 'template-modal-content bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto',
            id: 'template-modal-content'
        },
            // Modal Header
            React.createElement('div', {
                className: 'template-modal-header flex justify-between items-center p-6 border-b border-gray-700',
                id: 'template-modal-header'
            },
                React.createElement('h2', {
                    className: 'template-modal-title text-xl font-semibold text-white'
                }, template ? 'Edit Template' : 'Add New Template'),
                React.createElement('button', {
                    className: 'template-modal-close-btn text-gray-400 hover:text-white transition-colors',
                    onClick: onClose
                },
                    React.createElement('svg', {
                        className: 'template-modal-close-icon w-6 h-6',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M6 18L18 6M6 6l12 12'
                        })
                    )
                )
            ),

            // Modal Body
            React.createElement('form', {
                className: 'template-modal-form',
                id: 'template-modal-form',
                onSubmit: handleSubmit
            },
                React.createElement('div', {
                    className: 'template-modal-body p-6 space-y-6'
                },
                    // Basic Information Section
                    React.createElement('div', {
                        className: 'template-basic-info-section',
                        id: 'template-basic-info-section'
                    },
                        React.createElement('h3', {
                            className: 'template-section-title text-lg font-medium text-white mb-4'
                        }, 'Basic Information'),
                        React.createElement('div', {
                            className: 'template-basic-fields-grid grid grid-cols-1 md:grid-cols-2 gap-4'
                        },
                            // Template Name
                            React.createElement('div', {
                                className: 'template-name-field'
                            },
                                React.createElement('label', {
                                    className: 'template-name-label block text-sm font-medium text-gray-300 mb-2'
                                }, 'Template Name'),
                                React.createElement('input', {
                                    type: 'text',
                                    className: 'template-name-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                    id: 'template-name-input',
                                    placeholder: 'Enter template name...',
                                    value: formData.name,
                                    onChange: (e) => handleInputChange('name', e.target.value),
                                    required: true
                                })
                            ),
                            
                            // Category
                            React.createElement('div', {
                                className: 'template-category-field'
                            },
                                React.createElement('label', {
                                    className: 'template-category-label block text-sm font-medium text-gray-300 mb-2'
                                }, 'Category'),
                                React.createElement('select', {
                                    className: 'template-category-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                    id: 'template-category-input',
                                    value: formData.category,
                                    onChange: (e) => handleInputChange('category', e.target.value),
                                    required: true
                                }, categories.map(category => 
                                    React.createElement('option', {
                                        key: category,
                                        value: category
                                    }, category)
                                ))
                            )
                        ),
                        
                        // Description
                        React.createElement('div', {
                            className: 'template-description-field mt-4'
                        },
                            React.createElement('label', {
                                className: 'template-description-label block text-sm font-medium text-gray-300 mb-2'
                            }, 'Description'),
                            React.createElement('textarea', {
                                className: 'template-description-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                id: 'template-description-input',
                                placeholder: 'Enter template description...',
                                rows: 3,
                                value: formData.description,
                                onChange: (e) => handleInputChange('description', e.target.value),
                                required: true
                            })
                        )
                    ),

                    // Prompt Section
                    React.createElement('div', {
                        className: 'template-prompt-section',
                        id: 'template-prompt-section'
                    },
                        React.createElement('h3', {
                            className: 'template-section-title text-lg font-medium text-white mb-4'
                        }, 'Prompt Configuration'),
                        React.createElement('div', {
                            className: 'template-prompt-field'
                        },
                            React.createElement('label', {
                                className: 'template-prompt-label block text-sm font-medium text-gray-300 mb-2'
                            }, 'Base Prompt'),
                            React.createElement('textarea', {
                                className: 'template-prompt-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                id: 'template-prompt-input',
                                placeholder: 'Enter the base prompt for GPT-4 Vision...',
                                rows: 4,
                                value: formData.basePrompt,
                                onChange: (e) => handleInputChange('basePrompt', e.target.value),
                                required: true
                            })
                        )
                    ),

                    // Settings Section
                    React.createElement('div', {
                        className: 'template-settings-section',
                        id: 'template-settings-section'
                    },
                        React.createElement('h3', {
                            className: 'template-section-title text-lg font-medium text-white mb-4'
                        }, 'Template Settings'),
                        React.createElement('div', {
                            className: 'template-settings-grid grid grid-cols-1 md:grid-cols-2 gap-6'
                        },
                            // Include Person Toggle
                            React.createElement('div', {
                                className: 'template-setting-field'
                            },
                                React.createElement('label', {
                                    className: 'template-setting-label flex items-center cursor-pointer'
                                },
                                    React.createElement('input', {
                                        type: 'checkbox',
                                        className: 'template-setting-checkbox sr-only',
                                        checked: formData.settings.includePerson,
                                        onChange: (e) => handleSettingsChange('includePerson', e.target.checked)
                                    }),
                                    React.createElement('div', {
                                        className: `template-setting-toggle ${formData.settings.includePerson ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out`
                                    },
                                        React.createElement('span', {
                                            className: `${formData.settings.includePerson ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                                        })
                                    ),
                                    React.createElement('span', {
                                        className: 'template-setting-label-text ml-3 text-sm font-medium text-gray-300'
                                    }, 'Include Person')
                                )
                            ),

                            // Text Overlay Toggle
                            React.createElement('div', {
                                className: 'template-setting-field'
                            },
                                React.createElement('label', {
                                    className: 'template-setting-label flex items-center cursor-pointer'
                                },
                                    React.createElement('input', {
                                        type: 'checkbox',
                                        className: 'template-setting-checkbox sr-only',
                                        checked: formData.settings.textOverlay,
                                        onChange: (e) => handleSettingsChange('textOverlay', e.target.checked)
                                    }),
                                    React.createElement('div', {
                                        className: `template-setting-toggle ${formData.settings.textOverlay ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out`
                                    },
                                        React.createElement('span', {
                                            className: `${formData.settings.textOverlay ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                                        })
                                    ),
                                    React.createElement('span', {
                                        className: 'template-setting-label-text ml-3 text-sm font-medium text-gray-300'
                                    }, 'Text Overlay')
                                )
                            ),

                            // Include Icons Toggle
                            React.createElement('div', {
                                className: 'template-setting-field'
                            },
                                React.createElement('label', {
                                    className: 'template-setting-label flex items-center cursor-pointer'
                                },
                                    React.createElement('input', {
                                        type: 'checkbox',
                                        className: 'template-setting-checkbox sr-only',
                                        checked: formData.settings.includeIcons,
                                        onChange: (e) => handleSettingsChange('includeIcons', e.target.checked)
                                    }),
                                    React.createElement('div', {
                                        className: `template-setting-toggle ${formData.settings.includeIcons ? 'bg-purple-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out`
                                    },
                                        React.createElement('span', {
                                            className: `${formData.settings.includeIcons ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                                        })
                                    ),
                                    React.createElement('span', {
                                        className: 'template-setting-label-text ml-3 text-sm font-medium text-gray-300'
                                    }, 'Include Icons')
                                )
                            ),

                            // Expression (if person included)
                            formData.settings.includePerson && React.createElement('div', {
                                className: 'template-expression-field'
                            },
                                React.createElement('label', {
                                    className: 'template-expression-label block text-sm font-medium text-gray-300 mb-2'
                                }, 'Expression'),
                                React.createElement('select', {
                                    className: 'template-expression-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                    value: formData.settings.selectedExpression,
                                    onChange: (e) => handleSettingsChange('selectedExpression', e.target.value)
                                }, expressions.map(expression => 
                                    React.createElement('option', {
                                        key: expression,
                                        value: expression
                                    }, expression)
                                ))
                            )
                        ),

                        // Overlay Text (if text overlay enabled)
                        formData.settings.textOverlay && React.createElement('div', {
                            className: 'template-overlay-text-field mt-4'
                        },
                            React.createElement('label', {
                                className: 'template-overlay-text-label block text-sm font-medium text-gray-300 mb-2'
                            }, 'Overlay Text'),
                            React.createElement('input', {
                                type: 'text',
                                className: 'template-overlay-text-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                placeholder: 'Enter overlay text...',
                                value: formData.settings.overlayText,
                                onChange: (e) => handleSettingsChange('overlayText', e.target.value)
                            })
                        ),

                        // User Prompt Focus
                        React.createElement('div', {
                            className: 'template-prompt-focus-field mt-4'
                        },
                            React.createElement('label', {
                                className: 'template-prompt-focus-label block text-sm font-medium text-gray-300 mb-2'
                            }, 'User Prompt Focus'),
                            React.createElement('input', {
                                type: 'text',
                                className: 'template-prompt-focus-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                placeholder: 'Enter focus keywords for user prompt...',
                                value: formData.settings.userPromptFocus,
                                onChange: (e) => handleSettingsChange('userPromptFocus', e.target.value)
                            })
                        )
                    ),

                    // Preview Section
                    React.createElement('div', {
                        className: 'template-preview-section',
                        id: 'template-preview-section'
                    },
                        React.createElement('h3', {
                            className: 'template-section-title text-lg font-medium text-white mb-4'
                        }, 'Template Preview'),
                        React.createElement('div', {
                            className: 'template-preview-grid grid grid-cols-1 md:grid-cols-2 gap-4'
                        },
                            React.createElement('div', {
                                className: 'template-preview-text-field'
                            },
                                React.createElement('label', {
                                    className: 'template-preview-text-label block text-sm font-medium text-gray-300 mb-2'
                                }, 'Preview Text'),
                                React.createElement('input', {
                                    type: 'text',
                                    className: 'template-preview-text-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                    placeholder: 'Enter preview text...',
                                    value: formData.templateImagePlaceholder.text,
                                    onChange: (e) => handlePlaceholderChange('text', e.target.value)
                                })
                            ),
                            React.createElement('div', {
                                className: 'template-preview-color-field'
                            },
                                React.createElement('label', {
                                    className: 'template-preview-color-label block text-sm font-medium text-gray-300 mb-2'
                                }, 'Background Color'),
                                React.createElement('select', {
                                    className: 'template-preview-color-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                                    value: formData.templateImagePlaceholder.bgColor,
                                    onChange: (e) => handlePlaceholderChange('bgColor', e.target.value)
                                }, bgColors.map(color => 
                                    React.createElement('option', {
                                        key: color.value,
                                        value: color.value
                                    }, color.label)
                                ))
                            )
                        )
                    )
                ),

                // Modal Footer
                React.createElement('div', {
                    className: 'template-modal-footer flex justify-end gap-4 p-6 border-t border-gray-700',
                    id: 'template-modal-footer'
                },
                    React.createElement('button', {
                        type: 'button',
                        className: 'template-modal-cancel-btn px-6 py-2 text-gray-400 hover:text-white transition-colors',
                        onClick: onClose
                    }, 'Cancel'),
                    React.createElement('button', {
                        type: 'submit',
                        className: 'template-modal-save-btn px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors'
                    }, template ? 'Update Template' : 'Create Template')
                )
            )
        )
    );
}; 