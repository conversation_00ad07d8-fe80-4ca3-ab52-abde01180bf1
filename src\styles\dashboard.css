/* Dashboard Container Layout */
.dashboard-outer-wrapper {
    min-height: calc(100vh - 140px);
    width: 100%;
    display: flex;
    justify-content: center;
    background: #111827; /* Gray-900 */
    padding: 0;
    overflow-y: auto;
    /* Firefox scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: rgba(75, 85, 99, 0.9) rgba(17, 24, 39, 0.8);
}

/* Dashboard Modal Smooth Transitions */
.user-dashboard {
    /* Entry animation - fade in */
    animation: dashboardFadeIn 200ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
    transform-origin: center;
}

/* Dashboard fade animations */
@keyframes dashboardFadeIn {
    from {
        opacity: 0;
        transform: scale(0.98) translateY(8px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes dashboardFadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.98) translateY(-8px);
    }
}

/* Apply closing animation when isClosing prop is true */
.user-dashboard.closing {
    animation: dashboardFadeOut 200ms cubic-bezier(0.4, 0, 1, 1) forwards;
}

.dashboard-container {
    width: 100%;
    max-width: 1170px;
    min-width: 320px;
    padding: 0 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .dashboard-container {
        max-width: 100%;
        padding: 0 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        padding: 0 0.75rem;
    }
}

/* Header and Navigation centering */
.dashboard-header .flex {
    max-width: 960px;
    margin: 0 auto;
}

.dashboard-nav .flex {
    max-width: 960px;
    margin: 0 auto;
}

/* UserDashboard Scrollbar Styling */
.dashboard-content::-webkit-scrollbar {
    width: 8px;
}

.dashboard-content::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.5);
    border-radius: 4px;
}

.dashboard-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #2563eb);
}

/* For Firefox */
.dashboard-content {
    scrollbar-width: thin;
    scrollbar-color: #8b5cf6 rgba(55, 65, 81, 0.5);
}

/* Dashboard Outer Wrapper Scrollbar Styling */
.dashboard-outer-wrapper::-webkit-scrollbar {
    width: 8px;
}

.dashboard-outer-wrapper::-webkit-scrollbar-track {
    background: rgba(17, 24, 39, 0.8); /* Dark gray-900 background */
    border-radius: 4px;
}

.dashboard-outer-wrapper::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.9); /* Dark gray-600 */
    border-radius: 4px;
    transition: background 0.3s ease;
    border: 1px solid rgba(55, 65, 81, 0.5); /* Subtle border */
}

.dashboard-outer-wrapper::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 1); /* Lighter gray-500 on hover */
    border-color: rgba(139, 92, 246, 0.3); /* Subtle purple accent on hover */
}

/* Smooth scrolling */
.dashboard-content {
    scroll-behavior: smooth;
}

/* Smooth scrolling for outer wrapper */
.dashboard-outer-wrapper {
    scroll-behavior: smooth;
}

/* Ensure proper spacing for scrollable content */
.dashboard-overview,
.dashboard-account,
.dashboard-billing,
.dashboard-history {
    padding-bottom: 2rem;
}

/* Activity list scrolling for overflow */
.activity-list {
    max-height: 400px;
    /* overflow-y: auto; */
}

.activity-list::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.3);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
    background: rgba(139, 92, 246, 0.7);
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
    background: rgba(139, 92, 246, 0.9);
}

/* History Grid Responsive Layout */
.history-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.7rem;
    overflow-y: auto;
    overflow-x: visible; /* Ensure dropdowns can extend outside grid bounds */
    scrollbar-width: thin;
    scrollbar-color: #8b5cf6 rgba(55, 65, 81, 0.5);
}

/* Hide scrollbar when grid is empty or has only one row */
.history-grid:empty,
.history-grid:only-child {
    overflow-y: hidden;
}

/* Chrome/Safari: Hide scrollbar if not needed */
.history-grid::-webkit-scrollbar:vertical:single-button {
    display: none;
}

/* Mobile landscape (640px+) - 2 columns */
@media (min-width: 640px) {
    .history-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet landscape (900px+) - 3 columns */
@media (min-width: 900px) {
    .history-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* History Grid Scrollbar */
.history-grid::-webkit-scrollbar {
    width: 8px;
}

.history-grid::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.5);
    border-radius: 4px;
}

.history-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #8b5cf6, #3b82f6);
    border-radius: 4px;
}

.history-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #7c3aed, #2563eb);
}

/* Promotional Banner Styles */
.promotional-banner-section {
    width: 100%;
}

.upgrade-banner {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.upgrade-banner:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.banner-icon-wrapper {
    transition: all 0.3s ease;
}

.upgrade-banner:hover .banner-icon-wrapper {
    transform: scale(1.1);
    background: rgba(234, 88, 12, 0.3);
}

/* Dashboard-specific upgrade button styling - matches auth-cta-btn but with custom width */
.dashboard-upgrade-cta-btn.auth-cta-btn {
    /* Override auth-cta-btn width for banner context */
    width: auto !important;
    min-width: 160px !important;
    
    /* Maintain auth button styling but adjust for banner */
    white-space: nowrap !important;
    flex-shrink: 0 !important;
}

/* Legacy upgrade button styles for other contexts */
.upgrade-cta-btn {
    position: relative;
    overflow: hidden;
}

.upgrade-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.upgrade-cta-btn:hover::before {
    left: 100%;
}

.upgrade-cta-btn:active {
    transform: scale(0.98);
}

/* Responsive Banner Adjustments */
@media (max-width: 768px) {
    .upgrade-banner .banner-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .upgrade-banner .banner-action {
        width: 100%;
        display: flex;
        justify-content: center; /* Center align the CTA button */
    }
    
    .upgrade-cta-btn {
        width: 100%;
        justify-content: center;
    }
    
    /* Enhanced CTA button styling for mobile */
    .dashboard-upgrade-cta-btn.auth-cta-btn {
        font-size: 115% !important; /* 15% larger font size */
        font-weight: 500 !important; /* Font weight 500 */
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 auto !important; /* Center align the button */
    }
    
    .banner-text h3 {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .upgrade-banner {
        padding: 1rem;
    }
    
    .banner-text h3 {
        font-size: 1.125rem;
        margin-bottom: 0.5rem;
    }
    
    .banner-text p {
        font-size: 0.875rem;
    }
    
    .upgrade-cta-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
    }
}

/* History Item Card Styles */
.history-item {
    position: relative;
    background: #1f2937;
    border-radius: 1rem;
    overflow: visible; /* Allow dropdown to overflow */
    border: 1px solid #374151;
    min-height: 180px;
    z-index: 1; /* Base z-index for stacking context */
}

/* Ensure image doesn't overflow */
.history-item-thumbnail {
    border-radius: 1rem;
    overflow: hidden;
}

/* Special styling for first row items to handle dropdown positioning */
.history-item:nth-child(-n+3) {
    z-index: 10; /* Higher z-index for first row items */
}

/* Ensure thumbnail doesn't overflow when parent has overflow: visible */
.history-item-thumbnail {
    border-radius: 1rem; /* Match parent border radius */
}

/* Hover border color handled by premium transitions CSS */

/* Thumbnail fills entire card */
.history-item-thumbnail {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
    border-radius: 1rem; /* Match parent border radius to prevent overflow */
}

/* Fallback for items without thumbnails */
.history-item-placeholder {
    width: 100%;
    height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #374151;
    color: #9CA3AF;
}

/* Dark feather gradient overlay at bottom */
.history-item-gradient {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 60px;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(17,17,17,0.9) 100%);
    pointer-events: none;
    border-radius: 1em;
    transition: background 0.3s ease;
}

/* Increase contrast on hover for better button visibility */
.history-item:hover .history-item-gradient {
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.95) 100%);
}

/* Bottom Actions Container - revealed on hover */
.bottom-actions {
    position: absolute;
    left: 12px;
    right: 12px;
    bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    opacity: 0;
    pointer-events: none;
    z-index: 10;
}

.history-item:hover .bottom-actions {
    opacity: 1;
    pointer-events: auto;
}

/* View Details Button (Left) */
.view-details-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border-radius:8px;
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-details-btn:hover {
    /* background: rgba(0, 0, 0, 0.9); */
    color: rgba(255, 255, 255, 0.833);
    backdrop-filter: blur(4px);
    background: rgba(0, 0, 0, 0.865);
    border: none;
    border-radius: 8px;
    background: rgba(244, 244, 245, 0.2);
   

}

/* Show More Container (Right) */
.show-more-container {
    position: relative;
    display: flex;
    align-items: center;
    z-index: 10; /* Ensure dropdown appears above other elements */
}

/* Show More Button */
.show-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    /* background: rgba(0, 0, 0, 0.75); */
    border: none;
    border-radius: 1em;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.show-more-btn:hover {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: rgba(244, 244, 245, 0.2);
    backdrop-filter: blur(2.5px);
    color: #ffffff;
}

/* Dropdown Menu */
.dropdown-menu {
    position: absolute;
    right: 0;
    bottom: 25px; /* Position above button by default */
    display: none; /* Initially hidden */
    padding: 0.44rem;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 0.3rem;
    border-radius: var(--sds-size-radius-400, 12px);
    border: 1px solid #444;
    background: rgba(20, 20, 20, 0.78);
    box-shadow: 0px 12px 35px 0px rgba(0, 0, 0, 0.45), 
                0px 5px 15px 0px rgba(0, 0, 0, 0.35);
    /* Use both backdrop-filter and -webkit-backdrop-filter for Safari/Chrome, and also add -moz-backdrop-filter for Firefox support */
    backdrop-filter: blur(28px);
    -webkit-backdrop-filter: blur(28px);
    -moz-backdrop-filter: blur(28px);
    z-index: 1000; /* High z-index to be on top of everything */
    min-width: 140px;
    opacity: 0;
    transform: scale(0.95) translateY(20px);
    transform-origin: bottom center;
    transition: all 0.15s ease-in-out;
    /* For Firefox fallback if -moz-backdrop-filter is not supported, use a semi-opaque background */
    @supports not ((-moz-backdrop-filter: blur(28px)) or (backdrop-filter: blur(28px))) {
        background: rgba(30, 30, 30, 0.97);
    }
}

/* Position dropdown below button for first row items to prevent clipping */
/* .history-item:nth-child(-n+3) .dropdown-menu {
    bottom: auto;
    top: 0;
    transform-origin:  bottom center;
    transform: scale(0.95) translateY(-8px);
} */

/* Show dropdown with animation */
.dropdown-menu[style*="display: flex"] {
    opacity: 1 !important;
    transform: scale(1) translateY(0) !important;
}

/* Ensure first row items animate correctly when shown */
.history-item:nth-child(-n+3) .dropdown-menu[style*="display: flex"] {
    transform: scale(1) translateY(0) !important;
}

/* Dropdown Items */
.dropdown-item {
    display: flex;
    align-items: center;
    gap: 6.8px; /* Reduced by 15% from 8px */
    width: 100%;
    padding: 6.8px 10.2px; /* Reduced by 15% from 8px 12px */
    background: none;
    border: none;
    border-radius: 6px;
    color: #ffffff;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
    /* color: #a78bfa; */
}

.dropdown-item.delete-item:hover {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
}

/* Hide dropdown on click outside */
.dropdown-menu[style*="display: none"] {
    display: none !important;
}

.dropdown-menu[style*="display: flex"] {
    display: flex !important;
}

/* Ensure text truncation for long content */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Mobile Dashboard Navigation Fixes */
@media (max-width: 697px) {
    /* Reduce padding on nav container */
    .dashboard-nav .flex {
        padding-left: 0.75rem; /* Reduced from 1.5rem (px-6) */
        padding-right: 0.75rem;
        gap: 0.25rem; /* Add small gap between tabs */
        overflow-x: auto; /* Allow horizontal scroll if needed */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }
    
    /* Hide scrollbar but keep functionality */
    .dashboard-nav .flex::-webkit-scrollbar {
        display: none;
    }
    .dashboard-nav .flex {
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }
    
    /* Adjust nav button styles - KEEP FONT SIZE SAME AS DESKTOP */
    .dashboard-nav button {
        padding: 0.625rem 0.75rem; /* Reduced from py-3 px-4 */
        font-size: inherit; /* Keep desktop font size */
        white-space: nowrap; /* Prevent text wrapping */
        min-width: fit-content; /* Ensure buttons don't shrink too much */
        flex-shrink: 0; /* Prevent shrinking in flex container */
    }
    
    /* Keep icon size same as desktop */
    .dashboard-nav button .iconify {
        font-size: 18px !important; /* Keep original 18px */
    }
    
    /* Adjust gap between icon and text */
    .dashboard-nav button {
        gap: 0.375rem; /* Reduced from gap-2 (0.5rem) */
    }
    
    /* Ensure active border is still visible */
    .dashboard-nav button {
        border-bottom-width: 2px;
    }
    
    /* Optional: Hide icons on very small screens to save space */
    @media (max-width: 480px) {
        .dashboard-nav button .iconify {
            display: none;
        }
    }
}

/* Even smaller screens - extreme mobile */
@media (max-width: 480px) {
    .dashboard-nav .flex {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }
    
    .dashboard-nav button {
        padding: 0.5rem 0.625rem;
        font-size: 0.875rem; /* 14px - slightly smaller but not as small as before */
    }
}

/* Stack filter controls on small screens for better usability */
@media (max-width: 600px) {
    #generation-history-filter-controls {
        flex-direction: row;
        width: 60%;
        gap: 0.5rem; /* 8px gap between buttons */
    }

    #generation-history-filter-controls button {
        width: 100%;
        justify-content: center; /* Center text only */
    }
    
    #generation-history-filter-controls button .iconify {
        display: none; /* Hide icons at max-width 600px */
    }
}

/* Mobile landscape (640px+) - 2 columns */
/* @media (min-width: 640px) {
    .history-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}  */

/* Password Modal Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideOutScale {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(20px);
    }
}

/* Spin animation for loading state */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* ======================================
   Personal Info Section Styles
   ====================================== */

/* Personal Info Section Container */
.personal-info-section {
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Personal Info Content Container */
.personal-info-content {
    transition: all 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Edit Mode Transitions */
.personal-info-edit-mode {
    transform: translateY(-2px);
}

/* Personal Info Section - Soft rounded background and balanced spacing */
.personal-info-section {
    background: rgba(31, 41, 55, 0.6) !important; /* Soft gray-800 with transparency */
    border: 1px solid rgba(75, 85, 99, 0.3) !important; /* Subtle border */
    border-radius: 12px !important; /* Soft rounded corners */
    padding: 2rem !important; /* Balanced padding all around */
    backdrop-filter: blur(10px) !important; /* Subtle blur effect */
}

/* Avatar Container */
.personal-info-avatar-container {
    transition: all 0.2s ease;
    margin-top: 0 !important; /* Equal spacing from top */
    margin-bottom: 2rem !important; /* Equal spacing to bottom */
}

/* Avatar Image Container - Increased by 30% */
.personal-info-avatar-image-container {
    width: 6.5rem !important; /* 30% increase from 5rem (w-20) */
    height: 6.5rem !important; /* 30% increase from 5rem (h-20) */
}

.personal-info-avatar-container:hover {
    transform: translateY(-1px);
}

/* User Avatar */
.personal-info-user-avatar {
    align-items: center !important;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.personal-info-user-avatar:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 25px -5px rgba(139, 92, 246, 0.3);
}

/* Input Labels */
.personal-info-input-label {
    transition: color 0.2s ease;
}

/* Edit Controls */
.personal-info-edit-controls {
    transition: all 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Edit Button */
.personal-info-edit-btn:hover {
    transform: translateY(-1px);
}

/* Input Focus States */
.personal-info-content input:focus {
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
    transform: scale(1.01);
}

/* Mobile Portrait Responsive Layout */
@media (max-width: 768px) and (orientation: portrait) {
    .personal-info-section {
        padding: 1.5rem !important; /* Slightly less padding on mobile */
    }
    
    .personal-info-section .flex.items-center {
        flex-direction: row !important;
        align-items: center !important;
        text-align: center !important;
    }
    
    .personal-info-avatar-container {
        margin-top: 0 !important; /* Consistent top spacing */
        margin-bottom: 1.5rem !important; /* Balanced bottom spacing */
    }
    
    .personal-info-user-details {
        width: 100% !important;
    }
    
    .personal-info-edit-controls {
        justify-content: center;
        width: 100%;
    }
    
    .personal-info-edit-controls button {
        flex: 1;
        max-width: 150px;
    }
}

/* Tablet Portrait */
@media (max-width: 768px) and (min-width: 481px) and (orientation: portrait) {
    .personal-info-user-avatar {
        width: 5rem !important;
        height: 5rem !important;
    }
    
    .personal-info-user-avatar .iconify {
        font-size: 28px !important;
    }
}

/* Mobile Portrait */
@media (max-width: 480px) and (orientation: portrait) {
    .personal-info-section {
        padding: 1.25rem !important; /* Balanced padding for small screens */
        border-radius: 8px !important; /* Slightly smaller border radius */
    }
    
    .personal-info-user-avatar {
        width: 4rem !important;
        height: 4rem !important;
    }
    
    .personal-info-user-avatar .iconify {
        font-size: 24px !important;
    }
    
    .personal-info-edit-controls {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .personal-info-edit-controls button {
        max-width: none;
        width: 100%;
    }
}

/* Avatar Edit Mode Effects (only in edit mode) */
.personal-info-avatar-container .absolute {
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Remove automatic hover effects - only show in edit mode */
.personal-info-avatar-container .absolute:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Smooth transitions for all elements */
.personal-info-section * {
    transition: all 0.2s ease;
}

/* Enhanced focus states */
.personal-info-section input:focus,
.personal-info-section button:focus {
    outline: 2px solid rgba(139, 92, 246, 0.5);
    outline-offset: 2px;
}

/* Hide cancel button when text is empty (for success modals) */
.cancel-btn:empty,
.cancel-btn[id*="success-cancel-btn"] {
    display: none;
}

/* Loading state for avatar */
.personal-info-user-avatar.loading {
    opacity: 0.7;
    pointer-events: none;
}

.personal-info-user-avatar.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* End of Personal Info Section Styles */

/* ============================================
   Enhanced Dashboard Local Toast Notifications
   ============================================ */

/* Premium Liquid Glass Toast Animations - Enhanced timing for natural feel */
@keyframes liquidSlideInRight {
    0% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(15deg);
        filter: blur(4px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(0) scale(1.02) rotateY(0deg);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
}

@keyframes liquidSlideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
    40% {
        opacity: 0.8;
        transform: translateX(0) scale(0.98) rotateY(5deg);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(-15deg);
        filter: blur(4px);
    }
}

/* Fallback animations for older browsers */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    60% {
        opacity: 1;
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0);
    }
    40% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Enhanced liquid glass toast container */
.dashboard-liquid-toast-container {
    /* Ensure it appears above all dashboard content but below global modals */
    z-index: 10001 !important;
    pointer-events: auto !important;
    perspective: 1000px; /* Enable 3D transforms */
}

/* Liquid glass toast hover effects - premium macOS style for inverted colors */
.dashboard-liquid-toast-container:hover .liquid-glass-toast-inverted {
    transform: translateY(-2px) scale(1.01) !important;
    box-shadow: 
        0 32px 64px -12px rgba(0, 0, 0, 0.25),
        0 20px 25px -5px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2) !important;
}

/* Liquid glass toast base styling */
.liquid-glass-toast {
    background: rgba(255, 255, 255, 0.02) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
    transition: all 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Inverted liquid glass toast styling for accessibility */
.liquid-glass-toast-inverted {
    transition: all 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.liquid-glass-toast-inverted * {
    color: #ffffff !important; /* Ensure all text is white */
}

/* Icon container liquid glass styling */
.liquid-toast-icon-container {
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 2px 4px rgba(0, 0, 0, 0.1) !important;
    transition: all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Close button liquid glass styling */
.liquid-close-button {
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Enhanced accessibility and focus states with liquid glass */
.liquid-close-button:focus {
    box-shadow: 
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 1px 3px rgba(0, 0, 0, 0.1),
        0 0 0 3px rgba(59, 130, 246, 0.3) !important;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .liquid-glass-toast,
    .liquid-glass-toast-inverted,
    .liquid-toast-icon-container,
    .liquid-close-button {
        transition: none !important;
    }
    
    .dashboard-liquid-toast-container {
        animation: slideInRight 400ms ease-out !important;
    }
    
    .dashboard-liquid-toast-container[style*="liquidSlideOutRight"] {
        animation: slideOutRight 400ms ease-out !important;
    }
}

/* Enhanced responsive adjustments for mobile liquid glass */
@media (max-width: 768px) {
    .dashboard-liquid-toast-container {
        top: 16px !important;
        right: 16px !important;
        left: 16px !important;
        width: calc(100% - 32px) !important;
    }
    
    .dashboard-liquid-toast-container .liquid-glass-toast {
        min-width: auto !important;
        max-width: none !important;
        width: 100% !important;
        padding: 1rem !important;
        font-size: 14px !important;
    }
    
    .dashboard-liquid-toast-container .liquid-toast-icon-container {
        width: 36px !important;
        height: 36px !important;
    }
    
    .dashboard-liquid-toast-container .liquid-toast-icon-container .iconify {
        font-size: 18px !important;
    }
    
    .dashboard-liquid-toast-container .liquid-close-button {
        width: 28px !important;
        height: 28px !important;
    }
    
    .dashboard-liquid-toast-container .liquid-close-button .iconify {
        font-size: 16px !important;
    }
}

/* Tablet responsive adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .dashboard-liquid-toast-container {
        top: 20px !important;
        right: 20px !important;
    }
    
    .dashboard-liquid-toast-container .liquid-glass-toast {
        min-width: 360px !important;
        max-width: 460px !important;
    }
}

/* Legacy container support - remove after migration */
.dashboard-local-toast-container {
    z-index: 10001 !important;
    pointer-events: auto !important;
}

.dashboard-local-toast-container:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.dashboard-local-toast-container button:focus {
    outline: 2px solid rgba(139, 92, 246, 0.5) !important;
    outline-offset: 2px !important;
}

.dashboard-local-toast-container > div {
    backdrop-filter: blur(12px) saturate(180%) !important;
    -webkit-backdrop-filter: blur(12px) saturate(180%) !important;
    box-shadow: 
        0 20px 25px -5px rgba(0, 0, 0, 0.15), 
        0 10px 10px -5px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

/* ============================================
   Dashboard Empty State Animations
   ============================================ */

/* Shimmer effect for empty state icon */
.dashboard-empty-state-icon {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-left: auto !important;
    margin-right: auto !important;
}

.dashboard-empty-state-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255,255,255,0.18) 0%, rgba(255,255,255,0.08) 100%);
    transform: skewX(-20deg);
    transition: none;
    pointer-events: none;
    animation: dashboardIconShimmer 2s ease-in-out infinite;
}

@keyframes dashboardIconShimmer {
    0% {
        left: -75%;
        opacity: 1;
    }
    100% {
        left: 125%;
        opacity: 0;
    }
}

/* Shimmer/glow effect for CTA button */
.dashboard-empty-cta {
    position: relative;
    overflow: hidden;
}

.dashboard-empty-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -60%;
    width: 60%;
    height: 100%;
    background: linear-gradient(120deg, rgba(255,255,255,0.18) 0%, rgba(255,255,255,0.08) 100%);
    transform: skewX(-20deg);
    transition: none;
    pointer-events: none;
    opacity: 0;
}

.dashboard-empty-cta:hover::before {
    animation: dashboardShimmerMove 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes dashboardShimmerMove {
    0% {
        left: -60%;
        opacity: 1;
    }
    100% {
        left: 130%;
        opacity: 0;
    }
}

/* History empty state title styling */
.empty-state-title {
    font-size: 1.5rem !important;
}

/* History empty illustration size increase */
.empty-state-illustration,
#history-empty-illustration {
    transform: scale(1.2); /* 20% bigger */
}

/* CTA button styling */
#dashboard-history-create-thumbnail-btn {
    font-weight: 500 !important;
    font-size: 1.1rem !important;
    height: 3.2rem !important;
}

/* Responsive adjustments for empty state */
@media (max-width: 768px) {
    .dashboard-empty-state-icon {
        font-size: 80px !important;
        width: 80px !important;
        height: 79px !important;
    }
    
    #dashboard-history-create-thumbnail-btn {
        font-size: 1.0rem !important; /* Adjusted for mobile */
        height: 3rem !important; /* Slightly smaller height for mobile */
    }
}

@media (max-width: 480px) {
    .dashboard-empty-state-icon {
        font-size: 70px !important;
        width: 70px !important;
        height: 69px !important;
    }
    
    #dashboard-history-create-thumbnail-btn {
        font-size: 0.95rem !important; /* Adjusted for small mobile */
        height: 2.8rem !important; /* Smaller height for small mobile */
    }
}

/* ======================================
   Existing Dashboard Styles Continue...
   ====================================== */

@media (max-width: 768px) and (orientation: portrait) {
  .dashboard-empty-state-icon {
    font-size: 118px !important;
    width: 118px !important;
    height: 117px !important;
  }
}

/* Enhanced mobile portrait scrolling */
@media (max-width: 768px) and (orientation: portrait) {
    .dashboard-outer-wrapper {
        min-height: 100vh; /* Use full viewport height */
        overflow-y: auto; /* Ensure vertical scrolling is enabled */
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }
    
    .dashboard-container {
        min-height: auto; /* Allow content to determine height */
        padding-bottom: 2rem; /* Add bottom padding for better UX */
    }
    
    .dashboard-content {
        min-height: auto; /* Remove height restrictions */
        overflow: visible; /* Allow content to overflow naturally */
    }
    
    /* Ensure tab content can scroll */
    .tab-content {
        overflow: visible;
        min-height: auto;
    }
}

/* Mobile portrait responsive styles for history items */
@media (max-width: 639px) and (orientation: portrait) {
    .history-item {
        min-height: 230px;
    }
    
    .history-item-thumbnail {
        height: 230px;
    }
}

/* Hide username display block from Overview tab on all devices */
.username-display-block {
    display: none !important;
}

/* Hide the separator when username is hidden */
#username-lastlogin-separator {
    display: none !important;
}

/* Button styling for max-width 768px */
@media (max-width: 768px) {
    .personal-info-save-action-btn,
    .personal-info-cancel-action-btn {
        display: flex !important;
        width: 100% !important;
    }
    
    /* Input styling for max-width 768px - using unique class names */
    .personal-info-fullname-input,
    .personal-info-username-input,
    .personal-info-email-input {
        display: flex !important;
        flex-direction: column !important;
        align-items: flex-start !important;
    }
    
    /* Field container styling for max-width 768px */
    .personal-info-fullname-field-container,
    .personal-info-username-field-container,
    .personal-info-email-field-container {
        align-items: baseline !important;
        display: flex;
        flex-direction: column;
    }
    
    /* Member since field container styling for max-width 768px */
    .personal-info-member-since-field-container {
        display: flex;
        flex-direction: column !important;
        align-items: baseline !important;
    }
    
    /* Main content container styling for max-width 768px */
    #personal-info-main-content-container {
        display: flex !important;
        flex-direction: column !important;
    }
}

/* ================= ENHANCED BILLING & SUBSCRIPTION SECTION ================= */

/* Billing Dashboard Container */
.dashboard-billing {
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.95) 0%, rgba(31, 41, 55, 0.95) 100%);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Billing Form Grid Layout */
.billing-form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

@media (min-width: 768px) {
    .billing-form-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.75rem;
        align-items: start;
    }
}

/* Billing Edit Form Styles */
.billing-edit-form .form-group {
    position: relative;
    display: flex;
    flex-direction: column;
}

.billing-edit-form .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    color: #d1d5db;
}

.billing-edit-form .form-group input,
.billing-edit-form .form-group select,
.billing-edit-form .form-group textarea {
    width: 100%;
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    color: #ffffff;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    height: 48px;
}

/* All form inputs have consistent height */

.billing-edit-form .form-group input:focus,
.billing-edit-form .form-group select:focus,
.billing-edit-form .form-group textarea:focus {
    outline: none;
    border-color: transparent;
    box-shadow: 0 0 0 2px #8b5cf6;
}

.billing-edit-form .form-group input::placeholder,
.billing-edit-form .form-group textarea::placeholder {
    color: #9ca3af;
}

/* Error States */
.billing-edit-form .form-group input.error,
.billing-edit-form .form-group select.error,
.billing-edit-form .form-group textarea.error {
    border-color: #ef4444;
}

.billing-edit-form .form-group .error-message {
    color: #fca5a5;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Action Buttons */
.billing-edit-form .action-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.billing-edit-form .action-buttons button {
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.billing-edit-form .action-buttons button:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.5);
}

/* Mobile Responsive */
@media (max-width: 767px) {
    .billing-form-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .billing-edit-form .action-buttons {
        flex-direction: column-reverse;
    }
    
    .billing-edit-form .action-buttons button {
        width: 100%;
        justify-content: center;
    }
}

/* Billing form buttons */
.billing-edit-form .form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
}

.billing-edit-form .form-actions button {
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: none;
    cursor: pointer;
}

.billing-edit-form .form-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.billing-edit-form .form-actions .save-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
}

.billing-edit-form .form-actions .save-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.billing-edit-form .form-actions .cancel-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: #ffffff;
}

.billing-edit-form .form-actions .cancel-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

/* Subscription Section Enhanced */
.subscription-section {
    position: relative;
    overflow: hidden;
}

.subscription-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.5), transparent);
}

/* Subscription Header */
.subscription-header {
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
}

.subscription-actions {
    flex-wrap: wrap;
    gap: 1rem;
}

/* Enhanced Button Styles */
.upgrade-plan-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 14px rgba(139, 92, 246, 0.3);
}

.upgrade-plan-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.upgrade-plan-btn:hover::before {
    left: 100%;
}

.cancel-renewal-btn {
    position: relative;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.cancel-renewal-btn:hover {
    background: rgba(75, 85, 99, 0.1);
    transform: translateY(-1px);
}

/* Subscription Details Grid */
.subscription-details-grid {
    margin-top: 2rem;
}

.subscription-detail-item {
    padding: 1.5rem;
    background: rgba(55, 65, 81, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(75, 85, 99, 0.2);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
}

.subscription-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
}

.subscription-detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.6), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.subscription-detail-item:hover::before {
    opacity: 1;
}

.detail-header {
    margin-bottom: 1rem;
}

.detail-header p {
    letter-spacing: 0.05em;
    font-weight: 600;
}

/* Color-coded accent bars */
.detail-header .bg-purple-500 { background: linear-gradient(90deg, #8b5cf6, #a855f7); }
.detail-header .bg-green-500 { background: linear-gradient(90deg, #10b981, #059669); }
.detail-header .bg-blue-500 { background: linear-gradient(90deg, #3b82f6, #2563eb); }
.detail-header .bg-yellow-500 { background: linear-gradient(90deg, #f59e0b, #d97706); }

/* Change Billing Button */
.change-billing-btn {
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.change-billing-btn:hover {
    background: rgba(139, 92, 246, 0.1);
    transform: translateX(2px);
}

/* Credits Section Enhanced */
.credits-section {
    position: relative;
    overflow: hidden;
}

.credits-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(234, 179, 8, 0.5), transparent);
}

.credits-header {
    align-items: flex-start;
    flex-wrap: wrap;
}

.credits-summary {
    text-align: right;
    min-width: 120px;
}

/* Enhanced Progress Bar */
.credits-progress-container {
    position: relative;
}

.credits-progress-container .bg-gray-700 {
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.3);
}

.credits-progress-container .bg-gradient-to-r {
    position: relative;
    background: linear-gradient(90deg, #8b5cf6 0%, #3b82f6 50%, #10b981 100%);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
}

.credits-progress-container .bg-gradient-to-r::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Credit Usage Breakdown */
.credit-usage-breakdown {
    margin-top: 2rem;
}

.usage-grid {
    gap: 1.5rem;
}

.usage-item {
    background: rgba(55, 65, 81, 0.4);
    border: 1px solid rgba(75, 85, 99, 0.3);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
}

.usage-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(0, 0, 0, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
}

.usage-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.usage-item:hover::before {
    opacity: 0.6;
}

/* Color-coded indicators */
.usage-item:nth-child(1)::before { color: #10b981; }
.usage-item:nth-child(2)::before { color: #3b82f6; }
.usage-item:nth-child(3)::before { color: #8b5cf6; }

/* Billing Information Section */
.billing-info-section {
    position: relative;
    overflow: hidden;
}

.billing-info-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.5), transparent);
}

.billing-header {
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
}

.edit-billing-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 4px 14px rgba(139, 92, 246, 0.3);
}

.edit-billing-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.edit-billing-btn:hover::before {
    left: 100%;
}

.billing-details-grid {
    gap: 2rem;
}

.billing-detail-item {
    padding: 1.5rem;
    background: rgba(55, 65, 81, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(75, 85, 99, 0.2);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    position: relative;
    overflow: hidden;
}

.billing-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: rgba(16, 185, 129, 0.3);
}

/* Billing Details Grid - Enforced Gap */
.billing-details-grid {
    gap: 1.1rem !important;
}

/* ================= RESPONSIVE DESIGN ================= */

/* Tablet Responsive */
@media (max-width: 1024px) {
    .subscription-details-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .usage-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .usage-item:nth-child(3) {
        grid-column: 1 / -1;
        max-width: 50%;
        margin: 0 auto;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .dashboard-billing {
        padding: 1.5rem;
    }
    
    .subscription-section,
    .credits-section,
    .billing-info-section {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .subscription-header,
    .credits-header,
    .billing-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .subscription-actions {
        width: 100%;
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .upgrade-plan-btn,
    .cancel-renewal-btn,
    .edit-billing-btn {
        width: 100%;
        justify-content: center;
    }
    
    .subscription-details-grid,
    .billing-details-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .usage-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .usage-item:nth-child(3) {
        grid-column: auto;
        max-width: none;
        margin: 0;
    }
    
    .credits-summary {
        text-align: left;
        width: 100%;
    }
    
    .credits-progress-container .flex {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }
}

/* Small Mobile */
@media (max-width: 480px) {
    .dashboard-billing {
        padding: 1rem;
    }
    
    .subscription-section,
    .credits-section,
    .billing-info-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .subscription-detail-item,
    .billing-detail-item,
    .usage-item {
        padding: 1rem;
    }
    
    .detail-header {
        margin-bottom: 0.75rem;
    }
    
    .detail-header p {
        font-size: 0.75rem;
    }
}

/* ================= ACCESSIBILITY ENHANCEMENTS ================= */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .subscription-detail-item,
    .billing-detail-item,
    .usage-item {
        border-width: 2px;
        border-color: currentColor;
    }
    
    .upgrade-plan-btn,
    .edit-billing-btn {
        border: 2px solid currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .subscription-detail-item,
    .billing-detail-item,
    .usage-item,
    .upgrade-plan-btn,
    .cancel-renewal-btn,
    .edit-billing-btn,
    .change-billing-btn {
        transition: none;
        animation: none;
    }
    
    .subscription-detail-item:hover,
    .billing-detail-item:hover,
    .usage-item:hover {
        transform: none;
    }
    
    .credits-progress-container .bg-gradient-to-r::after {
        animation: none;
    }
}

/* Focus states for keyboard navigation */
.upgrade-plan-btn:focus,
.cancel-renewal-btn:focus,
.edit-billing-btn:focus,
.change-billing-btn:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.2);
}

/* ================= END ENHANCED BILLING SECTION ================= */

/* ================= SUBSCRIPTION OVERLAY STYLES ================= */

/* Subscription Overlay Backdrop */
.subscription-overlay-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: overlayFadeIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.subscription-overlay-backdrop.opacity-0 {
    animation: overlayFadeOut 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Subscription Overlay Container */
.subscription-overlay-container {
    animation: overlaySlideIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.subscription-overlay-container.translate-y-4 {
    animation: overlaySlideOut 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Subscription Overlay Header */
.subscription-overlay-header {
    position: relative;
    z-index: 10;
}

.back-button {
    position: relative;
    z-index: 11;
}

.back-icon-container {
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.back-button:hover .back-icon-container {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Pricing Plans Grid - Force 3 columns in same row */
.pricing-plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    min-height: 510px; /* Decreased by 15% from 600px */
}

/* Pricing Plans Grid - Force 3 columns in same row */
.pricing-plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    align-items: stretch; /* Ensure all cards have equal height */
}

/* Pricing Plan Cards */
.pricing-plan-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.06) 0%, 
        rgba(255, 255, 255, 0.02) 50%, 
        rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 1.7rem; /* Decreased by 15% from 2rem */
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
}

/* Pricing Plan Cards */
.pricing-plan-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.06) 0%, 
        rgba(255, 255, 255, 0.02) 50%, 
        rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 1.75rem 1.5rem; /* Optimized padding for better proportion */
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 480px; /* Consistent minimum height for all cards */
}

/* Plan Content */
.plan-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* Plan Content */
.plan-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between; /* Distribute content evenly */
}

/* Plan Header - Left aligned */
.plan-header {
    text-align: left;
    margin-bottom: 2rem;
}

/* Plan Header - Left aligned */
.plan-header {
    text-align: left;
    margin-bottom: 1.5rem; /* Reduced margin for better proportion */
    flex-shrink: 0; /* Prevent header from shrinking */
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    flex-grow: 1;
}

.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0; /* Reduced margin */
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* Align features to top */
}

.plan-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    transition: all 0.1s ease;
}

.plan-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.4rem 0; /* Slightly reduced padding */
    transition: all 0.1s ease;
}

/* Plan Action Buttons */
.plan-action-button {
    width: 100%;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
}

/* Plan Action Buttons */
.plan-action-button {
    width: 100%;
    padding: 0.875rem 1.5rem; /* Slightly reduced padding */
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
    margin-top: auto; /* Push button to bottom */
    flex-shrink: 0; /* Prevent button from shrinking */
}

/* Responsive Design - Enhanced for mobile */
@media (max-width: 1200px) {
    .pricing-plans-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem; /* Increased gap for mobile portrait spacing */
        min-height: auto;
    }
    
    .modal-liquid-glass-container {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .modal-glass-header,
    .modal-glass-body,
    .modal-glass-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

/* Responsive Design - Enhanced for mobile */
@media (max-width: 1200px) {
    .pricing-plans-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        align-items: stretch;
    }
    
    .pricing-plan-card {
        min-height: auto; /* Remove fixed height on mobile */
        padding: 1.5rem 1.25rem; /* Consistent mobile padding */
    }
    
    .modal-liquid-glass-container {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .modal-glass-header,
    .modal-glass-body,
    .modal-glass-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 768px) {
    .pricing-plans-grid {
        gap: 3rem; /* Increased gap specifically for mobile portrait */
    }
    
    .modal-glass-header {
        padding: 1.5rem 1rem 1rem 1rem;
    }
    
    .modal-glass-body {
        padding: 1.5rem 1rem;
    }
    
    .modal-glass-footer {
        padding: 1rem 1rem 1.5rem 1rem;
    }
    
    .modal-glass-title {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }
    
    .modal-glass-subtitle {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .pricing-plan-card {
        padding: 1.1rem; /* Decreased by 15% from 1.25rem */
    }
    
    .plan-price {
        font-size: 2.6rem; /* Increased from 2.25rem */
    }
    
    .plan-period {
        font-size: 1rem;
    }
    
    .plan-name {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
    
    .plan-description {
        font-size: 0.9rem;
    }
    
    .feature-text {
        font-size: 0.8rem;
    }
    
    .plan-action-button {
        padding: 0.875rem 1.25rem;
        font-size: 0.95rem;
    }
    
    .footer-guarantees {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .guarantee-item {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .pricing-plans-grid {
        gap: 3rem;
    }
    
    .pricing-plan-card {
        padding: 1.25rem 1rem; /* Consistent mobile padding */
        min-height: auto;
    }
    
    .plan-header {
        margin-bottom: 1.25rem;
    }
    
    .plan-features {
        margin-bottom: 1.25rem;
    }
    
    .plan-feature {
        padding: 0.35rem 0;
    }
    
    .modal-glass-header {
        padding: 1.5rem 1rem 1rem 1rem;
    }
    
    .modal-glass-body {
        padding: 1.5rem 1rem;
    }
    
    .modal-glass-footer {
        padding: 1rem 1rem 1.5rem 1rem;
    }
    
    .modal-glass-title {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }
    
    .modal-glass-subtitle {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .plan-price {
        font-size: 2.6rem;
    }
    
    .plan-period {
        font-size: 1rem;
    }
    
    .plan-name {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
    
    .plan-description {
        font-size: 0.9rem;
    }
    
    .feature-text {
        font-size: 0.8rem;
    }
    
    .plan-action-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
    }
    
    .footer-guarantees {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .guarantee-item {
        font-size: 0.8rem;
    }
}



/* Modal Footer */
.modal-glass-footer {
    padding: 1.5rem 2.5rem 2rem 2.5rem;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.04) 0%, 
        rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.footer-guarantees {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.guarantee-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #94a3b8;
    transition: all 0.2s ease;
}

.guarantee-item:hover {
    color: #e2e8f0;
    transform: translateY(-1px);
}

.guarantee-icon {
    font-size: 1rem;
    color: #10b981;
}

.guarantee-text {
    font-weight: 500;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .modal-liquid-glass-container,
    .pricing-plan-card,
    .plan-action-button,
    .guarantee-item {
        transition: none;
        animation: none;
    }
    
    .pricing-plan-card:hover,
    .plan-action-button:hover,
    .guarantee-item:hover {
        transform: none;
    }
    
    @keyframes modalFadeIn {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    
    @keyframes modalFadeOut {
        0% { opacity: 1; }
        100% { opacity: 0; }
    }
    
    @keyframes modalContentFadeIn {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    
    @keyframes modalContentFadeOut {
        0% { opacity: 1; }
        100% { opacity: 0; }
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .modal-liquid-glass-container {
        border: 2px solid #ffffff;
        background: rgba(0, 0, 0, 0.95);
    }
    
    .pricing-plan-card {
        border: 2px solid #ffffff;
        background: rgba(30, 30, 30, 0.95);
    }
    
    .plan-action-button.standard {
        border: 2px solid #ffffff;
        background: rgba(50, 50, 50, 0.95);
    }
}

/* ================= END MACOS LIQUID GLASS PRICING MODAL ================= */

/* ================= MACOS LIQUID GLASS PRICING MODAL ================= */

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

/* Modal Container */
.modal-liquid-glass-container {
    position: relative;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.12) 0%, 
        rgba(255, 255, 255, 0.06) 50%, 
        rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 24px;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    box-shadow: 
        0 32px 64px -12px rgba(0, 0, 0, 0.35),
        0 8px 16px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

/* Modal Header */
.modal-glass-header {
    padding: 2rem 2.5rem 1.5rem 2.5rem;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.06) 0%, 
        rgba(255, 255, 255, 0.03) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.modal-glass-header-content {
    text-align: left;
    max-width: 100%;
    margin: 0;
}

.modal-glass-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    letter-spacing: -0.025em;
}

.modal-glass-subtitle {
    font-size: 1.125rem;
    font-weight: 400;
    color: #94a3b8;
    line-height: 1.6;
    margin: 0;
}

/* Close Button */
.modal-glass-close-button {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.08) 0%, 
        rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: 10;
}

.modal-glass-close-button:hover {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.12) 0%, 
        rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.modal-glass-close-button .iconify {
    font-size: 1.25rem;
    color: #e2e8f0;
}

/* Modal Body */
.modal-glass-body {
    padding: 2rem 2.5rem;
    overflow-y: auto;
    max-height: calc(90vh - 200px);
}

/* Pricing Plans Grid */
.pricing-plans-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    align-items: stretch;
}

/* Pricing Plan Cards */
.pricing-plan-card {
    position: relative;
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.06) 0%, 
        rgba(255, 255, 255, 0.02) 50%, 
        rgba(255, 255, 255, 0.01) 100%);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 20px;
    padding: 1.75rem 1.5rem;
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 480px;
}

.pricing-plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.pricing-plan-card:hover {
    transform: translateY(-2px) scale(1.01);
    background: linear-gradient(145deg, 
        rgba(255, 255, 255, 0.08) 0%, 
        rgba(255, 255, 255, 0.04) 50%, 
        rgba(255, 255, 255, 0.02) 100%);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 
        0 12px 24px -8px rgba(0, 0, 0, 0.25),
        0 4px 8px rgba(0, 0, 0, 0.12);
}

.pricing-plan-card:hover::before {
    opacity: 1;
}

/* Plan Content */
.plan-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;
}

/* Plan Header */
.plan-header {
    text-align: left;
    margin-bottom: 1.5rem;
    flex-shrink: 0;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.plan-price-container {
    display: flex;
    align-items: baseline;
    justify-content: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.plan-price {
    font-size: 3.5rem;
    font-weight: 800;
    color: #ffffff;
    line-height: 1;
    letter-spacing: -0.05em;
}

.plan-period {
    font-size: 1.125rem;
    color: #94a3b8;
    font-weight: 500;
}

.plan-description {
    font-size: 1rem;
    color: #94a3b8;
    line-height: 1.5;
    margin: 0;
}

/* Plan Features */
.plan-features {
    list-style: none;
    padding: 0;
    margin: 0 0 1.5rem 0;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.plan-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.4rem 0;
    transition: all 0.1s ease;
}

.plan-feature:hover {
    transform: translateX(2px);
}

.feature-icon {
    font-size: 1.125rem;
    color: #10b981;
    flex-shrink: 0;
}

.feature-text {
    font-size: 0.875rem;
    color: #e2e8f0;
    line-height: 1.5;
}

/* Plan Action Buttons */
.plan-action-button {
    width: 100%;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
    margin-top: auto;
    flex-shrink: 0;
}

.plan-action-button.standard {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.08) 0%, 
        rgba(255, 255, 255, 0.04) 100%);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #ffffff;
}

.plan-action-button.standard:hover {
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.12) 0%, 
        rgba(255, 255, 255, 0.08) 100%);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.plan-action-button.highlighted {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.plan-action-button.highlighted:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: translateY(-1px) scale(1.01);
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.35);
}

.plan-action-button.disabled {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    color: #64748b;
    cursor: not-allowed;
}

.plan-action-button:active:not(.disabled) {
    transform: translateY(0) scale(0.99);
}

/* Plan Badges */
.plan-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.plan-badge-text {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.plan-badge.most-popular .plan-badge-text {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: #ffffff;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.plan-badge.current-plan-badge .plan-badge-text {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

/* Highlighted Plan */
.pricing-plan-card.highlighted-plan {
    background: linear-gradient(145deg, 
        rgba(139, 92, 246, 0.12) 0%, 
        rgba(139, 92, 246, 0.06) 50%, 
        rgba(139, 92, 246, 0.03) 100%);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 
        0 20px 40px -8px rgba(139, 92, 246, 0.2),
        0 8px 16px rgba(0, 0, 0, 0.15);
}

.pricing-plan-card.highlighted-plan:hover {
    background: linear-gradient(145deg, 
        rgba(139, 92, 246, 0.15) 0%, 
        rgba(139, 92, 246, 0.08) 50%, 
        rgba(139, 92, 246, 0.04) 100%);
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 
        0 16px 32px -8px rgba(139, 92, 246, 0.22),
        0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Current Plan */
.pricing-plan-card.current-plan {
    background: linear-gradient(145deg, 
        rgba(16, 185, 129, 0.08) 0%, 
        rgba(16, 185, 129, 0.04) 50%, 
        rgba(16, 185, 129, 0.02) 100%);
    border-color: rgba(16, 185, 129, 0.25);
}

/* Modal Footer */
.modal-glass-footer {
    padding: 1.5rem 2.5rem 2rem 2.5rem;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.04) 0%, 
        rgba(255, 255, 255, 0.02) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.footer-guarantees {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.guarantee-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #94a3b8;
    transition: all 0.2s ease;
}

.guarantee-item:hover {
    color: #e2e8f0;
    transform: translateY(-1px);
}

.guarantee-icon {
    font-size: 1rem;
    color: #10b981;
}

.guarantee-text {
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .pricing-plans-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        align-items: stretch;
    }
    
    .pricing-plan-card {
        min-height: auto;
        padding: 1.5rem 1.25rem;
    }
    
    .modal-liquid-glass-container {
        max-width: 95vw;
        margin: 1rem;
    }
    
    .modal-glass-header,
    .modal-glass-body,
    .modal-glass-footer {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }
}

@media (max-width: 768px) {
    .pricing-plans-grid {
        gap: 3rem;
    }
    
    .pricing-plan-card {
        padding: 1.25rem 1rem;
        min-height: auto;
    }
    
    .plan-header {
        margin-bottom: 1.25rem;
    }
    
    .plan-features {
        margin-bottom: 1.25rem;
    }
    
    .plan-feature {
        padding: 0.35rem 0;
    }
    
    .modal-glass-header {
        padding: 1.5rem 1rem 1rem 1rem;
    }
    
    .modal-glass-body {
        padding: 1.5rem 1rem;
    }
    
    .modal-glass-footer {
        padding: 1rem 1rem 1.5rem 1rem;
    }
    
    .modal-glass-title {
        font-size: 1.75rem;
        margin-bottom: 0.5rem;
    }
    
    .modal-glass-subtitle {
        font-size: 0.95rem;
        line-height: 1.5;
    }
    
    .plan-price {
        font-size: 2.6rem;
    }
    
    .plan-period {
        font-size: 1rem;
    }
    
    .plan-name {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
    
    .plan-description {
        font-size: 0.9rem;
    }
    
    .feature-text {
        font-size: 0.8rem;
    }
    
    .plan-action-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
    }
    
    .footer-guarantees {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .guarantee-item {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .pricing-plans-grid {
        gap: 3.5rem;
    }
    
    .pricing-plan-card {
        padding: 1rem 0.875rem;
        min-height: auto;
        margin-bottom: .95rem;
    }
    
    .plan-header {
        margin-bottom: 1rem;
    }
    
    .plan-features {
        margin-bottom: 1rem;
    }
    
    .plan-feature {
        padding: 0.4rem 0;
    }
    
    .modal-liquid-glass-container {
        margin: 0.5rem;
        border-radius: 20px;
    }
    
    .modal-glass-header {
        padding: 1rem 0.75rem 0.75rem 0.75rem;
    }
    
    .modal-glass-body {
        padding: 1rem 0.75rem;
    }
    
    .modal-glass-footer {
        padding: 0.75rem 0.75rem 1rem 0.75rem;
    }
    
    .modal-glass-close-button {
        top: 1rem;
        right: 1rem;
        width: 36px;
        height: 36px;
    }
    
    .modal-glass-title {
        font-size: 1.5rem;
    }
    
    .modal-glass-subtitle {
        font-size: 0.875rem;
    }
    
    .plan-price {
        font-size: 2.3rem;
    }
    
    .plan-name {
        font-size: 1.125rem;
    }
    
    .feature-icon {
        font-size: 1rem;
    }
    
    .feature-text {
        font-size: 0.95rem;
    }
    
    .plan-action-button {
        padding: 0.65rem 1rem;
        font-size: 0.875rem;
    }
}

/* Animations */
@keyframes modalFadeIn {
    0% {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
    100% {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
}

@keyframes modalFadeOut {
    0% {
        opacity: 1;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
    }
    100% {
        opacity: 0;
        backdrop-filter: blur(0px);
        -webkit-backdrop-filter: blur(0px);
    }
}

@keyframes modalContentFadeIn {
    0% {
        opacity: 0;
        transform: perspective(1000px) rotateX(10deg) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: perspective(1000px) rotateX(0deg) scale(1);
    }
}

@keyframes modalContentFadeOut {
    0% {
        opacity: 1;
        transform: perspective(1000px) rotateX(0deg) scale(1);
    }
    100% {
        opacity: 0;
        transform: perspective(1000px) rotateX(-10deg) scale(0.95);
    }
}

/* Animation Classes */
.modal-fade-in {
    animation: modalFadeIn 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

.modal-fade-out {
    animation: modalFadeOut 0.25s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

.modal-content-fade-in {
    animation: modalContentFadeIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) 0.1s forwards;
}

.modal-content-fade-out {
    animation: modalContentFadeOut 0.3s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .modal-liquid-glass-container,
    .pricing-plan-card,
    .plan-action-button,
    .guarantee-item {
        transition: none;
        animation: none;
    }
    
    .pricing-plan-card:hover,
    .plan-action-button:hover,
    .guarantee-item:hover {
        transform: none;
    }
    
    @keyframes modalFadeIn {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    
    @keyframes modalFadeOut {
        0% { opacity: 1; }
        100% { opacity: 0; }
    }
    
    @keyframes modalContentFadeIn {
        0% { opacity: 0; }
        100% { opacity: 1; }
    }
    
    @keyframes modalContentFadeOut {
        0% { opacity: 1; }
        100% { opacity: 0; }
    }
}

@media (prefers-contrast: high) {
    .modal-liquid-glass-container {
        border: 2px solid #ffffff;
        background: rgba(0, 0, 0, 0.95);
    }
    
    .pricing-plan-card {
        border: 2px solid #ffffff;
        background: rgba(30, 30, 30, 0.95);
    }
    
    .plan-action-button.standard {
        border: 2px solid #ffffff;
        background: rgba(50, 50, 50, 0.95);
    }
}

/* ================= END MACOS LIQUID GLASS PRICING MODAL ================= */

/* All form inputs have consistent height */

/* SearchableCountrySelect Component Styles */
.searchable-country-select {
    position: relative;
    width: 100%;
}

.searchable-country-select .dropdown-container {
    position: absolute;
    z-index: 50;
    width: 100%;
    margin-top: 0.25rem;
    background-color: #374151;
    border: 1px solid #4b5563;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-height: 15rem;
    overflow: hidden;
}

.searchable-country-select .search-input {
    width: 100%;
    padding: 0.75rem;
    background-color: #4b5563;
    border: 1px solid #6b7280;
    border-radius: 0.375rem;
    color: #ffffff;
    font-size: 0.875rem;
}

.searchable-country-select .search-input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 1px #8b5cf6;
}

.searchable-country-select .countries-list {
    max-height: 12rem;
    overflow-y: auto;
}

.searchable-country-select .country-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
    color: #d1d5db;
}

.searchable-country-select .country-option:hover,
.searchable-country-select .country-option.highlighted {
    background-color: #8b5cf6;
    color: #ffffff;
}

.searchable-country-select .country-option mark {
    background-color: rgba(147, 51, 234, 0.3);
    color: #ffffff;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
}
