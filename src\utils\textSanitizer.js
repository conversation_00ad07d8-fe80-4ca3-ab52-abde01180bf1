/**
 * Text Overlay Safe Character Sanitization System
 * 
 * This utility provides comprehensive text sanitization for YouTube thumbnail overlay text
 * to ensure compatibility with image generation APIs and prevent rendering issues.
 * 
 * @version 1.0.0
 * @description Removes special characters that can cause problems in image generation
 */

/**
 * Sanitizes overlay text by removing potentially problematic characters
 * while preserving the essential meaning and readability of the text.
 * 
 * @param {string} text - The raw overlay text to sanitize
 * @param {Object} options - Optional sanitization configuration
 * @param {boolean} options.preserveSpaces - Whether to preserve multiple spaces (default: false)
 * @param {boolean} options.removeEmojis - Whether to remove emoji characters (default: true)
 * @param {boolean} options.removeNumbers - Whether to remove numbers (default: false)
 * @param {boolean} options.convertToUppercase - Whether to convert to uppercase (default: true)
 * @returns {string} - The sanitized text safe for image generation
 */
export function sanitizeOverlayText(text, options = {}) {
    // Default options
    const {
        preserveSpaces = false,
        removeEmojis = true,
        removeNumbers = false,
        convertToUppercase = true
    } = options;

    if (!text || typeof text !== 'string') {
        return '';
    }

    let sanitized = text;

    // Remove problematic special characters that can interfere with image generation
    const specialCharsPattern = /[()[\]{}<>|\\`~@#$%^&*+=]/g;
    sanitized = sanitized.replace(specialCharsPattern, '');

    // Remove quotes that can break prompt parsing
    const quotesPattern = /["""''`]/g;
    sanitized = sanitized.replace(quotesPattern, '');

    // Remove mathematical and currency symbols
    const mathCurrencyPattern = /[÷×±∞€£¥₹$¢]/g;
    sanitized = sanitized.replace(mathCurrencyPattern, '');

    // Remove emoji characters if enabled
    if (removeEmojis) {
        // Comprehensive emoji pattern including skin tone modifiers
        const emojiPattern = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}-\u{2454}]|[\u{20D0}-\u{20FF}]|[\u{FE0F}]|[\u{200D}]|[\u{1F3FB}-\u{1F3FF}]/gu;
        sanitized = sanitized.replace(emojiPattern, '');
    }

    // Remove numbers if enabled
    if (removeNumbers) {
        sanitized = sanitized.replace(/[0-9]/g, '');
    }

    // Clean up punctuation - keep essential ones, remove problematic ones
    // Keep: ! ? . , : ; - 
    // Remove: everything else
    const problematicPunctuationPattern = /[^\w\s!?.,;:\-]/g;
    sanitized = sanitized.replace(problematicPunctuationPattern, '');

    // Normalize whitespace
    if (!preserveSpaces) {
        // Replace multiple spaces with single space
        sanitized = sanitized.replace(/\s+/g, ' ');
    }

    // Remove leading/trailing whitespace
    sanitized = sanitized.trim();

    // Convert to uppercase if enabled (default for YouTube thumbnails)
    if (convertToUppercase) {
        sanitized = sanitized.toUpperCase();
    }

    // Final cleanup: remove any remaining problematic characters
    // This is a safety net for any characters that might have been missed
    const finalCleanupPattern = /[^\w\s!?.,;:\-]/g;
    sanitized = sanitized.replace(finalCleanupPattern, '');

    return sanitized;
}

/**
 * Advanced sanitization with custom character replacement
 * Allows for more nuanced text transformation while maintaining readability
 * 
 * @param {string} text - The raw overlay text to sanitize
 * @param {Object} replacements - Custom character replacement mapping
 * @returns {string} - The sanitized text with custom replacements
 */
export function sanitizeWithReplacements(text, replacements = {}) {
    if (!text || typeof text !== 'string') {
        return '';
    }

    // Default smart replacements for common problematic characters
    const defaultReplacements = {
        // Brackets and parentheses → nothing or spaces
        '(': ' ',
        ')': ' ',
        '[': ' ',
        ']': ' ',
        '{': ' ',
        '}': ' ',
        '<': ' ',
        '>': ' ',
        
        // Common symbols → words or removal
        '&': 'AND',
        '+': 'PLUS',
        '@': 'AT',
        '#': '',
        '%': 'PERCENT',
        
        // Quotes → nothing
        '"': '',
        "'": '',
        '`': '',
        '\u201C': '', // left double quotation mark
        '\u201D': '', // right double quotation mark
        '\u2018': '', // left single quotation mark
        '\u2019': '', // right single quotation mark
        
        // Mathematical symbols → words
        '÷': 'DIVIDED BY',
        '×': 'TIMES',
        '=': 'EQUALS',
        
        // Currency symbols → words
        '$': 'DOLLAR',
        '€': 'EURO',
        '£': 'POUND',
        '¥': 'YEN'
    };

    // Merge default replacements with custom ones
    const allReplacements = { ...defaultReplacements, ...replacements };

    let sanitized = text;

    // Apply all replacements
    Object.entries(allReplacements).forEach(([char, replacement]) => {
        const regex = new RegExp(escapeRegExp(char), 'g');
        sanitized = sanitized.replace(regex, replacement);
    });

    // Clean up multiple spaces and normalize
    sanitized = sanitized.replace(/\s+/g, ' ').trim().toUpperCase();

    return sanitized;
}

/**
 * Validates if text is safe for image generation after sanitization
 * 
 * @param {string} text - The text to validate
 * @returns {Object} - Validation result with isValid boolean and issues array
 */
export function validateSanitizedText(text) {
    const issues = [];
    
    if (!text || text.trim() === '') {
        issues.push('Text is empty after sanitization');
        return { isValid: false, issues };
    }

    // Check for remaining problematic characters
    const problematicChars = /[()[\]{}<>|\\`~@#$%^&*+="""'']/g;
    const foundProblematic = text.match(problematicChars);
    if (foundProblematic) {
        issues.push(`Contains problematic characters: ${foundProblematic.join(', ')}`);
    }

    // Check text length (should be reasonable for thumbnail overlay)
    if (text.length > 100) {
        issues.push('Text is too long for effective thumbnail overlay (>100 characters)');
    }

    // Check for excessive punctuation
    const punctuationCount = (text.match(/[!?.,;:]/g) || []).length;
    const wordCount = text.split(/\s+/).length;
    if (punctuationCount > wordCount) {
        issues.push('Excessive punctuation relative to word count');
    }

    return {
        isValid: issues.length === 0,
        issues
    };
}

/**
 * Utility function to escape special regex characters
 * 
 * @param {string} string - String to escape
 * @returns {string} - Escaped string safe for regex
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Preset sanitization profiles for different use cases
 */
export const SANITIZATION_PRESETS = {
    // Maximum safety - removes almost everything except basic alphanumeric and essential punctuation
    ULTRA_SAFE: {
        preserveSpaces: false,
        removeEmojis: true,
        removeNumbers: false,
        convertToUppercase: true
    },
    
    // Balanced approach - keeps some characters but removes problematic ones
    BALANCED: {
        preserveSpaces: false,
        removeEmojis: true,
        removeNumbers: false,
        convertToUppercase: true
    },
    
    // Minimal sanitization - only removes the most problematic characters
    MINIMAL: {
        preserveSpaces: true,
        removeEmojis: false,
        removeNumbers: false,
        convertToUppercase: false
    }
};

/**
 * Quick sanitization using a preset profile
 * 
 * @param {string} text - The text to sanitize
 * @param {string} preset - The preset name from SANITIZATION_PRESETS
 * @returns {string} - Sanitized text
 */
export function sanitizeWithPreset(text, preset = 'BALANCED') {
    const options = SANITIZATION_PRESETS[preset] || SANITIZATION_PRESETS.BALANCED;
    return sanitizeOverlayText(text, options);
}

// Default export for backwards compatibility
export default sanitizeOverlayText; 