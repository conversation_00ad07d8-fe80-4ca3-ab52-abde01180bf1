/**
 * A simple debounce function that delays invoking a function until after `delay`
 * milliseconds have elapsed since the last time the debounced function was invoked.
 * @param {Function} func The function to debounce.
 * @param {number} delay The number of milliseconds to delay.
 * @returns {Function} Returns the new debounced function.
 */
export const debounce = (func, delay) => {
    let timeoutId;

    return function(...args) {
        // Clear the previous timeout.
        clearTimeout(timeoutId);

        // Set a new timeout.
        timeoutId = setTimeout(() => {
            func.apply(this, args);
        }, delay);
    };
}; 