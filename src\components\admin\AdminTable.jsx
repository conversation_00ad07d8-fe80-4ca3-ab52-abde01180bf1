const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;

export const AdminTable = ({ columns, data, emptyMessage, tableId }) => {
    if (!data || data.length === 0) {
        return React.createElement('div', {
            className: 'admin-table-empty-state bg-gray-800 border border-gray-700 rounded-lg p-12 text-center',
            id: `${tableId}-empty-state`
        },
            React.createElement('svg', {
                className: 'admin-table-empty-icon w-16 h-16 text-gray-500 mx-auto mb-4',
                fill: 'none',
                stroke: 'currentColor',
                viewBox: '0 0 24 24'
            },
                React.createElement('path', {
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round',
                    strokeWidth: 2,
                    d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
                })
            ),
            React.createElement('h3', {
                className: 'admin-table-empty-title text-lg font-medium text-gray-400 mb-2'
            }, 'No Data Found'),
            React.createElement('p', {
                className: 'admin-table-empty-message text-gray-500'
            }, emptyMessage || 'No items to display.')
        );
    }

    return React.createElement('div', {
        className: 'admin-table-container bg-gray-800 border border-gray-700 rounded-lg overflow-hidden',
        id: `${tableId}-container`
    },
        React.createElement('div', {
            className: 'admin-table-wrapper overflow-x-auto'
        },
            React.createElement('table', {
                className: 'admin-table w-full',
                id: tableId
            },
                // Table Header
                React.createElement('thead', {
                    className: 'admin-table-header bg-gray-700'
                },
                    React.createElement('tr', {
                        className: 'admin-table-header-row'
                    }, columns.map(column => 
                        React.createElement('th', {
                            key: column.key,
                            className: 'admin-table-header-cell px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider',
                            id: `${tableId}-header-${column.key}`
                        }, column.label)
                    ))
                ),
                
                // Table Body
                React.createElement('tbody', {
                    className: 'admin-table-body bg-gray-800 divide-y divide-gray-700'
                }, data.map((item, index) => 
                    React.createElement('tr', {
                        key: item.id || index,
                        className: 'admin-table-row hover:bg-gray-750 transition-colors',
                        id: `${tableId}-row-${item.id || index}`
                    }, columns.map(column => 
                        React.createElement('td', {
                            key: column.key,
                            className: 'admin-table-cell px-6 py-4 whitespace-nowrap',
                            id: `${tableId}-cell-${item.id || index}-${column.key}`
                        }, column.render ? column.render(item) : item[column.key])
                    ))
                ))
            )
        )
    );
}; 