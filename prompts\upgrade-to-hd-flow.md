# upgrade-to-hd-flow 

# 🔁 Feature Spec: Upgrade Thumbnail to High Quality (One-Step MVP Flow)

## 🎯 Goal
Allow users to generate a thumbnail using low-cost GPT-image-1 mode, preview the result, and optionally **upgrade it to high-quality rendering** with one click — using the same prompt and controls.

This avoids extra phases or manual repetition, streamlining the user experience while optimizing OpenAI API credit usage.

---

## ✅ UX Overview: One-Step Flow

1. User enters prompt + toggles (person, text overlay, mood, etc.)
2. App generates thumbnail via `gpt-image-1` in low-cost config
3. Thumbnail is previewed
4. User clicks: **🔁 Upgrade to HD**
5. App reuses the **exact same prompt**, but switches to:
   - `quality: "hd"`
   - Optionally adds richer visual instructions (cinematic light, sharp contrast)
6. HD image replaces or overlays the draft — seamlessly.

---

## 🧠 Prompt Generation Notes

- Save the **entire prompt and config state** after low-res render
- Do **not alter** the base layout/structure when re-rendering
- You may inject additional style details like:

```plaintext
Now render this in cinematic quality with high detail, dramatic lighting, and full-edge framing. Maintain the same composition and subject placement.

🔧 API Integration (GPT-image-1)

🔹 Step 1: Low-Res Render (Default)
POST /v1/images/generations
{
  "model": "gpt-image-1",
  "prompt": "<finalPrompt>",
  "quality": "standard",
  "size": "1280x720",
  "n": 1
}

🔸 Step 2: Upgrade to HD

POST /v1/images/generations
{
  "model": "gpt-image-1",
  "prompt": "<sameFinalPrompt>",
  "quality": "hd",
  "size": "1280x720",
  "n": 1
}

✅ Make sure to:

Store finalPrompt and all UI values in a state object (promptState)
Disable prompt regeneration during HD upgrade unless user edits
🧩 UI Components

🖼️ ImagePreview component: display initial low-res image
🔁 Button: Upgrade to HD (visible after image loads)
Tooltip: “Render this same thumbnail in high-quality mode”
Optional loading spinner or overlay state
✅ Advantages

Zero cognitive friction for users
Encourages creative testing without cost anxiety
Uses token-efficient entry point and opt-in for quality boost
Looks professional and responsive
🚫 Do Not Use

No dall-e-3 model
No external image-to-image tools
No prompt mutation or remix on HD step
📦 Optional Extension

Add a user toggle in settings:
[ ] Always use HD output by default
…for creators who prefer full-quality at all times.

