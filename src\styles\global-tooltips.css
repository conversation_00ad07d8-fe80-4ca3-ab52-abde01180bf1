/* global-tooltips.css - macOS Liquid Glass Tooltip System */

/* ================= CSS CUSTOM PROPERTIES ================= */
:root {
  /* Liquid Glass Theme Colors */
  --tooltip-glass-bg: rgba(45, 55, 75, 0.95);
  --tooltip-glass-border: rgba(148, 163, 184, 0.3);
  --tooltip-glass-shadow: rgba(0, 0, 0, 0.3);
  --tooltip-text-primary: rgba(255, 255, 255, 0.95);
  --tooltip-text-secondary: rgba(148, 163, 184, 0.9);
  
  /* Enhanced Animation Variables */
  --tooltip-transition-duration: 300ms;
  --tooltip-easing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --tooltip-blur-amount: 16px;
  
  /* Precise Positioning Variables */
  --tooltip-offset: 12px;
  --tooltip-arrow-size: 8px;
  --tooltip-min-edge-distance: 16px;
  
  /* Responsive Sizing */
  --tooltip-font-size: 13px;
  --tooltip-padding: 14px 18px;
  --tooltip-border-radius: 12px;
  --tooltip-max-width: 320px;
  --tooltip-min-width: 180px;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  :root {
    --tooltip-font-size: 12px;
    --tooltip-padding: 12px 16px;
    --tooltip-max-width: 280px;
    --tooltip-min-width: 160px;
    --tooltip-offset: 10px;
  }
}

/* ================= ENHANCED TOOLTIP CONTAINER ================= */

/* Primary tooltip wrapper - ensures proper stacking and isolation */
.tooltip-icon-wrapper {
  position: relative;
  display: inline-block;
  z-index: 1;
}

/* Fixed positioning tooltip container */
.tooltip-fixed-content {
  /* Positioning & Layout */
  position: fixed !important;
  z-index: 10000 !important;
  pointer-events: none !important;
  
  /* Enhanced Glass Morphism */
  background: var(--tooltip-glass-bg) !important;
  backdrop-filter: blur(var(--tooltip-blur-amount)) saturate(180%) !important;
  -webkit-backdrop-filter: blur(var(--tooltip-blur-amount)) saturate(180%) !important;
  border: 1px solid var(--tooltip-glass-border) !important;
  box-shadow: 
    0 8px 32px var(--tooltip-glass-shadow),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  
  /* Typography & Content */
  color: var(--tooltip-text-primary) !important;
  font-size: var(--tooltip-font-size) !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  letter-spacing: -0.01em !important;
  
  /* Dimensions & Spacing */
  padding: var(--tooltip-padding) !important;
  border-radius: var(--tooltip-border-radius) !important;
  min-width: var(--tooltip-min-width) !important;
  max-width: var(--tooltip-max-width) !important;
  width: max-content !important;
  
  /* Smooth Animations */
  transition: 
    opacity var(--tooltip-transition-duration) var(--tooltip-easing),
    visibility var(--tooltip-transition-duration) var(--tooltip-easing),
    transform var(--tooltip-transition-duration) var(--tooltip-easing) !important;
  transform-origin: center bottom !important;
  
  /* Enhanced Typography */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', system-ui, sans-serif !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* ================= ENHANCED TOOLTIP STATES ================= */

/* Visible state with smooth scale-in animation */
.tooltip-fixed-content[style*="visibility: visible"] {
  opacity: 1 !important;
  visibility: visible !important;
  transform: scale(1) translateY(0) !important;
  animation: tooltipFadeIn var(--tooltip-transition-duration) var(--tooltip-easing) !important;
}

/* Closing state with smooth scale-out animation */
.tooltip-fixed-content.tooltip-closing {
  opacity: 0 !important;
  visibility: hidden !important;
  transform: scale(0.95) translateY(-4px) !important;
  animation: tooltipFadeOut var(--tooltip-transition-duration) var(--tooltip-easing) !important;
}

/* ================= ENHANCED TOOLTIP ARROWS ================= */

/* Base arrow styling with glass effect */
.tooltip-arrow-fixed {
  position: absolute !important;
  width: var(--tooltip-arrow-size) !important;
  height: var(--tooltip-arrow-size) !important;
  background: var(--tooltip-glass-bg) !important;
  border: 1px solid var(--tooltip-glass-border) !important;
  backdrop-filter: blur(var(--tooltip-blur-amount)) !important;
  -webkit-backdrop-filter: blur(var(--tooltip-blur-amount)) !important;
  transform: rotate(45deg) !important;
  z-index: -1 !important;
}

/* Precise arrow positioning for each placement */

/* Arrow for bottom placement (tooltip below icon) */
.tooltip-arrow-top {
  top: calc(-1 * var(--tooltip-arrow-size) / 2) !important;
  left: 50% !important;
  transform: translateX(-50%) rotate(45deg) !important;
  border-bottom: none !important;
  border-right: none !important;
}

/* Arrow for top placement (tooltip above icon) */
.tooltip-arrow-bottom {
  bottom: calc(-1 * var(--tooltip-arrow-size) / 2) !important;
  left: 50% !important;
  transform: translateX(-50%) rotate(45deg) !important;
  border-top: none !important;
  border-left: none !important;
}

/* Arrow for right placement (tooltip to the right of icon) */
.tooltip-arrow-left {
  left: calc(-1 * var(--tooltip-arrow-size) / 2) !important;
  top: 50% !important;
  transform: translateY(-50%) rotate(45deg) !important;
  border-top: none !important;
  border-right: none !important;
}

/* Arrow for left placement (tooltip to the left of icon) */
.tooltip-arrow-right {
  right: calc(-1 * var(--tooltip-arrow-size) / 2) !important;
  top: 50% !important;
  transform: translateY(-50%) rotate(45deg) !important;
  border-bottom: none !important;
  border-left: none !important;
}

/* ================= ENHANCED TOOLTIP ICON ================= */

/* Tooltip trigger icon with improved hover states */
.tooltip-icon {
  width: 16px !important;
  height: 16px !important;
  color: rgba(156, 163, 175, 0.8) !important; /* gray-400 with opacity */
  cursor: help !important;
  transition: all 200ms var(--tooltip-easing) !important;
  outline: none !important;
  margin-top:-5px;
  border-radius: 4px !important;
}

/* Enhanced hover and focus states */
.tooltip-icon:hover {
  color: rgba(168, 85, 247, 0.9) !important; /* purple-500 */
  transform: scale(1.1) !important;
}

.tooltip-icon:focus {
  color: rgba(168, 85, 247, 1) !important;
  box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.3) !important;
  transform: scale(1.1) !important;
}

/* ================= ENHANCED ANIMATIONS ================= */

/* Smooth fade-in with scale animation */
@keyframes tooltipFadeIn {
  0% {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.9) translateY(-8px);
  }
  100% {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(0);
  }
}

/* Smooth fade-out with scale animation */
@keyframes tooltipFadeOut {
  0% {
    opacity: 1;
    visibility: visible;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95) translateY(-4px);
  }
}

/* ================= PRECISE POSITIONING CLASSES ================= */

/* Force tooltip to appear below icon (preferred placement) */
.tooltip-placement-bottom {
  /* Calculated positioning will be applied via JavaScript */
}

/* Force tooltip to appear above icon (fallback) */
.tooltip-placement-top {
  /* Calculated positioning will be applied via JavaScript */
}

/* Force tooltip to appear to the right of icon (fallback) */
.tooltip-placement-right {
  /* Calculated positioning will be applied via JavaScript */
}

/* Force tooltip to appear to the left of icon (fallback) */
.tooltip-placement-left {
  /* Calculated positioning will be applied via JavaScript */
}

/* ================= RESPONSIVE & ACCESSIBILITY ================= */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tooltip-fixed-content {
    background: #000000 !important;
    border: 2px solid #FFFFFF !important;
    color: #FFFFFF !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
  
  .tooltip-arrow-fixed {
    background: #000000 !important;
    border-color: #FFFFFF !important;
  }
  
  .tooltip-icon:hover {
    color: #FFFF00 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tooltip-fixed-content,
  .tooltip-icon,
  .tooltip-arrow-fixed {
    transition: none !important;
    animation: none !important;
  }
  
  .tooltip-fixed-content[style*="visibility: visible"] {
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
  }
  
  .tooltip-icon:hover,
  .tooltip-icon:focus {
    transform: none !important;
  }
}

/* ================= CONTAINER OVERFLOW FIXES ================= */

/* Ensure tooltips aren't clipped by parent containers */
.tooltip-icon-container,
.tooltip-icon-wrapper,
.face-upload-section, 
.person-settings-container,
.control-panel-main-container,
.premade-templates-section,
.templates-tab-content,
.toggle-label-group {
  overflow: visible !important;
}

/* Ensure z-index stacking for complex layouts */
.tooltip-icon-wrapper:hover {
  z-index: 10001 !important;
}

/* ================= TOOLTIP TEXT CONTENT ================= */

/* Ensure tooltip text is properly styled */
.tooltip-fixed-content p {
  margin: 0 !important;
  padding: 0 !important;
  color: var(--tooltip-text-primary) !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  word-wrap: break-word !important;
  hyphens: auto !important;
}

/* ================= LEGACY COMPATIBILITY ================= */

/* Hide any remaining tooltips or info icons in templates section */
#premade-templates-info,
[data-tooltip-id="premade-templates-info"],
.premade-templates-section .info-icon,
.premade-templates-section [data-icon="solar:info-circle-linear"],
.premade-templates-section [data-icon="solar:info-circle-bold"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}

/* Hide any tooltip containers that might be floating */
[id*="premade-templates-info"],
[class*="premade-templates-info"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Color tooltip arrow positioning (existing functionality) */
.color-tooltip.absolute.left-1\/2::after {
  left: 50% !important;
  transform: translateX(-50%) rotate(45deg) !important;
}

.color-tooltip.absolute.left-0::after {
  left: 16px !important;
  transform: rotate(45deg) !important;
}

.color-tooltip.absolute.right-0::after {
  right: 16px !important;
  transform: rotate(45deg) !important;
} 