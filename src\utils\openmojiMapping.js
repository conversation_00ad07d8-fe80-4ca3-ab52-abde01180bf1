// OpenMoji mapping for consistent cross-platform emoji rendering
// Using OpenMoji's hex codes from their library - ALWAYS SVG for crisp quality

// Using the latest stable version of OpenMoji for best quality
export const OPENMOJI_CDN_BASE = 'https://cdn.jsdelivr.net/gh/hfg-gmuend/openmoji@15/color/svg/';

// Alternative CDN for fallback
export const OPENMOJI_CDN_FALLBACK = 'https://openmoji.org/data/color/svg/';

// Mood & Expression emoji mappings
export const moodEmojiMapping = {
  'Default': {
    unicode: '🙂',
    openmojiHex: '1F642',
    label: 'Default',
    ariaLabel: 'slightly smiling face'
  },
  'Happy': {
    unicode: '😊',
    openmojiHex: '1F60A',
    label: 'Happy',
    ariaLabel: 'smiling face with smiling eyes'
  },
  'Shocked': {
    unicode: '😳',
    openmojiHex: '1F633',
    label: 'Shocked',
    ariaLabel: 'flushed face'
  },
  'Loved': {
    unicode: '😍',
    openmojiHex: '1F60D',
    label: 'Loved',
    ariaLabel: 'smiling face with heart-eyes'
  },
  'Thinking': {
    unicode: '🤔',
    openmojiHex: '1F914',
    label: 'Thinking',
    ariaLabel: 'thinking face'
  },
  'Angry': {
    unicode: '😠',
    openmojiHex: '1F620',
    label: 'Angry',
    ariaLabel: 'angry face'
  },
  'Crying': {
    unicode: '😢',
    openmojiHex: '1F622',
    label: 'Crying',
    ariaLabel: 'crying face'
  },
  'Laughing': {
    unicode: '😆',
    openmojiHex: '1F606',
    label: 'Laughing',
    ariaLabel: 'grinning squinting face'
  },
  'Neutral': {
    unicode: '😐',
    openmojiHex: '1F610',
    label: 'Neutral',
    ariaLabel: 'neutral face'
  },
  'Proud': {
    unicode: '😎',
    openmojiHex: '1F60E',
    label: 'Proud',
    ariaLabel: 'smiling face with sunglasses'
  },
  'Excited': {
    unicode: '🤩',
    openmojiHex: '1F929',
    label: 'Excited',
    ariaLabel: 'star-struck'
  },
  'Sleepy': {
    unicode: '😴',
    openmojiHex: '1F634',
    label: 'Sleepy',
    ariaLabel: 'sleeping face'
  },
  // === HAND GESTURE MOOD BEHAVIORS ===
  'Thumbs Up': {
    unicode: '👍',
    openmojiHex: '1F44D',
    label: 'Thumbs Up',
    ariaLabel: 'thumbs up hand sign'
  },
  'Thumbs Down': {
    unicode: '👎',
    openmojiHex: '1F44E',
    label: 'Thumbs Down',
    ariaLabel: 'thumbs down hand sign'
  },
  'OK Hand': {
    unicode: '👌',
    openmojiHex: '1F44C',
    label: 'OK Hand',
    ariaLabel: 'OK hand sign'
  }
};

// Gender emoji mappings
export const genderEmojiMapping = {
  'Auto': {
    unicode: '🤖',
    openmojiHex: '1F916',
    label: 'Auto',
    ariaLabel: 'robot'
  },
  'Male': {
    unicode: '👨',
    openmojiHex: '1F468',
    label: 'Male',
    ariaLabel: 'man'
  },
  'Female': {
    unicode: '👩',
    openmojiHex: '1F469',
    label: 'Female',
    ariaLabel: 'woman'
  },
  'Multigender': {
    unicode: '🧑',
    openmojiHex: '1F9D1',
    label: 'Bigender',
    ariaLabel: 'person'
  }
};

// Helper function to get OpenMoji URL with quality optimizations
export const getOpenMojiUrl = (hexCode, useFallback = false) => {
  const baseUrl = useFallback ? OPENMOJI_CDN_FALLBACK : OPENMOJI_CDN_BASE;
  const svgUrl = `${baseUrl}${hexCode}.svg`;
  
  // Log the URL for debugging
  console.log(`🎨 Loading OpenMoji SVG: ${hexCode} from ${svgUrl}`);
  
  return svgUrl;
};

// Helper function to get local OpenMoji path (if downloaded)
export const getLocalOpenMojiPath = (hexCode) => {
  return `/assets/openmoji/${hexCode}.svg`;
};

// Utility to preload a specific OpenMoji SVG
export const preloadOpenMojiSvg = (hexCode) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      console.log(`✅ Preloaded OpenMoji SVG: ${hexCode}`);
      resolve(img);
    };
    img.onerror = (error) => {
      console.error(`❌ Failed to preload OpenMoji SVG: ${hexCode}`, error);
      reject(error);
    };
    img.src = getOpenMojiUrl(hexCode);
  });
};

// Utility to validate SVG quality
export const validateSvgQuality = (imgElement) => {
  if (!imgElement || !imgElement.src) return false;
  
  // Check if it's actually an SVG
  const isSvg = imgElement.src.includes('.svg');
  const hasProperSize = imgElement.naturalWidth > 0 && imgElement.naturalHeight > 0;
  const isVector = imgElement.src.includes('svg');
  
  console.log(`🔍 SVG Quality Check:`, {
    src: imgElement.src,
    isSvg,
    hasProperSize,
    isVector,
    naturalWidth: imgElement.naturalWidth,
    naturalHeight: imgElement.naturalHeight
  });
  
  return isSvg && hasProperSize && isVector;
};

// Get all emoji data for bulk operations
export const getAllEmojiData = () => {
  return {
    mood: Object.values(moodEmojiMapping),
    gender: Object.values(genderEmojiMapping)
  };
}; 