// React hook for managing intelligent icon rendering mode
// Provides state management and utilities for icon rendering preferences

import { useState, useCallback } from 'react';

/**
 * Hook for managing icon rendering mode with intelligent classification
 * @param {string} initialMode - Initial rendering mode ('auto', 'realistic', 'cartoonish')
 * @returns {object} Icon rendering mode state and utilities
 */
export const useIconRenderingMode = (initialMode = 'auto') => {
    const [iconRenderingMode, setIconRenderingMode] = useState(initialMode);
    
    // Available rendering modes with metadata
    const modes = [
        { 
            id: 'auto', 
            name: 'Smart Auto', 
            description: 'Automatically choose realistic or cartoonish based on object type',
            icon: 'solar:check-circle-linear',
            isDefault: true
        },
        { 
            id: 'realistic', 
            name: 'All Realistic', 
            description: 'Render all icons with 70-80% photorealistic quality',
            icon: 'solar:camera-linear',
            isDefault: false
        },
        { 
            id: 'cartoonish', 
            name: 'All Cartoonish', 
            description: 'Render all icons in 3D cartoonish style',
            icon: 'solar:emoji-funny-square-linear',
            isDefault: false
        }
    ];
    
    // Get current mode metadata
    const getCurrentMode = useCallback(() => {
        return modes.find(mode => mode.id === iconRenderingMode) || modes[0];
    }, [iconRenderingMode]);
    
    // Change mode with validation
    const changeMode = useCallback((newMode) => {
        const validModes = modes.map(m => m.id);
        if (validModes.includes(newMode)) {
            setIconRenderingMode(newMode);
            return true;
        }
        console.warn(`Invalid icon rendering mode: ${newMode}. Valid modes: ${validModes.join(', ')}`);
        return false;
    }, []);
    
    // Reset to default mode
    const resetToDefault = useCallback(() => {
        const defaultMode = modes.find(m => m.isDefault) || modes[0];
        setIconRenderingMode(defaultMode.id);
    }, []);
    
    // Get mode configuration for prompt building
    const getModeConfig = useCallback(() => {
        const currentMode = getCurrentMode();
        return {
            mode: currentMode.id,
            forceRealistic: currentMode.id === 'realistic',
            forceCartoonish: currentMode.id === 'cartoonish',
            useAuto: currentMode.id === 'auto'
        };
    }, [getCurrentMode]);
    
    return {
        iconRenderingMode,
        setIconRenderingMode,
        modes,
        getCurrentMode,
        changeMode,
        resetToDefault,
        getModeConfig
    };
}; 