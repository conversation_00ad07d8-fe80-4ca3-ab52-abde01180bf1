// Contextual Engagement Prompt Variation Generator
// Implements contextual-engagement-prompt-variation-generator following comprehensive-thumbnail-prompt-enhancement rules

import { OPENAI_API_KEY } from '../../config.js';

/**
 * Generates 3-5 improved, highly engaging prompt variations for YouTube thumbnail generation
 * Each variation uses a distinct engagement strategy with concrete visual details, colors, and composition
 * 
 * @param {string} userPrompt - The original user prompt/video topic
 * @param {string} category - The detected video category (optional)
 * @returns {Promise<Array>} Array of enhanced prompt variations with explanations
 */
export const generateContextualEngagementVariations = async (userPrompt, category = 'general') => {
    if (!userPrompt || userPrompt.trim().length < 5) {
        return [];
    }

    const prompt = userPrompt.trim();
    
    // Check if we're on Netlify or missing API key - use fallback
    const isNetlify = typeof window !== 'undefined' && window.location.hostname.includes('netlify.app');
    
    if (isNetlify || !OPENAI_API_KEY) {
        console.log('Using fallback engagement variations (Netlify deployment or no API key)');
        return generateFallbackEngagementVariations(prompt, category);
    }

    try {
        // AI-powered generation using OpenAI
        const aiVariations = await generateAIEngagementVariations(prompt, category);
        
        if (aiVariations && aiVariations.length >= 3) {
            console.log('Generated AI engagement variations:', aiVariations);
            return aiVariations;
        }
        
        // Fallback if AI generation fails
        console.log('AI generation failed, using fallback engagement variations');
        return generateFallbackEngagementVariations(prompt, category);
        
    } catch (error) {
        console.error('Error in generateContextualEngagementVariations:', error);
        return generateFallbackEngagementVariations(prompt, category);
    }
};

/**
 * AI-powered engagement variation generation using OpenAI Chat Completions
 */
const generateAIEngagementVariations = async (originalPrompt, category) => {
    const systemPrompt = `You are a YouTube thumbnail design expert specialized in creating visually compelling, engagement-driven prompt variations. Generate 4-5 unique prompt variations that transform the user's basic idea into rich, specific, visually detailed thumbnail concepts.

=== CONTEXTUAL ENGAGEMENT PROMPT VARIATION GENERATOR ===

CORE REQUIREMENTS:
- Each variation should use a **different engagement strategy** and **visual approach**
- Include **concrete visual details** (composition, lighting, colors, effects)
- Suggest **specific color palettes, lighting setups, or visual techniques**
- **NEVER include text overlay, headline, or title suggestions** - these are handled separately by the UI
- Focus ONLY on visual scene description, composition, cinematic effects, and layout
- Follow **comprehensive thumbnail prompt enhancement rules** (rendering order, cinematic effects, glow, LUT)
- Be **context-aware** and relevant to the video topic
- Each variation should be **1-2 sentences, rich in visual specificity**

CRITICAL TEXT OVERLAY EXCLUSION RULES:
- **DO NOT** suggest any headline text, overlay text, or title content
- **DO NOT** include phrases like "with text overlay", "bold text saying", "title reading"
- **DO NOT** mention specific words or phrases for text overlays
- **FOCUS ONLY** on visual elements: backgrounds, subjects, lighting, composition, effects
- The text overlay will be generated separately by the Auto AI suggestion system

ENGAGEMENT STRATEGIES (use different one for each variation):
1. **Visual Metaphor + Curiosity Gap**: Create striking visual metaphors that make viewers curious
2. **Benefit-Driven + Urgency**: Focus on outcomes/results with urgent visual language  
3. **Minimalist + Aesthetic Focus**: Sophisticated, clean design with premium feel
4. **Contrast + Boldness**: High contrast, dramatic lighting, visual conflict
5. **Unconventional + Relatable**: Unexpected angles, modern UI elements, contemporary framing

VISUAL ENHANCEMENT REQUIREMENTS (include in each variation):
- **Rendering Order**: Specify background → subject → overlays → composition layering
- **Cinematic Effects**: Depth of field, motion blur, dramatic lighting (rim light, side light, spotlight)
- **Glow Effects**: Soft vibrant glow for icons, subject edges, key elements
- **Color Grading (LUT)**: Descriptive color mood (e.g., "cinematic orange-teal", "cold blue-gray", "neon purple-blue")
- **Composition**: Specific framing, placement, visual hierarchy

CATEGORY-SPECIFIC ADAPTATIONS:
- **Gaming**: Mention authentic game elements, UI, competitive dynamics (NO text content)
- **Business**: Focus on growth, success metrics, professional aesthetics (NO text content)
- **Tech**: Highlight innovation, features, modern interfaces (NO text content)
- **Food/Cooking**: Emphasize textures, colors, appetizing presentation (NO text content)
- **Fitness**: Show transformation, energy, dynamic movement (NO text content)

FORMAT: Return exactly 4-5 numbered variations as:
"Variation 1: [Enhanced visual prompt with NO text overlay content] | Why better: [Brief explanation]"

EXAMPLE:
Original: "design nutrition thumbnail for top tips"

Variation 1: Vibrant plate explosion with 5 floating food icons rendered against cinematic orange-teal background with dramatic side lighting creating depth of field blur and rule of thirds composition with warm LUT color grading | Why better: Creates visual metaphor + curiosity gap + color contrast

Variation 2: Split-screen before/after vitality comparison with cold blue-gray cinematic filter, rim lighting on subjects, and professional studio lighting setup | Why better: Emotional hook + outcome-focused visual storytelling

Generate variations for: "`;

    const payload = {
        model: 'gpt-4o-mini', // Use more capable model for complex prompt generation
        messages: [
            {
                role: 'system',
                content: systemPrompt
            },
            {
                role: 'user',
                content: originalPrompt
            }
        ],
        temperature: 0.8, // Higher temperature for more creative variations
        max_tokens: 800, // Allow longer responses for detailed variations
        top_p: 0.9
    };

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify(payload)
    });

    if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content;

    if (!content) {
        throw new Error('No content received from OpenAI');
    }

    return parseEngagementVariations(content);
};

/**
 * Parse AI response into structured variations
 */
const parseEngagementVariations = (content) => {
    const lines = content.split('\n').filter(line => line.trim());
    const variations = [];
    
    for (const line of lines) {
        // Match pattern: "Variation X: [prompt] | Why better: [explanation]"
        const match = line.match(/^Variation\s*\d+:\s*(.+?)\s*\|\s*Why better:\s*(.+)$/i);
        if (match) {
            const [, prompt, explanation] = match;
            variations.push({
                prompt: prompt.trim(),
                explanation: explanation.trim(),
                strategy: extractStrategy(prompt)
            });
        }
    }
    
    return variations.length >= 3 ? variations : null;
};

/**
 * Extract engagement strategy from prompt content
 */
const extractStrategy = (prompt) => {
    const strategies = {
        'metaphor': ['explosion', 'floating', 'transformation', 'flowing'],
        'contrast': ['split-screen', 'vs.', 'before/after', 'comparison'],
        'minimalist': ['clean', 'elegant', 'sophisticated', 'premium'],
        'bold': ['dramatic', 'neon', 'vibrant', 'high-contrast'],
        'unconventional': ['phone screen', 'notification', 'UI', 'interface']
    };
    
    const lowerPrompt = prompt.toLowerCase();
    for (const [strategy, keywords] of Object.entries(strategies)) {
        if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
            return strategy;
        }
    }
    return 'general';
};

/**
 * Fallback engagement variations when AI is unavailable
 */
const generateFallbackEngagementVariations = (originalPrompt, category) => {
    const strategies = [
        {
            name: 'visual_metaphor',
            template: (prompt, cat) => ({
                prompt: `Dynamic ${getVisualMetaphor(cat)} scene with floating elements | ${prompt} with dramatic lighting and depth of field | Bold text overlay with soft glow effect | Cinematic orange-teal color grading`,
                explanation: 'Creates visual metaphor + curiosity gap + color contrast',
                strategy: 'metaphor'
            })
        },
        {
            name: 'benefit_driven',
            template: (prompt, cat) => ({
                prompt: `Split-screen transformation showing clear before/after results | ${prompt} with professional studio lighting | High-impact headline with rim lighting | Cold blue-gray LUT for dramatic mood`,
                explanation: 'Emotional hook + outcome-focused + urgency',
                strategy: 'contrast'
            })
        },
        {
            name: 'minimalist_aesthetic',
            template: (prompt, cat) => ({
                prompt: `Clean, sophisticated flat-lay composition | ${prompt} with soft natural lighting | Elegant typography with subtle glow | Cream and sage color palette for premium feel`,
                explanation: 'Sophisticated visual direction + calming aesthetic',
                strategy: 'minimalist'
            })
        },
        {
            name: 'bold_contrast',
            template: (prompt, cat) => ({
                prompt: `High-contrast neon composition with dramatic spotlight | ${prompt} against dark background | Glowing headline with vibrant accent colors | Gaming-inspired purple-blue LUT`,
                explanation: 'Creates visual conflict + modern aesthetic + boldness',
                strategy: 'bold'
            })
        },
        {
            name: 'unconventional_angle',
            template: (prompt, cat) => ({
                prompt: `Modern phone screen interface showing ${prompt} | Notification-style UI elements with glitch effects | Contemporary framing with app-like typography | Tech-inspired color scheme`,
                explanation: 'Relatable framing + contemporary UI metaphor + uniqueness',
                strategy: 'unconventional'
            })
        }
    ];

    return strategies.map(strategy => strategy.template(originalPrompt, category));
};

/**
 * Get category-appropriate visual metaphors
 */
const getVisualMetaphor = (category) => {
    const metaphors = {
        'gaming': 'explosive gaming arena',
        'business': 'rising success graph',
        'tech': 'futuristic holographic',
        'food': 'ingredient explosion',
        'fitness': 'energy transformation',
        'education': 'knowledge illumination',
        'general': 'dynamic visual'
    };
    
    return metaphors[category] || metaphors.general;
};

/**
 * Enhanced prompt improvement that integrates with existing system
 * Replaces the simple title enhancement with comprehensive engagement variations
 */
export const enhancePromptWithEngagementVariations = async (userPrompt, category = 'general') => {
    const variations = await generateContextualEngagementVariations(userPrompt, category);
    
    if (variations.length > 0) {
        // Return the first (most engaging) variation as the primary enhancement
        // The full variations can be accessed via a separate prompt variations feature
        return {
            enhancedPrompt: variations[0].prompt,
            explanation: variations[0].explanation,
            strategy: variations[0].strategy,
            allVariations: variations
        };
    }
    
    // Fallback to simple enhancement if generation fails
    return {
        enhancedPrompt: `Enhanced: ${userPrompt} with cinematic lighting, professional composition, and vibrant colors`,
        explanation: 'Basic enhancement applied',
        strategy: 'general',
        allVariations: []
    };
}; 