---
description: 
globs: 
alwaysApply: false
---
---
title: user-driven-thumbnail-customization
id: user-driven-thumbnail-customization.mdc
ruleType: manual
---

## Feature: User-Driven Thumbnail Customization Suite

### Objective
Enhance the thumbnail generator MVP by empowering users with advanced customization options for personalization, branding, and expression — directly influencing fonts, icons, colors, gender representation, facial presence, and mood emojis.

This feature should flexibly adapt based on the selected **template type or mode**, such as "YouTube Reaction Thumbnail," "App Tutorial Promo," or "Podcast Episode Cover." Each mode can expose relevant controls only, simplifying the UX.

---

## Applies To
- /src/components/ThumbnailControls.jsx
- /src/components/PromptBuilder.js
- /src/components/TemplateSelector.tsx
- /src/state/userPreferences.ts
- /src/templates/
- /public/assets/icons/

---

## UI/UX Practices for Category-Based Template Navigation

### 📁 Category Browsing Layout
- Display all **main categories** (e.g., tech, vlogging, reactions, education, gaming) in a responsive grid layout.
- Each category block should:
  - Use a thumbnail preview or category illustration
  - Include a title + optional subtext
  - On hover: apply subtle scaling + glow (`hover:scale-105`, `hover:shadow-md`)

### 📂 Subcategory Reveal
- On click of a category, animate reveal of **subcategories** beneath or beside it (accordion, tabs, or modal)
- Display subcategories as horizontally scrollable thumbnails or tile cards with preset styles and locked defaults

### 🧭 Best UX Pattern
| Section            | UI Type             | Behavior                                  |
|--------------------|---------------------|--------------------------------------------|
| Category Picker    | Grid or List        | Responsive + animated entrance             |
| Subcategory Loader | Scrollable Cards    | Lazy-loaded on category click              |
| Template Preview   | Thumbnail Hover     | Preview with call-to-action: “Customize”   |
| Filter/Search      | Top Toolbar         | Search by keyword or tag ("funny", "hd")   |
| Back Navigation    | Breadcrumb or Arrow | Return to main category list               |

---

## UI Component Suggestions (Tailwind + Hero UI)

- Grid: `grid-cols-2 md:grid-cols-4 gap-4`
- Category Box: `bg-zinc-800 p-4 rounded-xl hover:scale-105 transition`
- Subcategory Card: `aspect-video w-full shadow-md rounded-lg overflow-hidden`
- Label text: `text-white font-semibold text-sm` with optional icon
- Preview CTA: Button like `Customize This →` on hover

---

## Template-Adaptive Customization
- When a user clicks a subcategory thumbnail:
  - Load corresponding `.json` from `/templates/{category}/{subcategory}.json`
  - Apply locked and editable fields into the customization UI
  - Disable or grey-out fields that are non-editable in that template

---

## 💾 JSON Format for Template Definitions
Each template should be stored as a `.json` object under `/src/templates/` with the following shape:

```json
{
  "template_name": "Tech Review Shock",
  "description": "Male subject reacting to a device with bold text overlay and warm lighting.",
  "locked_fields": ["composition", "rendering_style"],
  "tags": ["tech", "reaction", "AI"],
  "defaults": {
    "rendering_style": "Realistic Cinematic Thumbnail",
    "subject": {
      "person": {
        "expression": "shocked",
        "pose": "pointing",
        "gender": "male"
      },
      "object_of_focus": {
        "position": "right",
        "highlight_effect": {
          "glow": "orange-yellow"
        }
      }
    },
    "background": {
      "color_gradient": "red to black"
    },
    "text_overlay": {
      "title": "THIS DEVICE IS INSANE!",
      "style": {
        "font": "bold sans-serif",
        "placement": "top right"
      }
    },
    "composition": {
      "aspect_ratio": "16:9",
      "framing": "tight"
    }
  }
}
```

---

## Cursor Chat Integration
To activate this customization rule in chat cascade:
```mdc
@user-driven-thumbnail-customization
```

> Note: Developers must respect fallback logic if user inputs are missing. Auto-suggestions or defaults should remain functional.

---

## Notes for Developers
- Ensure uploaded images/icons are safely processed, and ideally previewed.
- Store user selections in localStorage or state to persist between sessions.
- If user uploads a face, toggle off default AI face generation and show their own.
- Emojis must stay additive — never block other UX controls.

## Bonus UX Tip 💡
Group related customization panels inside collapsible sections or tabs (e.g. “Fonts & Text”, “Branding”, “Face & Mood”) to keep the interface tidy and scalable.
Show or hide control groups dynamically based on the selected template or thumbnail mode.

---

✅ Ready for rule activation.
To apply, drop this .mdc into your active Cursor workspace and toggle in Chat Cascade when needed.
