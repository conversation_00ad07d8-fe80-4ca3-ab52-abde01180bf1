---
description: 
globs: 
alwaysApply: true
---
@gradient-text-overlay-enhancement
ruleId: gradient-text-overlay-enhancement
description: >
  Improves the text overlay color/gradient system for YouTube thumbnails. Applies only to the 7 core colors: yellow, black, white, purple, blue, green, red.

appliesTo:
  - /src/components/ControlPanel.jsx
  - /src/utils/colorUtils.js
  - /src/styles/controls.css
  - /src/utils/promptFormatter.js

ruleType: always

implementationNotes: |
  - Always generate a darker, high-contrast accent shade for the gradient (20–30% darker in HSL/HSV).
  - For black/white, use subtle off-black/off-white as accent to avoid harsh banding.
  - Color picker: shrink color circles by 25%, reduce gap, remove rectangle swatches and secondary row.
  - Yellow is always the first circle and default, with a visible active border.
  - Remove all small grey labels and rectangle hex/degree info.
  - When a color is selected, match the thumbnail’s color grade and background for optimal contrast (context7mcp).
  - For black/white overlays, ensure background adapts for maximum legibility.
  - For purple, blue, green, red overlays, always apply a low-medium stroke (outline) in white or black for contrast.
  - All color selection transitions must use smooth fade/scale animation.
  - No layout shifts or jank on color change.
  - These changes apply ONLY to the 7 core colors at this stage.

acceptanceCriteria:
  - Gradient text always uses a darker accent for strong contrast.
  - Color picker is compact, with only circles, no rectangles or extra labels.
  - Yellow is default, with a clear active border.
  - Overlay text is always readable and visually harmonious with the thumbnail.
  - UI transitions are smooth and premium.
  - No extra rows, labels, or rectangle swatches.
  - All logic is context-aware and only applies to the 7 specified colors.