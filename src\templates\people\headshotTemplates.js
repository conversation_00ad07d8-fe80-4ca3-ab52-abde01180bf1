/**
 * Headshot and Portrait Templates for Person-focused Thumbnails
 * Implements the person-headshot-enhancement rule
 */

export const headshotTemplates = [
    {
        id: "reaction-headshot",
        name: "Reaction Headshot",
        description: "Perfect for reaction videos and emotional content",
        category: "reaction",
        promptBase: "Create a close-up reaction headshot showing clear facial emotion. The person should be looking directly at the camera with an expressive face that conveys the video's emotional tone.",
        settingsToApply: {
            includePerson: true,
            selectedExpression: 'Shocked',
            textOverlay: true,
            userPromptFocus: "Reaction to unexpected content",
            overlayText: "CAN'T\nBELIEVE\nTHIS!"
        },
        headshotEnhanced: true
    },
    {
        id: "tutorial-presenter",
        name: "<PERSON>toria<PERSON> Presenter",
        description: "Professional headshot for educational content",
        category: "education",
        promptBase: "Create a professional presenter headshot with clear, confident facial features. The person should appear knowledgeable and approachable, perfect for tutorial and educational content.",
        settingsToApply: {
            includePerson: true,
            selectedExpression: 'Thinking',
            textOverlay: true,
            userPromptFocus: "Educational tutorial content",
            overlayText: "LEARN\nTHIS\nTRICK"
        },
        headshotEnhanced: true
    },
    {
        id: "review-face",
        name: "Product Review Face",
        description: "Honest reviewer expression for product content",
        category: "review",
        promptBase: "Create an honest, evaluative headshot for product review content. The facial expression should convey thoughtful consideration and trustworthiness.",
        settingsToApply: {
            includePerson: true,
            selectedExpression: 'Neutral',
            textOverlay: true,
            userPromptFocus: "Product review and comparison",
            overlayText: "HONEST\nREVIEW"
        },
        headshotEnhanced: true
    },
    {
        id: "excited-announcement",
        name: "Excited Announcement",
        description: "High-energy headshot for exciting news and announcements",
        category: "announcement",
        promptBase: "Create an energetic, excited headshot showing genuine enthusiasm. The facial expression should be highly engaging and convey excitement about the content.",
        settingsToApply: {
            includePerson: true,
            selectedExpression: 'Happy',
            textOverlay: true,
            userPromptFocus: "Exciting announcement or news",
            overlayText: "BIG\nNEWS!"
        },
        headshotEnhanced: true
    },
    {
        id: "thinking-portrait",
        name: "Contemplative Portrait",
        description: "Thoughtful headshot for deep-dive content",
        category: "analysis",
        promptBase: "Create a contemplative, thoughtful headshot showing deep consideration. The person should appear to be pondering important concepts, perfect for analytical content.",
        settingsToApply: {
            includePerson: true,
            selectedExpression: 'Thinking',
            textOverlay: true,
            userPromptFocus: "Deep analysis and explanation",
            overlayText: "DEEP\nDIVE"
        },
        headshotEnhanced: true
    }
];

export const getHeadshotTemplate = (templateId) => {
    return headshotTemplates.find(template => template.id === templateId);
};

export const getHeadshotTemplatesByCategory = (category) => {
    return headshotTemplates.filter(template => template.category === category);
}; 