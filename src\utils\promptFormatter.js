import { detectBrand<PERSON><PERSON><PERSON>, hasGaming<PERSON><PERSON>, detectBrandLogosWithPlacement } from './brandLogos.js'
import { 
    classifyIconRenderingStyle, 
    extractIconKeywords, 
    analyzePromptIcons 
} from './iconClassifier.js'
import { 
    extractContextualVisuals, 
    extractEnhancedContextualVisuals, 
    detectGameContext,
    generateGameSpecificIconInstructions 
} from './promptEnhancer.js';
import { generateSmartTextSuggestion } from './smartTextAnalyzer.js';
import { 
    analyzePromptContext, 
    shouldApplyGamingLogic, 
    getCorrectLogoDescription,
    validateIconRendering,
    hasExplicitCallOfDutyPhrases,
    shouldGenerateMakeIcon,
    filterBrandsForIconGeneration,
    analyzeReactContext
} from './contextAwareClassifier.js';
import { sanitizeOverlayText, validateSanitizedText } from './textSanitizer.js';

// === ENHANCED CONTEXT-AWARE ICON & TEXT OVERLAY GENERATION SYSTEM ===

/**
 * Enhanced context detection that prevents gaming icon cross-contamination
 * Only applies gaming logic when confidence is >80% that content is actually gaming
 * @param {string} userPrompt - The user's prompt
 * @returns {Object} - Enhanced context analysis with strict confidence thresholds
 */
function detectEnhancedGameContext(userPrompt) {
    if (!userPrompt || typeof userPrompt !== 'string') {
        return { isGaming: false, confidence: 0, context: 'unknown', reasoning: 'No prompt provided' };
    }

    const lower = userPrompt.toLowerCase().trim();
    
    // === STRICT GAMING DETECTION RULES ===
    
    // 1. Explicit gaming phrases (99% confidence)
    const explicitGamingPhrases = [
        'gameplay', 'game tutorial', 'gaming guide', 'game review', 'game tips',
        'gaming montage', 'game highlights', 'gaming stream', 'esports',
        'tournament', 'competitive gaming', 'ranked gameplay', 'gaming setup',
        'game walkthrough', 'speedrun', 'let\'s play', 'game comparison',
        'gaming performance', 'game settings', 'gaming hardware'
    ];
    
    const hasExplicitGaming = explicitGamingPhrases.some(phrase => lower.includes(phrase));
    if (hasExplicitGaming) {
        return {
            isGaming: true,
            confidence: 0.99,
            context: 'explicit_gaming',
            reasoning: 'Explicit gaming phrase detected'
        };
    }
    
    // 2. Specific game mentions with gaming context (95% confidence)
    const specificGameContexts = [
        'fortnite battle', 'valorant ace', 'cod warzone', 'minecraft build',
        'apex legends', 'cs2 highlights', 'league of legends', 'overwatch',
        'warzone loadout', 'fortnite montage', 'valorant clips'
    ];
    
    const hasSpecificGameContext = specificGameContexts.some(context => lower.includes(context));
    if (hasSpecificGameContext) {
        return {
            isGaming: true,
            confidence: 0.95,
            context: 'specific_game_context',
            reasoning: 'Specific game with gaming context detected'
        };
    }
    
    // 3. Check for non-gaming contexts that should NEVER trigger gaming logic
    const nonGamingIndicators = [
        // Tech/Development contexts
        'mobile app', 'app design', 'ui design', 'ux design', 'web design',
        'code review', 'programming', 'development', 'scripting', 'coding',
        'software', 'application', 'framework', 'library', 'api',
        'frontend', 'backend', 'database', 'server', 'deployment',
        
        // Business contexts
        'business strategy', 'marketing', 'sales', 'revenue', 'profit',
        'company', 'startup', 'enterprise', 'corporate', 'professional',
        'meeting', 'presentation', 'proposal', 'contract', 'negotiation',
        
        // Health/Medical contexts
        'health', 'medical', 'treatment', 'therapy', 'disease', 'illness',
        'nutrition', 'diet', 'fitness', 'wellness', 'hospital', 'doctor',
        
        // Education contexts
        'tutorial', 'course', 'lesson', 'learning', 'education', 'training',
        'study', 'research', 'academic', 'university', 'school',
        
        // Creative/Media contexts
        'video editing', 'content creation', 'social media', 'youtube',
        'streaming', 'podcast', 'music', 'art', 'design', 'photography'
    ];
    
    const hasNonGamingContext = nonGamingIndicators.some(indicator => lower.includes(indicator));
    if (hasNonGamingContext) {
        return {
            isGaming: false,
            confidence: 0.90,
            context: 'non_gaming',
            reasoning: 'Non-gaming context detected, blocking gaming logic'
        };
    }
    
    // 4. Battle/Combat metaphors in non-gaming contexts (should NOT be gaming)
    const battleMetaphorContexts = [
        'battle cancer', 'battle depression', 'battle addiction',
        'battle for market share', 'battle of ideas', 'legal battle',
        'epic battle: crafting', 'ultimate battle: creating',
        'cinematic ultimate battle: crafting', 'cinematic battle'
    ];
    
    const hasBattleMetaphor = battleMetaphorContexts.some(metaphor => lower.includes(metaphor));
    if (hasBattleMetaphor) {
        return {
            isGaming: false,
            confidence: 0.95,
            context: 'battle_metaphor',
            reasoning: 'Battle used as metaphor in non-gaming context'
        };
    }
    
    // 5. Game detection with context validation (only if high confidence)
    const gameDatabase = {
        'fortnite': { confidence: 0.85, requiresContext: true },
        'valorant': { confidence: 0.85, requiresContext: true },
        'warzone': { confidence: 0.90, requiresContext: false },
        'call of duty': { confidence: 0.90, requiresContext: false },
        'cod': { confidence: 0.60, requiresContext: true }, // Low confidence due to ambiguity
        'apex legends': { confidence: 0.90, requiresContext: false },
        'minecraft': { confidence: 0.80, requiresContext: true },
        'league of legends': { confidence: 0.90, requiresContext: false },
        'overwatch': { confidence: 0.85, requiresContext: true },
        'cs2': { confidence: 0.85, requiresContext: true },
        'counter strike': { confidence: 0.90, requiresContext: false }
    };
    
    let gameDetected = null;
    let gameConfidence = 0;
    
    for (const [gameName, gameData] of Object.entries(gameDatabase)) {
        if (lower.includes(gameName)) {
            // For games that require context, check for gaming-specific words nearby
            if (gameData.requiresContext) {
                const gamingContextWords = [
                    'play', 'playing', 'gameplay', 'game', 'gaming',
                    'win', 'lose', 'victory', 'defeat', 'match', 'battle',
                    'tournament', 'competitive', 'ranked', 'casual',
                    'montage', 'highlights', 'clips', 'stream', 'streaming',
                    'tips', 'guide', 'tutorial', 'walkthrough', 'review'
                ];
                
                const hasGamingContext = gamingContextWords.some(word => lower.includes(word));
                if (hasGamingContext) {
                    gameDetected = gameName;
                    gameConfidence = gameData.confidence;
                }
            } else {
                // Game name is explicit enough on its own
                gameDetected = gameName;
                gameConfidence = gameData.confidence;
            }
            break;
        }
    }
    
    // Only apply gaming logic if confidence is >80%
    if (gameDetected && gameConfidence > 0.80) {
        return {
            isGaming: true,
            confidence: gameConfidence,
            context: 'game_detected',
            reasoning: `Game detected: ${gameDetected} with ${Math.round(gameConfidence * 100)}% confidence`,
            primaryGame: {
                name: gameDetected,
                category: 'unknown',
                artStyle: 'game-specific',
                confidence: gameConfidence
            }
        };
    }
    
    // Default: Not gaming (confidence threshold not met)
    return {
        isGaming: false,
        confidence: Math.max(0.60, 1 - (gameConfidence || 0)),
        context: 'non_gaming_default',
        reasoning: gameDetected ? 
            `Game detected (${gameDetected}) but confidence too low (${Math.round(gameConfidence * 100)}%)` :
            'No clear gaming context detected'
    };
}

/**
 * Determines appropriate icon generation strategy based on content context
 * @param {string} userPrompt - The user's prompt
 * @param {string} videoTopic - The detected video topic
 * @returns {Object} - Icon generation strategy with context-specific rules
 */
function determineIconGenerationStrategy(userPrompt, videoTopic) {
    const lower = userPrompt.toLowerCase();
    
    // Context-specific icon strategies
    const strategies = {
        tech: {
            preferredIcons: ['code editor', 'terminal', 'laptop', 'smartphone', 'cloud', 'database', 'api'],
            renderingStyle: 'realistic',
            avoidIcons: ['combat items', 'entertainment controllers', 'fantasy elements'],
            distribution: 'clean-minimal'
        },
        business: {
            preferredIcons: ['charts', 'graphs', 'briefcase', 'handshake', 'money', 'calculator'],
            renderingStyle: 'realistic',
            avoidIcons: ['entertainment elements', 'cartoon characters', 'combat items'],
            distribution: 'professional-grid'
        },
        education: {
            preferredIcons: ['books', 'graduation cap', 'lightbulb', 'pencil', 'notebook', 'globe'],
            renderingStyle: 'mixed',
            avoidIcons: ['combat items', 'violent imagery'],
            distribution: 'balanced-educational'
        },
        health: {
            preferredIcons: ['heart', 'stethoscope', 'medical cross', 'healthy food', 'exercise equipment'],
            renderingStyle: 'realistic',
            avoidIcons: ['combat items', 'entertainment elements'],
            distribution: 'clean-medical'
        },
        entertainment: {
            preferredIcons: ['play button', 'heart', 'thumbs up', 'star', 'camera', 'microphone'],
            renderingStyle: 'cartoonish',
            avoidIcons: ['coding symbols', 'business charts'],
            distribution: 'dynamic-entertainment'
        },
        gaming: {
            preferredIcons: ['context-specific game objects'],
            renderingStyle: 'game-authentic',
            avoidIcons: ['generic emojis', 'non-game elements'],
            distribution: 'game-specific'
        }
    };
    
    // Determine context based on keywords and video topic
    let detectedContext = 'general';
    
    if (lower.includes('app') || lower.includes('code') || lower.includes('programming') || lower.includes('development')) {
        detectedContext = 'tech';
    } else if (lower.includes('business') || lower.includes('marketing') || lower.includes('sales') || lower.includes('profit')) {
        detectedContext = 'business';
    } else if (lower.includes('learn') || lower.includes('tutorial') || lower.includes('guide') || lower.includes('education')) {
        detectedContext = 'education';
    } else if (lower.includes('health') || lower.includes('fitness') || lower.includes('nutrition') || lower.includes('medical')) {
        detectedContext = 'health';
    } else if (lower.includes('reaction') || lower.includes('entertainment') || lower.includes('funny') || lower.includes('viral')) {
        detectedContext = 'entertainment';
    }
    
    return strategies[detectedContext] || strategies.general || {
        preferredIcons: ['contextual icons'],
        renderingStyle: 'mixed',
        avoidIcons: [],
        distribution: 'balanced'
    };
}

// === SMART TEXT & GRAPHICS SEPARATION SYSTEM ===

/**
 * Sanitizes prompts from external LLMs by removing headline text instructions
 * @param {string} userPrompt - The raw user prompt that may contain LLM-generated text
 * @returns {Object} - { sanitizedPrompt: string, detectedHeadlines: string[] }
 */
const sanitizeExternalLLMPrompt = (userPrompt) => {
    // Patterns that indicate headline text from external LLMs
    const headlinePatterns = [
        /bold text overlay:\s*['"]([^'"]+)['"]/gi,
        /text overlay:\s*['"]([^'"]+)['"]/gi,
        /title:\s*['"]([^'"]+)['"]/gi,
        /headline:\s*['"]([^'"]+)['"]/gi,
        /with text ['"]([^'"]+)['"]/gi,
        /displaying ['"]([^'"]+)['"] in bold/gi,
        /large text saying ['"]([^'"]+)['"]/gi,
        /bold text reading ['"]([^'"]+)['"]/gi,
        /with bold text ['"]([^'"]+)['"]/gi
    ];
    
    let sanitizedPrompt = userPrompt;
    const detectedHeadlines = [];
    
    // Extract and remove headline references
    headlinePatterns.forEach(pattern => {
        sanitizedPrompt = sanitizedPrompt.replace(pattern, (match, text) => {
            detectedHeadlines.push(text);
            return ''; // Remove the headline instruction
        });
    });
    
    // Clean up any font/text styling instructions
    const stylePatterns = [
        /\b(bold|uppercase|large|huge|big)\s+(text|font|letters?)\b/gi,
        /\bfont[- ]?size:\s*\w+/gi,
        /\btext[- ]?size:\s*\w+/gi,
        /\b\d+px\s+font\b/gi,
        /\bin\s+(bold|large|huge)\s+font\b/gi
    ];
    
    stylePatterns.forEach(pattern => {
        sanitizedPrompt = sanitizedPrompt.replace(pattern, '');
    });
    
    // Clean up any double spaces or trailing punctuation issues
    sanitizedPrompt = sanitizedPrompt.replace(/\s+/g, ' ').trim();
    sanitizedPrompt = sanitizedPrompt.replace(/\s+([.,;:])/g, '$1');
    
    return {
        sanitizedPrompt,
        detectedHeadlines
    };
};

/**
 * Analyzes the prompt for icon distribution requirements and provides placement guidance
 * @param {string} userPrompt - The user's prompt
 * @param {string} videoTopic - The detected video topic category
 * @returns {Object} - Object with distribution strategy and placement recommendations
 */
const analyzeIconDistribution = (userPrompt, videoTopic) => {
    const promptLower = userPrompt.toLowerCase();
    
    // Detect if the prompt suggests multiple objects or comparisons
    const multipleObjectIndicators = [
        'vs', 'versus', 'comparison', 'multiple', 'various', 'different', 'types of',
        'collection', 'set of', 'range of', 'variety', 'several', 'many', 'all',
        'best', 'top', 'list', 'showcase', 'display', 'features'
    ];
    
    const hasMultipleObjects = multipleObjectIndicators.some(indicator => 
        promptLower.includes(indicator)
    );
    
    // Detect specific distribution patterns based on content
    const distributionStrategy = {
        useRuleOfThirds: true, // Default for most content
        useCornerPlacement: hasMultipleObjects,
        useCenterFocus: promptLower.includes('main') || promptLower.includes('central') || promptLower.includes('focus'),
        useSymmetrical: promptLower.includes('vs') || promptLower.includes('versus') || promptLower.includes('comparison'),
        useScattered: promptLower.includes('many') || promptLower.includes('various') || promptLower.includes('collection'),
        minimumIcons: hasMultipleObjects ? 3 : 1,
        maximumIcons: hasMultipleObjects ? 7 : 3
    };
    
    // Topic-specific distribution preferences
    const topicDistribution = {
        'Gaming': { preferDynamic: true, allowOverlap: false, useDepthLayers: true },
        'Tech': { preferGrid: true, allowOverlap: false, useCleanSpacing: true },
        'Business & Finance': { preferSymmetrical: true, allowOverlap: false, useProfessionalSpacing: true },
        'Health & Nutrition': { preferOrganic: true, allowOverlap: false, useNaturalFlow: true },
        'General': { preferBalanced: true, allowOverlap: false, useStandardSpacing: true }
    };
    
    return {
        strategy: distributionStrategy,
        topicPreferences: topicDistribution[videoTopic] || topicDistribution['General'],
        recommendedPlacement: generatePlacementGuidance(distributionStrategy, videoTopic)
    };
};

/**
 * Generates specific placement guidance based on distribution strategy
 * @param {Object} strategy - The distribution strategy object
 * @param {string} videoTopic - The video topic
 * @returns {string} - Detailed placement guidance for the AI
 */
const generatePlacementGuidance = (strategy, videoTopic) => {
    let guidance = '';
    
    if (strategy.useSymmetrical) {
        guidance += 'Use symmetrical placement for comparison content - balance elements on left and right sides. ';
    }
    
    if (strategy.useCornerPlacement) {
        guidance += 'Utilize corner placement strategically - place key icons in the four corners while maintaining visual balance. ';
    }
    
    if (strategy.useCenterFocus) {
        guidance += 'Create a central focal point with supporting elements arranged around it in a circular or radial pattern. ';
    }
    
    if (strategy.useScattered) {
        guidance += 'Distribute icons in a scattered but intentional pattern across the canvas, ensuring no large empty areas. ';
    }
    
    if (strategy.useRuleOfThirds) {
        guidance += 'Apply rule of thirds for primary icon placement, positioning key elements at intersection points. ';
    }
    
    // Add depth and layering guidance
    guidance += 'Use foreground, middle ground, and background layers to create depth. Vary icon sizes based on importance and distance. ';
    
    return guidance.trim();
};

/**
 * Determines which graphics elements are appropriate for the video topic
 * @param {string} userPrompt - The user's prompt
 * @param {string} videoTopic - The detected video topic category
 * @returns {Object} - Object with boolean flags for each graphics element type
 */
const shouldIncludeGraphicsElements = (userPrompt, videoTopic) => {
    const promptLower = userPrompt.toLowerCase();
    
    const graphicsContexts = {
        charts: {
            keywords: ['analytics', 'growth', 'statistics', 'data', 'chart', 'graph', 'trend', 'performance', 'metrics', 'increase', 'decrease', 'revenue', 'profit', 'loss'],
            topics: ['Business & Finance', 'Tech']
        },
        dataVisualizations: {
            keywords: ['numbers', 'percentage', 'increase', 'decrease', 'comparison', 'versus', 'vs', 'statistics', 'data', 'metrics', 'kpi', 'dashboard'],
            topics: ['Business & Finance', 'Health & Nutrition']
        },
        uiElements: {
            keywords: ['dashboard', 'interface', 'app', 'software', 'platform', 'screen', 'ui', 'ux', 'website', 'mobile', 'desktop', 'button', 'menu'],
            topics: ['Tech']
        },
        badges: {
            keywords: ['achievement', 'rank', 'level', 'certification', 'verified', 'pro', 'premium', 'badge', 'award', 'trophy', 'milestone', 'status'],
            topics: ['Gaming', 'Business & Finance']
        }
    };
    
    const includedElements = {
        charts: false,
        dataVisualizations: false,
        uiElements: false,
        badges: false
    };
    
    // Check each element type for relevance
    Object.entries(graphicsContexts).forEach(([element, context]) => {
        const hasKeyword = context.keywords.some(keyword => promptLower.includes(keyword));
        const isRelevantTopic = context.topics.includes(videoTopic);
        
        // Include element if keyword is found OR if it's a relevant topic AND the prompt suggests it
        includedElements[element] = hasKeyword || (isRelevantTopic && promptLower.length > 20);
    });
    
    return includedElements;
};

export const buildThumbnailPrompt = (details) => {
    const {
        initialPrompt,
        includePerson,
        mood,
        includeIcons,
        textOverlayEnabled,
        overlayText, // Custom text from user
        textPosition, // Text position (e.g., 'top-right')
        fitFullCanvas, // Fit full canvas toggle
        selectedTextSize, // Added: 'Medium' or 'Large'
    } = details;

    // --- Base Prompt Structure ---
    let textBlock = '';

    if (textOverlayEnabled && overlayText) {
        textBlock = `
Text Overlay:
- Add a bold, uppercase title related to the video topic: "${overlayText}"
- Use this exact text. Do not add words from the main prompt.
- Arrange text in a pyramid layout if multiple lines are needed.
`;
    } else if (textOverlayEnabled && initialPrompt) {
        // Use initial prompt for text, keep it short (3-6 words), avoid ellipses
        textBlock = `
Text Overlay:
- Add a bold, uppercase title related to the video topic: "${initialPrompt}"
- Keep the title concise (3-6 words maximum). Do not use ellipses (...).
`;
    } else {
        // Explicitly state no text if disabled
        textBlock = `
Text Overlay:
- Do NOT include any text overlay.
`;
    }

    // Add Text Styling Rules ONLY if text is enabled
    if (textOverlayEnabled) {
        const sizeInstruction = selectedTextSize === 'Medium'
            ? `Use moderately sized title text. The text should be clearly readable but must NOT dominate the thumbnail. The text should take up about 20-30% of the image height, leaving plenty of space for the main subject and background. Avoid making the text too bold or oversized.`
            : `Use extremely large, bold, attention-grabbing title text. The text should be the most prominent element, taking up 40-60% of the image height. Let the text overlap or interact with the subject if needed, and make sure it is highly visible even on small mobile screens. Prioritize maximum impact and click appeal.`;

        textBlock += `
- Font: modern sans-serif, bold and legible.
- ${sizeInstruction} <!-- Text Size Control -->
- For "Large" text, make the title the main focus, even if it partially covers the subject.
- For "Medium" text, keep the title balanced with the rest of the design.
- Colors: high-contrast (e.g., yellow/white text on dark background, or red/black text on light background).
- Style: Add a STRONG, vibrant GLOW effect and a noticeable DROP SHADOW to all text to increase click appeal and make it CRISPY.
- Placement: ${textPosition || 'center-right'}. Ensure text is placed clearly within the frame.
- Safe Zone: Ensure text has **at least 40px margin** from all canvas edges (top, bottom, left, right) to prevent clipping on YouTube mobile/desktop views. **CRITICAL**: All text overlays MUST be written in ALL CAPS (uppercase letters only). If text is too long for the safe zone, resize or wrap it creatively to fit within the 40px boundary.
`;
    }

    // --- Visual Focus & Composition ---
    // ... existing code ...

    return textBlock;
}

// AUTO GAME THUMBNAIL ENHANCER (applies @auto-game-thumbnails rule)
function autoEnhanceGamePrompt(originalPrompt, overlayOn = true, detectedLogos = []) {
    // Early-out if no prompt provided
    if (!originalPrompt) return originalPrompt;

    // === CONTEXT-AWARE DISAMBIGUATION: Use new smart detection ===
    const contextAnalysis = analyzePromptContext(originalPrompt);
    
    // === EXPLICIT CALL OF DUTY RESTRICTION ===
    // Special check: Block COD logic unless explicit phrases are present
    const hasExplicitCOD = hasExplicitCallOfDutyPhrases(originalPrompt);
    const containsCODKeyword = originalPrompt.toLowerCase().includes('cod') || originalPrompt.toLowerCase().includes('call of duty');
    
    if (containsCODKeyword && !hasExplicitCOD) {
        // Block gaming logic for ambiguous COD usage (e.g., "cod jobs", "code review")
        return originalPrompt;
    }
    
    // Only apply game enhancement if:
    // 1. Context analysis determines it's gaming-related, OR
    // 2. Gaming brands are explicitly detected (fallback)
    const shouldApplyGaming = contextAnalysis.shouldApplyGamingLogic || hasGamingBrand(detectedLogos);
    
    if (!shouldApplyGaming) {
        // If context is clearly non-gaming (e.g., "code review", "battle cancer"),
        // do not apply game-specific styling. Return original prompt.
        return originalPrompt;
    }
    // === END CONTEXT-AWARE MODIFICATION ===

    const p = originalPrompt.trim();
    const lower = p.toLowerCase();

    // --- 1. Identify if the prompt is about a supported FPS/Battle-Royale game
    const gameKeywords = [
        'fortnite', 'warzone', 'call of duty', 'cod', 'valorant', 'pubg', 'apex', 'apex legends', 'cs2', 'counter strike', 'cs:go', 'minecraft',
        'league of legends', 'lol', 'marvel rivals', 'irl', 'grand theft auto', 'gta', 'gta v', 'dead by daylight', 'dbd',
        'clair obscur', 'expedition 33', 'world of warcraft', 'wow', 'fifa', 'ea sports fc', 'rematch', 'dream con', 'r.e.p.o', 'repo',
        'elden ring', 'overwatch', 'overwatch 2', 'roblox', 'rocket league', 'splitgate', 'star wars battlefront', 'battlefront',
        'clash royale', 'clash of clans', 'escape from tarkov', 'tarkov', 'crystal of atlan', 'tom clancy', 'the division', 'division 2',
        'streamer university', 'tainted grail', 'asmr challenges', 'destiny 2', 'destiny', 'djs', 'hearthstone', 'helldivers', 'helldivers 2',
        'elder scrolls', 'oblivion', 'sea of thieves', 'baldur\'s gate', 'baldurs gate', 'gray zone warfare', 'palia', 'zelda', 'ocarina of time',
        'dota 2', 'dota', 'rust', 'fantasy life', 'doom', 'dark ages', 'stardew valley', 'i\'m only sleeping', 'retro', 'tekken 8', 'tekken',
        'warframe', 'dayz', 'pubg mobile', 'free fire', 'street fighter', 'street fighter 6', 'halo infinite', 'halo', 'valheim',
        'super mario maker', 'mario maker', 'wuthering waves', 'enshrouded', 'dune awakening', 'dune', 'nba 2k', 'nba 2k25', 'pesc',
        'chess', 'mario kart', 'mario kart 8', 'world of tanks', 'the sims', 'sims 4', 'lethal company', 'elder scrolls online',
        '9 kinds', '7 days to die', 'detroit become human', 'detroit', 'red dead redemption', 'rdr2', 'red dead', 'delta force',
        'geoguessr', 'lost ark', 'resident evil', 'need for speed', 'nfs', 'age of empires', 'pokemon', 'heartgold', 'soulsilver',
        'smite 2', 'smite', 'diablo 2', 'diablo', 'snowrunner', 'snow runner', 'brawl stars', 'sonic'
    ];
    const compareKeywords = [' vs ', ' 1v1 ', ' noob vs pro', ' showdown', ' pro vs noob'];

    // === ADDITIONAL SAFETY CHECK: Only proceed if actual game keywords are found ===
    // This ensures we don't apply gaming logic to ambiguous words when context is clearly non-gaming
    const containsGameKeyword = gameKeywords.some(g => lower.includes(g));
    if (!containsGameKeyword) {
        // No actual game names found - return original prompt even if context analysis was uncertain
        return originalPrompt;
    }

    const containsCompare = compareKeywords.some(k => lower.includes(k));

    // --- 2. Resolve canonical game key
    let gameKey = gameKeywords.find(g => lower.includes(g)) || 'the game';
    // Normalise some aliases
    if (gameKey === 'cod') gameKey = 'call of duty';
    if (gameKey === 'apex legends') gameKey = 'apex';
    if (gameKey === 'cs:go') gameKey = 'cs2';
    if (gameKey === 'counter strike') gameKey = 'cs2';
    if (gameKey === 'lol') gameKey = 'league of legends';
    if (gameKey === 'gta' || gameKey === 'gta v') gameKey = 'grand theft auto';
    if (gameKey === 'dbd') gameKey = 'dead by daylight';
    if (gameKey === 'wow') gameKey = 'world of warcraft';
    if (gameKey === 'overwatch') gameKey = 'overwatch 2';
    if (gameKey === 'battlefront') gameKey = 'star wars battlefront';
    if (gameKey === 'tarkov') gameKey = 'escape from tarkov';
    if (gameKey === 'division 2') gameKey = 'tom clancy';
    if (gameKey === 'destiny') gameKey = 'destiny 2';
    if (gameKey === 'elder scrolls' || gameKey === 'oblivion') gameKey = 'elder scrolls oblivion';
    if (gameKey === 'baldurs gate') gameKey = 'baldur\'s gate';
    if (gameKey === 'zelda' || gameKey === 'ocarina of time') gameKey = 'zelda ocarina';
    if (gameKey === 'dota') gameKey = 'dota 2';
    if (gameKey === 'dark ages') gameKey = 'doom';
    if (gameKey === 'tekken') gameKey = 'tekken 8';
    if (gameKey === 'street fighter') gameKey = 'street fighter 6';
    if (gameKey === 'halo') gameKey = 'halo infinite';
    if (gameKey === 'mario maker') gameKey = 'super mario maker';
    if (gameKey === 'mario kart') gameKey = 'mario kart 8';
    if (gameKey === 'sims 4') gameKey = 'the sims';
    if (gameKey === 'detroit') gameKey = 'detroit become human';
    if (gameKey === 'red dead' || gameKey === 'rdr2') gameKey = 'red dead redemption';
    if (gameKey === 'nfs') gameKey = 'need for speed';
    if (gameKey === 'heartgold' || gameKey === 'soulsilver') gameKey = 'pokemon';
    if (gameKey === 'smite') gameKey = 'smite 2';
    if (gameKey === 'snow runner') gameKey = 'snowrunner';

    // --- 3. Up-to-date Game Meta with array pools (maps, chars, weapons) ---
    const rand = arr => arr[Math.floor(Math.random() * arr.length)];

    const gameMeta = {
        'fortnite': {
            chars: [
                'Lynx', 'Midas', 'Spider-Gwen', 'Miles Morales', 'Renegade Runner', 'Peely', 'Valor', 'The Foundation', 'Arnold (T-800)', 'Geralt of Rivia', 'Joni the Red', 'Kylo Ren',
                'Agent Jones', 'Drift', 'Raven', 'Skull Trooper', 'Fishstick', 'Aura', 'Black Knight', 'Dark Voyager', 'Meowscles', 'Cuddle Team Leader', 'Marshmello', 'Travis Scott',
                'John Wick', 'Deadpool', 'Storm', 'Wolverine', 'Carnage', 'Harley Quinn', 'Batman', 'Aquaman', 'Rick Sanchez', 'Guggimon', 'Raz', 'Sunny', 'Lexa', 'Mancake', 'Predator'
            ],
            maps: [
                'Mega City', 'Shattered Slabs', 'Frenzy Fields', 'Brutal Bastion', 'The Citadel', 'Rumble Ruins', 'Sunswoon Lagoon', 'Kenjutsu Crossing', 'Slappy Shores', 'Breakwater Bay',
                'Tilted Towers', 'Greasy Grove', 'Lazy Lake', 'Retail Row', 'Pleasant Park', 'Salty Springs', 'Coral Castle', 'Steamy Stacks', 'Weeping Woods', 'Sweaty Sands', 'Dirty Docks',
                'Risky Reels', 'Boney Burbs', 'Colossal Crops', 'Believer Beach', 'Corny Complex', 'Holly Hedges', 'Misty Meadows', 'Craggy Cliffs', 'Stealthy Stronghold', 'Primal Pond'
            ],
            weapons: [
                'Red-Eye Assault Rifle', 'Havoc Pump Shotgun', 'Kinetic Blade', 'Flapjak Rifle', 'Thunder Shotgun', 'Shockwave Hammer', 'Grapple Glove',
                'Pump Shotgun', 'Scar Assault Rifle', 'Tactical SMG', 'Bolt-Action Sniper Rifle', 'Rocket Launcher', 'Minigun', 'Drum Gun', 'Heavy Sniper Rifle',
                'Infinity Blade', 'Boom Bow', 'Suppressed Pistol', 'Chug Cannon', 'Quad Launcher', 'Charge Shotgun', 'Combat AR', 'Sideways Rifle', 'Primal Flame Bow',
                'Stark Industries Energy Rifle', 'Mythic Goldfish', 'Zapotron', 'Hand Cannon', 'Double Barrel Shotgun', 'Burst Assault Rifle', 'Crossbow', 'Storm Flip'
            ],
            logo: 'Fortnite'
        },
        'league of legends': {
            chars: ['Jinx', 'Vi', 'Yasuo', 'Ahri', 'Thresh', 'Lee Sin', 'Akali', 'Zed', 'Lux', 'Katarina', 'Ezreal', 'Vayne', 'Darius', 'Garen', 'Ashe', 'Jhin', 'K/DA Kai\'Sa', 'Arcane Jinx', 'Spirit Blossom Ahri', 'PROJECT Yasuo'],
            maps: ['Summoner\'s Rift', 'ARAM Howling Abyss', 'Twisted Treeline', 'Crystal Scar', 'Nexus Blitz', 'Arena', 'Wild Rift', 'Dragon Pit', 'Baron Pit', 'Mid Lane'],
            weapons: ['Infinity Edge', 'Rabadon\'s Deathcap', 'Guardian Angel', 'Thornmail', 'Blade of the Ruined King', 'Zhonya\'s Hourglass', 'Last Whisper', 'Banshee\'s Veil', 'Trinity Force', 'Riftmaker', 'Kraken Slayer'],
            logo: 'League of Legends'
        },
        'marvel rivals': {
            chars: ['Spider-Man', 'Iron Man', 'Hulk', 'Captain America', 'Black Widow', 'Thor', 'Doctor Strange', 'Wolverine', 'Storm', 'Magneto', 'Venom', 'Deadpool', 'Scarlet Witch', 'Loki', 'Groot', 'Star-Lord'],
            maps: ['Wakanda', 'Asgard', 'New York City', 'X-Mansion', 'Stark Tower', 'Sanctum Sanctorum', 'Savage Land', 'Dark Dimension', 'Knowhere', 'Hala'],
            weapons: ['Web Shooters', 'Mjolnir', 'Repulsors', 'Adamantium Claws', 'Vibranium Shield', 'Eye of Agamotto', 'Element Guns', 'Widow\'s Bite', 'Pym Particles', 'Power Stone'],
            logo: 'Marvel Rivals'
        },
        'minecraft': {
            chars: ['Steve', 'Alex', 'Enderman', 'Creeper', 'Zombie', 'Skeleton', 'Villager', 'Iron Golem', 'Wither', 'Ender Dragon', 'Herobrine', 'Notch Skin', 'Dream Skin', 'Technoblade Skin'],
            maps: ['Overworld', 'The Nether', 'The End', 'Village', 'Stronghold', 'Nether Fortress', 'End City', 'Ocean Monument', 'Woodland Mansion', 'Bastion Remnant', 'Ancient City'],
            weapons: ['Diamond Sword', 'Netherite Axe', 'Bow and Arrow', 'Crossbow', 'Trident', 'TNT', 'Lava Bucket', 'Enchanted Golden Apple', 'Elytra', 'Shield', 'Firework Rocket'],
            logo: 'Minecraft'
        },
        'grand theft auto': {
            chars: ['Michael De Santa', 'Franklin Clinton', 'Trevor Philips', 'Niko Bellic', 'CJ', 'Tommy Vercetti', 'Claude', 'Protagonist', 'Online Character', 'Lamar Davis'],
            maps: ['Los Santos', 'Vice City', 'Liberty City', 'San Andreas', 'Vinewood', 'Grove Street', 'Mount Chiliad', 'Paleto Bay', 'Sandy Shores', 'Downtown'],
            weapons: ['Assault Rifle', 'RPG', 'Sniper Rifle', 'Pistol', 'SMG', 'Grenade', 'Baseball Bat', 'Knife', 'Heavy Sniper', 'Minigun', 'Sticky Bomb'],
            logo: 'Grand Theft Auto'
        },
        'dead by daylight': {
            chars: ['The Trapper', 'Dwight Fairfield', 'Meg Thomas', 'Claudette Morel', 'Jake Park', 'The Wraith', 'The Hillbilly', 'The Nurse', 'Michael Myers', 'Laurie Strode', 'The Huntress', 'Feng Min', 'The Spirit', 'Legion', 'The Demogorgon'],
            maps: ['MacMillan Estate', 'Autohaven Wreckers', 'Coldwind Farm', 'Crotus Prenn Asylum', 'Haddonfield', 'Backwater Swamp', 'Léry\'s Memorial Institute', 'Red Forest', 'Springwood', 'Gideon Meat Plant'],
            weapons: ['Bear Trap', 'Chainsaw', 'Hunting Hatchets', 'Kitchen Knife', 'Wailing Bell', 'Bone Saw', 'Feral Frenzy', 'Demogorgon Portals', 'Flashlight', 'Medkit', 'Toolbox'],
            logo: 'Dead by Daylight'
        },
        'world of warcraft': {
            chars: ['Thrall', 'Jaina Proudmoore', 'Illidan Stormrage', 'Arthas', 'Sylvanas Windrunner', 'Anduin Wrynn', 'Varian Wrynn', 'Garrosh Hellscream', 'Tyrande Whisperwind', 'Malfurion Stormrage', 'Khadgar', 'Gul\'dan'],
            maps: ['Stormwind City', 'Orgrimmar', 'Ironforge', 'Thunder Bluff', 'Darnassus', 'Undercity', 'Shattrath City', 'Dalaran', 'Boralus', 'Dazar\'alor', 'Oribos'],
            weapons: ['Ashbringer', 'Frostmourne', 'Gorehowl', 'Thunderfury', 'Sulfuras', 'Doomhammer', 'The Kingslayers', 'Ebonchill', 'Aluneth', 'Xal\'atath'],
            logo: 'World of Warcraft'
        },
        'overwatch 2': {
            chars: ['Tracer', 'Widowmaker', 'Reaper', 'Soldier: 76', 'Cassidy', 'Pharah', 'Reinhardt', 'Winston', 'D.Va', 'Roadhog', 'Mercy', 'Lucio', 'Symmetra', 'Zenyatta', 'Genji', 'Hanzo', 'Junkrat', 'Mei', 'Bastion', 'Torbjörn', 'Ana', 'Sombra', 'Orisa', 'Doomfist', 'Moira', 'Brigitte', 'Wrecking Ball', 'Ashe', 'Baptiste', 'Sigma', 'Echo', 'Sojourn', 'Junker Queen', 'Kiriko', 'Ramattra', 'Lifeweaver', 'Mauga', 'Venture', 'Juno', 'Hazard', 'Graffiti Tracer', 'Punk Tracer', 'Rose Tracer', 'Huntress Widowmaker', 'Odette Widowmaker', 'Blackwatch Reaper', 'Mariachi Reaper', 'Strike Commander Morrison', 'Grillmaster: 76', 'Lifeguard McCree', 'Van Helsing McCree', 'Security Chief Pharah', 'Mechaqueen Pharah', 'Bundeswehr Reinhardt', 'Crusader Reinhardt', 'Frogston Winston', 'Yeti Winston', 'B.Va D.Va', 'Cruiser D.Va', 'Junker Roadhog', 'Mako Roadhog', 'Witch Mercy', 'Pink Mercy', 'Jazzy Lucio', 'Ribbit Lucio', 'Dragon Symmetra', 'Oasis Symmetra', 'Cultist Zenyatta', 'Nutcracker Zenyatta', 'Oni Genji', 'Blackwatch Genji', 'Lone Wolf Hanzo', 'Okami Hanzo', 'Dr. Junkenstein', 'Scarecrow Junkrat', 'Mei-rry', 'Pajamei', 'Overgrown Bastion', 'Tombstone Bastion', 'Barbarossa Torbjörn', 'Santaclad Torbjörn'],
            maps: ['King\'s Row', 'Hanamura', 'Temple of Anubis', 'Volskaya Industries', 'Dorado', 'Route 66', 'Watchpoint: Gibraltar', 'Numbani', 'Hollywood', 'Eichenwalde', 'Lijiang Tower', 'Nepal', 'Ilios', 'Oasis', 'Junkertown', 'Horizon Lunar Colony', 'Blizzard World', 'Rialto', 'Busan', 'Paris', 'Havana', 'Malevento', 'Kanezaka', 'Rome', 'New York', 'Toronto', 'Rio de Janeiro', 'Gothenburg', 'Esperança', 'Samoa', 'Runasapi', 'Hanaoka', 'Suravasa', 'New Junk City', 'Petra', 'Château Guillard', 'Necropolis', 'Ayutthaya', 'Castillo', 'Black Forest', 'Ecopoint: Antarctica', 'King\'s Row (Winter)', 'Hanamura (Winter)', 'Hollywood (Halloween)', 'Eichenwalde (Halloween)', 'Junkenstein\'s Revenge', 'Retribution', 'Storm Rising', 'Archives'],
            weapons: ['Pulse Pistols', 'Widow\'s Kiss', 'Hellfire Shotguns', 'Heavy Pulse Rifle', 'Peacekeeper', 'Rocket Launcher', 'Rocket Hammer', 'Tesla Cannon', 'Fusion Cannons', 'Scrap Gun', 'Caduceus Staff', 'Sonic Amplifier', 'Biotic Rifle', 'Machine Pistol', 'Fusion Driver', 'Hand Cannon', 'Biotic Grasp', 'Rocket Flail', 'Quad Cannons', 'The Viper', 'Biotic Launcher', 'Hyperspheres', 'Pulse Rifle', 'Scattergun', 'Kunai', 'Void Accelerator', 'Healing Blossom', 'Incendiary Chaingun', 'Smart Excavator', 'Mediblaster', 'Cryo-Cannon', 'Golden Pulse Pistols', 'Golden Widow\'s Kiss', 'Golden Hellfire Shotguns', 'Golden Heavy Pulse Rifle', 'Golden Peacekeeper', 'Golden Rocket Launcher', 'Golden Rocket Hammer', 'Golden Tesla Cannon', 'Golden Fusion Cannons', 'Golden Scrap Gun', 'Golden Caduceus Staff', 'Golden Sonic Amplifier', 'Legendary Pulse Pistols', 'Epic Widow\'s Kiss', 'Rare Hellfire Shotguns', 'Mythic Heavy Pulse Rifle'],
            icons: ['Overwatch Logo', 'Hero Icons', 'Ability Icons', 'Ultimate Icons', 'Rank Icons', 'Bronze Rank', 'Silver Rank', 'Gold Rank', 'Platinum Rank', 'Diamond Rank', 'Master Rank', 'Grandmaster Rank', 'Top 500 Icon', 'Competitive Points', 'Credits Icon', 'Overwatch Coins', 'Legacy Credits', 'Health Pack', 'Armor Pack', 'Shield Generator', 'Teleporter', 'Payload Icon', 'Capture Point', 'Spawn Room', 'Respawn Timer', 'Kill Feed', 'Scoreboard', 'Team Chat', 'Voice Chat', 'Endorsement Icons', 'Good Teammate', 'Sportsmanship', 'Shot Caller', 'Player Level', 'Prestige Stars', 'Golden Weapons', 'Achievement Icons', 'Spray Icons', 'Voice Line Icons', 'Emote Icons', 'Victory Pose Icons', 'Highlight Intro Icons', 'Player Icon Portraits', 'Event Icons', 'Seasonal Icons', 'Anniversary Event', 'Summer Games', 'Halloween Terror', 'Winter Wonderland', 'Lunar New Year', 'Archives Event', 'Role Queue Icons', 'Tank Role', 'Damage Role', 'Support Role', 'Flex Role', 'Priority Pass', 'Group Finder', 'Looking for Group', 'Custom Game Browser', 'Workshop Codes', 'Replay Viewer', 'Career Profile', 'Statistics', 'Match History', 'Friends List', 'Battle.net Integration', 'Cross-Platform Play', 'Console Icons', 'PC Icons', 'Mobile Icons', 'Overwatch League', 'OWL Team Icons', 'Contenders Icons', 'World Cup Icons', 'POTG Icon', 'Play of the Game', 'Highlight System', 'Medal Icons', 'Gold Medal', 'Silver Medal', 'Bronze Medal', 'Fire Meter', 'On Fire Icon', 'Critical Health', 'Low Health Warning', 'Ability Cooldown', 'Ultimate Charge', 'Ammo Counter', 'Reload Indicator', 'Damage Numbers', 'Healing Numbers', 'Barrier Health', 'Overhealth', 'Damage Boost', 'Speed Boost', 'Nano Boost', 'Supercharger', 'Sound Barrier', 'Transcendence', 'Valkyrie', 'Primal Rage', 'Dragonblade', 'Deadeye', 'Tactical Visor', 'Rocket Barrage', 'Earthshatter', 'Graviton Surge', 'Dragonstrike', 'Blizzard', 'Configuration Tank', 'Molten Core', 'Resurrect', 'Hack Indicator', 'EMP Effect', 'Fortify', 'Halt Effect', 'Meteor Strike', 'Coalescence', 'Rally', 'Adaptive Shield', 'Piledriver', 'B.O.B.', 'Immortality Field', 'Accretion', 'Duplicate', 'Disruptor Shot', 'Commanding Shout', 'Protection Suzu', 'Nemesis Form', 'Petal Platform', 'Cardiac Overdrive', 'Burrow', 'Hyper-Mobility', 'Pulsar Torpedoes', 'Spike Trap', 'Vanadium Plating'],
            chars: ['Tracer', 'Widowmaker', 'Reaper', 'Soldier: 76', 'McCree', 'Pharah', 'Reinhardt', 'Winston', 'D.Va', 'Roadhog', 'Mercy', 'Lucio', 'Symmetra', 'Zenyatta', 'Genji', 'Hanzo', 'Junkrat', 'Mei', 'Bastion', 'Torbjörn'],
            maps: ['King\'s Row', 'Hanamura', 'Temple of Anubis', 'Volskaya Industries', 'Dorado', 'Route 66', 'Watchpoint: Gibraltar', 'Numbani', 'Hollywood', 'Eichenwalde', 'Lijiang Tower', 'Nepal', 'Ilios', 'Oasis'],
            weapons: ['Pulse Pistols', 'Widow\'s Kiss', 'Hellfire Shotguns', 'Heavy Pulse Rifle', 'Peacekeeper', 'Rocket Launcher', 'Rocket Hammer', 'Tesla Cannon', 'Fusion Cannons', 'Scrap Gun', 'Caduceus Staff', 'Sonic Amplifier'],
            logo: 'Overwatch 2'
        },
        'roblox': {
            chars: ['Roblox Noob', 'Roblox Pro', 'Builderman', 'Shedletsky', 'Telamon', 'John Doe', 'Jane Doe', 'Guest', 'Robux Rich Player', 'Default Avatar', 'Bacon Hair', 'Oof Sound Guy'],
            maps: ['Adopt Me!', 'Brookhaven', 'MeepCity', 'Jailbreak', 'Arsenal', 'Phantom Forces', 'Murder Mystery 2', 'Tower of Hell', 'Natural Disaster Survival', 'Work at a Pizza Place'],
            weapons: ['Sword', 'Rocket Launcher', 'Paintball Gun', 'Knife', 'Taser', 'Handcuffs', 'Jetpack', 'Grappling Hook', 'Robux', 'Building Tools', 'Admin Commands'],
            logo: 'ROBLOX'
        },
        'rocket league': {
            chars: ['Octane', 'Dominus', 'Breakout', 'Batmobile', 'Fennec', 'Merc', 'Road Hog', 'X-Devil', 'Takumi', 'Venom', 'Backfire', 'Gizmo'],
            maps: ['DFH Stadium', 'Mannfield', 'Champions Field', 'Urban Central', 'Beckwith Park', 'Utopia Coliseum', 'Wasteland', 'Neo Tokyo', 'Aquadome', 'Starbase ARC', 'Farmstead', 'Salty Shores'],
            weapons: ['Boost', 'Ball', 'Demolition', 'Supersonic Speed', 'Jump', 'Double Jump', 'Air Roll', 'Powerslide', 'Ball Cam', 'Quick Chat'],
            logo: 'Rocket League'
        },
        'clash royale': {
            chars: ['Barbarian King', 'Archer Queen', 'Princess', 'Ice Wizard', 'Electro Wizard', 'Night Witch', 'Bandit', 'Inferno Dragon', 'Lumberjack', 'Miner', 'Sparky', 'Graveyard'],
            maps: ['Royal Arena', 'Barbarian Bowl', 'P.E.K.K.A\'s Playhouse', 'Spell Valley', 'Builder\'s Workshop', 'Jungle Arena', 'Hog Mountain', 'Electro Valley', 'Spooky Town', 'Rascal\'s Hideout'],
            weapons: ['Lightning', 'Fireball', 'Arrows', 'Zap', 'Rocket', 'Poison', 'Freeze', 'Tornado', 'Clone', 'Rage', 'Heal', 'Mirror'],
            logo: 'Clash Royale'
        },
        'clash of clans': {
            chars: ['Barbarian King', 'Archer Queen', 'Grand Warden', 'Royal Champion', 'Barbarian', 'Archer', 'Giant', 'Wizard', 'Dragon', 'P.E.K.K.A', 'Hog Rider', 'Valkyrie'],
            maps: ['Home Village', 'Builder Base', 'Clan Capital', 'War Base', 'Goblin Map', 'Single Player Campaign', 'Clan War Leagues', 'Legend League'],
            weapons: ['Lightning Spell', 'Rage Spell', 'Heal Spell', 'Jump Spell', 'Freeze Spell', 'Clone Spell', 'Invisibility Spell', 'Recall Spell', 'Poison Spell', 'Earthquake Spell'],
            logo: 'Clash of Clans'
        },
        'escape from tarkov': {
            chars: ['PMC', 'Scav', 'Killa', 'Reshala', 'Gluhar', 'Sanitar', 'Tagilla', 'Shturman', 'BEAR Operative', 'USEC Operative', 'Fence', 'Therapist'],
            maps: ['Customs', 'Factory', 'Woods', 'Shoreline', 'Interchange', 'The Lab', 'Reserve', 'Lighthouse', 'Streets of Tarkov', 'Ground Zero'],
            weapons: ['AK-74M', 'M4A1', 'AS VAL', 'HK 416', 'RSASS', 'Mosin Nagant', 'VSS Vintorez', 'SA-58', 'MP7', 'Vector .45 ACP', 'REAP-IR', 'NVG'],
            logo: 'Escape from Tarkov'
        },
        'destiny 2': {
            chars: ['Hunter', 'Titan', 'Warlock', 'Cayde-6', 'Zavala', 'Ikora Rey', 'Ghost', 'The Guardian', 'Saint-14', 'Osiris', 'Ana Bray', 'Eris Morn'],
            maps: ['The Tower', 'European Dead Zone', 'Titan', 'Nessus', 'Io', 'Mercury', 'Mars', 'The Tangled Shore', 'The Dreaming City', 'Europa', 'The Throne World'],
            weapons: ['Ace of Spades', 'Gjallarhorn', 'The Last Word', 'Thorn', 'Whisper of the Worm', 'Sleeper Simulant', 'Riskrunner', 'Sunshot', 'Graviton Lance', 'Sweet Business'],
            logo: 'Destiny 2'
        },
        'elden ring': {
            chars: ['Tarnished', 'Melina', 'Ranni the Witch', 'Margit the Fell Omen', 'Godrick the Grafted', 'Rennala', 'Radahn', 'Malenia', 'Mohg', 'Morgott', 'Godfrey', 'Radagon'],
            maps: ['Limgrave', 'Liurnia of the Lakes', 'Caelid', 'Altus Plateau', 'Mt. Gelmir', 'Leyndell', 'Mountaintops of the Giants', 'Crumbling Farum Azula', 'Mohgwyn Palace', 'Siofra River'],
            weapons: ['Uchigatana', 'Bloodhound\'s Fang', 'Rivers of Blood', 'Moonveil', 'Greatsword', 'Colossal Sword', 'Staff of Loss', 'Meteorite Staff', 'Dragon Communion Seal', 'Fingerprint Stone Shield'],
            logo: 'Elden Ring'
        },
        'dota 2': {
            chars: ['Pudge', 'Invoker', 'Anti-Mage', 'Crystal Maiden', 'Drow Ranger', 'Juggernaut', 'Phantom Assassin', 'Shadow Fiend', 'Windranger', 'Zeus', 'Sniper', 'Axe', 'Legion Commander', 'Techies'],
            maps: ['Radiant Base', 'Dire Base', 'Secret Shop', 'Roshan Pit', 'Ancient Camps', 'River', 'High Ground', 'Low Ground', 'Jungle', 'Mid Lane', 'Safe Lane', 'Off Lane'],
            weapons: ['Divine Rapier', 'Aegis of the Immortal', 'Black King Bar', 'Daedalus', 'Monkey King Bar', 'Bloodthorn', 'Scythe of Vyse', 'Refresher Orb', 'Heart of Tarrasque', 'Butterfly'],
            logo: 'Dota 2'
        },
        'street fighter 6': {
            chars: ['Ryu', 'Chun-Li', 'Ken', 'Zangief', 'Dhalsim', 'Blanka', 'E. Honda', 'Guile', 'Cammy', 'Dee Jay', 'Rashid', 'Juri', 'Kimberly', 'Marisa', 'Manon', 'Modern Fighter'],
            maps: ['Suzaku Castle', 'Bustling Side Street', 'Fighting Ground', 'Metro City', 'Genbu Temple', 'The Macho Ring', 'Dhalsim\'s Stage', 'Training Room', 'World Tour', 'Battle Hub'],
            weapons: ['Hadoken', 'Shoryuken', 'Tatsumaki', 'Sonic Boom', 'Lightning Legs', 'Spinning Bird Kick', 'Flash Kick', 'Tiger Knee', 'Psycho Crusher', 'Critical Art'],
            logo: 'Street Fighter 6'
        },
        'halo infinite': {
            chars: ['Master Chief', 'Cortana', 'The Weapon', 'Captain Lasky', 'Commander Palmer', 'Arbiter', 'Escharum', 'Atriox', 'The Pilot', 'Spartan Locke', 'Blue Team', 'Fireteam Osiris'],
            maps: ['Zeta Halo', 'Aquarius', 'Bazaar', 'Behemoth', 'Catalyst', 'Deadlock', 'Fragmentation', 'High Power', 'Launch Site', 'Live Fire', 'Recharge', 'Streets'],
            weapons: ['MA40 Assault Rifle', 'BR75 Battle Rifle', 'VK78 Commando', 'Energy Sword', 'Gravity Hammer', 'Rocket Launcher', 'Sniper Rifle', 'Shotgun', 'Plasma Pistol', 'Needler'],
            logo: 'Halo Infinite'
        },
        'super mario maker': {
            chars: ['Mario', 'Luigi', 'Princess Peach', 'Bowser', 'Yoshi', 'Toad', 'Koopa Troopa', 'Goomba', 'Piranha Plant', 'Hammer Bro', 'Lakitu', 'Shy Guy'],
            maps: ['Ground Theme', 'Underground Theme', 'Underwater Theme', 'Desert Theme', 'Snow Theme', 'Sky Theme', 'Forest Theme', 'Ghost House Theme', 'Airship Theme', 'Castle Theme'],
            weapons: ['Super Mushroom', 'Fire Flower', 'Cape Feather', 'Raccoon Suit', 'Frog Suit', 'Hammer Suit', 'Tanooki Suit', 'Super Star', 'P-Switch', 'POW Block'],
            logo: 'Super Mario Maker'
        },
        'mario kart 8': {
            chars: ['Mario', 'Luigi', 'Princess Peach', 'Bowser', 'Yoshi', 'Toad', 'Koopa Troopa', 'Shy Guy', 'Donkey Kong', 'Wario', 'Waluigi', 'Rosalina', 'King Boo', 'Dry Bones'],
            maps: ['Mario Kart Stadium', 'Water Park', 'Sweet Sweet Canyon', 'Thwomp Ruins', 'Mario Circuit', 'Toad Harbor', 'Twisted Mansion', 'Shy Guy Falls', 'Anti-Gravity Sections', 'Rainbow Road'],
            weapons: ['Green Shell', 'Red Shell', 'Blue Shell', 'Banana Peel', 'Mushroom', 'Triple Mushroom', 'Star', 'Lightning Bolt', 'Fire Flower', 'Boomerang Flower'],
            logo: 'Mario Kart 8 Deluxe'
        },
        'the sims': {
            chars: ['Bella Goth', 'Mortimer Goth', 'Don Lothario', 'Nancy Landgraab', 'Vlad', 'Johnny Zest', 'Eliza Pancakes', 'Bob Pancakes', 'Judith Ward', 'Custom Sim'],
            maps: ['Willow Creek', 'Oasis Springs', 'Newcrest', 'Magnolia Promenade', 'Sixam', 'Granite Falls', 'Selvadorada', 'Windenburg', 'San Myshuno', 'Forgotten Hollow'],
            weapons: ['Cowplant', 'Death by Embarrassment', 'Voodoo Doll', 'Lightning Strike', 'Rocket Ship Crash', 'Pufferfish Nigiri', 'Murphy Bed', 'Killer Rabbit', 'Flower Arrangement'],
            logo: 'The Sims 4'
        },
        'red dead redemption': {
            chars: ['Arthur Morgan', 'John Marston', 'Dutch van der Linde', 'Micah Bell', 'Sadie Adler', 'Charles Smith', 'Hosea Matthews', 'Abigail Roberts', 'Jack Marston', 'Bill Williamson'],
            maps: ['Valentine', 'Saint Denis', 'Rhodes', 'Strawberry', 'Blackwater', 'Armadillo', 'Tumbleweed', 'Guarma', 'New Austin', 'West Elizabeth', 'Lemoyne', 'New Hanover'],
            weapons: ['Cattleman Revolver', 'Schofield Revolver', 'Lancaster Repeater', 'Bolt Action Rifle', 'Springfield Rifle', 'Pump Action Shotgun', 'Sawed-off Shotgun', 'Tomahawk', 'Lasso', 'Dynamite'],
            logo: 'Red Dead Redemption'
        },
        'pokemon': {
            chars: ['Pikachu', 'Charizard', 'Blastoise', 'Venusaur', 'Mewtwo', 'Mew', 'Lugia', 'Ho-Oh', 'Celebi', 'Kyogre', 'Groudon', 'Rayquaza', 'Dialga', 'Palkia', 'Giratina', 'Arceus'],
            maps: ['Pallet Town', 'Viridian City', 'Pewter City', 'Cerulean City', 'Vermilion City', 'Saffron City', 'Celadon City', 'Fuchsia City', 'Cinnabar Island', 'Indigo Plateau', 'New Bark Town'],
            weapons: ['Poké Ball', 'Great Ball', 'Ultra Ball', 'Master Ball', 'Thunderbolt', 'Flamethrower', 'Hydro Pump', 'Solar Beam', 'Psychic', 'Earthquake', 'Ice Beam', 'Dragon Pulse'],
            logo: 'Pokémon'
        },
        'zelda ocarina': {
            chars: ['Link', 'Princess Zelda', 'Ganondorf', 'Navi', 'Saria', 'Epona', 'King of Red Lions', 'Midna', 'Sheik', 'Impa', 'Darunia', 'Ruto'],
            maps: ['Hyrule Field', 'Kokiri Forest', 'Goron City', 'Zora\'s Domain', 'Lake Hylia', 'Gerudo Valley', 'Death Mountain', 'Lost Woods', 'Hyrule Castle', 'Ganon\'s Castle'],
            weapons: ['Master Sword', 'Hylian Shield', 'Bow and Arrow', 'Hookshot', 'Boomerang', 'Bombs', 'Fire Arrows', 'Ice Arrows', 'Light Arrows', 'Ocarina of Time'],
            logo: 'The Legend of Zelda'
        },
        'elder scrolls oblivion': {
            chars: ['Hero of Kvatch', 'Martin Septim', 'Uriel Septim VII', 'Mankar Camoran', 'Lucien Lachance', 'Vicente Valtieri', 'Sheogorath', 'Mehrunes Dagon', 'Gray Fox', 'Baurus'],
            maps: ['Imperial City', 'Anvil', 'Bravil', 'Bruma', 'Cheydinhal', 'Chorrol', 'Leyawiin', 'Skingrad', 'Kvatch', 'Shivering Isles'],
            weapons: ['Blade of Woe', 'Dawnbreaker', 'Umbra', 'Goldbrand', 'Volendrung', 'Azura\'s Star', 'Ring of Khajiiti', 'Staff of Everscamp', 'Wabbajack', 'Mehrunes\' Razor'],
            logo: 'The Elder Scrolls'
        },
        'warzone': {
            chars: ['Ghost', 'Price', 'Valeria', 'Soap', 'Farah', 'Graves', 'Roze', 'Nikto'],
            maps: ['Al Mazrah', 'Urzikstan', 'Vondel', 'Fortune\'s Keep', 'Ashika Island'],
            weapons: ['TAQ-56', 'ISO Hemlock', 'Kastov-74U', 'Fennec 45', 'Victus XMR', 'MCPR-300', 'RPK'],
            logo: 'Call of Duty Warzone'
        },
        'call of duty': {
            chars: ['Ghost', 'Soap', 'Price', 'Makarov', 'Operator Mace', 'Operator Gaia'],
            maps: ['Highrise', 'Terminal', 'Rust', 'Shipment', 'Sub Base', 'Karachi', 'Skidrow'],
            weapons: ['MCW', 'BAS-B', 'AMR9', 'Holger 556', 'FR Avancer', 'KV Inhibitor'],
            logo: 'Call of Duty'
        },
        'valorant': {
            chars: ['Iso', 'Fade', 'Jett', 'Chamber', 'Raze', 'Viper', 'Harbor', 'Gekko'],
            maps: ['Lotus', 'Sunset', 'Pearl', 'Fracture', 'Bind', 'Ascent', 'Icebox'],
            weapons: ['Vandal', 'Phantom', 'Odin', 'Bulldog', 'Spectre', 'Operator'],
            logo: 'Valorant'
        },
        'pubg': {
            chars: ['Survivor in Level 3 Helmet', 'Paramo Explorer', 'Rondo Spec-Ops'],
            maps: ['Taego', 'Deston', 'Rondo', 'Vikendi Reborn', 'Miramar'],
            weapons: ['ACE32', 'Mk12', 'Mosin-Nagant', 'P90', 'K2'],
            logo: 'PUBG'
        },
        'apex': {
            chars: ['Conduit', 'Ballistic', 'Mad Maggie', 'Seer', 'Valkyrie', 'Horizon', 'Catalyst', 'Octane'],
            maps: ['Broken Moon', 'Storm Point', 'World\'s Edge', 'Kings Canyon', 'Olympus'],
            weapons: ['Nemesis AR', 'CAR SMG', '30-30 Repeater', 'Peacekeeper', 'R-301 Carbine'],
            logo: 'Apex Legends'
        },
        'cs2': {
            chars: ['Elite Crew T', 'SEAL Team CT', 'Operator Skin \"Soldier\"'],
            maps: ['Anubis', 'Ancient', 'Mirage', 'Nuke', 'Overpass', 'Vertigo', 'Dust II'],
            weapons: ['AK-47', 'M4A1-S', 'AWP', 'Desert Eagle', 'FAMAS', 'USP-S'],
            logo: 'Counter-Strike 2'
        },
        // Adding more games to complete the comprehensive list
        'tekken 8': {
            chars: ['Kazuya Mishima', 'Jin Kazama', 'Heihachi Mishima', 'Paul Phoenix', 'King', 'Nina Williams', 'Yoshimitsu', 'Hwoarang', 'Xiaoyu', 'Law', 'Steve Fox', 'Asuka Kazama'],
            maps: ['Mishima Dojo', 'King of Iron Fist Arena', 'Fallen Colony', 'Elegant Palace', 'Seaside Resort', 'Urban Square', 'Dragon\'s Nest', 'Geometric Plane'],
            weapons: ['Devil Gene', 'Rage Art', 'Heat System', 'Combo Attacks', 'Throws', 'Special Moves', 'Counter Hits', 'Wall Combos', 'Floor Breaks', 'Balcony Breaks'],
            logo: 'TEKKEN 8'
        },
        'rust': {
            chars: ['Naked Player', 'Geared Player', 'Scientist', 'Military Soldier', 'Bandit', 'Outpost Guard', 'Helicopter Pilot', 'Bradley Tank Crew'],
            maps: ['Rust Island', 'Large Oil Rig', 'Small Oil Rig', 'Launch Site', 'Military Tunnels', 'Water Treatment Plant', 'Power Plant', 'Airfield', 'Dome', 'Satellite Dish'],
            weapons: ['AK-47', 'LR-300', 'Thompson', 'Custom SMG', 'Bolt Action Rifle', 'L96', 'Rocket Launcher', 'C4', 'F1 Grenade', 'Crossbow'],
            logo: 'RUST'
        },
        'hearthstone': {
            chars: ['Jaina Proudmoore', 'Rexxar', 'Valeera Sanguinar', 'Uther Lightbringer', 'Thrall', 'Anduin Wrynn', 'Gul\'dan', 'Malfurion Stormrage', 'Garrosh Hellscream'],
            maps: ['Stormwind', 'Orgrimmar', 'Pandaria', 'Naxxramas', 'Blackrock Mountain', 'League of Explorers', 'Karazhan', 'Un\'Goro Crater', 'Knights of the Frozen Throne'],
            weapons: ['Fiery War Axe', 'Truesilver Champion', 'Eaglehorn Bow', 'Doomhammer', 'Ashbringer', 'Gorehowl', 'Sword of Justice', 'Perdition\'s Blade'],
            logo: 'Hearthstone'
        },
        'stardew valley': {
            chars: ['The Farmer', 'Abigail', 'Alex', 'Elliott', 'Emily', 'Haley', 'Harvey', 'Leah', 'Maru', 'Penny', 'Sam', 'Sebastian', 'Shane', 'Caroline', 'Clint', 'Demetrius'],
            maps: ['Pelican Town', 'The Farm', 'Community Center', 'JojaMart', 'The Mines', 'Skull Cavern', 'The Desert', 'Ginger Island', 'Beach', 'Forest', 'Mountain', 'Town Square'],
            weapons: ['Galaxy Sword', 'Lava Katana', 'Obsidian Edge', 'Neptune\'s Glaive', 'Yeti Tooth', 'Prismatic Shard', 'Dwarvish Safety Manual', 'Slime Charmer Ring'],
            logo: 'Stardew Valley'
        },
        'valheim': {
            chars: ['Viking Warrior', 'Eikthyr', 'The Elder', 'Bonemass', 'Moder', 'Yagluth', 'Hugin', 'Haldor the Merchant', 'Odin'],
            maps: ['Meadows', 'Black Forest', 'Swamp', 'Mountains', 'Plains', 'Ocean', 'Mistlands', 'Ashlands', 'Deep North'],
            weapons: ['Mjolnir', 'Frostner', 'Silver Sword', 'Blackmetal Sword', 'Draugr Fang', 'Huntsman Bow', 'Iron Sledge', 'Crystal Battleaxe'],
            logo: 'Valheim'
        },
        'brawl stars': {
            chars: ['Shelly', 'Nita', 'Colt', 'Bull', 'Jessie', 'Brock', 'Dynamike', 'Bo', 'Tick', 'Emz', 'Stu', 'Edgar', 'Bibi', 'Rosa', 'Jacky', 'Gale'],
            maps: ['Gem Grab Maps', 'Showdown Maps', 'Brawl Ball Maps', 'Bounty Maps', 'Heist Maps', 'Siege Maps', 'Hot Zone Maps', 'Knockout Maps'],
            weapons: ['Shotgun', 'Bear', 'Six-Shooters', 'Double Barrel', 'Shock Rifle', 'Rocket Launcher', 'Short Fuse', 'Explosive Arrows', 'Head First', 'Hairspray'],
            logo: 'Brawl Stars'
        }
    };

    const metaPool = gameMeta[gameKey] || {
        chars: ['a top current character'],
        maps: ['a current map location'],
        weapons: ['a popular weapon'],
        logo: gameKey
    };

    const meta = {
        char: rand(metaPool.chars),
        map: rand(metaPool.maps),
        weapon: rand(metaPool.weapons),
        logo: metaPool.logo
    };

    // --- 4. Layout variation to reduce repetition
    const layouts = [
        'split-screen face-off',
        'mirrored action pose',
        'side-by-side with dramatic dutch-angle',
        'one character in foreground, the rival in background with depth-of-field blur'
    ];
    // Use simple RNG for layout variety
    const layout = layouts[Math.floor(Math.random() * layouts.length)];

    // --- 5. Overlay logic
    let overlayClause;
    if (overlayOn) {
        const overlayTextSuggested = p.toUpperCase();
        overlayClause = ` Add bold, uppercase, glowing high-contrast text overlay (e.g., \"${overlayTextSuggested}\") in a modern font, positioned for maximum impact.`;
    } else {
        overlayClause = ' Do NOT include any text overlay.';
    }

    // --- 6. Build enhanced prompt
    const versusNote = containsCompare
        ? ' Depict a dramatic versus/comparison scenario with clear tension between the two sides.'
        : '';

    return `Create a cinematic YouTube thumbnail at EXACTLY 1280x720 pixels featuring instantly recognizable, official in-game characters from ${gameKey} (e.g., ${meta.char}) in a ${layout}.${versusNote} Characters must use game-authentic gear, facial expressions, and art style. Background: show ${meta.map}, rendered with faithful textures, lighting, and props from the real game. Integrate the official ${meta.logo} logo subtly into the composition. Ensure art style, color palette, and overall look precisely match ${gameKey}. Avoid using generic avatars or repeating the same background/character arrangement as previous thumbnails.${overlayClause}`;
}

/**
 * Builds a detailed prompt for GPT-image-1 YouTube thumbnail generation,
 * including face reference matching and all user controls.
 *
 * @param {Object} options
 * @param {string} options.userPrompt - The main topic or idea from the user.
 * @param {boolean} options.includePerson - Whether to include a person.
 * @param {string} options.mood - Mood for the person (e.g., "Excited", "Serious").
 * @param {boolean} options.includeIcons - Whether to include icons.
 * @param {boolean} options.textOverlay - Whether to add text overlay.
 * @param {string} options.overlayText - Custom text for overlay (if any).
 * @param {string} options.overlayPosition - Where to place the overlay text.
 * @param {string} options.faceDescription - Description of the reference face (if any).
 * @returns {string} The full prompt for the image generation API.
 */
export function buildPrompt({
  userPrompt,
  includePerson,
  mood,
  includeIcons,
  textOverlay,
  selectedExpression,
  faceDescription,
  overlayText,
  overlayPosition,
  selectedTextSize,
  selectedFontFamily,
  primaryTextColor,
  secondaryTextColor,
  fitFullCanvas,
  selectedGender,
  selectedBackgroundType,
  selectedBackgroundStyleId,
  selectedSolidBgColor,
  selectedColorGrade,
  iconRenderingMode = 'auto' // NEW: Icon rendering mode parameter
}) {
    // --- Brand Logo Detection with Context-Aware Placement (runs regardless of icon toggle) ---
    // Use the enhanced detection with placement analysis
    const brandAnalysis = detectBrandLogosWithPlacement(userPrompt, 3, overlayText || '', overlayPosition || '');
    let detectedBrandLogos = brandAnalysis.brands;
    const logoPlacementInstructions = brandAnalysis.logoInstructions;
    
    // === CONTEXT-AWARE BRAND FILTERING ===
    // Apply context-aware filtering to prevent unwanted brand icon generation
    // This prevents issues like "make" generating Make.com icons when used as a general verb
    const originalBrandCount = detectedBrandLogos.length;
    detectedBrandLogos = filterBrandsForIconGeneration(detectedBrandLogos, userPrompt);
    
    // Debug logging (can be removed in production)
    if (originalBrandCount !== detectedBrandLogos.length) {
        console.log(`[Context Filter] Filtered brands from ${originalBrandCount} to ${detectedBrandLogos.length} based on context analysis`);
    }

    // === ENHANCED CONTEXT-AWARE GAMING DETECTION PHASE 1 ===
    // Perform initial gaming detection to determine if autoEnhanceGamePrompt should be applied
    const preliminaryGameContext = detectEnhancedGameContext(userPrompt);
    
    // Only apply gaming enhancement if high confidence gaming content is detected
    if (preliminaryGameContext.isGaming && preliminaryGameContext.confidence > 0.80) {
        console.log(`[Gaming Enhancement] Applying gaming logic: ${preliminaryGameContext.reasoning}`);
    userPrompt = autoEnhanceGamePrompt(userPrompt, textOverlay, detectedBrandLogos);
    } else {
        console.log(`[Gaming Enhancement] Skipping gaming logic: ${preliminaryGameContext.reasoning} (${Math.round(preliminaryGameContext.confidence * 100)}% confidence)`);
    }
    
    // === SMART TEXT & GRAPHICS SEPARATION: Sanitize external LLM prompts ===
    const { sanitizedPrompt, detectedHeadlines } = sanitizeExternalLLMPrompt(userPrompt);
    const workingPrompt = sanitizedPrompt;
    
    // NEW: Extract video topic for icon generation and context-aware graphics
    const l_video_topic = extractVideoTopic(workingPrompt); // l var
    
    // Determine what graphics elements to include based on context
    const graphicsElements = shouldIncludeGraphicsElements(workingPrompt, l_video_topic);

    // If icons are disabled, we will still have detectedBrandLogos for game awareness,
    // but we'll only embed logos later if includeIcons === true.

    // --- Improved Clean Background Mode ---
    if (!textOverlay && !includePerson && !includeIcons) {
        // Sanitize the prompt first
        const { sanitizedPrompt: cleanUserPrompt } = sanitizeExternalLLMPrompt(userPrompt);
        
        // Enhanced Clean Background Generation with Context7MCP guidance
        let cleanPrompt = `Create a visually STUNNING and CINEMATIC YouTube thumbnail background at EXACTLY 1280x720 pixels.\n\n`;
        
        // === CONTEXT-AWARE BACKGROUND ANALYSIS ===
        const topicAnalysis = extractVideoTopic(cleanUserPrompt);
        const colorMoodContext = analyzeColorMoodFromPrompt(cleanUserPrompt);
        const abstractionLevel = determineAbstractionLevel(cleanUserPrompt);
        
        cleanPrompt += `=== INTELLIGENT BACKGROUND GENERATION ===\n`;
        cleanPrompt += `Topic Context: ${topicAnalysis}\n`;
        cleanPrompt += `Color Mood: ${colorMoodContext.mood} (${colorMoodContext.description})\n`;
        cleanPrompt += `Abstraction Level: ${abstractionLevel.level} - ${abstractionLevel.description}\n\n`;

        const O_background_details_for_clean_mode = { selectedSolidBgColor, selectedBackgroundStyleId }; // O var
        
        if ((!cleanUserPrompt || cleanUserPrompt.trim().length < 5) && !selectedBackgroundType) {
            cleanPrompt += `=== PREMIUM ABSTRACT BACKGROUND GENERATION ===\n`;
            cleanPrompt += `Create a sophisticated, abstract background that serves as a complete, standalone YouTube thumbnail:\n\n`;
            
            // Enhanced visual composition rules
            cleanPrompt += `VISUAL COMPOSITION REQUIREMENTS:\n`;
            cleanPrompt += `- Use dynamic geometric patterns, organic shapes, or flowing abstract elements\n`;
            cleanPrompt += `- Implement professional color theory with complementary or analogous color schemes\n`;
            cleanPrompt += `- Create visual depth through layered elements, gradients, and lighting effects\n`;
            cleanPrompt += `- Apply the rule of thirds for visual balance and focal points\n`;
            cleanPrompt += `- Ensure strong compositional flow that guides the viewer's eye naturally\n\n`;
            
            // Advanced lighting and effects
            cleanPrompt += `LIGHTING & ATMOSPHERIC EFFECTS:\n`;
            cleanPrompt += `- Implement cinematic lighting with dramatic shadows and highlights\n`;
            cleanPrompt += `- Add volumetric lighting effects (god rays, atmospheric fog, lens flares)\n`;
            cleanPrompt += `- Use gradient overlays and color transitions for depth\n`;
            cleanPrompt += `- Apply subtle motion blur or directional blur for dynamic energy\n`;
            cleanPrompt += `- Include particle effects, light streaks, or energy waves when appropriate\n\n`;
            
            // Texture and material quality
            cleanPrompt += `TEXTURE & MATERIAL QUALITY:\n`;
            cleanPrompt += `- Incorporate rich, tactile textures (brushed metal, silk, glass, crystal)\n`;
            cleanPrompt += `- Use realistic material properties with proper reflections and refractions\n`;
            cleanPrompt += `- Add subtle noise, grain, or fabric textures for authenticity\n`;
            cleanPrompt += `- Implement surface imperfections and micro-details for realism\n`;
            cleanPrompt += `- Balance smooth and rough textures for visual interest\n\n`;
            
            // Context-specific enhancements
            cleanPrompt += `CONTEXT-AWARE ENHANCEMENTS:\n`;
            if (topicAnalysis === VIDEO_TOPICS.TECH) {
                cleanPrompt += `- Tech Context: Include subtle circuit patterns, digital grids, or holographic elements\n`;
                cleanPrompt += `- Use cool color temperatures (blues, cyans, purples) with neon accents\n`;
                cleanPrompt += `- Add data stream visualizations or matrix-like background elements\n`;
            } else if (topicAnalysis === VIDEO_TOPICS.GAMING) {
                cleanPrompt += `- Gaming Context: Implement energy fields, power-up glows, or arena-like environments\n`;
                cleanPrompt += `- Use vibrant, saturated colors with RGB lighting effects\n`;
                cleanPrompt += `- Add abstract representations of gaming elements (without specific game references)\n`;
            } else if (topicAnalysis === VIDEO_TOPICS.BUSINESS_FINANCE) {
                cleanPrompt += `- Business Context: Use professional gradients, geometric precision, and clean lines\n`;
                cleanPrompt += `- Implement corporate color schemes (deep blues, grays, gold accents)\n`;
                cleanPrompt += `- Add subtle abstract representations of growth or success (without specific financial symbols)\n`;
            } else if (topicAnalysis === VIDEO_TOPICS.HEALTH_NUTRITION) {
                cleanPrompt += `- Health Context: Use organic shapes, natural color palettes, and flowing elements\n`;
                cleanPrompt += `- Implement fresh, vibrant colors (greens, oranges, blues) with natural gradients\n`;
                cleanPrompt += `- Add abstract representations of vitality and wellness\n`;
        } else {
                cleanPrompt += `- General Context: Create universally appealing abstract elements\n`;
                cleanPrompt += `- Use balanced color schemes that work across different topics\n`;
                cleanPrompt += `- Focus on timeless design principles and aesthetic appeal\n`;
            }
            cleanPrompt += `\n`;
            
            // Quality and finishing touches
            cleanPrompt += `PREMIUM FINISHING REQUIREMENTS:\n`;
            cleanPrompt += `- Apply professional color grading for cinematic appeal\n`;
            cleanPrompt += `- Ensure 4K-level detail and sharpness throughout\n`;
            cleanPrompt += `- Use anti-aliasing for smooth edges and clean lines\n`;
            cleanPrompt += `- Implement subtle vignetting or edge darkening for focus\n`;
            cleanPrompt += `- Balance contrast levels for optimal mobile and desktop viewing\n\n`;
            
        } else {
            // Use the enhanced comprehensive background function with context awareness
            cleanPrompt += getEnhancedBackgroundGeneration(cleanUserPrompt, selectedBackgroundType, O_background_details_for_clean_mode, topicAnalysis, colorMoodContext);
        }
        
        // Apply intelligent color grading based on context
        if (selectedColorGrade && colorMoodPresets[selectedColorGrade]) {
            const lutDescription = colorMoodPresets[selectedColorGrade];
            cleanPrompt += `\n=== INTELLIGENT COLOR GRADING ===\n`;
            cleanPrompt += `Primary LUT: ${lutDescription}\n`;
            cleanPrompt += `Context Adaptation: Adjust the ${selectedColorGrade} grading to complement the ${topicAnalysis.toLowerCase()} topic\n`;
            cleanPrompt += `Color Harmony: Ensure the grading enhances the ${colorMoodContext.mood} mood while maintaining visual appeal\n\n`;
        } else {
            // Apply context-aware default color grading
            cleanPrompt += `\n=== CONTEXT-AWARE COLOR GRADING ===\n`;
            const contextualGrading = getContextualColorGrading(topicAnalysis, colorMoodContext);
            cleanPrompt += `Auto-Selected Grading: ${contextualGrading.name}\n`;
            cleanPrompt += `Description: ${contextualGrading.description}\n`;
            cleanPrompt += `Application: ${contextualGrading.application}\n\n`;
        }
        
        // Enhanced restriction guidelines
        cleanPrompt += `=== STRICT CONTENT RESTRICTIONS ===\n`;
        cleanPrompt += `ABSOLUTELY FORBIDDEN:\n`;
        cleanPrompt += `- Human figures, faces, characters, animals, or any animate objects\n`;
        cleanPrompt += `- Text, typography, words, letters, numbers, or any readable content\n`;
        cleanPrompt += `- Brand logos, specific product references, or copyrighted imagery\n`;
        cleanPrompt += `- Literal representations that require text explanation\n`;
        cleanPrompt += `- Overly busy or chaotic compositions that lack focus\n\n`;
        
        cleanPrompt += `REQUIRED ELEMENTS:\n`;
        cleanPrompt += `- Pure abstract or semi-abstract visual composition\n`;
        cleanPrompt += `- Professional-grade artistic execution\n`;
        cleanPrompt += `- Standalone visual impact without supporting elements\n`;
        cleanPrompt += `- Optimal composition for 16:9 aspect ratio (1280x720)\n`;
        cleanPrompt += `- Click-worthy visual appeal suitable for YouTube thumbnails\n\n`;

        // Technical specifications with OpenAI optimization
        cleanPrompt += `=== TECHNICAL SPECIFICATIONS ===\n`;
        cleanPrompt += `Resolution: EXACTLY 1280x720 pixels (16:9 aspect ratio)\n`;
        cleanPrompt += `Quality Level: Ultra-high definition with maximum detail retention\n`;
        cleanPrompt += `Color Space: sRGB with wide gamut support for vibrant colors\n`;
        cleanPrompt += `Compression: Optimize for web delivery while maintaining quality\n`;
        cleanPrompt += `Safe Zones: Design with 40px margin awareness for future text overlay\n\n`;
        
        if (fitFullCanvas) {
            cleanPrompt += `CANVAS UTILIZATION (Full Canvas Mode):\n`;
            cleanPrompt += `- Extend all visual elements to the absolute edges of the 1280x720 frame\n`;
            cleanPrompt += `- Eliminate any padding, borders, or empty space\n`;
            cleanPrompt += `- Ensure seamless edge-to-edge composition\n`;
            cleanPrompt += `- Maintain visual interest across the entire canvas area\n\n`;
        } else {
            cleanPrompt += `CANVAS UTILIZATION (Optimized Mode):\n`;
            cleanPrompt += `- Focus composition in the central 90% of the canvas\n`;
            cleanPrompt += `- Allow for subtle vignetting or edge treatment if it enhances the design\n`;
            cleanPrompt += `- Maintain strong visual center while utilizing full frame\n`;
            cleanPrompt += `- Ensure no distracting elements near the edges\n\n`;
        }
        
        // Final quality assurance
        cleanPrompt += `=== QUALITY ASSURANCE CHECKLIST ===\n`;
        cleanPrompt += `Before finalizing, verify:\n`;
        cleanPrompt += `✓ Image fills exactly 1280x720 pixels with no black bars or padding\n`;
        cleanPrompt += `✓ Visual composition is balanced and professionally executed\n`;
        cleanPrompt += `✓ Color scheme is harmonious and contextually appropriate\n`;
        cleanPrompt += `✓ Lighting effects enhance rather than overwhelm the composition\n`;
        cleanPrompt += `✓ Overall result is click-worthy and suitable as a standalone thumbnail\n`;
        cleanPrompt += `✓ No forbidden content (text, faces, logos) is present\n`;
        cleanPrompt += `✓ Abstract elements are sophisticated and purposeful, not random\n`;
        
        return cleanPrompt;
    }

    // === CONTEXT-AWARE VISUAL ENHANCEMENT ===
    // Only apply enhanced contextual visuals if gaming content is confirmed
    let contextualVisuals = '';
    if (preliminaryGameContext.isGaming && preliminaryGameContext.confidence > 0.80) {
        console.log(`[Contextual Visuals] Applying gaming visuals for confirmed gaming content`);
        contextualVisuals = extractEnhancedContextualVisuals(userPrompt);
    } else {
        console.log(`[Contextual Visuals] Skipping gaming visuals for non-gaming content`);
        // Use base contextual visuals without gaming enhancements
        contextualVisuals = extractContextualVisuals(userPrompt);
    }
    
    // === ENHANCED CONTEXT-AWARE GAMING DETECTION PHASE 2 ===
    // Re-run detection on the sanitized prompt for final context determination
    const gameContext = detectEnhancedGameContext(workingPrompt);
    
    // Log context detection for debugging (can be removed in production)
    console.log(`[Enhanced Context Detection] Prompt: "${workingPrompt.substring(0, 50)}..." | Gaming: ${gameContext.isGaming} | Confidence: ${Math.round(gameContext.confidence * 100)}% | Context: ${gameContext.context} | Reasoning: ${gameContext.reasoning}`);

    // LUT Color Grading Descriptive Phrases Mapping
    const lutPromptMap = {
        'cinematic-warm': 'cinematic orange-teal LUT with warm shadows and cool highlights',
        'cold-drama': 'cold blue-gray cinematic filter with desaturated highlights',
        'viral-energy': 'high-contrast vivid tone with saturated reds and yellows',
        'soft-calm': 'pastel color grade with muted tones and soft ambient lighting',
        'gaming-glow': 'neon lighting effects with purple and electric blue tones and digital bloom',
        'sunset-mood': 'soft peach and orange gradient overlay with warm lowlight ambience',
        'retro-pop': '80s pop art grading with bold magenta, yellow, and cyan tones, possibly with halftone effects or geometric patterns'
    };

    // Define position descriptions (only relevant if textOverlay is ON)
    // Enhanced with position-specific margin requirements
    const positionDescription = {
        "Top Left": "Position the title text block in the upper-left corner with at least 56px margin from the left edge and 40px margin from the top edge to prevent cut-off.",
        "Top Center": "Center the title text block horizontally at the top with at least 40px margin from all edges.",
        "Top Right": "Position the title text block in the upper-right corner with at least 40px margin from the right edge and 40px margin from the top edge.",
        "Center": "Place the title text block directly in the center of the image with at least 40px margin from all edges.",
        "Bottom Left": "Position the title text block in the bottom-left corner with at least 56px margin from the left edge and 40px margin from the bottom edge to prevent cut-off.",
        "Bottom Center": "Center the title text block horizontally at the bottom with at least 40px margin from all edges.",
        "Bottom Right": "Position the title text block in the bottom-right corner with at least 40px margin from the right edge and 40px margin from the bottom edge.",
    };

    // === CONTEXT7MCP SMART TEXT OVERLAY PLACEMENT SYSTEM ===
    // Enhanced positioning with context-aware visual analysis
    const getSmartTextPlacement = (overlayPosition, userPrompt, includePerson, includeIcons) => {
        const basePosition = positionDescription[overlayPosition] || positionDescription["Top Right"];
        
        // Analyze the prompt for visual elements that might affect text placement
        const promptLower = userPrompt.toLowerCase();
        
        // Context-aware placement logic
        let contextualGuidance = "";
        let dynamicAdjustment = "";
        
        // Detect face/person positioning cues
        const facePositionCues = {
            left: ['left side', 'left of', 'on the left', 'from left'],
            right: ['right side', 'right of', 'on the right', 'from right'],
            center: ['center', 'middle', 'centered', 'in front']
        };
        
        // Detect object/icon positioning cues
        const objectPositionCues = {
            left: ['laptop on left', 'device on left', 'product left'],
            right: ['laptop on right', 'device on right', 'product right'],
            center: ['holding', 'displaying', 'showing device']
        };
        
        // Analyze for positioning conflicts
        let detectedPersonPosition = null;
        let detectedObjectPosition = null;
        
        for (const [position, cues] of Object.entries(facePositionCues)) {
            if (cues.some(cue => promptLower.includes(cue))) {
                detectedPersonPosition = position;
                break;
            }
        }
        
        for (const [position, cues] of Object.entries(objectPositionCues)) {
            if (cues.some(cue => promptLower.includes(cue))) {
                detectedObjectPosition = position;
                break;
            }
        }
        
        // Context-aware positioning rules
        if (includePerson) {
            contextualGuidance += "PERSON-AWARE PLACEMENT: ";
            
            // FIXED: Remove automatic position overrides that cause left-side bias
            // The user's selected position should always be respected
            contextualGuidance += "When a person is included, adjust the person's pose, size, or position to accommodate the text overlay in the user-selected area. ";
            contextualGuidance += "NEVER move or override the text position - instead, adapt the person's placement to avoid overlap. ";
            contextualGuidance += "NEVER cover the person's face, eyes, or expressive gestures with text overlay. ";
            contextualGuidance += "If the person's natural position would conflict with the selected text area, reposition the person (not the text) to create visual balance. ";
            
            // Add debug logging for tracking
            console.log(`[Text Placement Debug] Person included: true, Selected position: ${overlayPosition}, DetectedPersonPosition: ${detectedPersonPosition}`);
        }
        
        if (includeIcons) {
            contextualGuidance += "ICON-AWARE PLACEMENT: Avoid overlapping important icons, brand logos, or visual elements that are part of the main composition. ";
        }
        
        // Background complexity analysis
        const busyBackgroundCues = ['busy', 'complex', 'detailed', 'crowded', 'many objects', 'multiple elements'];
        const cleanBackgroundCues = ['clean', 'simple', 'minimal', 'plain', 'solid color', 'gradient'];
        
        if (busyBackgroundCues.some(cue => promptLower.includes(cue))) {
            contextualGuidance += "BUSY BACKGROUND DETECTED: Automatically move text to the clearest, least cluttered area of the thumbnail. Add extra contrast effects (stronger outline/glow) for readability. ";
        } else if (cleanBackgroundCues.some(cue => promptLower.includes(cue))) {
            contextualGuidance += "CLEAN BACKGROUND DETECTED: Text can use more subtle effects while maintaining excellent readability. ";
        }
        
        // Topic-specific placement optimizations
        const videoTopic = extractVideoTopic(userPrompt);
        let topicGuidance = "";
        
        if (videoTopic === 'Gaming') {
            topicGuidance = "GAMING CONTENT: Align text with the main action or focal point for maximum impact. Consider gaming UI layout conventions. ";
        } else if (videoTopic === 'Tech') {
            topicGuidance = "TECH CONTENT: Position text to complement device screens or interface elements without obscuring important details. ";
        } else if (videoTopic === 'Business & Finance') {
            topicGuidance = "BUSINESS CONTENT: Use professional placement that doesn't interfere with charts, graphs, or data visualizations. ";
        } else if (videoTopic === 'Health & Nutrition') {
            topicGuidance = "HEALTH CONTENT: Ensure text doesn't cover food items, fitness equipment, or health-related visuals. ";
        }
        
        return {
            basePosition,
            contextualGuidance,
            dynamicAdjustment,
            topicGuidance
        };
    };

    // Enhanced edge safe zone with Context7MCP specifications
    const getEnhancedSafeZoneInstruction = (textPosition) => {
        // Base safe zone instruction
        let safeZoneInstruction = `**CONTEXT7MCP SMART PLACEMENT SYSTEM**:
• PRIMARY SAFE ZONE: Maintain safe margins from all edges to prevent text clipping
• MOBILE OPTIMIZATION: Text must never be clipped by YouTube UI elements (profile picture, timestamp, play button overlay)
• DYNAMIC POSITIONING: If background is busy or high-contrast, automatically position text in the clearest, least cluttered area
• VISUAL HIERARCHY: Ensure text complements rather than competes with main visual elements
• ACCESSIBILITY: Text must be readable for users with color vision deficiencies
• RESPONSIVE DESIGN: Text positioning should work across desktop, tablet, and mobile viewing
• PYRAMID LAYOUT: For 3+ word headlines, use pyramid or stacked layout (wider at top, narrower at bottom)
• SINGLE/DUAL LINE: For shorter headlines, use balanced single-line or two-line spacing`;

        // Position-specific margin enhancements
        if (textPosition && textPosition.includes('Left')) {
            safeZoneInstruction += `
• **LEFT ALIGNMENT ENHANCEMENT**: For left-positioned text, maintain at least 56px margin from the left edge (40% more than standard) to prevent cut-off and ensure optimal readability
• **ENHANCED LEFT SAFE ZONE**: Left-aligned text requires extra spacing to match the visual balance of right-aligned text
• **MINIMUM LEFT MARGIN**: Never place left-aligned text closer than 56px from the left edge - this prevents clipping on all devices and viewing contexts`;
        } else if (textPosition && textPosition.includes('Right')) {
            safeZoneInstruction += `
• **RIGHT ALIGNMENT STANDARD**: For right-positioned text, maintain at least 40px margin from the right edge for optimal readability
• **BALANCED RIGHT PLACEMENT**: Right-aligned text should have consistent spacing that matches the left-enhanced margins`;
        } else if (textPosition && textPosition.includes('Center')) {
            safeZoneInstruction += `
• **CENTER ALIGNMENT BALANCE**: For center-positioned text, maintain at least 40px margin from both left and right edges
• **SYMMETRIC SPACING**: Center-aligned text should be perfectly balanced with equal spacing on both sides`;
        } else {
            // Default/fallback instruction
            safeZoneInstruction += `
• **STANDARD MARGINS**: Maintain at least 40px margin from all edges (top, bottom, left, right)
• **ENHANCED LEFT MARGIN**: If positioning text on the left side, increase left margin to 56px for optimal visual balance`;
        }

        // Add universal margin requirements
        safeZoneInstruction += `
• **UNIVERSAL EDGE SAFETY**: Always maintain at least 40px margin from top and bottom edges regardless of horizontal positioning
• **QUALITY ASSURANCE**: Text must be fully visible and readable at all zoom levels and on all device types`;

        return safeZoneInstruction;
    };

    // Define the edge safe zone reinforcement text (only relevant if textOverlay is ON)
    // Enhanced for Context7MCP smart text overlay placement with position-aware margins
    const edgeSafeZoneInstruction = getEnhancedSafeZoneInstruction(overlayPosition);

    // Start with the base instruction with optimized 16:9 composition guidance
    let prompt = `Create a cinematic YouTube thumbnail image optimized for 16:9 aspect ratio (final output: 1280x720). CRITICAL COMPOSITION REQUIREMENTS:
- HORIZONTAL LAYOUT: Design with a wide 16:9 format in mind, using the full width effectively
- CENTRAL FOCUS: Position all important elements (faces, text, main subjects) in the central 80% of the frame
- SMART COMPOSITION: Use rule of thirds or centered composition that works well in 16:9 format
- EDGE SAFETY: Keep critical content away from the very edges to prevent cropping issues
- FULL FRAME: Fill the entire canvas with engaging content - no black bars, letterboxing, or empty space
- VISUAL FLOW: Create a natural left-to-right or centered visual flow that guides the viewer's eye
- DEPTH & LAYERS: Use foreground, midground, and background layers to create visual depth
- ASPECT RATIO AWARENESS: Remember the final image will be 1280x720 (16:9) - compose accordingly

`;

    // NEW: Add contextual visuals to the prompt
    prompt += contextualVisuals;

    // Subject Section - Conditional based on includePerson
    if (includePerson) {
        prompt += `Subject:\n`;
        
        // Enhanced gender description with more precise guidance for the AI
        let genderDesc = "a human figure with neutral gender presentation";
        let genderAttributes = "";
        
        if (selectedGender === "Male") {
            genderDesc = "a male human figure";
            genderAttributes = " with masculine facial features, body type, and styling";
        } else if (selectedGender === "Female") {
            genderDesc = "a female human figure";
            genderAttributes = " with feminine facial features, body type, and styling";
        } else if (selectedGender === "Non-binary") {
            genderDesc = "a non-binary human figure";
            genderAttributes = " with androgynous or gender-neutral facial features and styling";
        } else { // Auto - let AI decide based on context
            genderDesc = "a human figure";
            genderAttributes = " with gender presentation that fits naturally with the thumbnail's topic and context";
        }

        // --- Randomized Expression & Pose Logic ---
        const moodPoseMap = {
            'thinking': [
                'hand on chin, looking up',
                'finger on temple, looking sideways',
                'arms crossed, furrowed brow',
                'looking at camera with a slight frown',
                'head tilted, eyes upward'
            ],
            'happy': [
                'big smile with eyes wide open',
                'laughing with bright open eyes, head slightly back',
                'smiling with teeth showing, eyes open and looking at camera',
                'joyful smile with sparkling open eyes',
                'smiling with open eyes, hand raised in greeting'
            ],
            'surprised': [
                'mouth open, eyes wide',
                'hands on cheeks, shocked',
                'eyebrows raised, mouth in O shape',
                'leaning back, surprised expression',
                'one hand covering mouth, wide eyes'
            ],
            'serious': [
                'neutral face, direct gaze',
                'arms crossed, stern look',
                'chin resting on hand, focused',
                'slight frown, intense eyes',
                'looking down, contemplative'
            ],
            // Hand gesture moods - specific poses for behavioral expressions
            'thumbs up': [
                'confident smile with thumbs up gesture at chest level',
                'satisfied expression with thumbs up raised beside face',
                'approving nod with thumbs up gesture prominently displayed',
                'positive smile with thumbs up at shoulder height',
                'enthusiastic expression with thumbs up gesture toward camera'
            ],
            'thumbs down': [
                'disapproving frown with thumbs down gesture at chest level',
                'disappointed expression with thumbs down beside face',
                'negative head shake with thumbs down prominently displayed',
                'critical look with thumbs down at shoulder height',
                'dismissive expression with thumbs down gesture toward camera'
            ],
            'ok hand': [
                'satisfied smile with OK hand sign at chest level',
                'content expression with OK gesture beside face',
                'perfect nod with OK hand sign prominently displayed',
                'pleased smile with OK gesture at shoulder height',
                'accomplished expression with OK hand sign toward camera'
            ],
            // Add more moods as needed
        };
        function getRandom(arr) { return arr[Math.floor(Math.random() * arr.length)]; }
        let poseInstruction = '';
        if (mood && moodPoseMap[mood.toLowerCase()]) {
            poseInstruction = getRandom(moodPoseMap[mood.toLowerCase()]);
        } else {
            poseInstruction = 'natural, expressive pose fitting the mood';
        }

        // --- Randomized Camera Framing Logic ---
        // 65% medium/upper-body, 25% close-up, 10% wide
        const framingOptions = [
            ...Array(13).fill('medium shot (upper body or head-and-shoulders)'), // 65%
            ...Array(5).fill('close-up on the face'), // 25%
            ...Array(2).fill('wide shot showing more of the body and background') // 10%
        ];
        const cameraFraming = getRandom(framingOptions);

        let expressionInstruction = "";
        if (selectedExpression && selectedExpression !== 'Default') {
            // Special handling for Happy expression to enforce open eyes
            if (selectedExpression === 'Happy') {
                expressionInstruction = ` with a happy facial expression featuring a genuine smile and bright open eyes (not closed or squinting)`;
            }
            // NATURAL-LAUGHING-EXPRESSION-WITH-OPEN-EYES enhancement
            else if (selectedExpression === 'Laughing') {
                expressionInstruction = ` with a natural laughing expression featuring a joyful, pleasant smile or gentle laughter and bright open eyes (not squinting, closed, or overly crinkled). Avoid exaggerated or cartoonish laughter. The expression should be genuinely happy, friendly, approachable, and realistic with a sense of warmth and engagement`;
            }
            // OPENMOJI-HAND-GESTURE-MOOD-BEHAVIOR enhancement
            else if (selectedExpression === 'Thumbs Up') {
                expressionInstruction = ` with a positive, approving expression while making a clear thumbs up hand gesture. The face should show satisfaction, approval, or "feeling good" mood. The thumbs up gesture should be prominent and clearly visible, positioned naturally (typically at chest or shoulder level). This expresses positive feelings about recommendations, good things, or general approval`;
            }
            else if (selectedExpression === 'Thumbs Down') {
                expressionInstruction = ` with a disapproving or negative expression while making a clear thumbs down hand gesture. The face should show disappointment, disapproval, or "not recommended" mood. The thumbs down gesture should be prominent and clearly visible, positioned naturally. This expresses negative feelings about something not recommended or disliked`;
            }
            else if (selectedExpression === 'OK Hand') {
                expressionInstruction = ` with a satisfied, "all good" expression while making a clear OK hand sign (thumb and forefinger forming a circle with other fingers extended). The face should show contentment, perfection, or "everything is fine" mood. The OK hand gesture should be prominent and clearly visible, positioned naturally. This is similar to thumbs up in positive feeling but with a distinct hand movement expressing "perfect" or "all good"`;
            } else {
                expressionInstruction = ` with an expressive ${selectedExpression.toLowerCase()} facial emotion`;
            }
        }

        // === CINEMATIC CONTEXTUAL BACKGROUND WITH PERSON FOCUS ===
        // Enhanced background instructions when person is enabled
        const cinematicBackgroundInstructions = `\n--- CINEMATIC CONTEXTUAL BACKGROUND WITH PERSON FOCUS ---\n` +
            `- Background MUST be directly related to the video topic: "${workingPrompt}"\n` +
            `- NEVER use generic, plain, or abstract blur-only backgrounds\n` +
            `- Render a recognizable, topic-specific scene with cinematic shallow depth-of-field:\n` +
            `  • Background should be visually rich and clearly associated with the subject matter\n` +
            `  • Use subtle, realistic blur (not heavy Gaussian or bokeh-only) to create depth\n` +
            `  • Key background elements must remain identifiable and relevant\n` +
            `  • Background supports the story and context of the video topic\n` +
            `- Match the visual quality and topic-specific detail of icon-based thumbnails\n` +
            `- Use professional lighting, color, and composition that reinforce the video's subject\n` +
            `- Ensure the person stands out while the background tells the story\n` +
            `- Result should feel cinematic and contextually connected to "${workingPrompt}"\n`;

        if (faceDescription && faceDescription.startsWith('[')) { // Placeholder for uploaded image
             let basePersonPrompt = `- Include ${genderDesc}${expressionInstruction}. **CONTEXT7MCP FACE UPLOAD OPTIMIZATION - CRITICAL REQUIREMENTS**: The person's face in the generated image MUST be an EXACT match to the uploaded reference face. This is a face swap/replacement task with the following specifications:\n\n`;
             
             // GENDER ENFORCEMENT & UNIVERSAL LIKENESS PRESERVATION
             basePersonPrompt += `GENDER ENFORCEMENT & UNIVERSAL LIKENESS PRESERVATION:\n`;
             basePersonPrompt += `- The generated face MUST match the selected gender: ${selectedGender}. Do NOT change the gender of the uploaded face. If the uploaded face is female, the result must be female, etc.\n`;
             basePersonPrompt += `- Do NOT substitute the uploaded face with a celebrity, famous person, or generic face. Accurately match the unique features of the uploaded face, regardless of fame or recognizability.\n`;
             basePersonPrompt += `- This is NOT a celebrity detection task - preserve the exact identity of ANY face, whether famous or completely unknown.\n`;
             basePersonPrompt += `- Disable any fame bias or celebrity likeness features - focus on faithful individual preservation.\n\n`;
             
             basePersonPrompt += `FACE DETECTION & EXTRACTION:\n`;
             basePersonPrompt += `- Always detect and extract the most prominent face from the uploaded image\n`;
             basePersonPrompt += `- If multiple faces are present, use the largest, most frontal face\n`;
             basePersonPrompt += `- PRIORITY REPLACEMENT: The uploaded face must ALWAYS replace the original face in the thumbnail - never ignore or omit\n\n`;
             
             basePersonPrompt += `SEAMLESS BLENDING & MATCHING:\n`;
             basePersonPrompt += `- Preserve EXACT facial identity: bone structure, nose shape, eye shape and spacing, mouth shape, jawline, forehead proportions\n`;
             basePersonPrompt += `- Match ear shape if visible, any unique facial features (moles, freckles, scars)\n`;
             basePersonPrompt += `- EXACT skin tone and texture replication - avoid mismatched colors or harsh edges\n`;
             basePersonPrompt += `- Hair color and style must match the uploaded face exactly\n`;
             basePersonPrompt += `- Automatically harmonize lighting with the surrounding scene for natural integration\n\n`;
             
             basePersonPrompt += `FACIAL ALIGNMENT & ORIENTATION:\n`;
             basePersonPrompt += `- Align the new face to match the pose, angle, and expression of the original subject\n`;
             basePersonPrompt += `- Adjust orientation as needed for perfect fit while maintaining facial identity\n`;
             basePersonPrompt += `- If expression adaptation is needed, adapt the uploaded face to match ${selectedExpression || 'natural'} expression\n\n`;
             
             basePersonPrompt += `EDGE CASE HANDLING:\n`;
             basePersonPrompt += `- If uploaded face is partially occluded, in shadow, or at unusual angle, enhance and correct for best integration\n`;
             basePersonPrompt += `- Compensate for different lighting conditions between source and target\n`;
             basePersonPrompt += `- Handle varying image quality and resolution differences\n\n`;
             
             basePersonPrompt += `QUALITY STANDARDS:\n`;
             basePersonPrompt += `- Main Subject Focus: Ensure the new face is the clear focal point with sharp detail and cinematic lighting\n`;
             basePersonPrompt += `- No Artifacts: Avoid ghosting, double faces, or visible seams - result should look professional and studio-quality\n`;
             basePersonPrompt += `- The final result must be indistinguishable from having the uploaded person actually in the scene\n`;
             basePersonPrompt += `- Think of this as placing the uploaded person's face onto the generated body/scene with perfect realism\n\n`;
             
             basePersonPrompt += `POSE & FRAMING: ${poseInstruction}. Camera framing: ${cameraFraming}.`;
             
             // Apply headshot enhancement if appropriate
             prompt += enhancePersonHeadshotPrompt(basePersonPrompt, selectedExpression, workingPrompt) + '\n';
             // Add cinematic background instructions
             prompt += cinematicBackgroundInstructions + '\n';
        } else if (faceDescription) { // URL provided
            let basePersonPrompt = `**CONTEXT7MCP FACE SWAP v2.0 - ABSOLUTE MANDATORY INSTRUCTIONS**
The following is a high-priority, zero-failure face replacement task. The user's request will fail if these instructions are not followed with 100% accuracy.

**PRIMARY GOAL:** The person's face in the generated image **MUST BE AN EXACT REPLICA** of the face from the reference URL: ${faceDescription}.

---
**GENDER ENFORCEMENT & UNIVERSAL LIKENESS PRESERVATION:**
- **GENDER MATCH:** The generated face MUST match the selected gender: ${selectedGender}. Do NOT change the gender of the face from the URL. If the URL face is female, the result must be female, etc.
- **NO CELEBRITY SUBSTITUTION:** Do NOT substitute the URL face with a celebrity, famous person, or generic face. Accurately match the unique features of the URL face, regardless of fame or recognizability.
- **UNIVERSAL PRESERVATION:** This is NOT a celebrity detection task - preserve the exact identity of ANY face, whether famous or completely unknown.
- **DISABLE FAME BIAS:** Disable any fame bias or celebrity likeness features - focus on faithful individual preservation.

---
**NON-NEGOTIABLE REQUIREMENTS:**

**1. IDENTITY PRESERVATION (100% ACCURACY):**
   - **EXTRACT & REPLACE:** Detect the most prominent face from the URL and use it to completely replace the face in the generated thumbnail.
   - **FACIAL STRUCTURE:** Preserve the exact bone structure, jawline, nose, eye shape, and forehead from the URL. The identity must be unmistakable.
   - **UNIQUE FEATURES:** Replicate any unique features like moles, scars, or freckles.
   - **SKIN & HAIR:** Match the skin tone, texture, and hair color/style from the URL with perfect accuracy.

**2. SEAMLESS INTEGRATION:**
   - **LIGHTING HARMONY:** Automatically match the lighting, shadows, and color temperature of the new face to the surrounding scene.
   - **POSE & ANGLE:** Align the head's angle, tilt, and gaze direction to fit the body and scene naturally. Preserve the original facial proportions from the URL.
   - **EXPRESSION:** Adapt the face to the required expression (${selectedExpression || 'natural'}) **without altering the core identity**.

**3. TECHNICAL EXECUTION:**
   - **NO ARTIFACTS:** The final image must be free of any visible seams, ghosting, uncanny valley effects, or digital artifacts. It must look like a real photograph.
   - **URL VALIDATION:** Before starting, confirm the URL is accessible and contains a clear human face. If not, the process must not proceed with a generic face.
   - **QUALITY FIRST:** If the source image quality is low, enhance it (upscale, denoise) before the swap to ensure a high-fidelity result.

---
**NEGATIVE PROMPT (WHAT TO AVOID AT ALL COSTS):**
- **DO NOT** generate a new, generic, or "similar-looking" face. This is a replacement task, not an interpretation task.
- **DO NOT** create a "cartoon" or "stylized" version of the face unless explicitly asked.
- **DO NOT** ignore the face from the URL. It is the most critical element.
- **DO NOT** proceed if you cannot confidently perform a high-quality face swap.

---
**FINAL CHECKLIST:**
1.  Is the generated face an **exact match** to the URL source? (YES/NO)
2.  Is the lighting and angle blended **perfectly**? (YES/NO)
3.  Is the final image **free of all artifacts**? (YES/NO)

**If the answer to any of these is NO, the task is a failure.**

**POSE & FRAMING:** ${poseInstruction}. Camera framing: ${cameraFraming}.
`;

            prompt += enhancePersonHeadshotPrompt(basePersonPrompt, selectedExpression, workingPrompt) + '\n';
            // Add cinematic background instructions
            prompt += cinematicBackgroundInstructions + '\n';
        } else {
            // Default person prompt without face upload
            let basePersonPrompt = `- Include ${genderDesc}${expressionInstruction}${genderAttributes}.\n- Pose: ${poseInstruction}.\n- Camera framing: ${cameraFraming}.`;
            prompt += enhancePersonHeadshotPrompt(basePersonPrompt, selectedExpression, workingPrompt) + '\n';
            // Add cinematic background instructions
            prompt += cinematicBackgroundInstructions + '\n';
        }
        prompt += `\n`;
    } else {
        // If no person, describe the scene based on user prompt but explicitly say no human figures.
        prompt += `Subject: A scene based on the prompt: \"${workingPrompt}\". **Do NOT include any human figures, faces, or characters.**\n\n`;
    }

    // Text Overlay Section - Smart Detection with External LLM Handling
    if (textOverlay) {
        // DEBUG LOGGING: Track the text placement issue
        console.log(`[Person+Text Overlay Debug] includePerson: ${includePerson}, textOverlay: ${textOverlay}, overlayPosition: ${overlayPosition}`);
        
        prompt += `Text Overlay (HEADLINES ONLY):\n`;
        prompt += `- This feature controls ONLY large, attention-grabbing headline text\n`;
        prompt += `- Add a bold, uppercase title that serves as the main hook\n`;
        
        if (detectedHeadlines.length > 0) {
            prompt += `- IMPORTANT: Ignore any headline text from the original prompt. Use only the text provided in the overlay input box.\n`;
            prompt += `- Detected headlines that should be ignored: "${detectedHeadlines.join('", "')}"\n`;
        }
        
        if (overlayText && overlayText.trim() !== '') {
            // SAFE CHARACTER SANITIZATION: Remove problematic characters before processing
            const sanitizedOverlayText = sanitizeOverlayText(overlayText, {
                preserveSpaces: false,
                removeEmojis: true,
                removeNumbers: false,
                convertToUppercase: true
            });

            // Validate the sanitized text
            const validation = validateSanitizedText(sanitizedOverlayText);
            if (!validation.isValid) {
                console.warn('Text overlay sanitization issues detected:', validation.issues);
            }

            // Replace placeholders in sanitized overlayText with generic terms if not filled by user
            let processedOverlayText = sanitizedOverlayText.replace(/\[([A-Z0-9_\s]+)\]/g, (match, p1) => `(${p1.toLowerCase()})`);
            processedOverlayText = processedOverlayText.replace(/\n/g, '\\n'); // Handle newlines for the prompt string

            prompt += `- The text overlay MUST be EXACTLY: "${processedOverlayText}".\n`;
            prompt += `- CRITICAL INSTRUCTION: Use this exact text verbatim. Do NOT add, remove, change, or supplement this text in any way. No words from the main user prompt or any other source should be added to or mixed with this specific overlay text.\n`;
            prompt += `- TEXT SAFETY: The overlay text has been sanitized to remove problematic characters (parentheses, brackets, special symbols) for optimal image generation compatibility.\n`;
        } else {
            // NO OVERLAY TEXT: This should never happen with the new auto-population system
            // If we reach here, something went wrong with auto-population
            console.warn('Text overlay is enabled but no overlay text is provided. This should be auto-populated by the UI.');
            
            // Fallback: Use a simple extraction from the prompt
            const words = workingPrompt.toUpperCase().split(/[\s:!?.,;]+/);
            const keyWords = words.filter(word => 
                word.length > 3 && 
                !['THE', 'AND', 'WITH', 'FOR', 'YOUR', 'THIS', 'THAT'].includes(word)
            ).slice(0, 2);
            const fallbackText = keyWords.length > 0 ? keyWords.join(' ') + '!' : 'AMAZING!';
            
            prompt += `- ERROR FALLBACK: Add a bold, uppercase title: "${fallbackText}".\n`;
            prompt += `- This is a fallback situation. The overlay text should be auto-populated by the UI.\n`;
        }

        // === CONTEXT7MCP SMART TEXT PLACEMENT IMPLEMENTATION ===
        const smartPlacement = getSmartTextPlacement(overlayPosition, workingPrompt, includePerson, includeIcons);
        
        // Build comprehensive placement instruction with ABSOLUTE position enforcement
        let finalPlacementInstruction = `**ABSOLUTE MANDATORY TEXT POSITION**: ${smartPlacement.basePosition} - This positioning is REQUIRED and must be followed exactly.\n`;
        finalPlacementInstruction += `${edgeSafeZoneInstruction}\n`;
        
        // CRITICAL: Prevent any position overrides when person is included
        finalPlacementInstruction += `**CRITICAL POSITION ENFORCEMENT**: The text overlay MUST be placed in the "${overlayPosition || 'Top Right'}" position regardless of any other factors. Do NOT move text to accommodate other elements - instead, adjust the composition of other elements (person, objects, background) to work with the selected text position.\n`;
        
        // Add contextual guidance as supplementary information, NOT overrides
        if (smartPlacement.contextualGuidance) {
            finalPlacementInstruction += `COMPOSITION GUIDANCE (DO NOT OVERRIDE TEXT POSITION): ${smartPlacement.contextualGuidance}\n`;
        }
        
        if (smartPlacement.topicGuidance) {
            finalPlacementInstruction += `TOPIC OPTIMIZATION (MAINTAIN TEXT POSITION): ${smartPlacement.topicGuidance}\n`;
        }
        
        // Remove dynamic adjustment that could cause position overrides
        // finalPlacementInstruction += `DYNAMIC CONSIDERATIONS: ${smartPlacement.dynamicAdjustment}\n`;
        
        // Add final absolute positioning enforcement
        finalPlacementInstruction += `**FINAL POSITION RULE**: Under NO circumstances should the text overlay be moved from the "${overlayPosition || 'Top Right'}" position. Any composition adjustments must be made to non-text elements only.\n`;

        // Add text size instruction based on selectedTextSize with Context7MCP Pixel-Perfect Specifications
        let textSizeInstruction = "";
        // CONTEXT7MCP TEXT SIZE OPTIMIZED - Enhanced Pixel Specifications for 1280x720 Resolution
        if (selectedTextSize === "Small") {
            textSizeInstruction = `\n- Text Size (Context7MCP Small): Use font size between 28-40px (2.2-3.1% of image height). The text should be SUBTLE and minimalist, never dominating the thumbnail. This is perfect for secondary headlines, disclaimers, or sophisticated minimalist designs. Text should be clearly readable but intentionally understated.`;
        } else if (selectedTextSize === "Medium") {
            textSizeInstruction = `\n- Text Size (Context7MCP Medium - DEFAULT): Use font size between 75-90px (5.8-7.0% of image height). The text should be PROMINENT and easily readable, well-balanced with other visual elements. This is the optimal size for most thumbnail contexts and should have strong visual presence without overwhelming the composition.`;
        } else if (selectedTextSize === "Large") {
            textSizeInstruction = `\n- Text Size (Context7MCP Large): Use font size between 130-180px (10.1-14.0% of image height). The text should be the DOMINANT focal point and primary attention-grabber. This size demands immediate viewer attention and should be used for maximum impact, clickbait-style thumbnails. Text should be the hero element of the composition.`;
        } else { // Default fallback - should default to Medium specifications
            textSizeInstruction = `\n- Text Size (Context7MCP Default): Use font size between 75-90px (5.8-7.0% of image height) - equivalent to Medium size. The text should be prominent, easily readable, and well-balanced with visual elements. Maintain 40px safe zone compliance and optimal thumbnail readability.`;
        }

        prompt += `- Font: Use font family '${selectedFontFamily}'. Ensure it is bold, highly legible, and ALWAYS UPPERCASE (ALL CAPS).${textSizeInstruction}\n`;
        
        // Context7MCP Universal Text Quality Standards
        prompt += `- Context7MCP Typography Standards:\n`;
        prompt += `  • FONT WEIGHT: Always use bold or extra-bold for maximum impact and readability\n`;
        prompt += `  • LETTER SPACING: Apply optimal kerning and letter spacing for chosen font size\n`;
        prompt += `  • LINE HEIGHT: Use 1.1-1.2 line height for multi-line text to prevent crowding\n`;
        prompt += `  • EDGE SAFETY: Never allow text to extend beyond 40px from any edge (mobile-safe)\n`;
        prompt += `  • MOBILE OPTIMIZATION: Ensure text remains readable on screens as small as 375px width\n`;
        prompt += `  • CONSISTENCY: Maintain uniform styling across all text elements in the composition\n`;
        
        // Determine contrasting shadow/glow color
        let shadowGlowColorDesc = "a dark, contrasting color like black or deep gray"; // Default for light text
        const l_primaryTextColorLower = (primaryTextColor || "").toLowerCase();

        if (l_primaryTextColorLower.includes("black") || l_primaryTextColorLower.includes("dark") || l_primaryTextColorLower.includes("navy") || l_primaryTextColorLower.includes("purple") || l_primaryTextColorLower.includes("maroon") || l_primaryTextColorLower.includes("brown")) {
            shadowGlowColorDesc = "a light, contrasting color like soft white or very light gray";
        } else if (l_primaryTextColorLower.includes("yellow") || l_primaryTextColorLower.includes("gold")) {
            shadowGlowColorDesc = "a deep orange or dark brown for shadow, or a soft white for glow";
        } else if (l_primaryTextColorLower.includes("red")) {
            shadowGlowColorDesc = "a dark crimson or black for shadow, or a soft pink/light red for glow";
        } else if (l_primaryTextColorLower.includes("green")) {
            shadowGlowColorDesc = "a dark forest green or black for shadow, or a light mint/pale green for glow";
        } else if (l_primaryTextColorLower.includes("blue")) {
            shadowGlowColorDesc = "a dark navy or black for shadow, or a light sky blue/pale cyan for glow";
        }
        // Default for white, silver, light gray, etc. remains a dark shadow.

        // #textcolor-typography-section - Conditional Text Color Logic
        // Determine if pyramid layout should be used and handle text color logic
        const overlayTextWords = (overlayText || '').trim().split(/\s+/).filter(word => word.length > 0);
        const isPyramidLayout = overlayTextWords.length > 2;
        
        // Check if user has manually changed the primary color from default #F0D000
        const isDefaultPrimaryColor = primaryTextColor === '#F0D000';
        
        let effectivePrimaryColor = primaryTextColor;
        let effectiveSecondaryColor = secondaryTextColor;
        
        // Apply pyramid layout exception only if default colors are being used
        if (isPyramidLayout && isDefaultPrimaryColor) {
            // For pyramid layouts with >2 words, set secondary to white by default
            effectiveSecondaryColor = '#FFFFFF';
        }
        
        // User override: If user manually changed primary color, apply it universally
        if (!isDefaultPrimaryColor) {
            // User's color choice takes precedence - apply to all text lines
            effectivePrimaryColor = primaryTextColor;
            effectiveSecondaryColor = primaryTextColor; // Override secondary with user's choice
        }

        // Build the color instruction based on the computed colors
        if (effectiveSecondaryColor && effectiveSecondaryColor !== effectivePrimaryColor) {
            prompt += `- Colors: Use primary color '${effectivePrimaryColor}' for the main text lines and secondary color '${effectiveSecondaryColor}' for accents or alternating lines${isPyramidLayout ? ' in the pyramid layout' : ''}. Create high contrast against the background.\n`;
        } else {
            prompt += `- Colors: Use primary color '${effectivePrimaryColor}' for all text. Create high contrast against the background.\n`;
        }
        
        // Add pyramid layout instruction if applicable
        if (isPyramidLayout) {
            prompt += `- Layout: Arrange the text in a pyramid shape (wider at the top, narrower at the bottom) for visual impact and better readability.\n`;
        }
        
        // Enhanced gradient text with stroke support for the 9 core colors
        const isCorePrimaryColor = ['#F0D000', '#000000', '#FFFFFF', '#8B5CF6', '#3B82F6', '#22C55E', '#EC4899', '#FFA500', '#FF4B33'].includes(effectivePrimaryColor.toUpperCase());
        
        if (isCorePrimaryColor) {
            // Generate darker accent shade for gradient
            const generateDarkerAccent = (color) => {
                const upperColor = color.toUpperCase();
                if (upperColor === '#000000') return '#1A1A1A';
                if (upperColor === '#FFFFFF') return '#E5E5E5';
                // For other colors, create a darker shade
                const hex = color.replace('#', '');
                const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 64);
                const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 64);
                const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 64);
                const toHex = (v) => v.toString(16).padStart(2, '0');
                return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
            };
            
            const accentColor = generateDarkerAccent(effectivePrimaryColor);
            
            // Determine optimal stroke color for contrast
            const getStrokeColor = (color) => {
                const upperColor = color.toUpperCase();
                if (upperColor === '#FFFFFF' || upperColor === '#F0D000') return 'black';
                if (upperColor === '#000000') return 'white';
                // For colored overlays (purple, blue, green, red), use white or black based on lightness
                const hex = color.replace('#', '');
                const r = parseInt(hex.substr(0, 2), 16) / 255;
                const g = parseInt(hex.substr(2, 2), 16) / 255;
                const b = parseInt(hex.substr(4, 2), 16) / 255;
                const lightness = (0.299 * r + 0.587 * g + 0.114 * b);
                return lightness > 0.5 ? 'black' : 'white';
            };
            
            const strokeColor = getStrokeColor(effectivePrimaryColor);
            
            prompt += `- Enhanced Gradient Text (Core Color - Context7MCP): Render text with a high-contrast gradient from '${effectivePrimaryColor}' to '${accentColor}' (25% darker accent). Apply a low-medium ${strokeColor} stroke/outline for optimal readability, especially for purple, blue, green, and red overlays. Ensure text is CRISPY and sharp with no blur.\n`;
            prompt += `- Context7MCP Text Effects: The gradient should be smooth and professional, with the stroke providing necessary contrast against any background. Apply dynamic contrast adjustment based on background complexity. Avoid harsh banding between gradient colors.\n`;
        } else {
        prompt += `- Context7MCP Text Effects: Ensure text is CRISPY and sharp with professional-grade rendering. Apply intelligent contrast system:\n`;
        prompt += `  • PRIMARY: Clean, precise DROP SHADOW using ${shadowGlowColorDesc} for depth and readability\n`;
        prompt += `  • SECONDARY: Add subtle GLOW using ${shadowGlowColorDesc} only if background is very busy or low-contrast\n`;
        prompt += `  • ADAPTIVE: Automatically increase contrast effects if background complexity is detected\n`;
        prompt += `  • QUALITY: Text should pop dramatically from background while maintaining elegant, professional appearance\n`;
        prompt += `  • BALANCE: Effects enhance readability without visual clutter or color conflicts\n`;
        }
        prompt += `- Placement & Safe Zone: ${finalPlacementInstruction}\n`;
        
        // ADDITIONAL POSITION ENFORCEMENT: Explicit position restatement to prevent AI confusion
        prompt += `\n**TEXT OVERLAY POSITION CONFIRMATION**: Place the text overlay in the exact "${overlayPosition || 'Top Right'}" position. This means:\n`;
        if (overlayPosition === 'Top Right' || !overlayPosition) {
            prompt += `- Text goes in the UPPER-RIGHT corner of the thumbnail\n`;
            prompt += `- Text should NOT appear on the left side, center, or bottom\n`;
        } else if (overlayPosition === 'Top Left') {
            prompt += `- Text goes in the UPPER-LEFT corner of the thumbnail\n`;
            prompt += `- Text should NOT appear on the right side, center, or bottom\n`;
        } else if (overlayPosition === 'Top Center') {
            prompt += `- Text goes in the CENTER-TOP area of the thumbnail\n`;
            prompt += `- Text should NOT appear on the left/right sides or bottom\n`;
        } else if (overlayPosition === 'Bottom Right') {
            prompt += `- Text goes in the LOWER-RIGHT corner of the thumbnail\n`;
            prompt += `- Text should NOT appear on the left side, center, or top\n`;
        } else if (overlayPosition === 'Bottom Left') {
            prompt += `- Text goes in the LOWER-LEFT corner of the thumbnail\n`;
            prompt += `- Text should NOT appear on the right side, center, or top\n`;
        } else if (overlayPosition === 'Bottom Center') {
            prompt += `- Text goes in the CENTER-BOTTOM area of the thumbnail\n`;
            prompt += `- Text should NOT appear on the left/right sides or top\n`;
        } else if (overlayPosition === 'Center') {
            prompt += `- Text goes in the CENTER of the thumbnail\n`;
            prompt += `- Text should NOT appear in any corner or edge position\n`;
        }
        prompt += `- Double-check the text position before finalizing the image\n`;
        
        // === CONTEXT7MCP ADVANCED LAYOUT & COMPOSITION ===
        // Note: overlayTextWords and isPyramidLayout are already defined above in the color logic section
        
        prompt += `\n--- CONTEXT7MCP ADVANCED TEXT COMPOSITION ---\n`;
        prompt += `LAYOUT OPTIMIZATION (${selectedTextSize} Size):\n`;
        
        if (isPyramidLayout) {
            prompt += `• PYRAMID LAYOUT: Arrange text in pyramid shape (wider at top, narrower at bottom) for ${overlayTextWords.length} words\n`;
            prompt += `• WORD DISTRIBUTION: Optimize word grouping for visual balance and reading flow\n`;
            prompt += `• STACKING HIERARCHY: Create natural reading progression with appropriate line spacing\n`;
            if (selectedTextSize === "Large") {
                prompt += `• LARGE SIZE ADAPTATION: Use generous vertical spacing between lines to prevent crowding\n`;
            } else if (selectedTextSize === "Small") {
                prompt += `• SMALL SIZE ADAPTATION: Tighten vertical spacing while maintaining readability\n`;
            }
        } else {
            prompt += `• BALANCED LAYOUT: Use single-line or dual-line layout with optimal spacing for ${overlayTextWords.length} word(s)\n`;
            prompt += `• HORIZONTAL FLOW: Ensure natural left-to-right reading progression\n`;
            if (selectedTextSize === "Large") {
                prompt += `• LARGE SIZE CONSIDERATIONS: May require single-line layout to fit within safe zone\n`;
            } else if (selectedTextSize === "Small") {
                prompt += `• SMALL SIZE FLEXIBILITY: Can accommodate longer text strings while maintaining readability\n`;
            }
        }
        
        prompt += `\nVISUAL INTEGRATION (${selectedTextSize} Size Optimized):\n`;
        prompt += `• COMPOSITION HARMONY: Text positioning complements overall thumbnail composition\n`;
        prompt += `• VISUAL WEIGHT: Text size and placement create proper visual hierarchy with other elements\n`;
        if (selectedTextSize === "Large") {
            prompt += `• LARGE SIZE BALANCE: Text dominates as primary focal point, other elements support but don't compete\n`;
            prompt += `• LARGE SIZE SPACING: Ensure generous whitespace around text to prevent visual crowding\n`;
        } else if (selectedTextSize === "Medium") {
            prompt += `• MEDIUM SIZE BALANCE: Text and visual elements share equal importance in composition\n`;
            prompt += `• MEDIUM SIZE INTEGRATION: Text works harmoniously with faces, icons, and background elements\n`;
        } else if (selectedTextSize === "Small") {
            prompt += `• SMALL SIZE SUPPORT: Text complements rather than competes with primary visual elements\n`;
            prompt += `• SMALL SIZE PRECISION: Text placement is strategic and doesn't interfere with key visual focus\n`;
        }
        prompt += `• BRAND ALIGNMENT: Professional YouTube thumbnail standards with creator-friendly aesthetics\n`;
        prompt += `• MOBILE FIRST: Optimized for mobile viewing while maintaining desktop quality\n`;
        
        prompt += `\nQUALITY STANDARDS (${selectedTextSize} Size Specifications):\n`;
        prompt += `• TYPOGRAPHY: Professional font rendering with proper kerning and letter spacing\n`;
        if (selectedTextSize === "Large") {
            prompt += `• LARGE SIZE RENDERING: Extra attention to font weight and edge sharpness for maximum impact\n`;
            prompt += `• LARGE SIZE SPACING: Generous letter spacing to prevent character crowding at high sizes\n`;
        } else if (selectedTextSize === "Medium") {
            prompt += `• MEDIUM SIZE RENDERING: Balanced font weight with optimal readability across devices\n`;
            prompt += `• MEDIUM SIZE SPACING: Standard letter spacing optimized for 72-96px font sizes\n`;
        } else if (selectedTextSize === "Small") {
            prompt += `• SMALL SIZE RENDERING: Enhanced font weight compensation for smaller sizes (48-60px)\n`;
            prompt += `• SMALL SIZE CLARITY: Tighter letter spacing while maintaining individual character distinction\n`;
        }
        prompt += `• CONTRAST RATIO: Minimum 4.5:1 contrast ratio for accessibility compliance\n`;
        prompt += `• EDGE QUALITY: Anti-aliased edges with crisp, clean appearance\n`;
        prompt += `• CONSISTENCY: Uniform styling across all text elements in the composition\n`;
        prompt += `• SAFE ZONE COMPLIANCE: All text elements respect the 40px margin from edges\n`;
    } else {
        prompt += `Text Overlay (HEADLINES): DISABLED\n`;
        prompt += `- Do NOT add any large headline text, main titles, or attention-grabbing text overlays\n`;
        prompt += `- Do NOT add any text that was mentioned as "bold text overlay", "title", or "headline" in the prompt\n`;
        
        // Only mention secondary text if contextually relevant
        if (Object.values(graphicsElements).some(v => v)) {
            prompt += `- CONTEXTUAL SECONDARY TEXT: The following may be included if relevant to the specific graphics:\n`;
            
            if (graphicsElements.charts) {
                prompt += `  • Chart labels, axis labels, data points (for financial/analytics content)\n`;
            }
            if (graphicsElements.dataVisualizations) {
                prompt += `  • Statistics, numbers, percentages within visualizations\n`;
            }
            if (graphicsElements.uiElements) {
                prompt += `  • UI element labels, button text (for tech/software content)\n`;
            }
            if (graphicsElements.badges) {
                prompt += `  • Badge text, achievement labels, rank indicators\n`;
            }
            
            prompt += `- These secondary text elements should be small, functional, and integrated into their respective graphics ONLY if such graphics are part of the concept\n`;
        }
        prompt += `\n`;
    }

    // Graphics Section - Context-Aware Generation
    prompt += `Graphics & Visual Elements:\n`;
    prompt += `- Generate graphics based SOLELY on what's described in the user's prompt: "${workingPrompt}"\n`;
    prompt += `- Do NOT automatically add charts, UI elements, or data visualizations unless explicitly mentioned or contextually essential\n`;
    
    if (Object.values(graphicsElements).some(v => v)) {
        prompt += `- Based on the topic, the following elements MAY be relevant (include only if naturally fitting the concept):\n`;
        Object.entries(graphicsElements).forEach(([element, include]) => {
            if (include) {
                const elementDescriptions = {
                    charts: 'Charts or graphs with appropriate labels',
                    dataVisualizations: 'Data visualizations with numbers',
                    uiElements: 'UI elements or interface components',
                    badges: 'Badges, labels, or achievement indicators'
                };
                prompt += `  • ${elementDescriptions[element]}\n`;
            }
        });
    }
    
    prompt += `- Secondary text within graphics (if any) is allowed but should be minimal and functional\n`;
    prompt += `- Focus on creating visually compelling imagery that matches the prompt's intent\n\n`;
    
    // Visual Focus & Icons - Conditional based on includeIcons
    prompt += `Visual Focus & Composition:\n`;
    prompt += `- Keep layout simple and high-impact with a strong center or rule-of-thirds composition.\n`;
    
    // === ENHANCED GAMING RICH OBJECTS RULE IMPLEMENTATION ===
    // Use the enhanced context detection instead of brand detection
    const isConfirmedGamingContent = gameContext.isGaming && gameContext.confidence > 0.80;
    const isCleanBackgroundMode = !textOverlay && !includePerson && !includeIcons;
    
    // Apply @games-thumbnail-rich-objects rule ONLY when Icons Mode OR Clean Background Mode is enabled for CONFIRMED gaming content
    if ((includeIcons || isCleanBackgroundMode) && isConfirmedGamingContent) {
        prompt += `\n=== GAMING RICH OBJECTS ENHANCEMENT ===\n`;
        prompt += `When Icons Mode or Clean Background is enabled for gaming content, always:\n`;
        prompt += `- Render a visually rich scene filled with multiple game-related objects, icons, gadgets, and UI elements that are instantly recognizable to fans of the game.\n`;
        prompt += `- DISTRIBUTE OBJECTS EVENLY: Spread game elements across the entire 1280x720 canvas using rule of thirds, corners, and strategic placement around the composition center.\n`;
        prompt += `- Include a diverse selection of characters, skins, and avatars from the game, showing them in dynamic, action-oriented, or expressive poses.\n`;
        prompt += `- Display a variety of weapons, weapon skins, loadouts, and equipment, arranged in a balanced composition that highlights their uniqueness and in-game value.\n`;
        prompt += `- AVOID CLUSTERING: Do not place all objects in one area - ensure visual balance and professional spacing throughout the frame.\n`;
        prompt += `- Integrate authentic in-game UI elements such as scoreboards, health bars, ability icons, rank badges, or inventory panels, styled to match the game's visual language.\n`;
        prompt += `- Add collectible items, power-ups, or rare gadgets that are iconic within the game's universe.\n`;
        prompt += `- If applicable, show multiple player statuses (e.g., score, rank, health, shield, ammo) as part of the composition.\n`;
        prompt += `- Use a clean, high-contrast background that makes all objects and UI elements stand out clearly, avoiding clutter but maximizing visual interest.\n`;
        prompt += `- Ensure all elements are game-accurate, using official designs, colors, and proportions.\n`;
        prompt += `- Arrange the composition to feel energetic and immersive, as if capturing a highlight moment or a showcase of the game's best features.\n`;
        prompt += `- Prioritize clarity and recognizability for each object, icon, and character, so viewers can instantly identify the game and its unique style.\n`;
        prompt += `- **Do NOT include the game's logo or brand name anywhere in the thumbnail image, text, watermark, or overlay.**\n`;
        prompt += `\n`;
    }
    
    if (includeIcons) {
        // === ENHANCED CONTEXT-AWARE ICON GENERATION SYSTEM ===
        
        // Define icon keywords at the top scope for use throughout the function
        const detectedIconKeywords = extractIconKeywords(workingPrompt);
        
        // Get context-specific icon strategy
        const iconStrategy = determineIconGenerationStrategy(workingPrompt, l_video_topic);
        
        prompt += `\n=== CONTEXT-AWARE ICON GENERATION ===\n`;
        prompt += `Context Detection: ${gameContext.context} (${Math.round(gameContext.confidence * 100)}% confidence)\n`;
        prompt += `Icon Strategy: ${iconStrategy.renderingStyle} rendering with ${iconStrategy.distribution} distribution\n\n`;
        
        if (gameContext.isGaming && gameContext.confidence > 0.80) {
            // === STRICT GAMING CONTENT LOGIC (>80% CONFIDENCE REQUIRED) ===
            
            // Only use game-specific icon generation for high-confidence gaming content
            const gameIconInstructions = generateGameSpecificIconInstructions(gameContext, workingPrompt);
            prompt += gameIconInstructions;
            
            prompt += `\n--- CONFIRMED GAMING CONTENT ---\n`;
            prompt += `- Detected Context: ${gameContext.context}\n`;
            prompt += `- Confidence Level: ${Math.round(gameContext.confidence * 100)}%\n`;
            prompt += `- Reasoning: ${gameContext.reasoning}\n`;
            if (gameContext.primaryGame) {
                prompt += `- Primary Game: ${gameContext.primaryGame.name}\n`;
                prompt += `- Art Style: ${gameContext.primaryGame.artStyle}\n`;
            }
            prompt += `\n`;
            
        } else {
            // === CONTEXT-SPECIFIC NON-GAMING ICON GENERATION ===
            
            prompt += `\n--- NON-ENTERTAINMENT CONTENT DETECTED ---\n`;
            prompt += `- Context: ${gameContext.context}\n`;
            prompt += `- Confidence: ${Math.round(gameContext.confidence * 100)}%\n`;
            prompt += `- Reasoning: ${gameContext.reasoning}\n`;
            prompt += `- STRICT RULE: NO entertainment-related icons, combat items, or character elements allowed\n\n`;
            
            // Use context-specific icon generation
            prompt += `\n--- CONTEXT-SPECIFIC ICON STRATEGY ---\n`;
            prompt += `- Preferred Icons: ${iconStrategy.preferredIcons.join(', ')}\n`;
            prompt += `- Rendering Style: ${iconStrategy.renderingStyle}\n`;
            prompt += `- Avoid Icons: ${iconStrategy.avoidIcons.join(', ')}\n`;
            prompt += `- Distribution Pattern: ${iconStrategy.distribution}\n\n`;
            
            // Use intelligent icon rendering system for non-gaming content
            const iconAnalysis = analyzePromptIcons(workingPrompt, l_video_topic);
        const distributionAnalysis = analyzeIconDistribution(workingPrompt, l_video_topic);
        
        prompt += `\n--- INTELLIGENT ICON RENDERING SYSTEM ---\n`;
            prompt += `- Generate ${distributionAnalysis.strategy.minimumIcons}-${distributionAnalysis.strategy.maximumIcons} contextually appropriate icons\n`;
            prompt += `- Use DUAL-STYLE rendering based on object classification\n`;
            prompt += `- STRICT CONTENT FILTERING: Only icons relevant to "${l_video_topic}" topic\n`;
        }
        
        // Add distribution guidance (only for non-gaming content, as gaming has its own composition rules)
        if (!(gameContext.isGaming && gameContext.confidence > 0.80)) {
            const distributionAnalysis = analyzeIconDistribution(workingPrompt, l_video_topic);
        prompt += `\n--- SMART DISTRIBUTION STRATEGY ---\n`;
        prompt += `- ${distributionAnalysis.recommendedPlacement}\n`;
        prompt += `- Topic-specific preferences: ${JSON.stringify(distributionAnalysis.topicPreferences).replace(/[{}]/g, '').replace(/"/g, '').replace(/:/g, ' - ').replace(/,/g, ', ')}\n`;
        }
        
        // Handle rendering modes (only for non-gaming content, as gaming uses authentic game art styles)
        if (!(gameContext.isGaming && gameContext.confidence > 0.80)) {
            const iconAnalysis = analyzePromptIcons(workingPrompt, l_video_topic);
            
        if (iconRenderingMode === 'realistic') {
            // Force all icons to realistic rendering
            prompt += `\n🌍 ALL ICONS: REALISTIC RENDERING (70-80% Photorealistic):\n`;
            prompt += `- User Override: All icons will be rendered in realistic style\n`;
            prompt += `- Objects: All detected icons including ${detectedIconKeywords.join(', ') || 'contextual icons'}\n`;
            prompt += `- Rendering Style: High-detail realistic rendering with natural lighting and textures\n`;
            prompt += `- Specifications:\n`;
            prompt += `  • Natural photographic lighting with proper shadows and highlights\n`;
            prompt += `  • Realistic material surfaces (metal, plastic, glass, fabric textures)\n`;
            prompt += `  • Subtle wear/aging effects where appropriate\n`;
            prompt += `  • Professional photography quality with depth of field\n`;
            prompt += `  • Accurate proportions and real-world physics\n`;
            prompt += `  • Material-accurate reflections and surface properties\n`;
        }
        
        if (iconAnalysis.cartoonishIcons.length > 0) {
            prompt += `\n🎭 3D CARTOONISH ICONS (Stylized):\n`;
            prompt += `- Objects: ${iconAnalysis.cartoonishIcons.join(', ')}\n`;
            prompt += `- Rendering Style: Clean 3D stylized appearance with vibrant colors\n`;
            prompt += `- Specifications:\n`;
            prompt += `  • Vibrant, saturated colors with smooth gradient surfaces\n`;
            prompt += `  • Simplified geometry with playful proportions\n`;
            prompt += `  • Glossy or matte finish with clean edges\n`;
            prompt += `  • Soft, even lighting without harsh shadows\n`;
            prompt += `  • Cartoon-like expressiveness and appeal\n`;
            prompt += `  • Abstract representation for intangible concepts\n`;
        }
        
        // If no specific icons detected, provide general guidance
        if (iconAnalysis.totalIcons === 0) {
            prompt += `\n🎯 GENERAL ICON GUIDANCE:\n`;
            prompt += `- Since no specific icons were detected in the prompt, use context-appropriate icons\n`;
            prompt += `- For business/finance/health topics: Use realistic rendering (70-80% photorealistic)\n`;
            prompt += `- For gaming/emotions/abstract topics: Use 3D cartoonish rendering\n`;
            prompt += `- Ensure icons are strictly relevant to the topic: "${l_video_topic}"\n`;
            }
        }
        
        prompt += `\n--- UNIVERSAL ICON ENHANCEMENT ---\n`;
        prompt += `- Apply subtle isometric perspective (15-30 degree angle) for natural depth to all icons\n`;
        prompt += `- Icons should match the thumbnail's main light source direction\n`;
        prompt += `- Include subtle ambient occlusion in corners and crevices\n`;
        prompt += `- Position icons in 3D space with proper perspective and scale\n`;
        prompt += `- Create visual hierarchy through size and depth placement\n`;
        prompt += `- Ensure icons cast realistic shadows onto background surfaces\n`;
        prompt += `- Use depth-based color adjustments (darker in shadows, brighter in highlights)\n`;
        prompt += `- Maintain consistent lighting direction across all icons\n`;
        
        prompt += `\n--- ADVANCED ICON EFFECTS ---\n`;
        prompt += `- Add subtle depth of field blur to background icons for focus control\n`;
        prompt += `- Apply realistic reflections and highlights based on scene lighting\n`;
        prompt += `- Consider particle effects or energy trails for dynamic icons (if topic-appropriate)\n`;
        prompt += `- Use volumetric lighting for atmospheric effects around icons\n`;
        prompt += `- Add subtle motion blur for action-oriented icons when relevant\n`;
        
        // === CONTEXT-AWARE LOGO CORRECTIONS ===
        // Validate and correct icon rendering based on context analysis
        const iconValidation = validateIconRendering(workingPrompt, detectedIconKeywords);
        
        // === REACT DISAMBIGUATION ===
        // Use the React context analysis to disambiguate React.js vs reaction videos
        const reactContext = analyzeReactContext(workingPrompt);
        
        // PRIORITY: Check for reaction patterns that should block ALL tech logos
        const reactionPatterns = [
            'react to ', 'reacting to ', 'reacting ', ' reaction', 'first reaction',
            'initial reaction', 'live reaction', 'honest reaction', 'trailer reaction',
            'reaction video', 'my reaction', 'our reaction', 'his reaction', 'her reaction',
            'watching', 'first time watching'
        ];
        
        const hasReactionPattern = reactionPatterns.some(pattern => 
            workingPrompt.toLowerCase().includes(pattern)
        );
        
        if (iconValidation.corrections.length > 0 || reactContext.shouldUseAtomLogo || reactContext.shouldAvoidAtomLogo || hasReactionPattern) {
            prompt += `\n--- CONTEXT-AWARE LOGO CORRECTIONS ---\n`;
            
            // Handle React-specific corrections with priority for reaction patterns
            if (hasReactionPattern || reactContext.shouldAvoidAtomLogo) {
                prompt += `- REACTION VIDEO DETECTED: This is about emotional reactions/video reactions, NOT programming\n`;
                prompt += `- STRICTLY FORBIDDEN: Do NOT render any React.js atom logo, programming logos, or tech library symbols\n`;
                prompt += `- AVOID ALL TECH LOGOS: React, Vue, Angular, Node.js, JavaScript, or any programming-related icons\n`;
                prompt += `- Focus on emotional/entertainment icons: hearts, play buttons, thumbs up, emotional faces, video symbols\n`;
                prompt += `- Context: Reaction/Entertainment (confidence: ${hasReactionPattern ? '99' : Math.round(reactContext.confidence * 100)}%)\n`;
            } else if (reactContext.shouldUseAtomLogo && reactContext.context === 'tech') {
                prompt += `- REACT TECHNOLOGY DETECTED: Use the React.js ATOM logo with three orbital rings, NEVER an infinity symbol (∞)\n`;
                prompt += `- Context: Programming/Tech (confidence: ${Math.round(reactContext.confidence * 100)}%)\n`;
            }
            
            // Handle other technology corrections (but not if reaction context detected)
            if (!hasReactionPattern && !reactContext.shouldAvoidAtomLogo) {
                iconValidation.corrections.forEach(correction => {
                    if (correction.icon !== 'react') { // React is handled above
                        prompt += `- ${correction.icon}: MUST render as ${correction.correctDescription}\n`;
                    }
                });
            }
        }
        
        // Integrate brand logos if detected and relevant (with reaction context safety check)
        if (detectedBrandLogos && detectedBrandLogos.length > 0) {
            // Filter out React logo if reaction context is detected
            let allowedBrandLogos = detectedBrandLogos;
            if (hasReactionPattern || reactContext.shouldAvoidAtomLogo) {
                allowedBrandLogos = detectedBrandLogos.filter(brand => 
                    brand.name.toLowerCase() !== 'react'
                );
                if (allowedBrandLogos.length !== detectedBrandLogos.length) {
                    prompt += `\n--- BRAND LOGO SAFETY FILTER ---\n`;
                    prompt += `- React logo filtered out due to reaction video context\n`;
                }
            }
            
            if (allowedBrandLogos.length > 0) {
                const brandNames = allowedBrandLogos.map(b => b.name).join(', ');
                prompt += `\n--- BRAND LOGO INTEGRATION ---\n`;
                prompt += `- Integrate official ${brandNames} logo${allowedBrandLogos.length > 1 ? 's' : ''} using realistic rendering\n`;
                prompt += `- Maintain original aspect ratio and brand colors\n`;
                prompt += `- Logos should be noticeable yet subtle, complementing the composition\n`;
                if (allowedBrandLogos.length === 2) {
                    prompt += `- Position logos to emphasize comparison or versus scenarios\n`;
                }
                
                // Add context-specific logo corrections for detected brands
                allowedBrandLogos.forEach(brand => {
                    const logoCorrection = getCorrectLogoDescription(brand.name.toLowerCase());
                    if (logoCorrection) {
                        prompt += `- ${brand.name} logo: ${logoCorrection}\n`;
                    }
                });
            }
        }
        
        // === CONTEXT-AWARE GAMING LOGO PLACEMENT ===
        // Add smart placement instructions for gaming brands
        if (logoPlacementInstructions && logoPlacementInstructions.trim() !== '') {
            prompt += logoPlacementInstructions;
        }
        
        prompt += `\n--- ICON DISTRIBUTION & COMPOSITION WITH VISIBILITY SAFEGUARDS ---\n`;
        if (gameContext.isGaming && gameContext.confidence > 0.80) {
            prompt += `- For CONFIRMED GAMING content: Arrange game objects authentically as they would appear in the game universe\n`;
            prompt += `- Use game-appropriate placement (e.g., weapons in action poses, UI elements in familiar positions)\n`;
            prompt += `- Create dynamic, action-oriented compositions that tell the gaming story\n`;
            prompt += `- Maintain the game's visual hierarchy and design principles\n`;
            prompt += `- Support the gaming narrative with strategic object placement\n`;
            prompt += `- GAMING VISIBILITY RULE: Even authentic game layouts must ensure NO objects are cut off at frame edges\n`;
        } else {
            prompt += `- For NON-ENTERTAINMENT content: Distribute contextually appropriate icons evenly across the canvas\n`;
            prompt += `- STRICT RULE: NO combat items, controllers, or character-specific elements\n`;
            prompt += `- Use professional, topic-relevant icon placement following ${iconStrategy.distribution} pattern\n`;
            prompt += `- AVOID clustering all icons in one area - maintain balanced, professional composition\n`;
            prompt += `- Arrange icons using the rule of thirds, corners, or around the main subject\n`;
            prompt += `- Icons must be strictly relevant to the detected context: ${gameContext.context}\n`;
        }
        prompt += `- Use the available 1280x720 space to create a balanced, harmonious composition\n`;
        prompt += `- CRITICAL PLACEMENT RULE: Never place icons closer than 60px to the left edge or 40px to other edges\n`;
        prompt += `- OVERLAP PREVENTION: Ensure each icon is clearly visible and not overlapping with others\n`;
        prompt += `- CONTAINMENT VERIFICATION: Every icon must be completely contained within the visible frame area\n`;
        prompt += `- Maintain visual interest and avoid leaving large empty spaces unless required by the concept\n`;
        prompt += `- The final thumbnail should feel intentional, professional, and visually engaging\n`;
        prompt += `- Fill the canvas strategically - use foreground, middle ground, and background placement for depth\n`;
        prompt += `- ANTI-CLIPPING MEASURE: If an icon would be cut off, scale it down or reposition rather than allowing clipping\n`;
        
        prompt += `\n--- ENHANCED QUALITY STANDARDS ---\n`;
        if (gameContext.isGaming && gameContext.confidence > 0.80) {
            prompt += `- CONFIRMED GAMING CONTENT: Maintain 100% authenticity to the detected game's visual style\n`;
            prompt += `- Use ONLY official game assets, colors, and design language\n`;
            if (gameContext.primaryGame) {
                prompt += `- Objects must be instantly recognizable to fans of ${gameContext.primaryGame.name}\n`;
            }
            prompt += `- Apply appropriate lighting and effects from the game universe\n`;
            prompt += `- NO generic icons - everything must be authentic to the detected title\n`;
        } else {
            prompt += `- NON-ENTERTAINMENT CONTENT: Maintain strict context adherence and professional quality\n`;
            prompt += `- ABSOLUTE PROHIBITION: NO combat items, controllers, character avatars, or entertainment UI elements\n`;
            prompt += `- Context Compliance: Icons must match the ${gameContext.context} context exactly\n`;
            prompt += `- Use ${iconStrategy.renderingStyle} rendering style consistently\n`;
        prompt += `- Ensure clear visual distinction between realistic and cartoonish elements\n`;
            prompt += `- DO NOT use generic 2D emojis or flat shapes - all icons need 3D depth\n`;
            prompt += `- Quality Filter: Only professional, topic-appropriate icons that enhance the ${l_video_topic} theme\n`;
        }
        prompt += `- Icons must remain recognizable and readable at thumbnail size\n`;
        prompt += `- Effects should enhance, not clutter the overall composition\n`;
        
        // === ICON-OBJECT-FULL-VISIBILITY-ENHANCEMENT ===
        prompt += `\n--- ICON FULL VISIBILITY ENHANCEMENT (Critical for 90% icon success rate) ---\n`;
        prompt += `- MANDATORY COMPLETE VISIBILITY: ALL icons and objects MUST be rendered completely within the visible thumbnail area\n`;
        prompt += `- ZERO CLIPPING TOLERANCE: NO icon or object should be cut off, clipped, or cropped at ANY edge, especially the left side\n`;
        prompt += `- ENHANCED SAFE ZONE: Maintain minimum 60px margin from the left edge and 40px from all other edges for icons\n`;
        prompt += `- SMART CONTAINMENT: Use intelligent scaling and positioning to ensure every icon/object is 100% visible and aesthetically placed\n`;
        prompt += `- LAYOUT VALIDATION: Before finalizing placement, verify that every icon's complete outline fits within the frame boundaries\n`;
        prompt += `- BALANCED COMPOSITION: Use centered or right-weighted distribution to prevent left-edge clustering and cutting\n`;
        prompt += `- OBJECT INTEGRITY: Every icon/object must maintain its complete visual form without any part extending beyond frame edges\n`;
        prompt += `- VISIBILITY GUARANTEE: Apply proper scaling, spacing, and layout to ensure 100% icon visibility success rate\n`;
        
        prompt += `- All icons must be fully visible with enhanced safe zone compliance (60px left, 40px other edges)\n`;
        prompt += `- Strictly relevant to topic "${l_video_topic}" and user prompt: "${workingPrompt}"\n`;
        
        // === FINAL CONTEXT VALIDATION ===
        prompt += `\n--- CONTEXT VALIDATION CHECKLIST ---\n`;
        if (gameContext.isGaming && gameContext.confidence > 0.80) {
            prompt += `✓ GAMING CONTENT CONFIRMED (${Math.round(gameContext.confidence * 100)}% confidence)\n`;
            prompt += `✓ Title-specific icons and objects are appropriate\n`;
            prompt += `✓ Authentic game art style maintained\n`;
        } else {
            prompt += `✓ NON-ENTERTAINMENT CONTENT CONFIRMED (Context: ${gameContext.context})\n`;
            prompt += `✓ NO entertainment elements, combat items, or character-specific icons included\n`;
            prompt += `✓ Context-appropriate icons only (${iconStrategy.preferredIcons.slice(0, 3).join(', ')}, etc.)\n`;
            prompt += `✓ Professional ${iconStrategy.renderingStyle} rendering style applied\n`;
        }
        prompt += `✓ All icons relevant to "${l_video_topic}" topic\n`;
        prompt += `✓ No cross-contamination between content categories\n`;
        prompt += `✓ ICON VISIBILITY VALIDATION: All icons completely contained within frame boundaries\n`;
        prompt += `✓ ENHANCED SAFE ZONES: 60px left margin, 40px other edges applied to all icons/objects\n`;
        prompt += `✓ ZERO CLIPPING GUARANTEE: No icons cut off, especially at left edge (addresses 90% clipping issue)\n\n`;
        
    } else {
        prompt += `- Do not include any illustrative icons, emojis, or generic shapes unless they are naturally embedded in the subject (e.g., a product logo on a device).\n`;
    }
    prompt += `\n`;

    // Background Section
    prompt += `\nBackground Style & Color Grading:\n`;

    // Use the new comprehensive background generation function
    const I_details_for_background_blah_blah = { selectedSolidBgColor, selectedBackgroundStyleId }; // I var
    prompt += getThePerfectBackgroundMyDude(workingPrompt, selectedBackgroundType, I_details_for_background_blah_blah, includePerson);

    // Common instructions for all background types (can be refined based on type)
    prompt += `Incorporate creative background elements where appropriate for the selected style or user prompt: abstract shapes, subtle patterns, motion blur, light streaks, or bokeh effects, IF they enhance the mapped background and don't contradict it.\n`;
    prompt += `Rotate and flip the color/gradient direction or lighting angle for each new thumbnail to avoid visual repetition, unless a specific template or mapped style dictates a fixed direction (e.g., a specific sunset direction).\n`;
    prompt += `Ensure the background ALWAYS provides strong contrast with the subject (if any) and overlay text (if any) for maximum readability and click appeal. This is a critical rule.\n`;

    // Inject LUT Color Grading Description
    if (selectedColorGrade && colorMoodPresets[selectedColorGrade]) { // Use the new colorMoodPresets
        const lutDescription = colorMoodPresets[selectedColorGrade];
        prompt += `\nColor Grading:\n- Apply a ${lutDescription} color grade effect across the entire image, including subject and background. This grading should unify the image and enhance the desired mood (e.g., 'viral-energy' for exciting content, 'soft-calm' for relaxing topics). Ensure the color grade does not wash out important details or reduce contrast too much.\n`;
    } else {
        // Optional: Add default or no grading instruction if needed
        prompt += `\nColor Grading:\n- Apply a standard, natural color balance that enhances sharpness and vibrancy without overtly stylizing the colors, unless a specific background style implies its own color grading (like a sepia tone for a historical theme if chosen by the background generator).\n`;
    }

    // Output Format & Universal Quality Rules
    prompt += `Output Format & Quality:\n`;
    prompt += `- CRITICAL: The output image MUST be EXACTLY 1280 pixels wide by 720 pixels tall (1280x720). Do NOT generate any other resolution. This is the required YouTube thumbnail standard.\n`;
    prompt += `- ASPECT RATIO ENFORCEMENT: The image MUST fill the entire 16:9 frame with NO black bars, letterboxing, or pillarboxing. The background and all visual content must extend completely to all four edges of the 1280x720 canvas.\n`;
    prompt += `- NO PADDING OR BORDERS: Do not add any empty space, black bars, or padding around the image. The content must be composed to naturally fill the entire rectangular frame.\n`;
    prompt += `- When displaying or previewing the image in the application's canvas, it will be scaled down to 640x360 pixels (also 16:9), maintaining the same aspect ratio and all visual details.\n`;
    prompt += `- The scaling must be proportional, with NO cropping, stretching, or distortion. All elements (text, subject, background) must remain fully visible and legible at both sizes.\n`;
    prompt += `- The image should be designed with the 1280x720 output in mind, but always previewed at 640x360 in the UI canvas for performance and usability.\n`;
    prompt += `- Ensure that all safe zones, margins, and visual hierarchy are preserved and readable at both the full and preview sizes.\n`;
    prompt += `- Style: Cinematic, high sharpness, and vibrant saturation, suitable for a YouTube thumbnail.\n`;
    prompt += `- Ensure all elements are clearly visible on both mobile and desktop screens.\n`;
    prompt += `- Overall a clean, professional, and click-worthy thumbnail.\n`;
    prompt += `- IMPORTANT PREVIEW CROPPING & SAFE ZONE: To prevent text or important elements from being cut off on any device or platform, ensure a minimum safe zone of **at least 40px** from ALL edges (top, bottom, left, right). All text overlays MUST be written in ALL CAPS and positioned within this safe zone. All text, faces, and key icons MUST be fully visible within this safe area. Do NOT place any critical text or elements near any edge; keep them well within the 40px boundary. This ensures perfect readability across all devices and prevents clipping.\n`;
    prompt += `- ENHANCED ICON SAFE ZONE: For icons and objects specifically, use an ENHANCED safe zone of **60px from the left edge** and **40px from all other edges** to prevent the common issue of left-side icon clipping. This addresses the 90% clipping problem by providing extra margin where it's most needed.\n`;

    // --- Comprehensive Prompt Enhancement (Rendering Order, Cinematic Effects, Glow, LUT) ---
    prompt += `\nRendering & Effects Instructions:\n`;
    prompt += `- Render the background first, then the main subject in the foreground, followed by overlays/icons, and finally the text overlay.\n`;
    prompt += `- Apply cinematic effects: use depth of field to blur the background slightly, add motion blur to any moving elements, and use dramatic lighting (e.g., rim light, side light, or spotlight) to highlight the subject.\n`;
    prompt += `- Add a soft, vibrant glow to key elements such as the text overlay, icons, and subject edges to increase visual impact and separation from the background.\n`;
    prompt += `- Apply scene color grading (LUT) to match the intended mood. Use descriptive phrases (e.g., 'cinematic orange-teal for warmth and drama', 'cold blue-gray for a dramatic look', 'neon purple and blue for a tech vibe').\n`;
    prompt += `- Ensure all effects work together for a visually balanced, cinematic, and high-impact thumbnail.\n`;

    if (fitFullCanvas) {
        prompt += `\nFit Full Canvas (ON):\n`;
        prompt += `Generate the thumbnail to fill the entire 1280x720 frame. The background should extend to all edges without any visible borders, padding, or letterboxing. Ensure all critical elements (like faces or text if present) are within a safe zone of at least 40px from all edges (top, bottom, left, right). All text overlays MUST be in ALL CAPS and positioned within this safe zone to ensure full visibility on all devices and platforms.\n`;
    } else {
        prompt += `\nFit Full Canvas (OFF):\n`;
        prompt += `IMPORTANT: Even with Fit Full Canvas OFF, the image MUST still fill the entire 1280x720 frame with NO black bars or letterboxing. The main subject and action should be clearly visible and well-composed within the full frame. The background must extend to all edges. Focus on optimal composition while maintaining the full 16:9 aspect ratio coverage.\n`;
    }

    // If both are off, add a specific instruction for background-only mode.
    if (!includePerson && !textOverlay) {
        prompt += `\n**IMPORTANT: BACKGROUND-ONLY MODE (Fallback within main prompt - should ideally use cleanPrompt mode)**\n`;
        prompt += `- Generate a high-quality, visually appealing background scene based on the prompt: "${workingPrompt}".\n`;
        prompt += `- This image is intended as a background layer, so ensure it is compositionally balanced to allow for later manual placement of text or subjects in an external editor.\n`;
        prompt += `- NO human figures, NO characters, and NO text overlays should be present in the generated image. This is background ONLY.\n`;
    }

    // Universal thumbnail requirements from rule
    let thumbnailGuidance = `\n\nCinematic YouTube thumbnail at 1280x720 resolution. `;
    
    // Add Context7MCP face matching priority when custom face is provided
    if (faceDescription) {
        thumbnailGuidance += `**CONTEXT7MCP FACE UPLOAD - HIGHEST PRIORITY**: This is a face replacement/swap operation that MUST preserve the exact identity of the provided reference face. `;
        
        if (faceDescription.startsWith('[')) {
            thumbnailGuidance += `The uploaded face image is the primary reference - ensure perfect facial identity preservation with seamless blending into the thumbnail scene. `;
        } else {
            thumbnailGuidance += `The face from URL ${faceDescription} is the primary reference - ensure perfect facial identity preservation with seamless blending into the thumbnail scene. `;
        }
        
        thumbnailGuidance += `Apply Context7MCP face upload optimization: priority replacement (never ignore), seamless blending (exact skin tone/texture matching), facial alignment (pose/expression adaptation), edge case handling (lighting/quality compensation), and professional quality standards (no artifacts, studio-quality result). `;
    }
    
    thumbnailGuidance += `Focus on high-impact, clear composition that drives clicks. Ensure all elements are properly sized for mobile viewing. Vibrant lighting with strong contrast between subject and background. Professional quality suitable for viral content.`;

    prompt += thumbnailGuidance;

    return prompt;
}

// Representation of thumbnail-logic.yaml background_logic.smart_mapping
const smartBackgroundMappings = {
    gaming: {
        fortnite: "a dynamic Fortnite-style build battle background with recognizable in-game elements like wooden structures, ramps, and a vibrant sky, possibly with storm effects or iconic map locations in the distance. Add an abstract sunburst effect in the background for extra energy and visual impact.",
        warzone: "a gritty, realistic military battlefield at dusk or dawn, featuring urban ruins, tactical gear elements, or distant combat effects like smoke and tracers. Cinematic and tense. Incorporate an abstract sunburst background to enhance drama and focus.",
        csgo: "an urban combat scene reminiscent of Counter-Strike maps, with graffiti, damaged buildings, and an overall tense, competitive atmosphere. Balanced for readability. Overlay an abstract sunburst background for added intensity.",
        minecraft: "a blocky, pixel-art landscape or textures in Minecraft style. Bright and engaging, with characteristic Minecraft elements. Include a subtle abstract sunburst background to boost vibrancy.",
        'battle royale': "an atmospheric background evoking the specific battle royale game mentioned. Could be map overviews, weapon silhouettes, or action effects like smoke or explosions, with a gritty, competitive feel. Add an abstract sunburst background for dynamic energy.",
        'mobile game': "a background incorporating mobile UI elements, touch control overlays, or a stylized mobile device frame. Bright and engaging, reflecting the game's art style. Integrate an abstract sunburst background for extra excitement."
    },
    tech: {
        'apple review': "a sleek, modern, minimalist tech setup background. Could feature soft lighting, brushed aluminum textures, or abstract representations of Apple's design language. Clean and premium.",
        'app comparison': "a split background visually separating two distinct UI styles or color themes, perhaps with a subtle dividing line or contrasting textures. Modern and clear.",
        'software': "an abstract digital background with faint code snippets, glowing data streams, or a futuristic interface aesthetic. Dark mode preferred, with pops of color.",
        'gadget': "a clean, modern aesthetic with circuit patterns, abstract digital gradients, or a sleek futuristic look. May include close-ups of device textures if relevant to the userPrompt.",
        'tech review': "a sophisticated background that blends abstract technology motifs (like circuits or data flows) with elements specific to the reviewed product category (e.g., for a phone review, perhaps a stylized representation of a smartphone component).",
    },
    reaction: {
        cringe: "a vibrant, colorful radial comic book style burst effect (like 'POW!' or 'CRINGE!'), with dynamic lines and possibly halftone patterns. Energetic and humorous.",
        scary: "a dark, atmospheric background with deep reds, blues, or purples. Cinematic lighting with strong shadows, possibly with fog, mist, or unsettling textures. Suspenseful.",
        'reacting to': "a dynamic and expressive background, perhaps with pop-art halftone patterns, abstract emotional color splashes, or subtle visual cues from what is being reacted to (if implied by userPrompt).",
    },
    unboxing: {
        product: "a dramatic background with a glowing box or product silhouette at the center. Use volumetric lighting, light rays, and a sense of anticipation. Premium and exciting.",
        'mystery box': "visually exciting elements like abstract packaging textures, surprise effects (e.g., light rays, particles), product silhouettes shrouded in mystery, or a sense of anticipation and curiosity."
    },
    vlogs: {
        default: "scenic and aesthetically pleasing lifestyle, travel, or daily environment scenes matching the specific topic of userPrompt. If travel, show iconic landmarks blurred or stylized. If daily life, a cozy or dynamic setting reflecting the vlog's theme."
    },
    tutorials: {
        default: "a clean and professional background, possibly featuring abstract representations of whiteboards, code editors (if relevant), or relevant workspace imagery. Avoid clutter and maintain focus on clarity."
    }
};

// Representation of thumbnail-logic.yaml color_moods.presets
// Aligned with existing lutPromptMap and YAML, preferring more descriptive for AI
const colorMoodPresets = {
    'cinematic-warm': 'cinematic orange-teal LUT with warm shadows and cool highlights, enhancing depth and mood',
    'cold-drama': 'cold blue-gray cinematic filter with desaturated highlights and crushed blacks, creating a dramatic and moody atmosphere',
    'gaming-glow': 'neon lighting effects with vibrant purple, electric blue, and magenta tones, digital bloom, and high contrast, perfect for gaming content',
    'viral-energy': 'high-contrast vivid color grading with saturated reds, yellows, and cyans, creating an energetic and eye-catching look',
    'soft-calm': 'pastel color grade with muted tones, soft ambient lighting, and a gentle haze, for a relaxing and aesthetic vibe',
    'sunset-mood': 'soft peach and orange gradient overlay with warm lowlight ambience, evoking a sunset atmosphere',
    'retro-pop': '80s pop art grading with bold magenta, yellow, and cyan tones, possibly with halftone effects or geometric patterns'
};

// Per custom instructions, this function is long and has a unique style.
function getThePerfectBackgroundMyDude(userPromptString, selectedUserBackgroundType, someDetailsForBackground, includePerson) {
    let l_promptForBg = ""; // single letter var - prompt for background
    const O_userPromptLower = (userPromptString || "").toLowerCase().trim(); // another single letter var - user prompt lowercased

    // --- 1. Check for manual user selection FIRST ---
    if (selectedUserBackgroundType === 'solid' && someDetailsForBackground.selectedSolidBgColor) {
        l_promptForBg = `- Use a solid color background with the hex code: ${someDetailsForBackground.selectedSolidBgColor}. Add a very subtle radial gradient or vignette to give it some depth, but it should primarily appear as a solid color.\n`;
    } else if (selectedUserBackgroundType === 'template' && someDetailsForBackground.selectedBackgroundStyleId) {
        const l_styleId = someDetailsForBackground.selectedBackgroundStyleId;
        let l_templateDesc = `a pre-designed background template identified by '${l_styleId}'.`;
        // Attempt to find a more descriptive prompt from smartBackgroundMappings
        for (const category in smartBackgroundMappings) {
            if (smartBackgroundMappings[category][l_styleId]) {
                l_templateDesc = smartBackgroundMappings[category][l_styleId];
                break;
            }
        }
        l_promptForBg = `- Use the background style: ${l_templateDesc}\n`;
    }

    // --- 2. If NO manual selection, infer from context ---
    if (!l_promptForBg) {
        const contextualBackgroundMap = {
            // Gaming
            'fortnite': "a dynamic Fortnite-style build battle background with recognizable in-game elements like wooden structures, ramps, and a vibrant sky, possibly with storm effects or iconic map locations in the distance.",
            'warzone': "a gritty, realistic military battlefield at dusk or dawn, featuring urban ruins, tactical gear elements, or distant combat effects like smoke and tracers.",
            'call of duty': "a gritty, realistic military battlefield with smoke, debris, and a tense atmosphere, inspired by multiplayer maps.",
            'valorant': "an atmospheric background with the distinct art style of Valorant, featuring sleek geometric shapes, glowing energy effects, and color palettes from maps like Ascent or Bind.",
            'cs:go': "an urban combat scene reminiscent of Counter-Strike maps, with graffiti, damaged buildings, and an overall tense, competitive atmosphere.",
            'cs2': "an urban combat scene reminiscent of Counter-Strike maps, with graffiti, damaged buildings, and an overall tense, competitive atmosphere.",
            'apex legends': "a dynamic sci-fi landscape inspired by Apex Legends maps like World's Edge or Olympus, with futuristic structures and dramatic skies.",
            'battle royale': "an atmospheric background evoking a battle royale game. Could be map overviews, weapon silhouettes, or action effects like smoke or explosions.",
            'mobile game': "a background incorporating mobile UI elements, touch control overlays, or a stylized mobile device frame.",
            'gaming': "a dynamic gaming setup with RGB lighting, high-end peripherals (mouse, keyboard, headset), and abstract neon energy streaks.",

            // Tech / Unboxing
            'tech review': "a sophisticated background that blends abstract technology motifs (like circuits or data flows) with elements specific to the reviewed product category.",
            'unboxing': "a dramatic background with a glowing box or product silhouette at the center. Use volumetric lighting, light rays, and a sense of anticipation.",
            'mystery box': "visually exciting elements like abstract packaging textures, surprise effects, product silhouettes shrouded in mystery, or a sense of anticipation.",
            'gadget': "a clean, modern aesthetic with circuit patterns, abstract digital gradients, or a sleek futuristic look.",

            // Reaction / Mystery
            'reacting to': "a dynamic and expressive background, perhaps with pop-art halftone patterns, abstract emotional color splashes, or subtle visual cues from what is being reacted to.",
            'cringe': "a vibrant, colorful radial comic book style burst effect, with dynamic lines and possibly halftone patterns.",
            'scary': "a dark, atmospheric background with deep reds, blues, or purples, with strong shadows, possibly with fog or mist.",
            'weirdest gadgets': "a background with glowing mystery boxes, question marks, and a sense of intrigue, with cinematic blur.",
            'old facebook photos': "a background with a pop-art halftone texture and subtle blush emoji patterns.",

            // Vlogs / Lifestyle
            'vlog': "scenic and aesthetically pleasing lifestyle, travel, or daily environment scenes matching the specific topic. If travel, show iconic landmarks blurred or stylized.",

            // Tutorials / Education
            'tutorial': "a clean and professional background, possibly featuring abstract representations of whiteboards, code editors, or relevant workspace imagery.",

            // Finance / Business
            'crypto': "a futuristic digital background with elements like glowing circuit patterns or abstract representations of blockchain technology.",
            'bitcoin': "a futuristic digital background with stylized Bitcoin coins and glowing circuit patterns.",
            'finance': "a professional background incorporating financial charts (line graphs, bar charts) or stock market tickers.",
            'money': "a background visually representing wealth, such as artistic arrangements of currency or abstract gold textures.",
            'business': "a background depicting a modern corporate environment, such as an abstract office setting or cityscapes with skyscrapers."
        };

        for (const keyword in contextualBackgroundMap) {
            if (O_userPromptLower.includes(keyword)) {
                l_promptForBg = `- Background Style: ${contextualBackgroundMap[keyword]}\n`;
                break; // Use the first match
            }
        }
    }

    // --- 3. Fallback if no context or manual selection is found ---
    if (!l_promptForBg) {
        if (O_userPromptLower.length > 0) {
            l_promptForBg = `- Generate a background that is visually relevant to the theme: "${userPromptString}". It should be cinematic, high-quality, and enhance the main topic without being distracting.\n`;
        } else {
            l_promptForBg = `- Generate a visually STUNNING and CINEMATIC abstract background. Incorporate elements like dramatic lighting, vibrant color gradients, and subtle textures to create depth and visual appeal.\n`;
        }
    }
    
    // --- 4. Final adjustments ---
    if (includePerson) {
        l_promptForBg += "- Apply a subtle depth-of-field effect to the background, making it slightly blurry to ensure the main subject and any text overlay remain sharp and visually prominent.\n";
    }
    
    l_promptForBg += `Always ensure the background provides strong visual contrast with any foreground elements. The background itself should be high quality, with good lighting and composition.\n`;
    return l_promptForBg;
}


// NEW: Topic-based icon generation
const VIDEO_TOPICS = {
    GAMING: 'Gaming',
    HEALTH_NUTRITION: 'Health & Nutrition',
    BUSINESS_FINANCE: 'Business & Finance',
    LEGAL_FRAUD: 'Legal & Fraud',
    FOOD_VLOGS: 'Food & Vlogs',
    TECH: 'Tech', // Added tech as it was already somewhat handled
    GENERAL: 'General'
};

const TOPIC_KEYWORDS = {
    [VIDEO_TOPICS.GAMING]: ['game', 'gaming', 'fortnite', 'warzone', 'cod', 'valorant', 'pubg', 'apex', 'cs2', 'playthrough', 'esports', 'console', 'pc gaming', 'rpg', 'mmo', 'fps', '1v1', 'boss fight'],
    [VIDEO_TOPICS.HEALTH_NUTRITION]: ['health', 'healthy', 'nutrition', 'diet', 'fitness', 'workout', 'vitamins', 'calories', 'recipe', 'wellness', 'exercise', 'gym', 'yoga', 'meditation'],
    [VIDEO_TOPICS.BUSINESS_FINANCE]: ['business', 'finance', 'money', 'investing', 'stocks', 'crypto', 'bitcoin', 'market', 'startup', 'entrepreneur', 'budget', 'economy', 'trading', 'wealth'],
    [VIDEO_TOPICS.LEGAL_FRAUD]: ['legal', 'law', 'lawyer', 'fraud', 'scam', 'court', 'attorney', 'copyright', 'contract', 'justice', 'police', 'crime'],
    [VIDEO_TOPICS.FOOD_VLOGS]: ['food', 'vlog', 'recipe', 'cooking', 'restaurant', 'eat', 'travel', 'daily vlog', 'lifestyle', 'review', 'kitchen', 'dish', 'meal prep'],
    [VIDEO_TOPICS.TECH]: ['tech', 'technology', 'software', 'hardware', 'ai', 'code', 'programming', 'gadget', 'review', 'smartphone', 'computer', 'app', 'website', 'robotics']
};

const TOPIC_ICON_PROMPTS = {
    [VIDEO_TOPICS.GAMING]: "Generate 3D icons like game controllers, joysticks, gaming mice, keyboards, headsets, specific in-game items if mentioned, or abstract symbols representing victory, strategy, or competition. Ensure they are stylized to match a gaming aesthetic (e.g., neon glows, metallic textures, dynamic shapes).",
    [VIDEO_TOPICS.HEALTH_NUTRITION]: "Generate 3D icons like fruits (apple, banana, berries), vegetables (broccoli, carrot), a heart symbol (for health), a dumbbell, a yoga pose silhouette, a water bottle, a running shoe, or a stylized DNA strand. Icons should look clean, vibrant, and healthy.",
    [VIDEO_TOPICS.BUSINESS_FINANCE]: "Generate 3D icons like upward trending graphs, briefcases, piggy banks, abstract representations of currency (not specific symbols unless requested), negotiation/handshake symbols, a lightbulb for ideas, or a building silhouette for corporate. Style should be professional and modern.",
    [VIDEO_TOPICS.LEGAL_FRAUD]: "Generate 3D icons like a gavel, scales of justice, a shield, a magnifying glass (for investigation), a document scroll, a key/lock (for security), or an abstract warning symbol. Style should be serious and authoritative.",
    [VIDEO_TOPICS.FOOD_VLOGS]: "Generate 3D icons like a chef's hat, cutlery (fork, knife, spoon), a steaming plate of food, a camera or play button (for vlogs), a specific food item if central to the prompt, or a map pin/airplane for travel vlogs. Style should be appetizing and engaging.",
    [VIDEO_TOPICS.TECH]: "Generate 3D icons like a stylized microchip, a gear, a cloud symbol, a code bracket (</>), a mouse pointer, a futuristic orb, or abstract network/data visualizations. Style should be modern, sleek, possibly with circuit patterns or glowing elements.",
    [VIDEO_TOPICS.GENERAL]: "Generate 1-3 visually appealing, modern 3D icons that are abstractly related to the core concept of the prompt: [USER_PROMPT_SUMMARY]. These could be geometric shapes, stylized representations of ideas (like a lightbulb or a checkmark), or simple, clean symbols that evoke the mood or theme. Avoid overly specific or niche icons unless the prompt clearly calls for them."
};

/**
 * Enhanced video topic extraction with context-aware classification
 * @param {string} userPrompt - The user's input prompt.
 * @returns {string} The identified video topic (from VIDEO_TOPICS) or VIDEO_TOPICS.GENERAL.
 */
export function extractVideoTopic(userPrompt) {
    if (!userPrompt || userPrompt.trim() === '') {
        return VIDEO_TOPICS.GENERAL;
    }
    
    const promptLower = userPrompt.toLowerCase();
    
    // === ENHANCED CONTEXT-AWARE TOPIC DETECTION ===
    
    // 1. Food and vlogs detection (check first to avoid false tech matches)
    const foodIndicators = [
        'food', 'recipe', 'cooking', 'kitchen', 'restaurant', 'meal', 'dish', 'vlog'
    ];
    
    const hasFoodContext = foodIndicators.some(indicator => promptLower.includes(indicator));
    if (hasFoodContext) {
        return VIDEO_TOPICS.FOOD_VLOGS;
    }
    
    // 2. Health and nutrition detection
    const healthIndicators = [
        'health', 'nutrition', 'fitness', 'workout', 'diet', 'exercise', 'wellness', 'medical'
    ];
    
    const hasHealthContext = healthIndicators.some(indicator => promptLower.includes(indicator));
    if (hasHealthContext) {
        return VIDEO_TOPICS.HEALTH_NUTRITION;
    }
    
    // 3. Business/finance detection
    const businessIndicators = [
        'business', 'financial', 'investment', 'money', 'profit', 'revenue', 
        'startup', 'entrepreneur', 'market', 'trading', 'stocks', 'crypto'
    ];
    
    const hasBusinessContext = businessIndicators.some(indicator => promptLower.includes(indicator));
    if (hasBusinessContext) {
        return VIDEO_TOPICS.BUSINESS_FINANCE;
    }
    
    // 4. Strict gaming detection (only if explicit gaming context)
    const explicitGamingIndicators = [
        'gameplay', 'gaming', 'game tutorial', 'game review', 'esports', 
        'gaming setup', 'gaming performance', 'competitive gaming'
    ];
    
    const hasExplicitGamingContext = explicitGamingIndicators.some(indicator => promptLower.includes(indicator));
    
    // Check for specific game mentions with gaming context
    const gameNames = ['fortnite', 'valorant', 'warzone', 'cod warzone', 'cs2', 'apex legends', 'pubg'];
    const hasGameWithContext = gameNames.some(game => {
        if (promptLower.includes(game)) {
            // Check if it's mentioned in a gaming context
            const gamingContextWords = ['play', 'playing', 'gameplay', 'win', 'lose', 'rank', 'match', 'wins', 'building'];
            return gamingContextWords.some(context => promptLower.includes(context));
        }
        return false;
    });
    
    if (hasExplicitGamingContext || hasGameWithContext) {
        return VIDEO_TOPICS.GAMING;
    }
    
    // 5. Tech/development detection (more specific to avoid false matches)
    const techIndicators = [
        'app design', 'mobile app', 'code', 'coding', 'programming', 'development', 
        'software', 'website', 'api', 'database', 'algorithm', 'technology',
        'scripting', 'developer', 'programming language', 'framework', 'library'
    ];
    
    // More specific tech detection to avoid false matches like "techniques"
    const hasTechContext = techIndicators.some(indicator => {
        if (indicator === 'tech') {
            // Only match 'tech' if it's not part of another word
            return promptLower.match(/\btech\b/);
        }
        return promptLower.includes(indicator);
    });
    
    if (hasTechContext) {
        return VIDEO_TOPICS.TECH;
    }
    
    // 6. Legal and fraud detection
    const legalIndicators = [
        'legal', 'law', 'court', 'fraud', 'scam', 'lawyer', 'attorney', 'justice'
    ];
    
    const hasLegalContext = legalIndicators.some(indicator => promptLower.includes(indicator));
    if (hasLegalContext) {
        return VIDEO_TOPICS.LEGAL_FRAUD;
    }
    
    // 7. Fallback to original keyword matching for edge cases
    for (const topic in TOPIC_KEYWORDS) {
        // Skip gaming topic as it's handled above with context
        if (topic === VIDEO_TOPICS.GAMING) continue;
        
        for (const keyword of TOPIC_KEYWORDS[topic]) {
            if (promptLower.includes(keyword.toLowerCase())) {
                return topic;
            }
        }
    }
    
    return VIDEO_TOPICS.GENERAL; // Default to general if no specific context detected
}

// Person Headshot Enhancement Rule Implementation
function enhancePersonHeadshotPrompt(basePersonPrompt, selectedExpression, userPrompt) {
    // Check if this looks like a headshot request based on keywords or expression
    const headshotKeywords = [
        'headshot', 'portrait', 'face', 'facial', 'close-up', 'closeup',
        'profile', 'selfie', 'reaction', 'expression', 'looking at camera'
    ];
    
    const isHeadshotRequest = headshotKeywords.some(keyword => 
        userPrompt.toLowerCase().includes(keyword)
    ) || ['Shocked', 'Surprised', 'Thinking', 'Happy', 'Loved', 'Angry', 'Crying', 'Laughing'].includes(selectedExpression);
    
    if (!isHeadshotRequest) {
        return basePersonPrompt; // Return original if not a headshot
    }
    
    // Apply the enhanced headshot prompt from the rule
    const headshotEnhancement = `Render a cinematic portrait or video-style headshot that emphasizes facial features with exceptional clarity and detail. Ensure lifelike, flattering skin tones—avoid over-smoothing, plasticity, or unnatural color casts. Apply professional-grade LUT color grading (cinematic or soft natural preferred) to enhance contrast, depth, and mood, while preserving authentic skin fidelity. Use balanced, studio-quality lighting with gentle, realistic shadows and a wide dynamic range to achieve a polished yet natural look. The result should feel vibrant, engaging, and true-to-life, suitable for high-end thumbnails or profile imagery.`;
    
    return `${basePersonPrompt} ${headshotEnhancement}`;
}

/**
 * Analyzes the prompt to determine color mood context
 * @param {string} prompt - The user's prompt
 * @returns {Object} - Color mood analysis with mood type and description
 */
const analyzeColorMoodFromPrompt = (prompt) => {
    const promptLower = prompt.toLowerCase();
    
    const moodPatterns = {
        energetic: {
            keywords: ['energy', 'dynamic', 'vibrant', 'exciting', 'action', 'power', 'intense', 'explosive'],
            colors: ['bright reds', 'electric blues', 'neon greens', 'vibrant oranges'],
            description: 'High-energy with vibrant, saturated colors and dynamic contrasts'
        },
        calming: {
            keywords: ['calm', 'peaceful', 'serene', 'relaxing', 'soft', 'gentle', 'soothing', 'meditation'],
            colors: ['soft blues', 'gentle greens', 'warm pastels', 'muted earth tones'],
            description: 'Peaceful and serene with soft, muted colors and gentle transitions'
        },
        professional: {
            keywords: ['business', 'corporate', 'professional', 'clean', 'minimal', 'elegant', 'sophisticated'],
            colors: ['deep blues', 'charcoal grays', 'gold accents', 'pristine whites'],
            description: 'Professional and sophisticated with refined color palettes'
        },
        dramatic: {
            keywords: ['dramatic', 'cinematic', 'dark', 'moody', 'intense', 'atmospheric', 'mysterious'],
            colors: ['deep purples', 'rich blacks', 'dramatic reds', 'golden highlights'],
            description: 'Dramatic and cinematic with strong contrasts and mood lighting'
        },
        warm: {
            keywords: ['warm', 'cozy', 'friendly', 'inviting', 'comfort', 'sunset', 'golden', 'amber'],
            colors: ['warm oranges', 'golden yellows', 'rich browns', 'soft reds'],
            description: 'Warm and inviting with golden tones and comfortable atmosphere'
        },
        cool: {
            keywords: ['cool', 'tech', 'modern', 'digital', 'futuristic', 'ice', 'crystal', 'steel'],
            colors: ['cool blues', 'icy cyans', 'silver grays', 'electric purples'],
            description: 'Cool and modern with technological aesthetic and crisp colors'
        }
    };
    
    // Find the best matching mood
    let bestMatch = { mood: 'balanced', score: 0, description: 'Balanced color palette suitable for general content' };
    
    for (const [moodType, moodData] of Object.entries(moodPatterns)) {
        const score = moodData.keywords.reduce((acc, keyword) => {
            return acc + (promptLower.includes(keyword) ? 1 : 0);
        }, 0);
        
        if (score > bestMatch.score) {
            bestMatch = {
                mood: moodType,
                score: score,
                description: moodData.description,
                colors: moodData.colors
            };
        }
    }
    
    return bestMatch;
};

/**
 * Determines the appropriate abstraction level for the background
 * @param {string} prompt - The user's prompt
 * @returns {Object} - Abstraction level analysis
 */
const determineAbstractionLevel = (prompt) => {
    const promptLower = prompt.toLowerCase();
    
    const abstractionLevels = {
        highly_abstract: {
            keywords: ['abstract', 'artistic', 'creative', 'experimental', 'avant-garde'],
            level: 'Highly Abstract',
            description: 'Pure abstract forms with emphasis on color, shape, and composition'
        },
        semi_abstract: {
            keywords: ['modern', 'stylized', 'geometric', 'pattern', 'design'],
            level: 'Semi-Abstract',
            description: 'Stylized elements with recognizable forms but artistic interpretation'
        },
        thematic: {
            keywords: ['tech', 'gaming', 'business', 'health', 'education', 'tutorial'],
            level: 'Thematic Abstract',
            description: 'Abstract representation of specific themes without literal elements'
        },
        minimal: {
            keywords: ['minimal', 'clean', 'simple', 'elegant', 'sophisticated'],
            level: 'Minimal Abstract',
            description: 'Clean, minimal composition with focus on essential elements'
        }
    };
    
    // Find the best matching abstraction level
    let bestMatch = { level: 'Balanced Abstract', description: 'Balanced abstraction suitable for general YouTube thumbnails' };
    let maxScore = 0;
    
    for (const [levelType, levelData] of Object.entries(abstractionLevels)) {
        const score = levelData.keywords.reduce((acc, keyword) => {
            return acc + (promptLower.includes(keyword) ? 1 : 0);
        }, 0);
        
        if (score > maxScore) {
            maxScore = score;
            bestMatch = {
                level: levelData.level,
                description: levelData.description
            };
        }
    }
    
    return bestMatch;
};

/**
 * Gets contextual color grading based on topic and mood analysis
 * @param {string} topicAnalysis - The detected video topic
 * @param {Object} colorMoodContext - The color mood analysis
 * @returns {Object} - Contextual color grading recommendation
 */
const getContextualColorGrading = (topicAnalysis, colorMoodContext) => {
    const contextualGradings = {
        [VIDEO_TOPICS.TECH]: {
            name: 'Tech-Enhanced Cool Grading',
            description: 'Cool blue-cyan grading with digital enhancement and crisp highlights',
            application: 'Apply cool temperature shift with enhanced blues and cyans, add digital glow effects'
        },
        [VIDEO_TOPICS.GAMING]: {
            name: 'Gaming RGB Enhancement',
            description: 'Vibrant RGB grading with neon accents and high saturation',
            application: 'Boost saturation levels, add RGB color separation, enhance neon glow effects'
        },
        [VIDEO_TOPICS.BUSINESS_FINANCE]: {
            name: 'Professional Cinematic Grading',
            description: 'Sophisticated orange-teal with corporate refinement',
            application: 'Apply subtle orange-teal LUT with enhanced shadows and professional color balance'
        },
        [VIDEO_TOPICS.HEALTH_NUTRITION]: {
            name: 'Natural Vitality Grading',
            description: 'Fresh, natural color enhancement with vibrant greens and warm highlights',
            application: 'Enhance natural colors, boost green and orange tones, add healthy glow effects'
        }
    };
    
    // Get base grading from topic
    let baseGrading = contextualGradings[topicAnalysis] || {
        name: 'Universal Balanced Grading',
        description: 'Balanced color enhancement suitable for general content',
        application: 'Apply gentle contrast enhancement with natural color balance'
    };
    
    // Modify based on mood context
    if (colorMoodContext.mood === 'dramatic') {
        baseGrading.name += ' with Dramatic Enhancement';
        baseGrading.application += ', increase contrast and shadow depth';
    } else if (colorMoodContext.mood === 'energetic') {
        baseGrading.name += ' with Energy Boost';
        baseGrading.application += ', enhance saturation and vibrant highlights';
    } else if (colorMoodContext.mood === 'calming') {
        baseGrading.name += ' with Soft Treatment';
        baseGrading.application += ', reduce contrast and add gentle warmth';
    }
    
    return baseGrading;
};

/**
 * Enhanced background generation with context awareness
 * @param {string} userPrompt - The user's prompt
 * @param {string} selectedBackgroundType - The selected background type
 * @param {Object} backgroundDetails - Background configuration details
 * @param {string} topicAnalysis - The detected video topic
 * @param {Object} colorMoodContext - The color mood analysis
 * @returns {string} - Enhanced background generation prompt
 */
const getEnhancedBackgroundGeneration = (userPrompt, selectedBackgroundType, backgroundDetails, topicAnalysis, colorMoodContext) => {
    let enhancedPrompt = '';
    
    if (selectedBackgroundType === 'solid' && backgroundDetails.selectedSolidBgColor) {
        enhancedPrompt += `=== ENHANCED SOLID COLOR BACKGROUND ===\n`;
        enhancedPrompt += `Base Color: ${backgroundDetails.selectedSolidBgColor}\n`;
        enhancedPrompt += `Enhancement Level: Premium solid color with sophisticated treatment\n\n`;
        
        enhancedPrompt += `SOLID COLOR ENHANCEMENTS:\n`;
        enhancedPrompt += `- Create a rich, textured interpretation of ${backgroundDetails.selectedSolidBgColor}\n`;
        enhancedPrompt += `- Add subtle gradient variations and depth through lighting\n`;
        enhancedPrompt += `- Implement soft radial gradients or vignetting for visual interest\n`;
        enhancedPrompt += `- Include barely perceptible texture overlays (fabric, paper, or digital noise)\n`;
        enhancedPrompt += `- Apply contextual mood enhancement based on ${colorMoodContext.mood} aesthetic\n`;
        enhancedPrompt += `- Ensure the color remains dominant while adding sophisticated depth\n\n`;
        
    } else if (selectedBackgroundType === 'template' && backgroundDetails.selectedBackgroundStyleId) {
        const styleId = backgroundDetails.selectedBackgroundStyleId;
        
        enhancedPrompt += `=== ENHANCED TEMPLATE BACKGROUND ===\n`;
        enhancedPrompt += `Template Style: ${styleId}\n`;
        enhancedPrompt += `Topic Context: ${topicAnalysis}\n`;
        enhancedPrompt += `Mood Enhancement: ${colorMoodContext.mood}\n\n`;
        
        // Get template description from smart mappings
        let templateDescription = `a pre-designed background template identified by '${styleId}'`;
        for (const category in smartBackgroundMappings) {
            if (smartBackgroundMappings[category][styleId]) {
                templateDescription = smartBackgroundMappings[category][styleId];
                break;
            }
        }
        
        enhancedPrompt += `TEMPLATE ENHANCEMENT:\n`;
        enhancedPrompt += `- Base Template: ${templateDescription}\n`;
        enhancedPrompt += `- Apply ${colorMoodContext.description.toLowerCase()} color treatment\n`;
        enhancedPrompt += `- Enhance with ${topicAnalysis.toLowerCase()}-appropriate visual elements\n`;
        enhancedPrompt += `- Increase detail level and visual sophistication\n`;
        enhancedPrompt += `- Add contextual lighting and atmospheric effects\n`;
        enhancedPrompt += `- Ensure template serves as complete, standalone thumbnail background\n\n`;
        
    } else {
        // Contextual background based on prompt analysis
        enhancedPrompt += `=== CONTEXTUAL BACKGROUND GENERATION ===\n`;
        enhancedPrompt += `User Intent: "${userPrompt}"\n`;
        enhancedPrompt += `Detected Topic: ${topicAnalysis}\n`;
        enhancedPrompt += `Color Mood: ${colorMoodContext.mood}\n\n`;
        
        enhancedPrompt += `CONTEXTUAL INTERPRETATION:\n`;
        enhancedPrompt += `- Analyze the core concept from: "${userPrompt}"\n`;
        enhancedPrompt += `- Create abstract visual representation without literal elements\n`;
        enhancedPrompt += `- Apply ${topicAnalysis.toLowerCase()}-specific visual language\n`;
        enhancedPrompt += `- Implement ${colorMoodContext.mood} color psychology\n`;
        enhancedPrompt += `- Ensure sophisticated abstraction that hints at the topic\n`;
        enhancedPrompt += `- Focus on emotional and visual impact over literal representation\n\n`;
    }
    
    return enhancedPrompt;
};
