import React, { useState, useRef, useEffect } from 'react'
import { authAPI, supabase } from '../utils/supabase.mjs'
import { enhancedAuth, rememberMeAuth } from '../utils/auth.js'
import { AuthThemeToggle } from '../components/ui/AuthThemeToggle.jsx'

/**
 * Welcome Screen Component - Real Authentication Login
 * Styled with Hero UI Kit (Tailwind CDN)
 * 
 * This component provides a login form for real user authentication with Supabase.
 * 
 * @param {Object} props
 * @param {Function} props.onAuthenticated - Callback for when login is completed
 * @param {Function} props.onNavigateToSignUp - Navigate to signup page
 * @param {Function} props.onNavigateToForgotPassword - Navigate to forgot password page
 * @param {boolean} props.isLightTheme - Current theme state
 * @param {Function} props.onToggleTheme - Theme toggle handler
 */
const Welcome = ({ onAuthenticated, onNavigateToSignUp, onNavigateToForgotPassword, isLightTheme, onToggleTheme }) => {
    // State for form inputs
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    });
    
    // State for Remember Me checkbox
    const [rememberMe, setRememberMe] = useState(false);
    
    // State for validation errors
    const [errors, setErrors] = useState({
        email: '',
        password: '',
        auth: ''
    });

    // Touched fields tracking for validation
    const [touched, setTouched] = useState({
        email: false,
        password: false
    });
    
    // Refs for form fields
    const inputRefs = {
        email: useRef(null),
        password: useRef(null)
    };
    
    // State for password visibility and loading
    const [showPassword, setShowPassword] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    
    // Auto-fill email if user has saved preferences
    useEffect(() => {
        const userPrefs = rememberMeAuth.getUserPreferences();
        if (userPrefs && userPrefs.email) {
            setFormData(prev => ({ ...prev, email: userPrefs.email }));
            setRememberMe(true); // Check Remember Me if we have saved preferences
        }
    }, []);

    // Validation functions
    const validateEmail = (emailInput) => {
        const email = emailInput ? emailInput.trim() : '';
        if (!email) return "Email is required";
        const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i;
        if (!emailRegex.test(email)) return "Invalid email address";
        return "";
    };
    
    const validatePassword = (password) => {
        if (!password) return "Password is required";
        return "";
    };
    
    const handleInputChange = (e) => {
        const { name, value } = e.target;
        
        setFormData(prev => ({ ...prev, [name]: value }));
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        
        setErrors(prev => ({ ...prev, [name]: errorMessage, auth: '' }));
    };
    
    const handleBlur = (e) => {
        const { name, value } = e.target;
        if (!touched[name]) setTouched(prev => ({ ...prev, [name]: true }));
        let errorMessage = "";
        if (name === 'email') errorMessage = validateEmail(value);
        else if (name === 'password') errorMessage = validatePassword(value);
        setErrors(prev => ({ ...prev, [name]: errorMessage }));
    };
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        const fieldsToTouch = { email: true, password: true };
        setTouched(prev => ({ ...prev, ...fieldsToTouch }));

        const currentErrors = {
            email: validateEmail(formData.email),
            password: validatePassword(formData.password)
        };
        setErrors(prev => ({ ...prev, ...currentErrors, auth: '' }));

        if (currentErrors.email || currentErrors.password) {
            if (currentErrors.email) inputRefs.email.current?.focus();
            else if (currentErrors.password) inputRefs.password.current?.focus();
            return;
        }
        
        setIsSubmitting(true);
        
        try {
            // Authenticate with enhanced auth system that supports Remember Me
            const result = await enhancedAuth.signIn(supabase, formData.email, formData.password, rememberMe);
            
            if (result.success && result.user) {
                console.log(`✅ Login successful${rememberMe ? ' with Remember Me enabled' : ''}`);
                onAuthenticated(result.user);
            } else {
                setErrors(prev => ({
                    ...prev,
                    auth: result.error || 'Invalid email or password. Please check your credentials and try again.'
                }));
            }
        } catch (error) {
            console.error('Authentication error:', error);
            setErrors(prev => ({
                ...prev,
                auth: 'An unexpected error occurred. Please try again.'
            }));
            setIsSubmitting(false);
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleGoogleSignIn = async () => {
        setIsSubmitting(true);
        setErrors(prev => ({ ...prev, auth: '' }));
        
        try {
            console.log('🔄 Starting Google sign-in...');
            const result = await authAPI.signInWithGoogle();
            
            if (result.success) {
                console.log('✅ Google sign-in initiated successfully');
                // The OAuth flow will redirect the user to Google and back
                // Authentication completion will be handled by the auth state listener
            } else {
                console.error('❌ Google sign-in failed:', result.error);
                setErrors(prev => ({
                    ...prev,
                    auth: result.error || 'Failed to sign in with Google. Please try again.'
                }));
                setIsSubmitting(false);
            }
        } catch (error) {
            console.error('Google sign-in error:', error);
            setErrors(prev => ({
                ...prev,
                auth: 'An unexpected error occurred with Google sign-in. Please try again.'
            }));
            setIsSubmitting(false);
        }
    };

    return (
        <div className="welcome-page-container flex min-h-screen bg-gray-900 text-white" id="welcome-page-container">
            {/* Theme Toggle Button */}
            <AuthThemeToggle isLightTheme={isLightTheme} onToggle={onToggleTheme} />
            
            {/* Left Panel - Branding */}
            <div className="welcome-branding-panel hidden lg:flex lg:w-1/2 bg-gradient-to-br from-purple-900 via-indigo-800 to-blue-900 justify-center items-center p-12" id="welcome-branding-panel">
                <div className="branding-content max-w-md flex flex-col items-start" id="welcome-branding-content">
                    <img 
                        src="/assets/main-logo.svg" 
                        alt="Thumbspark Logo" 
                        className="mb-7" 
                        style={{ width: "80%", height: "auto" }} 
                        draggable="false" 
                    />
                    <h1 className="text-4xl font-bold mb-6 text-left">
                        Thumbspark Thumbnail Generator
                    </h1>
                    <p className="text-xl text-gray-300 mb-8 text-left">
                        Create stunning YouTube thumbnails with AI.
                    </p>
                    <div className="flex gap-4">
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:image-add-bold"></span>
                        </div>
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-400 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:magic-stick-3-bold"></span>
                        </div>
                        <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-amber-500 to-orange-400 flex items-center justify-center">
                            <span className="iconify text-3xl" data-icon="solar:star-bold"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Right Panel - Login Form */}
            <div className="welcome-form-panel w-full lg:w-1/2 relative min-h-screen" id="welcome-form-panel">
                {/* Content container */}
                <div className="form-content-container flex flex-col justify-center items-center p-8 md:p-16 min-h-screen" id="welcome-form-content-container">
                    <div className="form-wrapper w-full max-w-md" id="welcome-form-wrapper">
                        {/* Mobile Logo */}
                        <div className="mobile-logo-section lg:hidden text-center mb-8" id="welcome-mobile-logo-section">
                            <img 
                                src="/assets/main-logo.svg" 
                                alt="Thumbspark Logo" 
                                className="mx-auto mb-4" 
                                style={{ width: "60%", height: "auto" }} 
                                draggable="false" 
                            />
                        </div>

                        <div className="welcome-header-section text-center mb-8" id="welcome-header-section">
                            <h2 className="welcome-title text-4xl font-bold mb-2">Log in</h2>
                            <p className="welcome-subtitle text-gray-400">Welcome back</p>
                        </div>

                        {/* Error Message */}
                        {errors.auth && (
                            <div className="auth-error-container bg-red-500/10 border border-red-500/20 text-red-400 p-3 rounded-lg mb-6 text-sm flex items-start gap-2" id="welcome-auth-error-container">
                                <span className="iconify flex-shrink-0 mt-0.5" data-icon="solar:danger-circle-bold"></span>
                                <span className="auth-error-text">{errors.auth}</span>
                            </div>
                        )}

                        {/* Login Form */}
                        <form onSubmit={handleSubmit} className="welcome-login-form space-y-4" id="welcome-login-form">
                            {/* Google Sign In Button */}
                            <div className="google-auth-section mb-4" id="google-auth-section">
                                <button
                                    type="button"
                                    onClick={handleGoogleSignIn}
                                    disabled={isSubmitting}
                                    className="auth-google-btn"
                                    id="google-signin-btn"
                                >
                                    <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none">
                                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="white"/>
                                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="white"/>
                                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="white"/>
                                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="white"/>
                                    </svg>
                                    Continue with Google
                                </button>
                                
                                {/* Divider */}
                                <div className="auth-divider flex items-center my-4" id="auth-divider">
                                    <div className="flex-1 border-t border-gray-600"></div>
                                    <div className="px-3 text-gray-400 text-sm">or</div>
                                    <div className="flex-1 border-t border-gray-600"></div>
                                </div>
                            </div>

                            {/* Email Field */}
                            <div className="email-field-container mb-4" id="email-field-container">
                                <label htmlFor="email" className="email-field-label block text-sm font-medium text-gray-300 mb-1">
                                    Email Address
                                </label>
                                <div className="email-input-wrapper relative" id="email-input-wrapper">
                                    <div className="email-icon-container absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none z-[100]">
                                        <span className="iconify text-gray-400" data-icon="solar:letter-linear" style={{fontSize:'18px'}}></span>
                                    </div>
                                    <input 
                                        type="email" 
                                        id="welcome-email-input" 
                                        name="email" 
                                        value={formData.email} 
                                        onChange={handleInputChange} 
                                        onBlur={handleBlur} 
                                        ref={inputRefs.email} 
                                        required
                                        className={`
                                            welcome-email-field w-full pl-10 pr-4 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400
                                            focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors
                                            ${errors.email && touched.email ? 'border-red-500' : 'border-gray-600'}
                                        `}
                                        placeholder="Enter your email address"
                                    />
                                </div>
                                {errors.email && touched.email && (
                                    <p className="email-error-message mt-1 text-sm text-red-400 flex items-center gap-1" id="email-error-message">
                                        <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                        {errors.email}
                                    </p>
                                )}
                            </div>

                            {/* Password Field */}
                            <div className="password-field-container mb-4" id="password-field-container">
                                <label htmlFor="password" className="password-field-label block text-sm font-medium text-gray-300 mb-1">
                                    Password
                                </label>
                                <div className="password-input-container relative" id="welcome-password-input-wrapper">
                                    <div className="password-icon-container absolute left-3 top-1/2 -translate-y-1/2 flex items-center pointer-events-none z-[100]">
                                        <span className="iconify text-gray-400" data-icon="solar:lock-keyhole-linear" style={{fontSize:'18px'}}></span>
                                    </div>
                                    <input 
                                        type={showPassword ? "text" : "password"} 
                                        id="welcome-password-input" 
                                        name="password" 
                                        value={formData.password} 
                                        onChange={handleInputChange} 
                                        onBlur={handleBlur} 
                                        ref={inputRefs.password} 
                                        required
                                        className={`
                                            welcome-password-field w-full pl-10 pr-12 py-3 bg-gray-800 border rounded-lg text-white placeholder-gray-400
                                            focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors
                                            ${errors.password && touched.password ? 'border-red-500' : 'border-gray-600'}
                                        `}
                                        placeholder="Enter your password"
                                    />
                                    <button 
                                        type="button" 
                                        onClick={() => setShowPassword(!showPassword)} 
                                        className="password-toggle-btn welcome-password-toggle"
                                        id="welcome-password-toggle-btn"
                                        aria-label={showPassword ? 'Hide password' : 'Show password'}
                                    >
                                        <span className="iconify" data-icon={showPassword ? "solar:eye-bold" : "solar:eye-closed-bold"}></span>
                                    </button>
                                </div>
                                {errors.password && touched.password && (
                                    <p className="password-error-message mt-1 text-sm text-red-400 flex items-center gap-1" id="password-error-message">
                                        <span className="iconify" data-icon="solar:danger-triangle-linear"></span>
                                        {errors.password}
                                    </p>
                                )}
                            </div>

                            {/* Remember Me Checkbox */}
                            <div className="remember-forgot-section flex items-center justify-between mb-5" id="remember-forgot-section">
                                <div className="remember-me-container flex items-center" id="remember-me-container">
                                    <input
                                        id="welcome-remember-me-checkbox"
                                        name="remember-me"
                                        type="checkbox"
                                        checked={rememberMe}
                                        onChange={(e) => setRememberMe(e.target.checked)}
                                        className="welcome-remember-checkbox h-4 w-4 text-purple-600 bg-gray-800 border-gray-600 rounded focus:ring-purple-500 focus:ring-2"
                                    />
                                    <label htmlFor="welcome-remember-me-checkbox" className="remember-me-label ml-[0.125rem] md:ml-2 block text-sm text-gray-300 cursor-pointer">
                                        Remember me
                                    </label>
                                </div>
                                <div className="forgot-password-container text-sm" id="forgot-password-container">
                                    <button 
                                        type="button"
                                        onClick={onNavigateToForgotPassword}
                                        className="welcome-forgot-password-btn text-blue-400 hover:text-blue-300 font-medium bg-transparent border-none cursor-pointer"
                                        id="welcome-forgot-password-btn"
                                    >
                                        Forgot password?
                                    </button>
                                </div>
                            </div>

                            {/* Submit Button */}
                            <button
                                type="submit"
                                disabled={isSubmitting || (errors.email && touched.email) || (errors.password && touched.password)}
                                className="auth-cta-btn"
                                id="welcome-signin-submit-btn"
                            >
                                {isSubmitting ? (
                                    <>
                                        <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span className="auth-loading-text">Signing In...</span>
                                    </>
                                ) : (
                                    <span className="auth-cta-text">Sign In</span>
                                )}
                            </button>
                        </form>

                        {/* Additional Links */}
                        <div className="signup-link-section mt-6 text-center" id="signup-link-section">
                            <p className="signup-prompt-text text-gray-400 text-sm">
                                Don't have an account?{' '}
                                <button 
                                    onClick={onNavigateToSignUp}
                                    className="welcome-signup-link-btn text-blue-400 hover:text-blue-200 font-medium bg-transparent border-none cursor-pointer transition-colors duration-300 ease-in-out px-2 py-1 rounded-md"
                                    id="welcome-signup-link-btn"
                                >
                                    Sign up here
                                </button>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export { Welcome };