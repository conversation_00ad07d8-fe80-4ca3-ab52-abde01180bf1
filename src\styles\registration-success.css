/* Registration Success Screen - MacOS Liquid Glass Effect */

/* Main container with liquid glass background */
.registration-success-container {
    min-height: 100vh;
    background: 
        radial-gradient(ellipse at top left, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(16, 185, 129, 0.08) 0%, transparent 70%),
        #0f172a;
    backdrop-filter: blur(40px);
    position: relative;
    overflow: hidden;
}

/* Animated background particles */
.registration-success-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(1deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

/* Main glass card */
.registration-success-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.registration-success-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Success icon container with glow */
.registration-success-icon-container {
    width: 96px;
    height: 96px;
    background: 
        radial-gradient(circle at center, rgba(16, 185, 129, 0.3) 0%, rgba(16, 185, 129, 0.1) 50%, transparent 100%),
        rgba(16, 185, 129, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 50%;
    position: relative;
    animation: pulse-glow 2s ease-in-out infinite;
}

.registration-success-icon-container::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(16, 185, 129, 0.4), rgba(5, 150, 105, 0.4));
    z-index: -1;
    filter: blur(8px);
    animation: rotate-glow 4s linear infinite;
}

@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 
            0 0 20px rgba(16, 185, 129, 0.4),
            0 0 40px rgba(16, 185, 129, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% { 
        box-shadow: 
            0 0 30px rgba(16, 185, 129, 0.6),
            0 0 60px rgba(16, 185, 129, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
}

@keyframes rotate-glow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Typography styles */
.registration-success-title {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.registration-success-subtitle {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.125rem;
    font-weight: 400;
    color: #94a3b8;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.registration-success-email {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1.5rem;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

/* CTA Button with liquid glass effect */
.registration-success-cta-btn {
    width: 100%;
    padding: 1rem 2rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 
        0 10px 25px -5px rgba(139, 92, 246, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.registration-success-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.registration-success-cta-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 15px 35px -5px rgba(139, 92, 246, 0.4),
        0 5px 15px -3px rgba(139, 92, 246, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(139, 92, 246, 0.5);
}

.registration-success-cta-btn:hover::before {
    left: 100%;
}

.registration-success-cta-btn:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

/* Support link styling */
.registration-success-support-link {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    color: #94a3b8;
    text-decoration: none;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 0;
}

.registration-success-support-link::after {
    content: '';
    position: absolute;
    bottom: 0.25rem;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
    transition: width 0.3s ease;
}

.registration-success-support-link:hover {
    color: #e2e8f0;
}

.registration-success-support-link:hover::after {
    width: 100%;
}

/* Responsive design */
@media (max-width: 768px) {
    .registration-success-card {
        margin: 1rem;
        border-radius: 20px;
    }
    
    .registration-success-title {
        font-size: 1.875rem;
    }
    
    .registration-success-icon-container {
        width: 80px;
        height: 80px;
    }
    
    .registration-success-cta-btn {
        font-size: 1rem;
        padding: 0.875rem 1.5rem;
    }
}

/* Mobile portrait specific enhancements */
@media (max-width: 576px) and (orientation: portrait) {
    .registration-success-cta-btn {
        font-size: 1.09375rem; /* 25% larger as per auth button standards */
        padding: 1rem 1.75rem;
        letter-spacing: 0.025em;
    }
} 