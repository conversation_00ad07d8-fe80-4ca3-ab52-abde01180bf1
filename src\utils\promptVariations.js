// Enhanced Cinematic Prompt Variations Generator
// Uses OpenAI Chat Completions API for intelligent, context-aware variations

import { OPENAI_API_KEY } from '../../config.js';

// Cinematic keyword categories for professional enhancement
const CINEMATIC_KEYWORDS = {
    lighting: [
        'cinematic lighting', 'dramatic lighting', 'soft ambient lighting', 
        'volumetric lighting', 'golden hour lighting', 'moody lighting',
        'studio lighting', 'natural lighting', 'rim lighting', 'hard lighting',
        'chiaroscuro', 'backlighting', 'side lighting', 'key lighting'
    ],
    composition: [
        'dynamic composition', 'rule of thirds', 'centered composition',
        'asymmetric layout', 'leading lines', 'depth of field',
        'balanced framing', 'visual hierarchy', 'wide shot', 'close-up',
        'medium shot', 'bird\'s eye view', 'worm\'s eye view', 'dutch angle'
    ],
    color: [
        'rich color grading', 'vibrant color palette', 'muted tones',
        'high contrast', 'color harmony', 'saturated colors',
        'warm color scheme', 'cool color tones', 'monochromatic',
        'complementary colors', 'split-tone', 'desaturated palette'
    ],
    texture: [
        'soft mesh background', 'gradient mesh', 'smooth gradients',
        'organic textures', 'subtle patterns', 'clean surfaces',
        'textured overlay', 'layered elements', 'glossy finish',
        'matte textures', 'grain overlay', 'bokeh effect'
    ],
    mood: [
        'epic atmosphere', 'intense mood', 'serene ambiance',
        'energetic vibe', 'mysterious atmosphere', 'professional tone',
        'engaging presence', 'captivating scene', 'dynamic energy',
        'dramatic tension', 'uplifting mood', 'suspenseful atmosphere'
    ],
    quality: [
        'ultra-detailed', 'high-resolution', 'photorealistic quality',
        'polished finish', 'premium quality', 'sharp details',
        'crisp imagery', 'professional grade', 'studio quality',
        'hyper-realistic', 'highly detailed', 'crystal clear'
    ]
};

// AI-powered prompt enhancement using OpenAI Chat Completions
const generateAIVariations = async (originalPrompt) => {
    try {
        const systemPrompt = `You are a contextual prompt variations expert specialized in creating engaging, medium-length video topic prompts for YouTube thumbnails. Generate 3-5 unique, creative prompt variations that are contextually rich and focused.

=== CONTEXTUAL MEDIUM-LENGTH PROMPT VARIATIONS GENERATOR ===

CORE REQUIREMENTS:
- Each variation should be **medium length** (2–3 sentences, 25–45 words)
- Avoid being a single short sentence or a long paragraph
- Focus on a **different angle, benefit, or subtopic** for each variation
- Use **engaging, natural language** - avoid generic or repetitive phrasing
- Be contextually relevant to the original topic
- Never simply rephrase the original; always add a new perspective or detail
- Use a **clear, inviting call to action or hook**
- Avoid clickbait or misleading claims

VARIATION FOCUS STRATEGIES:
1. **Strategy/Technique Focus**: Emphasize methods, skills, or approaches
2. **Comparison/Analysis**: Highlight differences, matchups, or evaluations  
3. **Community/Social**: Focus on audience, culture, or social aspects
4. **Performance/Results**: Emphasize outcomes, achievements, or metrics
5. **Learning/Educational**: Focus on tips, guides, or knowledge transfer

CONTEXTUAL ADAPTATION:
- **Gaming**: Mention mechanics, skills, strategies, community, highlights, competition
- **Business**: Focus on growth, strategy, innovation, results, success metrics
- **Education**: Emphasize learning, skills, mastery, tips, step-by-step guidance
- **Tech**: Highlight features, comparisons, performance, innovation, user experience
- **Entertainment**: Focus on highlights, reactions, best moments, compilations

EXAMPLES:

Original: "Dota 2 vs League of Legends: Ultimate Showdown"
Variations:
1. "Dota 2 vs League of Legends: The Ultimate Showdown! Compare the Hero Rosters, Map Mechanics, and Team Strategies. Which MOBA Reigns Supreme?"
2. "Dota 2 vs League of Legends: Epic Battle of the Titans! Dive into the Differences in Game Modes, Item Builds, and Community Culture. Who Will Claim Victory?"
3. "Dota 2 vs League of Legends: The Ultimate Showdown! Analyze the Lane Phases, Jungle Routes, and Late-Game Power Plays. Discover Which Game Suits Your Playstyle Best!"

Original: "Step-by-step guide to set record-breaking time trial runs"
Variations:
1. "Step-by-Step Guide to Setting Record-Breaking Time Trial Runs! Master the Art of Perfect Lines, Drift Techniques, and Precision Pad Usage. Your Ultimate Racing Strategy!"
2. "Unlock Record-Breaking Time Trial Runs: Step-by-Step Tips for Optimal Lines, Drifts, and Pad Mastery. From Novice to Pro, Improve Your Racing Skills Today!"
3. "Step-by-Step Guide to Crushing Time Trials! Learn the Secrets of Efficient Lines, Controlled Drifts, and Effective Pad Usage. Set New Personal Bests Every Time!"

FORMAT: Generate exactly 3-5 numbered variations with "Variation 1:", "Variation 2:", etc.`;

        const userContent = `Original prompt: "${originalPrompt}"

Generate enhanced cinematic variations following the requirements above.`;

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify({
                model: 'gpt-4o-mini', // Using cost-effective model for text generation
                messages: [
                    { role: 'system', content: systemPrompt },
                    { role: 'user', content: userContent }
                ],
                max_tokens: 1000,
                temperature: 0.8, // Creative but consistent
                top_p: 0.9
            })
        });

        if (!response.ok) {
            console.error('OpenAI API error:', response.status, response.statusText);
            return null;
        }

        const data = await response.json();
        const aiResponse = data.choices[0]?.message?.content;
        
        if (!aiResponse) {
            console.error('No response content from OpenAI');
            return null;
        }

        // Parse numbered variations from AI response
        const variations = [];
        const lines = aiResponse.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            // Match patterns like "Variation 1:", "1. ", "2. ", etc.
            const variationMatch = trimmed.match(/^Variation\s+\d+:\s*(.+)$/i);
            const numberMatch = trimmed.match(/^\d+\.\s*(.+)$/);
            
            let variation = null;
            if (variationMatch && variationMatch[1]) {
                variation = variationMatch[1].trim();
            } else if (numberMatch && numberMatch[1]) {
                variation = numberMatch[1].trim();
            }
            
            if (variation && variation.length >= 20 && variation !== originalPrompt) {
                // Remove quotes if present
                variation = variation.replace(/^["']|["']$/g, '');
                variations.push(variation);
            }
        }

        return variations.slice(0, 5); // Maximum 5 variations

    } catch (error) {
        console.error('Error generating AI variations:', error);
        return null;
    }
};

// Contextual medium-length strategy templates for when AI generation fails
const FALLBACK_STRATEGIES = [
    {
        name: 'strategy_focus',
        enhance: (prompt) => {
            const strategies = ['Master the Techniques', 'Learn the Strategies', 'Discover the Methods', 'Unlock the Secrets'];
            const actions = ['Improve Your Skills', 'Level Up Your Game', 'Take Your Performance to the Next Level', 'Achieve Better Results'];
            const strategy = strategies[Math.floor(Math.random() * strategies.length)];
            const action = actions[Math.floor(Math.random() * actions.length)];
            return `${prompt}: ${strategy} and Essential Tips You Need to Know. ${action} with This Complete Guide!`;
        }
    },
    {
        name: 'comparison_analysis',
        enhance: (prompt) => {
            const comparisons = ['Ultimate Comparison', 'Head-to-Head Analysis', 'Complete Breakdown', 'Detailed Showdown'];
            const outcomes = ['Find Out Which is Best', 'Discover the Winner', 'See the Results', 'Make the Right Choice'];
            const comparison = comparisons[Math.floor(Math.random() * comparisons.length)];
            const outcome = outcomes[Math.floor(Math.random() * outcomes.length)];
            return `${prompt}: The ${comparison}! Explore the Key Differences, Performance, and Features. ${outcome} for Your Needs!`;
        }
    },
    {
        name: 'results_performance',
        enhance: (prompt) => {
            const results = ['Amazing Results', 'Incredible Performance', 'Outstanding Achievements', 'Impressive Outcomes'];
            const calls = ['See What\'s Possible', 'Witness the Power', 'Experience the Difference', 'Get Inspired'];
            const result = results[Math.floor(Math.random() * results.length)];
            const call = calls[Math.floor(Math.random() * calls.length)];
            return `${prompt}: ${result} You Won't Believe! Top Performance, Best Practices, and Pro Tips. ${call} and Start Today!`;
        }
    },
    {
        name: 'learning_educational',
        enhance: (prompt) => {
            const learning = ['Step-by-Step Guide', 'Complete Tutorial', 'Essential Training', 'Pro Masterclass'];
            const benefits = ['From Beginner to Expert', 'Master the Fundamentals', 'Build Your Skills', 'Get Professional Results'];
            const guide = learning[Math.floor(Math.random() * learning.length)];
            const benefit = benefits[Math.floor(Math.random() * benefits.length)];
            return `${prompt}: ${guide} for Success! Everything You Need to Know and More. ${benefit} with This Comprehensive Training!`;
        }
    },
    {
        name: 'highlights_showcase',
        enhance: (prompt) => {
            const showcases = ['Best Moments', 'Top Highlights', 'Greatest Hits', 'Epic Compilation'];
            const experiences = ['Get Ready to Be Amazed', 'Prepare for Something Special', 'Experience the Excellence', 'Witness the Magic'];
            const showcase = showcases[Math.floor(Math.random() * showcases.length)];
            const experience = experiences[Math.floor(Math.random() * experiences.length)];
            return `${prompt}: ${showcase} and Incredible Scenes! The Most Impressive Examples and Standout Performances. ${experience} Right Now!`;
        }
    }
];

// Helper function to get random keyword from category
function getRandomKeyword(category) {
    const keywords = CINEMATIC_KEYWORDS[category];
    return keywords[Math.floor(Math.random() * keywords.length)];
}

// Generate fallback variations using local strategies
const generateFallbackVariations = (originalPrompt) => {
    const prompt = originalPrompt.trim();
    const variations = [];
    const usedStrategies = new Set();
    
    // Generate 3-5 variations using different strategies
    const numVariations = Math.floor(Math.random() * 3) + 3; // 3, 4, or 5
    
    for (let i = 0; i < numVariations && variations.length < 5; i++) {
        let strategy;
        let attempts = 0;
        
        do {
            strategy = FALLBACK_STRATEGIES[Math.floor(Math.random() * FALLBACK_STRATEGIES.length)];
            attempts++;
        } while (usedStrategies.has(strategy.name) && attempts < 10);
        
        if (attempts < 10) {
            usedStrategies.add(strategy.name);
        }
        
        try {
            let variation = strategy.enhance(prompt);
            variation = cleanVariation(variation);
            
            if (variation !== prompt && 
                !variations.includes(variation) &&
                variation.length >= 20) {
                // Check if it's medium-length (25-45 words is ideal)
                const wordCount = variation.split(/\s+/).length;
                if (wordCount >= 15 && wordCount <= 60) { // Allow some flexibility
                    variations.push(variation);
                }
            }
        } catch (error) {
            console.error('Error generating fallback variation:', error);
        }
    }

    // Add guaranteed fallbacks if needed (medium-length, contextual)
    while (variations.length < 3) {
        const fallbackEnhancements = [
            `${prompt}: Ultimate Guide with Expert Tips and Proven Strategies! Everything You Need to Know for Success. Get Started Today!`,
            `${prompt}: Complete Analysis and In-Depth Breakdown! Discover the Key Insights, Best Practices, and Pro Techniques. Transform Your Approach!`,
            `${prompt}: Essential Training for Amazing Results! Master the Fundamentals, Learn from the Best, and Achieve Your Goals. Start Your Journey!`
        ];
        
        const fallback = cleanVariation(fallbackEnhancements[variations.length]);
        if (!variations.includes(fallback)) {
            variations.push(fallback);
        } else {
            break;
        }
    }

    return variations.slice(0, 5);
};

// Clean and optimize the variation text
function cleanVariation(text) {
    return text
        .replace(/\s+/g, ' ') // Remove multiple spaces
        .replace(/,\s*,/g, ',') // Remove double commas
        .replace(/\s*,\s*/g, ', ') // Normalize comma spacing
        .replace(/\s*\.\s*/g, '. ') // Normalize period spacing
        .replace(/["'"]/g, '') // Remove quotes
        .trim()
        .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
}

// Main function to generate enhanced prompt variations
export const generatePromptVariations = async (originalPrompt) => {
    if (!originalPrompt || originalPrompt.trim().length < 5) {
        return [];
    }

    const prompt = originalPrompt.trim();
    
    // Check word count - only show for prompts with 5+ words
    const wordCount = prompt.split(/\s+/).length;
    if (wordCount < 5) {
        return [];
    }

    // Track start time for minimum loading duration
    const startTime = Date.now();
    const minLoadingTime = 1500; // Minimum 1.5 seconds for better UX

    try {
        // First try AI-powered generation
        const aiVariations = await generateAIVariations(prompt);
        
        // Ensure minimum loading time has passed
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime < minLoadingTime) {
            await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
        }
        
        if (aiVariations && aiVariations.length >= 3) {
            console.log('Generated AI variations:', aiVariations);
            return aiVariations;
        }
        
        // Fallback to local generation if AI fails
        console.log('Falling back to local variation generation');
        return generateFallbackVariations(prompt);
        
    } catch (error) {
        console.error('Error in generatePromptVariations:', error);
        
        // Ensure minimum loading time even on error
        const elapsedTime = Date.now() - startTime;
        if (elapsedTime < minLoadingTime) {
            await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
        }
        
        return generateFallbackVariations(prompt);
    }
};

// Backward compatibility function
export const generateThumbnailPromptVariations = async (originalPrompt) => {
    return await generatePromptVariations(originalPrompt);
};

// Export for testing and debugging
export const getCinematicKeywords = () => CINEMATIC_KEYWORDS;
export const getFallbackStrategies = () => FALLBACK_STRATEGIES; 