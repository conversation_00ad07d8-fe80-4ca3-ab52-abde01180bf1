// Named export for ThumbnailPreview component
export const ThumbnailPreview = ({ imageURL, isLoading, overlayText, selectedFontFamily, selectedTextSize, primaryTextColor, secondaryTextColor, onOpenFullPreview, progress = 0 }) => {

    const isFinalizing = isLoading && progress >= 99 && !imageURL;

    // Placeholder content when no image is loaded or during loading
    const placeholder = React.createElement('div', {
        className: 'w-full h-full flex flex-col items-center justify-center bg-gray-700 border-2 border-dashed border-gray-500 rounded-lg text-gray-400',
        style: { aspectRatio: '16/9', minHeight: '180px' }
        className: 'w-full h-full flex flex-col items-center justify-center bg-gray-700 border-2 border-dashed border-gray-500 rounded-lg text-gray-400',
        style: { aspectRatio: '16/9', minHeight: '180px' }
    },
        isLoading ? React.createElement('div', {
                className: 'loading-indicator-container flex flex-col items-center justify-center gap-4'
            },
                // Premium percentage display - responsive desktop enhancement (25% larger at 768px+)
                React.createElement('div', {
                    className: `loading-percentage font-black text-gray-100${isFinalizing ? ' finalizing-shimmer' : ''}`,
                    style: {
                        fontFamily: 'SF Mono, Monaco, Cascadia Code, Roboto Mono, Consolas, monospace',
                        textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
                        letterSpacing: '0.5px',
                        transition: 'all 0.3s ease',
                    }
                }, isFinalizing ? '99%' : `${Math.round(progress)}%`),
                // Animated "Generating Thumbnail..." or Finalizing message
                React.createElement('div', {
                    className: 'loading-text-container'
                },
                    isFinalizing ?
                        React.createElement('span', {
                            className: 'finalizing-text text-lg font-medium text-gray-300'
                        }, 'Finalizing') :
                        React.createElement('span', {
                            className: 'cursor-thinking-text text-lg font-medium',
                            style: {
                                color: 'transparent',
                                background: 'linear-gradient(90deg, rgba(181, 181, 181, 0.85) 20%, #FFFFFF 50%, rgba(181, 181, 181, 0.85) 80%)',
                                backgroundSize: '200% 100%',
                                WebkitBackgroundClip: 'text',
                                backgroundClip: 'text',
                                animation: 'cursorThinking 2.42s linear infinite'
                            }
                        }, 'Generating Thumbnail...')
                )
            ) :
            React.createElement('div', {
                className: 'thumbnail-preview-empty-state-container',
                id: 'thumbnail-preview-empty-state-main-wrapper'
            },
                React.createElement('img', {
                    src: '/assets/empty-states/Album.svg',
                    alt: 'Album',
                    style: {
                        width: '144px',
                        height: '113px',
                        objectFit: 'contain',
                        display: 'block',
                        margin: '0 auto'
                    },
                    className: 'thumbnail-preview-empty-state-icon'
                }),
                React.createElement('p', {
                    className: 'cursor-thinking-text text-lg',
                    style: {
                        fontWeight: '500',
                        fontSize: '1rem',
                        color: 'transparent',
                        background: 'linear-gradient(90deg, rgba(181, 181, 181, 0.85) 20%, #FFFFFF 50%, rgba(181, 181, 181, 0.85) 80%)',
                        backgroundSize: '200% 100%',
                        WebkitBackgroundClip: 'text',
                        backgroundClip: 'text',
                        animation: 'cursorThinking 2.42s linear infinite'
                    }
                }, 'Create some Magic')
            )
    );

    // Image element if URL exists
    const imageElement = imageURL ? React.createElement('img', {
        src: imageURL,
        alt: 'Generated YouTube Thumbnail',
        style: {
            width: '100%',
            height: '100%',
            aspectRatio: '16/9',
            display: 'block',
            borderRadius: '8px'
            // Remove inline objectFit to let CSS handle it responsively
        },
        className: 'generated-thumbnail'
    }) : null;

    // Container wrapper for hover effects
    const thumbnailContainer = React.createElement('div', {
        className: `thumbnail-area relative overflow-hidden preview-container ${imageURL ? 'has-image' : ''}`,
        style: { aspectRatio: '16/9', width: '100%', borderRadius: '8px', minHeight: '180px' }
    },
        imageURL ? imageElement : placeholder,
        
        // Full container gradient overlay - appears on hover when image is present
        imageURL && React.createElement('div', {
            className: 'preview-container-gradient',
            'aria-hidden': 'true'
        }),
        
        // Enhanced maximize button with UX improvements
        imageURL && !isLoading && onOpenFullPreview && React.createElement('div', {
            className: 'thumbnail-maximize-container',
        },
            // Gradient feather overlay (top-to-bottom dark fade)
            React.createElement('div', {
                className: 'gradient-feather',
                'aria-hidden': 'true'
            }),
            
            // Maximize button with enhanced opacity behavior
            React.createElement('button', {
                className: 'thumbnail-maximize-btn',
        onClick: onOpenFullPreview,
        'aria-label': 'Open full-size preview',
        tabIndex: 0,
                title: 'View full size'
    },
        React.createElement('span', {
                    className: 'iconify',
            'data-icon': 'solar:maximize-square-minimalistic-linear',
                    style: { fontSize: '20px', color: '#fff' }
        })
                         )
         ),
            
            overlayText && overlayText.trim() !== '' && React.createElement('div', {
                className: 'absolute inset-0 flex flex-col items-center justify-center pointer-events-none',
                style: { zIndex: 10 }
            },
            overlayText.toUpperCase().split('\n').map((line, idx) => {
                // Generate enhanced gradient with darker accent
                const generateDarkerAccentShade = (primaryColor) => {
                    if (primaryColor.toUpperCase() === '#000000') return '#1A1A1A';
                    if (primaryColor.toUpperCase() === '#FFFFFF') return '#E5E5E5';
                    
                    // Simple HSL darkening for other colors
                    const hex = primaryColor.replace('#', '');
                    const r = parseInt(hex.substr(0, 2), 16) / 255;
                    const g = parseInt(hex.substr(2, 2), 16) / 255;
                    const b = parseInt(hex.substr(4, 2), 16) / 255;
                    const max = Math.max(r, g, b);
                    const min = Math.min(r, g, b);
                    let h, s, l = (max + min) / 2;
                    
                    if (max === min) {
                        h = s = 0;
                    } else {
                        const d = max - min;
                        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                        switch (max) {
                            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                            case g: h = (b - r) / d + 2; break;
                            case b: h = (r - g) / d + 4; break;
                        }
                        h /= 6;
                    }
                    
                    // Darken by 25%
                    const darkerL = Math.max(0, l - 0.25);
                    const enhancedS = Math.min(1, s + 0.05);
                    
                    // Convert back to hex
                    const hue2rgb = (p, q, t) => {
                        if (t < 0) t += 1;
                        if (t > 1) t -= 1;
                        if (t < 1/6) return p + (q - p) * 6 * t;
                        if (t < 1/2) return q;
                        if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                        return p;
                    };
                    
                    let rNew, gNew, bNew;
                    if (enhancedS === 0) {
                        rNew = gNew = bNew = darkerL;
                    } else {
                        const q = darkerL < 0.5 ? darkerL * (1 + enhancedS) : darkerL + enhancedS - darkerL * enhancedS;
                        const p = 2 * darkerL - q;
                        rNew = hue2rgb(p, q, h + 1/3);
                        gNew = hue2rgb(p, q, h);
                        bNew = hue2rgb(p, q, h - 1/3);
                    }
                    
                    const toHex = (c) => {
                        const hex = Math.round(c * 255).toString(16);
                        return hex.length === 1 ? '0' + hex : hex;
                    };
                    
                    return `#${toHex(rNew)}${toHex(gNew)}${toHex(bNew)}`;
                };
                
                const accentColor = generateDarkerAccentShade(primaryTextColor);
                const enhancedGradient = `linear-gradient(135deg, ${primaryTextColor} 0%, ${accentColor} 100%)`;
                
                return React.createElement('span', {
                        key: `overlay-line-${idx}`,
                        style: {
                            fontFamily: selectedFontFamily || 'Impact, Arial, sans-serif',
                            fontSize: selectedTextSize === 'Large' ? '3.5rem' : selectedTextSize === 'Medium' ? '2.5rem' : '2rem',
                            fontWeight: 'bold',
                            textTransform: 'uppercase',
                            letterSpacing: '0.02em',
                            lineHeight: '1.1',
                            marginBottom: '0.2em',
                        background: enhancedGradient,
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            backgroundClip: 'text',
                            color: 'transparent',
                        filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.4))',
                            maxWidth: '90%',
                            whiteSpace: 'pre-line',
                            textAlign: 'center',
                            display: 'block',
                        },
                    className: 'gradient-text-enhanced',
                }, line);
            })
        )
    );

    return thumbnailContainer;
};

// Export the component for dynamic import
export default ThumbnailPreview; 