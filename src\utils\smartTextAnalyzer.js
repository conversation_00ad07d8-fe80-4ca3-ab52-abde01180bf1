// Smart Text Analyzer for Auto-Suggesting Thumbnail Headlines
import { OPENAI_API_KEY } from '../../config.js';

// Fallback keywords for different categories
const categoryKeywords = {
  business: ['GROWTH', 'PROFIT', 'REVENU<PERSON>', 'SUCCESS', 'MONE<PERSON>', 'STARTUP', 'SCALE'],
  fitness: ['TRANSFORM', 'BURN FAT', 'WORKOUT', 'RESULTS', 'STRONG', 'FIT', 'GAINS'],
  tech: ['AI POWER', 'NEXT-GEN', 'INNOVATION', 'BREAKTHROUGH', 'SMART', 'FUTURE', 'TECH'],
  gaming: ['EPIC', 'SHOWDOWN', 'ULTIMATE', 'BATTLE', 'WIN', 'PRO', 'LEGEND'],
  cooking: ['DELICIOUS', 'GOURMET', 'PERFECT', 'AMAZING', 'RECIPE', 'TASTY', 'YUMMY'],
  finance: ['WEALTH', 'INVEST', 'PROFIT', 'MONEY', 'RICH', 'TRADING', 'CRYPTO'],
  travel: ['ADVENTURE', 'EXPLORE', 'DISCOVER', 'JOURNEY', 'ESCAPE', 'WANDERLUST'],
  education: ['MASTER', 'LEARN', 'STUDY', 'ACE', 'GENIUS', 'SMART', 'SUCCESS'],
  fashion: ['STYLE', 'TREND', 'CHIC', 'FASHION', 'LOOK', 'GLAMOUR', 'OUTFIT'],
  motivation: ['SUCCESS', 'POWER', 'ACHIEVE', 'UNLOCK', 'BREAKTHROUGH', 'INSPIRE'],
  general: ['AMAZING', 'ULTIMATE', 'BEST', 'TOP', 'INCREDIBLE', 'AWESOME', 'MUST SEE']
};

// Action words commonly used in thumbnails
const actionWords = [
  'MAXIMIZE', 'TRANSFORM', 'UNLOCK', 'MASTER', 'ACHIEVE', 
  'BOOST', 'DISCOVER', 'EXPLOSIVE', 'ULTIMATE', 'EPIC',
  'PROVEN', 'SECRET', 'POWER', 'SUCCESS', 'BREAKTHROUGH',
  'DESTROY', 'DOMINATE', 'CRUSH', 'CONQUER', 'BUILD'
];

// Words to exclude from suggestions
const excludeWords = [
  'the', 'and', 'with', 'for', 'your', 'how', 'what', 'when', 'where', 'why',
  'can', 'will', 'should', 'could', 'would', 'make', 'get', 'use', 'using',
  'this', 'that', 'these', 'those', 'from', 'into', 'over', 'under',
  'before', 'after', 'during', 'between', 'through', 'about', 'against'
];

/**
 * Detect the category of the prompt
 */
const detectCategory = (prompt) => {
  const lowerPrompt = prompt.toLowerCase();
  
  if (lowerPrompt.match(/business|revenue|profit|startup|entrepreneur|growth|scale/)) return 'business';
  if (lowerPrompt.match(/fitness|workout|gym|muscle|fat|exercise|health|transform/)) return 'fitness';
  if (lowerPrompt.match(/tech|ai|software|app|code|digital|innovation|future/)) return 'tech';
  if (lowerPrompt.match(/game|gaming|fortnite|battle|play|epic|win|pro/)) return 'gaming';
  if (lowerPrompt.match(/cook|recipe|food|meal|delicious|eat|kitchen|dish/)) return 'cooking';
  if (lowerPrompt.match(/money|invest|stock|crypto|finance|trading|wealth|rich/)) return 'finance';
  if (lowerPrompt.match(/travel|adventure|explore|journey|trip|vacation|destination/)) return 'travel';
  if (lowerPrompt.match(/learn|study|education|exam|course|teach|master|skill/)) return 'education';
  if (lowerPrompt.match(/fashion|style|trend|outfit|wear|look|clothing|design/)) return 'fashion';
  if (lowerPrompt.match(/motivat|success|achieve|goal|dream|inspire|power|unlock/)) return 'motivation';
  
  return 'general';
};

/**
 * Generate fallback suggestion when AI is unavailable
 */
export const generateFallbackSuggestion = (prompt) => {
  if (!prompt || prompt.trim() === '') return 'AMAZING!';
  
  const category = detectCategory(prompt);
  const words = prompt.toUpperCase().split(/[\s:!?.,;]+/);
  
  // Filter out common words and keep only impactful ones
  const keyWords = words.filter(word => 
    word.length > 3 && 
    !excludeWords.includes(word.toLowerCase()) &&
    word !== ''
  );
  
  // Look for numbers (like $100k)
  const numberMatch = prompt.match(/\$?\d+[kKmM]?/);
  
  // Try to find action words in the prompt
  const foundActionWord = keyWords.find(word => 
    actionWords.includes(word) || 
    actionWords.some(action => word.includes(action))
  );
  
  // Build suggestion based on findings
  if (numberMatch && foundActionWord) {
    return `${foundActionWord} ${numberMatch[0].toUpperCase()}!`;
  }
  
  if (foundActionWord) {
    const categoryWord = categoryKeywords[category][0];
    return `${foundActionWord} ${categoryWord}!`;
  }
  
  // Extract most impactful 2-3 words
  const impactfulWords = keyWords
    .filter(word => !word.match(/^(HOW|WHAT|WHEN|WHERE|WHY)$/))
    .slice(0, 2);
  
  if (impactfulWords.length > 0) {
    return impactfulWords.join(' ') + '!';
  }
  
  // Last resort: use category-specific suggestion
  const suggestions = categoryKeywords[category];
  return suggestions[Math.floor(Math.random() * suggestions.length)] + '!';
};

/**
 * Clean and prepare prompt for analysis
 */
const sanitizePromptForAnalysis = (prompt) => {
  // Remove URLs
  let cleaned = prompt.replace(/https?:\/\/[^\s]+/g, '');
  
  // Remove special characters but keep numbers and dollar signs
  cleaned = cleaned.replace(/[^\w\s\$\-]/g, ' ');
  
  // Remove extra spaces
  cleaned = cleaned.replace(/\s+/g, ' ').trim();
  
  return cleaned;
};

/**
 * Get AI-powered text suggestion using OpenAI
 */
export const getAITextSuggestion = async (prompt, category = 'general') => {
  // Check if we're on Netlify
  const isNetlify = window.location.hostname.includes('netlify.app');
  
  if (isNetlify || !OPENAI_API_KEY) {
    console.log('Using fallback suggestion (Netlify deployment or no API key)');
    return generateFallbackSuggestion(prompt);
  }

  const payload = {
    model: 'gpt-3.5-turbo-1106',
    messages: [
      {
        role: 'system',
        content: `You are a YouTube thumbnail text expert. Generate punchy, 2-4 word headlines from video prompts.

RULES:
- Maximum 4 words (prefer 2-3 words)
- Use UPPERCASE for impact
- Focus on action words or key concepts
- Make it clickable and exciting
- Remove filler words (the, a, an, with, for, etc.)
- Capture the main benefit or hook
- Add exclamation mark for energy

EXAMPLES:
Input: "Maximize Business Growth: Achieve a $100k Revenue Explosion!"
Output: "REVENUE EXPLOSION!"

Input: "Transform Your Body: Burn Fat Fast with 30-Minute Workouts!"
Output: "TRANSFORM NOW!"

Input: "Epic Showdown: Can You Survive the Ultimate Battle Royale?"
Output: "EPIC SHOWDOWN!"

Input: "Master Stock Trading: Grow Your Wealth with Pro Strategies!"
Output: "MASTER TRADING!"

Input: "Unlock AI Power: Build Smarter Apps with Next-Gen Tools!"
Output: "AI POWER!"

Category context: ${category}

Return ONLY the headline text, nothing else.`
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 10,
    top_p: 1
  };

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      console.error('OpenAI API error:', response.status);
      return generateFallbackSuggestion(prompt);
    }

    const data = await response.json();
    const suggestion = data.choices?.[0]?.message?.content?.trim() || '';
    
    // Validate the suggestion
    if (suggestion && suggestion.split(' ').length <= 4) {
      return suggestion;
    }
    
    // Fallback if AI gives invalid response
    return generateFallbackSuggestion(prompt);
  } catch (error) {
    console.error('Failed to get AI text suggestion:', error);
    return generateFallbackSuggestion(prompt);
  }
};

// Global cache to track recent suggestions and prevent repetition
let suggestionCache = new Map();

/**
 * Clear suggestion cache for a specific prompt
 */
export const clearSuggestionCache = (userPrompt) => {
  if (suggestionCache.has(userPrompt)) {
    suggestionCache.delete(userPrompt);
  }
};

/**
 * Generate multiple unique text suggestions for variation
 */
const generateMultipleVariations = async (cleanPrompt, detectedCategory, excludeSuggestions = []) => {
  const variations = [];
  const maxAttempts = 8; // Try up to 8 times to get unique suggestions
  
  // Enhanced variation strategies for 2-4 word suggestions
  const variationStrategies = [
    {
      systemPrompt: `You are a YouTube thumbnail text expert. Generate punchy, 2-4 word headlines.

RULES:
- EXACTLY 2-4 words maximum
- Use UPPERCASE for impact
- Focus on ACTION WORDS or BENEFITS
- Make it clickable and exciting
- Remove filler words (the, a, an, with, for, etc.)
- Add exclamation mark for energy
- Be UNIQUE and DIFFERENT from previous suggestions

AVOID these phrases: ${excludeSuggestions.join(', ')}

EXAMPLES:
"REVENUE EXPLOSION!", "TRANSFORM NOW!", "EPIC SHOWDOWN!", "MASTER TRADING!", "AI POWER!"

Category: ${detectedCategory}
Return ONLY the headline text, nothing else.`,
      temperature: 0.8
    },
    {
      systemPrompt: `Create a DIFFERENT 2-4 word YouTube thumbnail headline. Focus on EMOTIONAL HOOKS.

REQUIREMENTS:
- 2-4 words only
- UPPERCASE format
- Create urgency or curiosity
- Use power words (ULTIMATE, SECRET, INSTANT, PROVEN, etc.)
- End with exclamation mark
- Must be UNIQUE - avoid: ${excludeSuggestions.join(', ')}

Examples: "ULTIMATE SECRET!", "INSTANT RESULTS!", "PROVEN METHOD!", "SHOCKING TRUTH!"

Category: ${detectedCategory}
Only return the headline:`,
      temperature: 0.9
    },
    {
      systemPrompt: `Generate a UNIQUE 2-4 word thumbnail title focused on BENEFITS or OUTCOMES.

STRICT RULES:
- Maximum 4 words
- ALL CAPS
- Focus on what the viewer gets/achieves
- Use benefit-driven language
- End with exclamation mark
- Cannot repeat: ${excludeSuggestions.join(', ')}

Examples: "MASSIVE GAINS!", "QUICK PROFITS!", "HUGE SUCCESS!", "TOTAL VICTORY!"

Category: ${detectedCategory}
Response format: HEADLINE ONLY`,
      temperature: 0.7
    }
  ];

  for (let attempt = 0; attempt < maxAttempts && variations.length < 3; attempt++) {
    try {
      const strategy = variationStrategies[attempt % variationStrategies.length];
      
      const payload = {
        model: 'gpt-3.5-turbo-1106',
        messages: [
          { role: 'system', content: strategy.systemPrompt },
          { role: 'user', content: cleanPrompt }
        ],
        temperature: strategy.temperature,
        max_tokens: 10,
        top_p: 1
      };

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${OPENAI_API_KEY}`
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        const data = await response.json();
        const suggestion = data.choices?.[0]?.message?.content?.trim() || '';
        
        // Validate suggestion (2-4 words, unique)
        const wordCount = suggestion.split(' ').length;
        if (suggestion && 
            wordCount >= 2 && 
            wordCount <= 4 && 
            !excludeSuggestions.includes(suggestion) &&
            !variations.includes(suggestion)) {
          variations.push(suggestion);
        }
      }
    } catch (error) {
      console.error('Error generating variation:', error);
    }
    
    // Small delay between requests to avoid rate limits
    if (attempt < maxAttempts - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return variations;
};

/**
 * Enhanced function to generate unique text suggestions with variation tracking
 */
export const generateSmartTextSuggestion = async (userPrompt, category = 'general', forceNew = false) => {
  if (!userPrompt || userPrompt.trim() === '') {
    return '';
  }
  
  // Clean the prompt
  const cleanPrompt = sanitizePromptForAnalysis(userPrompt);
  const cacheKey = cleanPrompt.toLowerCase();
  
  // Get or initialize cache for this prompt
  if (!suggestionCache.has(cacheKey)) {
    suggestionCache.set(cacheKey, {
      suggestions: [],
      lastGenerated: 0,
      currentIndex: 0
    });
  }
  
  const cache = suggestionCache.get(cacheKey);
  const now = Date.now();
  
  // If we have cached suggestions and it's not a forced refresh, use them
  if (!forceNew && cache.suggestions.length > 0 && cache.currentIndex < cache.suggestions.length) {
    const suggestion = cache.suggestions[cache.currentIndex];
    cache.currentIndex++;
    console.log(`[Text Variation] Using cached suggestion ${cache.currentIndex}/${cache.suggestions.length}: ${suggestion}`);
    return suggestion;
  }
  
  // Generate new batch of unique suggestions
  console.log('[Text Variation] Generating new batch of unique suggestions...');
  
  // Detect category if not provided
  const detectedCategory = category === 'general' ? detectCategory(cleanPrompt) : category;
  
  // Check if we're on Netlify or missing API key
  const isNetlify = typeof window !== 'undefined' && window.location.hostname.includes('netlify.app');
  
  if (isNetlify || !OPENAI_API_KEY) {
    console.log('Using fallback suggestions (Netlify deployment or no API key)');
    // Generate multiple fallback variations
    const fallbackVariations = generateMultipleFallbackSuggestions(cleanPrompt, detectedCategory);
    cache.suggestions = fallbackVariations;
    cache.currentIndex = 1; // Return first one now
    cache.lastGenerated = now;
    return fallbackVariations[0];
  }
  
  try {
    // Get previously used suggestions to avoid repetition
    const excludeSuggestions = cache.suggestions.slice(-3); // Last 3 suggestions
    
    // Generate multiple unique variations
    const variations = await generateMultipleVariations(cleanPrompt, detectedCategory, excludeSuggestions);
    
    if (variations.length > 0) {
      // Update cache with new suggestions
      cache.suggestions = variations;
      cache.currentIndex = 1; // Return first one now
      cache.lastGenerated = now;
      
      console.log(`[Text Variation] Generated ${variations.length} unique suggestions:`, variations);
      return variations[0];
    } else {
      // Fallback if AI fails
      console.log('[Text Variation] AI failed, using enhanced fallback');
      const fallbackVariations = generateMultipleFallbackSuggestions(cleanPrompt, detectedCategory, excludeSuggestions);
      cache.suggestions = fallbackVariations;
      cache.currentIndex = 1;
      cache.lastGenerated = now;
      return fallbackVariations[0];
    }
  } catch (error) {
    console.error('Error in generateSmartTextSuggestion:', error);
    // Generate fallback suggestions
    const fallbackVariations = generateMultipleFallbackSuggestions(cleanPrompt, detectedCategory);
    cache.suggestions = fallbackVariations;
    cache.currentIndex = 1;
    cache.lastGenerated = now;
    return fallbackVariations[0];
  }
};

/**
 * Generate multiple fallback suggestions for offline/error scenarios
 */
const generateMultipleFallbackSuggestions = (prompt, category, excludeSuggestions = []) => {
  const suggestions = [];
  const maxSuggestions = 5;
  
  // Base fallback generation
  for (let i = 0; i < maxSuggestions; i++) {
    const variation = generateEnhancedFallbackSuggestion(prompt, category, i, excludeSuggestions);
    if (variation && !suggestions.includes(variation) && !excludeSuggestions.includes(variation)) {
      suggestions.push(variation);
    }
  }
  
  // Ensure we have at least 3 unique suggestions
  if (suggestions.length < 3) {
    const categoryWords = categoryKeywords[category] || categoryKeywords.general;
    const additionalSuggestions = [
      `${categoryWords[0]} NOW!`,
      `${categoryWords[1]} FAST!`,
      `${categoryWords[2]} TODAY!`
    ].filter(s => !suggestions.includes(s) && !excludeSuggestions.includes(s));
    
    suggestions.push(...additionalSuggestions.slice(0, 3 - suggestions.length));
  }
  
  return suggestions.slice(0, maxSuggestions);
};

/**
 * Enhanced fallback suggestion with variation strategies
 */
const generateEnhancedFallbackSuggestion = (prompt, category, variation = 0, excludeSuggestions = []) => {
  if (!prompt || prompt.trim() === '') return 'AMAZING!';
  
  const words = prompt.toUpperCase().split(/[\s:!?.,;]+/);
  const keyWords = words.filter(word => 
    word.length > 3 && 
    !excludeWords.includes(word.toLowerCase()) &&
    word !== ''
  );
  
  const categoryWords = categoryKeywords[category] || categoryKeywords.general;
  const randomActionWord = actionWords[Math.floor(Math.random() * actionWords.length)];
  
  // Different variation strategies
  switch (variation % 5) {
    case 0: // Action + Key word
      if (keyWords.length > 0) {
        const suggestion = `${randomActionWord} ${keyWords[0]}!`;
        if (suggestion.split(' ').length <= 4) return suggestion;
      }
      break;
      
    case 1: // Key words combination
      if (keyWords.length >= 2) {
        const suggestion = `${keyWords[0]} ${keyWords[1]}!`;
        if (suggestion.split(' ').length <= 4) return suggestion;
      }
      break;
      
    case 2: // Category + Action
      const suggestion2 = `${categoryWords[variation % categoryWords.length]} ${randomActionWord}!`;
      if (suggestion2.split(' ').length <= 4) return suggestion2;
      break;
      
    case 3: // Key word + Category
      if (keyWords.length > 0) {
        const suggestion3 = `${keyWords[0]} ${categoryWords[0]}!`;
        if (suggestion3.split(' ').length <= 4) return suggestion3;
      }
      break;
      
    case 4: // Pure category with variation
      return `${categoryWords[(variation + 1) % categoryWords.length]} NOW!`;
  }
  
  // Final fallback
  return `${categoryWords[variation % categoryWords.length]} POWER!`;
};

// Export for testing
export default {
  generateSmartTextSuggestion,
  generateFallbackSuggestion,
  detectCategory
}; 