const React = window.React;
const { useState, useEffect } = React;

export const PasswordChangeModal = ({ 
    isOpen, 
    onClose, 
    onPasswordChange,
    passwordState,
    setPasswordState,
    rateLimitInfo
}) => {
    const [isClosing, setIsClosing] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState({ score: 0, label: '', color: '' });

    useEffect(() => {
        if (!isOpen) {
            setIsClosing(false);
        }
    }, [isOpen]);

    // Calculate password strength
    const calculatePasswordStrength = (password) => {
        if (!password) {
            setPasswordStrength({ score: 0, label: '', color: '' });
            return;
        }

        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            numbers: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };

        // Calculate score
        if (checks.length) score += 20;
        if (password.length >= 12) score += 20;
        if (checks.lowercase) score += 20;
        if (checks.uppercase) score += 20;
        if (checks.numbers) score += 20;
        if (checks.special) score += 20;

        // Determine strength label and color
        let label = '';
        let color = '';
        if (score <= 20) {
            label = 'Very Weak';
            color = 'bg-red-500';
        } else if (score <= 40) {
            label = 'Weak';
            color = 'bg-orange-500';
        } else if (score <= 60) {
            label = 'Fair';
            color = 'bg-yellow-500';
        } else if (score <= 80) {
            label = 'Good';
            color = 'bg-blue-500';
        } else {
            label = 'Strong';
            color = 'bg-green-500';
        }

        setPasswordStrength({ score, label, color });
    };

    // Update password strength when new password changes
    useEffect(() => {
        calculatePasswordStrength(passwordState.newPassword);
    }, [passwordState.newPassword]);

    // Validate passwords
    const validatePasswords = () => {
        const errors = {};

        if (!passwordState.currentPassword) {
            errors.currentPassword = 'Current password is required';
        }

        if (!passwordState.newPassword) {
            errors.newPassword = 'New password is required';
        } else if (passwordState.newPassword.length < 8) {
            errors.newPassword = 'Password must be at least 8 characters';
        } else if (passwordState.newPassword === passwordState.currentPassword) {
            errors.newPassword = 'New password must be different from current password';
        }

        if (!passwordState.confirmPassword) {
            errors.confirmPassword = 'Please confirm your new password';
        } else if (passwordState.newPassword !== passwordState.confirmPassword) {
            errors.confirmPassword = 'Passwords do not match';
        }

        setPasswordState(prev => ({ ...prev, errors }));
        return Object.keys(errors).length === 0;
    };

    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            onClose();
            // Reset form after closing
            setTimeout(() => {
                setPasswordState({
                    isOpen: false,
                    currentPassword: '',
                    newPassword: '',
                    confirmPassword: '',
                    showCurrentPassword: false,
                    showNewPassword: false,
                    showConfirmPassword: false,
                    errors: {},
                    isLoading: false,
                    successMessage: ''
                });
                setPasswordStrength({ score: 0, label: '', color: '' });
            }, 100);
        }, 200);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validatePasswords()) {
            return;
        }

        setPasswordState(prev => ({ ...prev, isLoading: true, errors: {} }));

        try {
            // Call the password change function
            await onPasswordChange({
                currentPassword: passwordState.currentPassword,
                newPassword: passwordState.newPassword
            });

            // Show success message
            setPasswordState(prev => ({ 
                ...prev, 
                isLoading: false,
                successMessage: 'Password changed successfully!',
                currentPassword: '',
                newPassword: '',
                confirmPassword: '',
                showCurrentPassword: false,
                showNewPassword: false,
                showConfirmPassword: false
            }));

            // Auto close after 3 seconds
            setTimeout(() => {
                handleClose();
            }, 3000);
        } catch (error) {
            setPasswordState(prev => ({ 
                ...prev, 
                isLoading: false,
                errors: { 
                    submit: error.message || 'Failed to change password. Please try again.' 
                }
            }));
        }
    };

    const togglePasswordVisibility = (field) => {
        setPasswordState(prev => ({
            ...prev,
            [field]: !prev[field]
        }));
    };

    if (!isOpen) return null;

    return React.createElement('div', {
        className: `fixed inset-0 z-[10002] flex items-center justify-center${isClosing ? ' closing' : ''}`,
        onClick: (e) => e.target === e.currentTarget && handleClose(),
        style: { animation: isClosing ? 'fadeOut 0.2s ease-in' : 'fadeIn 0.2s ease-out' }
    },
        // Backdrop
        React.createElement('div', {
            className: 'absolute inset-0 bg-black/60 backdrop-blur-sm'
        }),
        
        // Modal Container
        React.createElement('div', {
            className: 'relative w-full max-w-md mx-4',
            style: { animation: isClosing ? 'slideOutScale 0.2s cubic-bezier(0.4, 0, 1, 1)' : 'slideInScale 0.3s cubic-bezier(0.16, 1, 0.3, 1)' }
        },
            // Modal Content
            React.createElement('div', {
                className: 'bg-gray-800 border border-gray-700 rounded-2xl shadow-2xl overflow-hidden'
            },
                passwordState.successMessage ? (
                    // Success State
                    React.createElement('div', {
                        className: 'p-8 text-center'
                    },
                        // Success Icon
                        React.createElement('div', {
                            className: 'mb-6 flex justify-center'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '52px' }
                            })
                        ),
                        
                        // Success Title
                        React.createElement('h2', {
                            className: 'text-2xl font-bold text-white mb-3'
                        }, 'Password Changed!'),
                        
                        // Success Message
                        React.createElement('p', {
                            className: 'text-gray-400 mb-8'
                        }, 'Your password has been updated successfully.'),
                        
                        // Done Button
                        React.createElement('button', {
                            onClick: handleClose,
                            className: 'w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors',
                            autoFocus: true
                        }, 'Done')
                    )
                ) : (
                    // Form State
                    React.createElement(React.Fragment, {},
                        // Header
                        React.createElement('div', {
                            className: 'px-6 py-5 border-b border-gray-700'
                        },
                            React.createElement('div', { className: 'flex items-center justify-between' },
                                React.createElement('h2', { className: 'text-xl font-bold text-white' }, 'Change Password'),
                                React.createElement('button', {
                                    onClick: handleClose,
                                    className: 'text-gray-400 hover:text-gray-300 transition-colors p-1',
                                    'aria-label': 'Close modal'
                                },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:close-circle-bold',
                                        style: { fontSize: '24px' }
                                    })
                                )
                            )
                        ),

                        // Rate Limiting Information (if available)
                        rateLimitInfo && rateLimitInfo.message && !rateLimitInfo.canChange && (
                            React.createElement('div', {
                                className: 'px-6 py-4 border-b border-gray-700 bg-gray-800/50'
                            },
                                React.createElement('div', { 
                                    className: `flex items-start gap-3 text-sm ${
                                        rateLimitInfo.message.type === 'warning' ? 'text-yellow-400' :
                                        rateLimitInfo.message.type === 'info' ? 'text-blue-400' :
                                        'text-red-400'
                                    }`
                                },
                                    React.createElement('span', {
                                        className: 'iconify mt-0.5',
                                        'data-icon': 
                                            rateLimitInfo.message.type === 'warning' ? 'solar:clock-circle-linear' :
                                            rateLimitInfo.message.type === 'info' ? 'solar:info-circle-linear' :
                                            'solar:danger-triangle-linear',
                                        style: { fontSize: '18px' }
                                    }),
                                    React.createElement('div', {},
                                        React.createElement('p', { className: 'font-medium mb-1' }, rateLimitInfo.message.title),
                                        React.createElement('p', { className: 'text-xs opacity-90' }, rateLimitInfo.message.message),
                                        rateLimitInfo.message.nextAvailable && (
                                            React.createElement('p', { className: 'text-xs opacity-75 mt-2' }, 
                                                `Next available: ${rateLimitInfo.message.nextAvailable.toLocaleString()}`
                                            )
                                        )
                                    )
                                )
                            )
                        ),

                        // Rate Limiting Success Info (when changes remaining)
                        rateLimitInfo && rateLimitInfo.message && rateLimitInfo.canChange && rateLimitInfo.eligibility?.changesRemaining !== undefined && (
                            React.createElement('div', {
                                className: 'px-6 py-3 border-b border-gray-700 bg-green-900/10'
                            },
                                React.createElement('div', { className: 'flex items-center gap-2 text-green-400 text-sm' },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:check-circle-linear',
                                        style: { fontSize: '16px' }
                                    }),
                                    React.createElement('span', {}, 
                                        `${rateLimitInfo.eligibility.changesRemaining} password change${rateLimitInfo.eligibility.changesRemaining !== 1 ? 's' : ''} remaining this month`
                                    )
                                )
                            )
                        ),

                        // Form
                React.createElement('form', {
                    onSubmit: handleSubmit,
                    className: 'p-6'
                },
                    // Current Password Field
                    React.createElement('div', { className: 'mb-5' },
                        React.createElement('label', {
                            htmlFor: 'current-password',
                            className: 'block text-sm font-medium text-gray-300 mb-2'
                        }, 'Current Password'),
                        React.createElement('div', { className: 'relative' },
                            React.createElement('input', {
                                type: passwordState.showCurrentPassword ? 'text' : 'password',
                                id: 'current-password',
                                value: passwordState.currentPassword,
                                onChange: (e) => setPasswordState(prev => ({ 
                                    ...prev, 
                                    currentPassword: e.target.value,
                                    errors: { ...prev.errors, currentPassword: '' }
                                })),
                                className: `w-full bg-gray-700 border ${passwordState.errors.currentPassword ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                placeholder: 'Enter current password',
                                disabled: passwordState.isLoading,
                                autoComplete: 'current-password'
                            }),
                            React.createElement('button', {
                                type: 'button',
                                onClick: () => togglePasswordVisibility('showCurrentPassword'),
                                className: 'absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors',
                                'aria-label': passwordState.showCurrentPassword ? 'Hide password' : 'Show password'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': passwordState.showCurrentPassword ? 'solar:eye-closed-linear' : 'solar:eye-linear',
                                    style: { fontSize: '20px' }
                                })
                            )
                        ),
                        passwordState.errors.currentPassword && React.createElement('p', {
                            className: 'mt-1 text-red-400 text-xs'
                        }, passwordState.errors.currentPassword)
                    ),

                    // New Password Field
                    React.createElement('div', { className: 'mb-5' },
                        React.createElement('label', {
                            htmlFor: 'new-password',
                            className: 'block text-sm font-medium text-gray-300 mb-2'
                        }, 'New Password'),
                        React.createElement('div', { className: 'relative' },
                            React.createElement('input', {
                                type: passwordState.showNewPassword ? 'text' : 'password',
                                id: 'new-password',
                                value: passwordState.newPassword,
                                onChange: (e) => setPasswordState(prev => ({ 
                                    ...prev, 
                                    newPassword: e.target.value,
                                    errors: { ...prev.errors, newPassword: '' }
                                })),
                                className: `w-full bg-gray-700 border ${passwordState.errors.newPassword ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                placeholder: 'Enter new password',
                                disabled: passwordState.isLoading,
                                autoComplete: 'new-password'
                            }),
                            React.createElement('button', {
                                type: 'button',
                                onClick: () => togglePasswordVisibility('showNewPassword'),
                                className: 'absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors',
                                'aria-label': passwordState.showNewPassword ? 'Hide password' : 'Show password'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': passwordState.showNewPassword ? 'solar:eye-closed-linear' : 'solar:eye-linear',
                                    style: { fontSize: '20px' }
                                })
                            )
                        ),
                        passwordState.errors.newPassword && React.createElement('p', {
                            className: 'mt-1 text-red-400 text-xs'
                        }, passwordState.errors.newPassword),
                        
                        // Password Strength Indicator
                        passwordState.newPassword && React.createElement('div', { className: 'mt-3' },
                            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                                React.createElement('span', { className: 'text-xs text-gray-400' }, 'Password Strength'),
                                React.createElement('span', { className: `text-xs font-medium ${
                                    passwordStrength.score <= 40 ? 'text-red-400' :
                                    passwordStrength.score <= 60 ? 'text-yellow-400' :
                                    passwordStrength.score <= 80 ? 'text-blue-400' : 'text-green-400'
                                }` }, passwordStrength.label)
                            ),
                            React.createElement('div', { className: 'w-full bg-gray-700 rounded-full h-1.5' },
                                React.createElement('div', {
                                    className: `${passwordStrength.color} h-1.5 rounded-full transition-all duration-300`,
                                    style: { width: `${passwordStrength.score}%` }
                                })
                            )
                        )
                    ),

                    // Confirm Password Field
                    React.createElement('div', { className: 'mb-6' },
                        React.createElement('label', {
                            htmlFor: 'confirm-password',
                            className: 'block text-sm font-medium text-gray-300 mb-2'
                        }, 'Confirm New Password'),
                        React.createElement('div', { className: 'relative' },
                            React.createElement('input', {
                                type: passwordState.showConfirmPassword ? 'text' : 'password',
                                id: 'confirm-password',
                                value: passwordState.confirmPassword,
                                onChange: (e) => setPasswordState(prev => ({ 
                                    ...prev, 
                                    confirmPassword: e.target.value,
                                    errors: { ...prev.errors, confirmPassword: '' }
                                })),
                                className: `w-full bg-gray-700 border ${passwordState.errors.confirmPassword ? 'border-red-500' : 'border-gray-600'} rounded-lg px-4 py-2.5 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors`,
                                placeholder: 'Confirm new password',
                                disabled: passwordState.isLoading,
                                autoComplete: 'new-password'
                            }),
                            React.createElement('button', {
                                type: 'button',
                                onClick: () => togglePasswordVisibility('showConfirmPassword'),
                                className: 'absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors',
                                'aria-label': passwordState.showConfirmPassword ? 'Hide password' : 'Show password'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': passwordState.showConfirmPassword ? 'solar:eye-closed-linear' : 'solar:eye-linear',
                                    style: { fontSize: '20px' }
                                })
                            )
                        ),
                        passwordState.errors.confirmPassword && React.createElement('p', {
                            className: 'mt-1 text-red-400 text-xs'
                        }, passwordState.errors.confirmPassword)
                    ),

                    // Error Message
                    passwordState.errors.submit && React.createElement('div', {
                        className: 'mb-6 p-3 bg-red-900/30 border border-red-700/50 rounded-lg flex items-center gap-2'
                    },
                        React.createElement('span', {
                            className: 'iconify text-red-400',
                            'data-icon': 'solar:danger-triangle-linear',
                            style: { fontSize: '20px' }
                        }),
                        React.createElement('p', { className: 'text-red-400 text-sm' }, passwordState.errors.submit)
                    ),

                    // Password Requirements
                    React.createElement('div', { className: 'mb-6 p-3 bg-gray-700/50 rounded-lg' },
                        React.createElement('p', { className: 'text-xs text-gray-400 mb-2' }, 'Password Requirements:'),
                        React.createElement('ul', { className: 'space-y-1' },
                            React.createElement('li', { className: 'flex items-center gap-2 text-xs' },
                                React.createElement('span', {
                                    className: `iconify ${passwordState.newPassword.length >= 8 ? 'text-green-400' : 'text-gray-500'}`,
                                    'data-icon': passwordState.newPassword.length >= 8 ? 'solar:check-circle-bold' : 'solar:close-circle-bold',
                                    style: { fontSize: '14px' }
                                }),
                                React.createElement('span', { 
                                    className: passwordState.newPassword.length >= 8 ? 'text-gray-300' : 'text-gray-500' 
                                }, 'At least 8 characters')
                            ),
                            React.createElement('li', { className: 'flex items-center gap-2 text-xs' },
                                React.createElement('span', {
                                    className: `iconify ${/[A-Z]/.test(passwordState.newPassword) && /[a-z]/.test(passwordState.newPassword) ? 'text-green-400' : 'text-gray-500'}`,
                                    'data-icon': /[A-Z]/.test(passwordState.newPassword) && /[a-z]/.test(passwordState.newPassword) ? 'solar:check-circle-bold' : 'solar:close-circle-bold',
                                    style: { fontSize: '14px' }
                                }),
                                React.createElement('span', { 
                                    className: /[A-Z]/.test(passwordState.newPassword) && /[a-z]/.test(passwordState.newPassword) ? 'text-gray-300' : 'text-gray-500' 
                                }, 'Both uppercase and lowercase letters')
                            ),
                            React.createElement('li', { className: 'flex items-center gap-2 text-xs' },
                                React.createElement('span', {
                                    className: `iconify ${/\d/.test(passwordState.newPassword) ? 'text-green-400' : 'text-gray-500'}`,
                                    'data-icon': /\d/.test(passwordState.newPassword) ? 'solar:check-circle-bold' : 'solar:close-circle-bold',
                                    style: { fontSize: '14px' }
                                }),
                                React.createElement('span', { 
                                    className: /\d/.test(passwordState.newPassword) ? 'text-gray-300' : 'text-gray-500' 
                                }, 'At least one number')
                            )
                        )
                    ),

                    // Action Buttons
                    React.createElement('div', { className: 'flex gap-3' },
                        React.createElement('button', {
                            type: 'button',
                            onClick: handleClose,
                            className: 'flex-1 px-4 py-2.5 rounded-lg text-gray-400 hover:text-gray-300 transition-colors text-sm font-medium',
                            disabled: passwordState.isLoading
                        }, 'Cancel'),
                        React.createElement('button', {
                            type: 'submit',
                            className: 'flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white px-4 py-2.5 rounded-lg font-medium text-sm transition-colors flex items-center justify-center gap-2',
                            disabled: passwordState.isLoading
                        },
                            passwordState.isLoading && React.createElement('span', {
                                className: 'iconify animate-spin',
                                'data-icon': 'solar:refresh-linear',
                                style: { fontSize: '16px' }
                            }),
                            passwordState.isLoading ? 'Changing...' : 'Change Password'
                        )
                    )
                )
                    ) // Close React.Fragment
                ) // Close ternary operator
            )
        )
    );
};
