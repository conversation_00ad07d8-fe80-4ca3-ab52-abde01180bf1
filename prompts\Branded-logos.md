# Feature Update: Robust Brand Logo Embedding & Context-Aware Prompt Enhancement

## Context
We now support dynamic brand logo embedding for tech, design, and AI tools in thumbnail prompts. This update ensures that when users mention **Bolt**, **Bolt.new**, or **Lovable**—all modern AI-powered development platforms (with Bolt.new being an AI web code agent similar to Cursor AI)—the system will always include their official brand logos in the generated thumbnail, provided the "Include Icons" option is enabled.

## 🛡️ Conflict Fix: Game Logic vs. Tech/Dev Brands

- **Problem:**  
  Previously, the prompt enhancer sometimes applied gaming thumbnail logic (e.g., Call of Duty) to tech/dev prompts if keywords like `vs` or `showdown` were present, even when the brands were not games.
- **Solution:**  
  The enhancer now checks if the detected brands are in the gaming category before applying any game-specific logic. If the brands are tech/dev/AI tools (e.g., Bolt.new, Lovable, Figma), it will **not** apply game logic, and will instead focus on a modern, tech-forward composition with correct logo embedding.

## Requirements

### Brand Detection
- Detect keywords: `Bolt`, `Bolt.new`, and `Lovable` (case-insensitive, including variations like "bolt new").
- Recognize `Bolt.new` as a distinct AI web code agent, not to be confused with generic "bolt" or unrelated brands.

### Context-Aware Prompt Enhancement
- **If detected brands are games:**  
  Apply the cinematic game thumbnail logic (split-screen, in-game characters, etc.).
- **If detected brands are tech/dev/AI tools:**  
  - Do **not** apply game logic, even if the prompt contains `vs`, `showdown`, or similar keywords.
  - Focus on a modern, tech/dev visual style.
  - Embed the official brand logos as described below.

### Logo Embedding
- When any of these brands are detected, automatically instruct the image generation prompt to embed the **official brand logo** for each.
- Logos must be:
  - High quality (SVG or PNG, not generic icons)
  - Displayed in their original colors and aspect ratio
  - Clearly visible but not overpowering (≤10% of thumbnail height, ≥100px from edges)
  - Placed contextually (e.g., for "Bolt.new vs Cursor AI", use a split layout with both logos)

### Prompt Enhancement Example

**User Input:**  
`Bolt.new vs Lovable: Which AI dev tool is better?`

**Enhanced Prompt:**  
`Create a cinematic YouTube thumbnail comparing Bolt.new and Lovable, both leading AI-powered web code agents. Integrate the official Bolt.new and Lovable logos on opposite sides of the frame in a split-screen layout. Ensure each logo is clear, maintains its original brand colors and aspect ratio, and is placed at least 100px from all edges. The design should emphasize a modern, tech-forward atmosphere, with bold, high-contrast text overlay if enabled.`

### Brand Library
- Ensure the logo asset library includes up-to-date, official logos for Bolt, Bolt.new, and Lovable.
- If a logo is missing, add it to `/public/assets/brand-logos/` or the relevant logo directory.

## 🎯 Goal
Guarantee that all references to Bolt, Bolt.new, and Lovable in prompts result in accurate, visually appealing brand logo integration, **never triggering game logic unless the brands are actual games**. This enhances the authenticity and professionalism of tech/AI/developer thumbnails.

---

**Action:**  
Update the prompt enhancer logic to check the detected brands' category before applying any game-specific rules. For tech/dev/AI brands, always use a modern, tech-forward style and embed the correct logos.

## 🎯 Objective
Enhance the "Include Icons" feature by automatically detecting and embedding official brand logos (e.g., Webflow, Figma, Photoshop, Bolt.new, Framer) into the generated thumbnail when relevant tech, design, or development tools are mentioned in the user's prompt. This will increase visual appeal, relevance, and click-through rates for tech-focused video content.

## ⚙️ Trigger Conditions
- The "Include Icons" toggle is **enabled** by the user.
- The user's prompt contains names of popular software, platforms, or tools from a predefined list (see "Brand Asset & Keyword List" below).
- Keywords like `review`, `tutorial`, `vs` (for comparisons), `guide`, `how to`, `alternative`, `new feature`, `update` are present, suggesting a tool-focused video.

## ✨ Enhancement Behavior

1.  **Keyword Detection & Logo Matching**:
    *   When a recognized brand name/keyword is detected in the prompt, the system will attempt to fetch its official logo.
    *   Prioritize a local, curated library of high-quality SVG logos.
    *   If not found locally, consider an API call to a service like Brandfetch or a similar reliable source for logos (with appropriate caching).

2.  **Logo Placement & Styling**:
    *   **Contextual Placement**:
        *   If one brand is mentioned (e.g., "Figma tutorial"), place its logo subtly, perhaps in a corner or integrated into a graphic element, ensuring it doesn't overpower the main subject.
        *   If two brands are compared (e.g., "Webflow vs Framer"), position logos on opposing sides, potentially with a "VS" graphic, similar to the existing `1v1` logic in `auto-game-thumbnails` rule.
    *   **Styling**:
        *   Ensure logos are clearly visible and legible against the thumbnail background.
        *   Apply a subtle drop shadow or a soft outline if needed for contrast, especially on busy backgrounds.
        *   Maintain original logo aspect ratio and colors. Do not distort or recolor brand logos unless it's a monochrome version for stylistic consistency.
    *   **Size**: Logos should be noticeable but not dominant, typically occupying 5-10% of the thumbnail height.

3.  **Prioritization & Limits**:
    *   If multiple (3+) brands are mentioned, prioritize the first 1-2 most prominent or relevant ones, or consider a collage/grid if the design allows without clutter.
    *   Avoid cluttering the thumbnail. If too many icons/logos are requested or detected, the system should intelligently select the most relevant ones or default to a generic "tech" icon if placement becomes too complex.

4.  **Fallback**:
    *   If a specific brand logo cannot be fetched or isn't available, the system should fall back to a relevant generic icon (e.g., a generic "design software" icon if "Affinity Designer" is mentioned but its logo isn't available).

## 🖼️ Visual Examples

*   **User Input:** "My Honest Figma Review After 1 Year"
    *   **Thumbnail:** User's face/graphic + Figma logo subtly placed.
*   **User Input:** "Webflow vs Framer: Which is Better for Landing Pages?"
    *   **Thumbnail:** Split screen, Webflow logo on one side, Framer logo on the other, "VS" text.
*   **User Input:** "Top 5 AI Tools for Developers in 2024"
    *   **Thumbnail:** Abstract tech background, perhaps with 1-2 prominent AI tool logos (e.g., GitHub Copilot, OpenAI) or a generic "AI" icon.

## 📋 Initial Brand Asset & Keyword List (Expandable)

This list should be regularly updated and managed. For now, an initial set of ~100 popular tools across AI, Design, and Development:

// 🕵️‍♂️ Hidden Main Categories You Might Have Missed

**Hidden/Underrepresented Main Categories to Consider:**

*   **Education & eLearning Platforms**
    *   Coursera
    *   Udemy
    *   Khan Academy
    *   edX
    *   Skillshare
    *   LinkedIn Learning
    *   Pluralsight

*   **Cloud & Infrastructure Providers**
    *   AWS
    *   Google Cloud Platform (GCP)
    *   Microsoft Azure
    *   DigitalOcean
    *   Heroku
    *   Netlify
    *   Vercel

*   **Database & Data Tools**
    *   MongoDB
    *   PostgreSQL
    *   MySQL
    *   Redis
    *   Firebase
    *   Snowflake
    *   Supabase

*   **Cybersecurity & Privacy**
    *   1Password
    *   LastPass
    *   NordVPN
    *   Cloudflare
    *   Okta

*   **APIs & Developer Tools**
    *   Postman
    *   Swagger
    *   RapidAPI
    *   Stripe
    *   Twilio
    *   SendGrid

*   **Browser Extensions & Utilities**
    *   Chrome
    *   Firefox
    *   Brave
    *   Opera
    *   uBlock Origin
    *   Grammarly (extension)

*   **Hardware & Devices**
    *   Raspberry Pi
    *   Arduino
    *   Wacom
    *   Elgato
    *   Logitech

*   **Social Media & Content Creation**
    *   YouTube
    *   Twitter (X)
    *   Instagram
    *   TikTok
    *   Facebook
    *   LinkedIn

*   **Project Management & Collaboration**
    *   Basecamp
    *   Wrike
    *   Smartsheet
    *   Teamwork

*   **Finance & Payments**
    *   PayPal
    *   Square
    *   Wise
    *   QuickBooks

*   **Streaming & Media**
    *   OBS Studio
    *   Streamlabs
    *   Spotify
    *   Apple Music

*   **Gaming & Game Development**
    *   Unity
    *   Unreal Engine
    *   Godot
    *   Roblox Studio
    *   Steam

**Design & UX/UI:**
*   Figma
*   Adobe XD
*   Sketch
*   InVision
*   Marvel
*   Framer
*   Principle
*   ProtoPie
*   Balsamiq
*   Axure RP
*   Adobe Photoshop
*   Adobe Illustrator
*   Affinity Designer
*   Affinity Photo
*   Canva
*   Procreate
*   CorelDRAW
*   Zeplin
*   Abstract
*   LottieFiles
*   Webflow
*   WordPress
*   Elementor
*   Shopify
*   Squarespace
*   Wix
*   Dorik
*   Carrd
*   Bubble
*   Readymag
*   Tilda Publishing

**Development & DevOps:**
*   Visual Studio Code (VS Code)
*   Sublime Text
*   Atom

*   JetBrains (IntelliJ IDEA, PyCharm, WebStorm, etc.)
*   GitHub
*   GitLab
*   Bitbucket
*   Jira
*   Confluence
*   Slack
*   Microsoft Teams
*   Docker
*   Kubernetes
*   Jenkins
*   CircleCI
*   Travis CI
*   AWS (Amazon Web Services)
*   Google Cloud (GCP)
*   Microsoft Azure
*   Heroku
*   Netlify
*   Vercel
*   DigitalOcean
*   Linode
*   Firebase
*   Supabase
*   MongoDB
*   PostgreSQL
*   MySQL
*   Redis
*   Elasticsearch
*   Terraform
*   Ansible
*   Puppet
*   Chef
*   Datadog
*   New Relic
*   Sentry
*   Postman
*   Insomnia
*   Swagger / OpenAPI
*   GraphQL
*   React
*   Angular
*   Vue.js
*   Svelte
*   Next.js
*   Nuxt.js
*   Gatsby
*   Node.js
*   Python (Django, Flask)
*   Ruby (Ruby on Rails)
*   PHP (Laravel, Symfony)
*   Java (Spring)
*   C# (.NET)
*   Go (Golang)
*   Rust
*   Swift (for iOS)
*   Kotlin (for Android)
*   Flutter
*   React Native
*   Electron
*   Unity
*   Unreal Engine
*   Zapier
*   IFTTT
*   Make (formerly Integromat)
*   n8n
*   Bolt.new
*   Bolt
*   Cursor

**AI & Machine Learning:**
*   OpenAI (ChatGPT, DALL-E, GPT-4 etc.)
*   Google AI (Gemini, Bard, Vertex AI)
*   Deepseek
*   Cohere
*   AI21 Labs (Jurassic)
*   Aleph Alpha
*   Mistral AI
*   MosaicML
*   Llama (Meta AI)
*   Baidu ERNIE
*   Alibaba Tongyi Qianwen
*   Amazon Bedrock
*   IBM Watsonx
*   Databricks Dolly
*   Google Cloud AutoML
*   DeepMind (AlphaFold, Gemini Ultra)
*   Anthropic (Claude)
*   Perplexity Labs
*   Replit AI
*   Writer.com
*   You.com
*   Character.AI
*   Poe (Quora)
*   Forefront AI
*   Open Assistant
*   Vicuna
*   MiniMax
*   Zhipu AI (GLM)
*   Inflection AI (Pi)
*   Adept AI
*   Magic.dev
*   Luminous (Aleph Alpha)
*   LightOn
*   Stability AI (StableLM)
*   Midjourney
*   Stable Diffusion
*   Hugging Face
*   TensorFlow
*   PyTorch
*   Scikit-learn
*   Keras
*   Jupyter
*   Anaconda
*   RunwayML
*   Synthesia
*   Descript
*   Murf.ai
*   ElevenLabs
*   Notion AI
*   Jasper (formerly Jarvis)
*   Copy.ai
*   Rytr
*   Writesonic
*   Claude (Anthropic)
*   Perplexity AI
*   Bolt.new
*   Bolt
*   Lovable
*   Firebase Studio


**Productivity & Other:**
*   Notion
*   Obsidian
*   Roam Research
*   Airtable
*   Trello
*   Asana
*   Monday.com
*   ClickUp
*   Miro
*   FigJam
*   Lucidchart
*   Grammarly
*   Zoom
*   Google Meet
*   Discord
*    Loom
*   Adobe Premiere Pro
*   Final Cut Pro
*   DaVinci Resolve
*   Audacity
*   Ableton Live
*   Logic Pro X

**Cars and Automobile:**
*   Tesla
*   Toyota
*   Honda
*   Ford
*   Chevrolet
*   BMW
*   Mercedes-Benz
*   Audi
*   Volkswagen
*   Porsche
*   Ferrari
*   Lamborghini
*   Maserati
*   Aston Martin
*   Jaguar
*   Land Rover
*   Subaru
*   Nissan
*   Hyundai
*   Kia
*   Mazda
*   Lexus
*   Acura
*   Infiniti
*   Genesis
*   Cadillac
*   Buick
*   GMC
*   Dodge
*   Jeep
*   Chrysler
*   Ram
*   Alfa Romeo
*   Fiat
*   Mini
*   Volvo
*   Saab
*   Peugeot
*   Citroën
*   Renault
*   Skoda
*   SEAT
*   Suzuki
*   Mitsubishi
*   Isuzu
*   Daihatsu
*   Tata Motors
*   Mahindra
*   BYD
*   Geely
*   Great Wall Motors
*   Chery
*   NIO
*   Polestar
*   Lucid Motors
*   Rivian
*   Bugatti
*   Koenigsegg
*   Pagani
*   McLaren
*   Rolls-Royce
*   Bentley
*   Smart
*   Scion
*   Hummer
*   Pontiac
*   Oldsmobile
*   Saturn
*   Dacia
*   Proton
*   Perodua
*   SsangYong
*   Lada
*   ZAZ
*   Moskvitch
*   GAZ
*   UAZ
*   Daewoo
*   Opel
*   Vauxhall
*   Holden
*   Rover
*   MG
*   Lotus
*   Fisker
*   DeLorean
*   Shelby
*   Saleen
*   Ariel
*   Caterham
*   Morgan
*   TVR
*   Wiesmann
*   Pininfarina
*   Rimac
*   Cupra
*   Baojun
*   Roewe
*   Lynk & Co
*   VinFast
*   Faraday Future
*   Lordstown Motors



## 🛠️ Technical Considerations
-   **Logo Source Management**: Establish a reliable and scalable way to store and retrieve logos. Consider a folder in `/public/assets/brand-logos/` or a simple JSON mapping keywords to SVG paths/URLs.
-   **API Rate Limits**: If using external APIs for logos, manage rate limits and implement caching.
-   **Performance**: Ensure logo detection and fetching doesn't significantly slow down thumbnail generation.
-   **Extensibility**: Design the system to easily add new brands and keywords.
-   **Conflict Resolution**: Define logic for when multiple variations of a brand name are used (e.g., "Photoshop" vs. "Adobe Photoshop").

## ✅ Acceptance Criteria
-   When "Include Icons" is on, and a prompt mentions "Figma tutorial", the Figma logo appears on the thumbnail.
-   When "Include Icons" is on, and a prompt mentions "Webflow vs Framer", both logos appear in a balanced comparison layout.
-   If a logo isn't available, a generic relevant icon is used as a fallback, or no icon if a generic one isn't suitable.
-   Logos are clear, correctly proportioned, and don't clutter the main subject.
-   The system can be easily updated with new brand logos and keywords.

---

This detailed prompt should give a clear direction for implementing this fantastic feature! Let me know if you'd like any part of it refined further.