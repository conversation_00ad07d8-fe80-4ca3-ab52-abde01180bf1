// imgbbUploader.js - Lightweight helper to proxy images through imgbb for universal accessibility
// NOTE: This helper is browser-safe: it uses the Fetch API + FormData. No external deps.
// If the image URL is inaccessible due to CORS or hosting restrictions, proxying via imgbb
// makes it publicly reachable at a direct https://i.ibb.co/... link that is face-swap compatible.

const IMGBB_API_KEY = '4b2adc0937af7c5224bd30f33e12781e'; // Provided by user – OK for client demo
const IMGBB_ENDPOINT = `https://api.imgbb.com/1/upload?key=${IMGBB_API_KEY}`;

/**
 * Uploads a remote image URL to imgbb and returns the new direct URL.
 * @param {string} imageUrl - The original image URL (can be remote URL or data URL)
 * @param {number} [expiration] - Optional expiration in seconds (60–15552000)
 * @returns {Promise<string>} - Resolves with the imgbb direct image URL
 */
export async function uploadImageToImgbb(imageUrl, expiration = 0) {
  try {
    if (!imageUrl || typeof imageUrl !== 'string') {
      throw new Error('Image URL required for imgbb upload');
    }

    const formData = new FormData();
    formData.append('image', imageUrl);
    if (expiration > 0) {
      formData.append('expiration', String(expiration));
    }

    const response = await fetch(`${IMGBB_ENDPOINT}${expiration > 0 ? `&expiration=${expiration}` : ''}`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`imgbb upload failed: ${response.status}`);
    }

    const json = await response.json();
    if (json && json.success && json.data && json.data.url) {
      return json.data.url; // Direct link is under data.url
    }

    throw new Error('Unexpected imgbb response');
  } catch (err) {
    console.error('uploadImageToImgbb error:', err);
    throw err;
  }
} 