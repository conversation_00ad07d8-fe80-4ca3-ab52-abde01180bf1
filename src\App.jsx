import React, { useEffect, useState, useRef, Fragment, useCallback } from 'react'
import ReactDOM from 'react-dom'

// CSS files are imported in main.jsx

// Import Geist font utilities
import { loadGeistFontsWithFallback } from './utils/geistFonts.js';
import { sanitizeOverlayText, validateSanitizedText } from './utils/textSanitizer.js';
import CollapsibleControlPanel from './components/ControlPanel.jsx'; // NEW: external collapsible sidebar component
import { ConfirmationModal } from './components/ui/ConfirmationModal.jsx'; // Import confirmation modal
import SubscriptionCheckoutModal from './components/ui/SubscriptionCheckoutModal.jsx'; // Import subscription checkout modal
import { ThumbnailPreviewModal } from './components/ui/ThumbnailPreviewModal.jsx'; // Import thumbnail preview modal
import { ImageRequirementsCarousel } from './components/ui/ImageRequirementsCarousel.jsx'; // Import image requirements carousel

// Note: CSS styles are now imported via main.jsx

// Helper function to create an info icon using Solar Icon Set via Iconify
const createInfoIcon = (className = 'w-4 h-4 text-gray-400 hover:text-yellow-400 transition-colors cursor-help focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-gray-900 rounded') => {
    return React.createElement('span', {
        className: className,
        role: 'img',
        'aria-label': 'Information',
        dangerouslySetInnerHTML: {
            __html: `<span class="iconify" data-icon="solar:info-circle-linear" style="color: currentColor;"></span>`
        }
    });
};

// ================= Configuration and Constants =================

// ... existing code ...

// Import API key
import { OPENAI_API_KEY } from '/config.js';
import { buildPrompt } from './utils/promptFormatter.js'; // UPDATED: Correct relative path
import { findPlaceholders } from './utils/textUtils.js'; // Import findPlaceholders from utility file
import { enhancePrompt } from './utils/promptEnhancer.js'; // Fallback enhancer (regex based)
import { getSmartTitle, getEnhancedPromptWithEngagement } from './utils/openaiPromptEnhancer.js'; // NEW: OpenAI powered enhancer with engagement variations
import { suggestedPrompts, startTypewriter, prefersReducedMotion, startImproveTypewriter } from './utils/typewriterSuggestions.js'; // NEW: Typewriter animation
import { generateThumbnailPromptVariations } from './utils/promptVariations.js'; // NEW: Prompt variations
import { generateSmartTextSuggestion } from './utils/smartTextAnalyzer.js'; // NEW: Smart text overlay suggestions
import { emergencyCleanup, checkStorageUsage } from './utils/emergencyCleanup.js'; // NEW: Emergency localStorage cleanup
import { resizeImageTo1280x720, downloadThumbnailAt1280x720 } from './utils/imageUtils.js'; // NEW: Consistent image processing and download utilities
import { saveGeneration } from './utils/supabaseHistoryManager.js'; // NEW: Supabase generation history
// Demo functionality removed - now using real authentication

// ADD IMPORT FOR ADMIN DASHBOARD
import { UserDashboard } from './components/UserDashboard.jsx';
import { NotificationSystem, NotificationBadge } from './components/NotificationSystem.jsx';
import { TemplatePreviewImage } from './components/TemplatePreviewImage.jsx';
import { ToastProvider, ToastContainer, useToast } from './contexts/ToastContext.jsx';

// Import Supabase client for session management
import { supabase } from './utils/supabase.mjs';


// IMPORT TEMPLATE ARRAYS

import techTemplates from '../Templates/Tech.js';
import gamingTemplates from '../Templates/Gaming.js';
import vloggingTemplates from '../Templates/Vlogging.js';
import reactionTemplates from '../Templates/Reaction.js';
import fitnessTemplates from '../Templates/fitness.js';

// ================= PREMADE TEMPLATES DATA =================
const premadeTemplatesData = [
    {
        id: "travel-vlog",
        name: "Travel & Vlog",
        categoryImagePlaceholder: { text: "Travel & Vlog", bgColor: "bg-orange-600" },
        unsplashImage: "https://images.unsplash.com/photo-1488646953014-85cb44e25828?auto=format&fit=crop&w=800&q=80",
        templates: vloggingTemplates
    },
    {
        id: "health-fitness",
        name: "Health Fitness",
        categoryImagePlaceholder: { text: "Health Fitness", bgColor: "bg-green-600" },
        unsplashImage: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=800&q=80",
        templates: [...fitnessTemplates]
    },
    {
        id: "business",
        name: "Business",
        categoryImagePlaceholder: { text: "Business", bgColor: "bg-blue-600" },
        unsplashImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "business-success",
                name: "How I Made $[AMOUNT] in [TIME]",
                description: "For business success and entrepreneurship content.",
                promptBase: "Create a professional YouTube thumbnail for 'How I Made $5000 in Day'. Show a confident entrepreneur with money/success symbols. Text overlay: 'MADE $[AMOUNT]'. Use professional blue and gold colors.",
                settingsToApply: { 
                    includePerson: true, 
                    selectedExpression: 'Confident', 
                    textOverlay: true, 
                    userPromptFocus: "How I Made $[AMOUNT] in [TIME]",
                    overlayText: "MADE\n$[AMOUNT]\nIN [TIME]"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-success.webp" }
            },
            {
                id: "crypto-explosion",
                name: "Crypto Explosion",
                description: "Bitcoin and Ethereum coins, glowing green arrow, and stock chart. Great for crypto growth videos.",
                promptBase: "Create a cinematic YouTube thumbnail with Bitcoin and Ethereum coins glowing, upward green arrow, stock market candlestick chart background, dramatic lighting with deep shadows and bright highlights.",
                settingsToApply: {
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "CRYPTO\nEXPLOSION!",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Impact",
                    userPromptFocus: "Crypto Explosion"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/crypto-explosion.webp" }
            },
            {
                id: "dropshipping-secrets",
                name: "Dropshipping Secrets",
                description: "Reveal the hidden strategies of successful dropshipping businesses.",
                promptBase: "Design a cinematic YouTube thumbnail showing packages, shipping boxes, and money flow graphics with vibrant colors and dramatic lighting effects.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Thinking',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "DROPSHIPPING\nSECRETS\nREVEALED",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Oswald",
                    userPromptFocus: "Dropshipping Secrets"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/dropshipping-secrets.webp" }
            },
            {
                id: "money-mindset",
                name: "Money Mindset",
                description: "Transform your relationship with money and wealth building.",
                promptBase: "Create a powerful YouTube thumbnail with brain imagery, golden light effects, and wealth symbols, using rich textures and cinematic composition.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "MONEY\nMINDSET\nSECRETS",
                    selectedTextSize: "Medium",
                    selectedFontFamily: "Roboto Condensed",
                    userPromptFocus: "Money Mindset Transformation"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/money-mindset.webp" }
            },
            {
                id: "passive-income",
                name: "Passive Income Stream",
                description: "Build automated income sources that work while you sleep.",
                promptBase: "Generate a cinematic thumbnail showing money flowing like a river, automation symbols, and sleeping person silhouette with glowing income streams.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Happy',
                    includeIcons: false,
                    textOverlay: true,
                    overlayText: "PASSIVE\nINCOME\nSTREAM",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Montserrat",
                    userPromptFocus: "Passive Income Building"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/passive-income.webp" }
            },
            {
                id: "business-vs-job",
                name: "Business vs Job",
                description: "Compare entrepreneurship with traditional employment paths.",
                promptBase: "Design a split-screen cinematic thumbnail showing business owner on left and employee on right, with contrasting lighting and lifestyle elements.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Confident',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "BUSINESS\nvs\nJOB",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Impact",
                    userPromptFocus: "Business vs Job Comparison"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-vs-job.webp" }
            },
            {
                id: "startup-failure",
                name: "Why Startups Fail",
                description: "Analyze common mistakes that lead to business failure.",
                promptBase: "Create a dramatic thumbnail with crashing graphs, warning symbols, and dark atmosphere with red accent lighting showing business collapse.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "WHY\nSTARTUPS\nFAIL",
                    selectedTextSize: "Medium",
                    selectedFontFamily: "Oswald",
                    userPromptFocus: "Startup Failure Analysis"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/startup-failure.webp" }
            },
            {
                id: "amazon-fba",
                name: "Amazon FBA Success",
                description: "Master the Amazon fulfillment business model.",
                promptBase: "Generate a cinematic thumbnail featuring Amazon boxes, fulfillment center imagery, and profit arrows with high-contrast orange and blue lighting.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Proud',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "AMAZON\nFBA\nSUCCESS",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Roboto Condensed",
                    userPromptFocus: "Amazon FBA Business"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/amazon-fba.webp" }
            },
            {
                id: "business-automation",
                name: "Business Automation",
                description: "Automate your business processes for maximum efficiency.",
                promptBase: "Design a futuristic thumbnail with robotic arms, gear mechanisms, and automation workflows with blue and silver metallic textures.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "BUSINESS\nAUTOMATION\nSECRETS",
                    selectedTextSize: "Medium",
                    selectedFontFamily: "Montserrat",
                    userPromptFocus: "Business Automation"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-automation.webp" }
            },
            {
                id: "sales-funnel",
                name: "Sales Funnel Mastery",
                description: "Build high-converting sales funnels that generate revenue.",
                promptBase: "Create a cinematic thumbnail showing a literal funnel with customers flowing through stages, conversion symbols, and money coming out the bottom.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Thinking',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "SALES\nFUNNEL\nMASTERY",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Impact",
                    userPromptFocus: "Sales Funnel Strategy"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/sales-funnel.webp" }
            },
            {
                id: "online-course",
                name: "Online Course Empire",
                description: "Build a profitable online education business.",
                promptBase: "Generate a thumbnail with graduation cap, digital screens showing course content, and students learning with golden educational lighting.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Happy',
                    includeIcons: false,
                    textOverlay: true,
                    overlayText: "ONLINE\nCOURSE\nEMPIRE",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Oswald",
                    userPromptFocus: "Online Course Business"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/online-course.webp" }
            },
            {
                id: "real-estate-investing",
                name: "Real Estate Wealth",
                description: "Build wealth through strategic real estate investments.",
                promptBase: "Design a cinematic thumbnail with luxury properties, keys, and upward trending property value graphs with rich golden and deep blue tones.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Confident',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "REAL ESTATE\nWEALTH\nSECRETS",
                    selectedTextSize: "Medium",
                    selectedFontFamily: "Roboto Condensed",
                    userPromptFocus: "Real Estate Investment"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/real-estate-investing.webp" }
            },
            {
                id: "side-hustle",
                name: "Ultimate Side Hustle",
                description: "Start profitable side businesses while keeping your day job.",
                promptBase: "Create a split-time thumbnail showing person working day job and night hustle with clock elements and money symbols in vibrant contrasting lighting.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Proud',
                    includeIcons: true,
                    textOverlay: true,
                    overlayText: "ULTIMATE\nSIDE\nHUSTLE",
                    selectedTextSize: "Large",
                    selectedFontFamily: "Montserrat",
                    userPromptFocus: "Side Hustle Ideas"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/side-hustle.webp" }
            }
        ]
    },
    {
        id: "tech",
        name: "Tech",
        categoryImagePlaceholder: { text: "Tech", bgColor: "bg-blue-500" },
        unsplashImage: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80",
        templates: techTemplates
    },
    {
        id: "reactions",
        name: "Reactions",
        categoryImagePlaceholder: { text: "Reactions", bgColor: "bg-pink-500" },
        unsplashImage: "https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=800&q=80",
        templates: reactionTemplates
    },
    {
        id: "gaming",
        name: "Gaming",
        categoryImagePlaceholder: { text: "Gaming", bgColor: "bg-green-500" },
        unsplashImage: "https://images.unsplash.com/photo-**********-adc38448a05e?auto=format&fit=crop&w=800&q=80",
        templates: gamingTemplates
    }
];

// ================= EXPANDED TEMPLATE CATEGORIES =================
// These categories will be shown in the "Show More" modal
const expandedTemplatesData = [
    {
        id: "business",
        name: "Business",
        description: "Professional, persuasive templates for business content",
        categoryImagePlaceholder: { text: "Business", bgColor: "bg-blue-800" },
        unsplashImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "business-pitch-deck",
                name: "Pitch Deck Secrets",
                description: "Eye-catching slide for startup pitches.",
                promptBase: "Create a professional YouTube thumbnail for 'Pitch Deck Secrets'. Show a confident presenter with a slick visual presentation in the background. Text overlay: 'PITCH DECK SECRETS'. Use a modern corporate style with blue and gray color scheme.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Confident',
                    textOverlay: true,
                    userPromptFocus: "Pitch Deck Secrets",
                    overlayText: "PITCH DECK\nSECRETS"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-pitch-deck.webp" }
            },
            {
                id: "business-market-trends",
                name: "Market Trends 2024",
                description: "Data-driven, modern analytics look.",
                promptBase: "Generate a YouTube thumbnail for 'Market Trends 2024'. Feature data visualizations, charts, or graphs with an upward trend. Text overlay: 'MARKET TRENDS 2024'. Use a modern, professional style with a blue gradient background.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    userPromptFocus: "Market Trends 2024",
                    overlayText: "MARKET\nTRENDS 2024"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-market-trends.webp" }
            },
            {
                id: "business-remote-team",
                name: "Remote Team Success",
                description: "Friendly, collaborative team vibe.",
                promptBase: "Design a YouTube thumbnail for 'Remote Team Success'. Show a diverse team in a virtual meeting, looking productive and happy. Text overlay: 'REMOTE TEAM SUCCESS'. Use a friendly, collaborative style with warm colors.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Happy',
                    textOverlay: true,
                    userPromptFocus: "Remote Team Success",
                    overlayText: "REMOTE TEAM\nSUCCESS"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/business/business-remote-team.webp" }
            }
        ]
    },
    {
        id: "health-fitness",
        name: "Health & Fitness",
        description: "Energetic, motivational templates for health and fitness content",
        categoryImagePlaceholder: { text: "Fitness", bgColor: "bg-green-800" },
        unsplashImage: "https://images.unsplash.com/photo-1517836357463-d25dfeac3438?auto=format&fit=crop&w=800&q=80",
        templates: [...fitnessTemplates]
    },
    {
        id: "travel",
        name: "Travel",
        description: "Scenic, adventurous templates for travel content",
        categoryImagePlaceholder: { text: "Travel", bgColor: "bg-yellow-800" },
        unsplashImage: "https://images.unsplash.com/photo-1503220317375-aaad61436b1b?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "travel-hidden-gems",
                name: "Hidden Gems",
                description: "Scenic, off-the-beaten-path locations.",
                promptBase: "Create a stunning YouTube thumbnail for 'Hidden Gems'. Show a breathtaking landscape or secret spot that looks magical and undiscovered. Text overlay: 'HIDDEN GEMS'. Use a dreamy, cinematic style with vibrant colors.",
                settingsToApply: {
                    includePerson: false,
                    textOverlay: true,
                    userPromptFocus: "Hidden Gems - Travel Secrets",
                    overlayText: "HIDDEN\nGEMS"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/travel/hidden-gems.webp" }
            },
            {
                id: "travel-packing-list",
                name: "Ultimate Packing List",
                description: "Organized, checklist style for travelers.",
                promptBase: "Design a YouTube thumbnail for 'Ultimate Packing List'. Show neatly organized travel items or suitcase with essentials. Text overlay: 'ULTIMATE PACKING LIST'. Use a clean, organized style with travel-themed colors.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    userPromptFocus: "Ultimate Packing List",
                    overlayText: "ULTIMATE\nPACKING LIST"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/travel/packing-list.webp" }
            },
            {
                id: "travel-city-vs-nature",
                name: "City vs. Nature",
                description: "Split-screen comparison for travelers.",
                promptBase: "Generate a YouTube thumbnail for 'City vs. Nature'. Create a split-screen effect showing urban skyline on one side and natural landscape on the other. Text overlay: 'CITY vs. NATURE'. Use a dramatic, contrasting style.",
                settingsToApply: {
                    includePerson: false,
                    textOverlay: true,
                    userPromptFocus: "City vs. Nature - Travel Comparison",
                    overlayText: "CITY vs.\nNATURE"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/travel/city-vs-nature.webp" }
            }
        ]
    },
    {
        id: "finance",
        name: "Finance",
        description: "Trustworthy, informative templates for financial content",
        categoryImagePlaceholder: { text: "Finance", bgColor: "bg-green-700" },
        unsplashImage: "https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "finance-saved-10k",
                name: "How I Saved $10,000",
                description: "Relatable, personal finance success.",
                promptBase: "Create a YouTube thumbnail for 'How I Saved $10,000'. Show a relatable person looking happy with money/savings visual cues. Text overlay: 'HOW I SAVED $10,000'. Use a trustworthy, positive style with green accents.",
                settingsToApply: {
                    includePerson: true,
                    selectedExpression: 'Happy',
                    textOverlay: true,
                    userPromptFocus: "How I Saved $10,000",
                    overlayText: "HOW I SAVED\n$10,000"
                },
                templateImagePlaceholder: { text: "Saved $10k", bgColor: "bg-green-700" }
            },
            {
                id: "finance-crypto-explained",
                name: "Crypto Explained",
                description: "Futuristic, techy cryptocurrency guide.",
                promptBase: "Design a YouTube thumbnail for 'Crypto Explained'. Feature crypto symbols or blockchain visualization with a futuristic tech aesthetic. Text overlay: 'CRYPTO EXPLAINED'. Use a modern, tech-focused style with blue lighting.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    userPromptFocus: "Crypto Explained",
                    overlayText: "CRYPTO\nEXPLAINED"
                },
                templateImagePlaceholder: { text: "Crypto", bgColor: "bg-green-700" }
            },
            {
                id: "finance-budgeting-101",
                name: "Budgeting 101",
                description: "Simple, approachable financial basics.",
                promptBase: "Generate a YouTube thumbnail for 'Budgeting 101'. Show simple budget visuals or money-saving concepts in an approachable way. Text overlay: 'BUDGETING 101'. Use a clean, beginner-friendly style with calming colors.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    userPromptFocus: "Budgeting 101",
                    overlayText: "BUDGETING\n101"
                },
                templateImagePlaceholder: { text: "Budgeting 101", bgColor: "bg-green-700" }
            }
        ]
    },
    {
        id: "art-design",
        name: "Art & Design",
        description: "Creative, artistic templates for design content",
        categoryImagePlaceholder: { text: "Art & Design", bgColor: "bg-purple-800" },
        unsplashImage: "https://images.unsplash.com/photo-1513364776144-60967b0f800f?auto=format&fit=crop&w=800&q=80",
        templates: [
            {
                id: "design-speedpaint",
                name: "Speedpaint Timelapse",
                description: "Colorful, creative art process.",
                promptBase: "Create a YouTube thumbnail for 'Speedpaint Timelapse'. Show vibrant artwork in progress with art supplies visible. Text overlay: 'SPEEDPAINT TIMELAPSE'. Use a colorful, artistic style with paint splatter effects.",
                settingsToApply: {
                    includePerson: false,
                    textOverlay: true,
                    userPromptFocus: "Speedpaint Timelapse",
                    overlayText: "SPEEDPAINT\nTIMELAPSE"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/art-design/speedpaint-timelapse.webp" }
            },
            {
                id: "design-logo-tips",
                name: "Logo Design Tips",
                description: "Minimal, professional design advice.",
                promptBase: "Design a YouTube thumbnail for 'Logo Design Tips'. Feature clean, minimal logo design examples or process sketch. Text overlay: 'LOGO DESIGN TIPS'. Use a professional, designer-focused style with grid background.",
                settingsToApply: {
                    includePerson: false,
                    includeIcons: true,
                    textOverlay: true,
                    userPromptFocus: "Logo Design Tips",
                    overlayText: "LOGO DESIGN\nTIPS"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/art-design/logo-design-tips.webp" }
            },
            {
                id: "design-before-after",
                name: "Before & After",
                description: "Transformation, side-by-side comparison.",
                promptBase: "Generate a YouTube thumbnail for 'Before & After'. Create a split screen showing dramatic before/after transformation of a design project. Text overlay: 'BEFORE & AFTER'. Use a high-contrast, impactful style highlighting the difference.",
                settingsToApply: {
                    includePerson: false,
                    textOverlay: true,
                    userPromptFocus: "Before & After Design Transformation",
                    overlayText: "BEFORE\n& AFTER"
                },
                templateImagePlaceholder: { backgroundImage: "/background-templates/art-design/before-after.webp" }
            }
        ]
    }
];

// ================= Constants =================
// Color palette presets for text overlays
const TEXT_COLOR_PALETTES = [
    { id: 'default', name: 'Default', colors: ['#FFFFFF', '#FFFF00'], category: 'basic' },
    { id: 'glacier', name: 'Glacier', colors: ['#7BDFF2', '#B2F7EF', '#EFFFE9'], category: 'cold' },
    { id: 'blueberry', name: 'Blueberry', colors: ['#1F8A70', '#004358', '#BEDB39'], category: 'cold' },
    { id: 'minty', name: 'Minty', colors: ['#A9FBD7', '#CBF1F5', '#D6FFF6'], category: 'cold' },
    { id: 'coldspring', name: 'Cold Spring', colors: ['#A3F7BF', '#5EDFFF', '#B967FF'], category: 'cold' },
    { id: 'deepsea', name: 'Deep Sea', colors: ['#1D1E2C', '#3D426B', '#8797AF', '#C3CBDC'], category: 'cold' },
    { id: 'fiery', name: 'Fiery', colors: ['#FF5E5B', '#FFED66', '#FFA45B'], category: 'warm' },
    { id: 'sunset', name: 'Sunset', colors: ['#FF9F1C', '#FFBF69', '#FFF7F8'], category: 'warm' },
    { id: 'neon', name: 'Neon', colors: ['#F72585', '#4CC9F0', '#4361EE'], category: 'gaming' },
    { id: 'corporate', name: 'Corporate', colors: ['#003049', '#FCBF49', '#EAE2B7'], category: 'professional' }
];



// Cinematic style suffix applied to all premade templates
const CINEMATIC_SUFFIX = " Use a cinematic composition with dynamic camera angles, rich layered textures, vibrant high-contrast colours, dramatic lighting (deep shadows + glowing highlights), and engaging props/icons that emphasise the topic.";

// ================= Inline Components (avoid dynamic import issues) =================

const PromptInput = ({ displayValue, onChange, isLocked, onImprovePrompt, onPromptVariations, isTyping, cursorVisible, isImprovingPrompt, improvingAnimationText, showVariations, isVariationsClosing, variations, onSelectVariation, isLoading = false, isGeneratingVariations = false, variationsLoadingMessage = '' }) => {
    // Add React hooks for typewriter animation
    const [placeholderText, setPlaceholderText] = React.useState('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
    const typewriterRef = React.useRef(null);
    const inputRef = React.useRef(null);
    const [isTypewriterActive, setIsTypewriterActive] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);

    // Auto typewriter animation logic
    React.useEffect(() => {
        // Only run auto typewriter if not improving, not focused, not typing, and input is empty
        if (!isImprovingPrompt && !isFocused && !isTyping && !displayValue && !isLocked) {
            setIsTypewriterActive(true);
            const enableSounds = localStorage.getItem('typewriter-sounds') === 'true';
            typewriterRef.current = startTypewriter(
                inputRef.current,
                suggestedPrompts,
                setPlaceholderText,
                enableSounds
            );
        } else {
            setIsTypewriterActive(false);
            if (typewriterRef.current) typewriterRef.current.stop();
        }
        return () => {
            if (typewriterRef.current) typewriterRef.current.stop();
        };
    }, [isImprovingPrompt, isFocused, isTyping, displayValue, isLocked]);

    // Stop animation when user types or when input changes
    React.useEffect(() => {
        if (displayValue && typewriterRef.current) {
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
            // Reset to default placeholder
            setPlaceholderText('e.g., A surprised cat reacting to a cucumber, dramatic lighting');
        }
    }, [displayValue]);

    // Stop animation when input is locked or typing
    React.useEffect(() => {
        if ((isLocked || isTyping) && typewriterRef.current) {
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
        }
    }, [isLocked, isTyping]);

    // Handle focus events to control animation
    const handleFocus = () => {
        if (typewriterRef.current && !displayValue) {
            // Pause animation while focused if empty
            typewriterRef.current.stop();
            setIsTypewriterActive(false);
        }
        setIsFocused(true);
    };

    const handleBlur = () => {
        if (!displayValue && !isLocked && !isTyping && !prefersReducedMotion()) {
            // Restart animation on blur if still empty
            if (inputRef.current) {
                setIsTypewriterActive(true);
                // Enable sound effects on user interaction (optional)
                const enableSounds = localStorage.getItem('typewriter-sounds') === 'true';
                typewriterRef.current = startTypewriter(
                    inputRef.current,
                    suggestedPrompts,
                    setPlaceholderText,
                    enableSounds
                );
            }
        }
        setIsFocused(false);
    };

    // Dynamic classes based on typewriter state
    const getTextareaClasses = () => {
        const baseClasses = 'prompt-textarea transition-all duration-300 ease-in-out text-base tracking-wide';
        const monoClasses = isImprovingPrompt || isTyping ? 'font-mono' : '';
        const improvingClasses = isImprovingPrompt ? 'improving-typewriter' : '';
        const stateClasses = isLocked ? 'opacity-70 cursor-not-allowed' : '';
        return `${baseClasses} ${monoClasses} ${improvingClasses} ${stateClasses}`.trim();
    };

    return (
        React.createElement('div', { className: 'w-full relative prompt-input-container' },
            React.createElement('label', {
                htmlFor: 'userPromptInput',
                className: 'block text-sm font-medium text-gray-300 mb-3'
            }, 'Enter Your Thumbnail Prompt:'),

            // Enhanced container for wider, more cinematic feel
            React.createElement('div', {
                className: 'prompt-input-wrapper relative'
            },
            React.createElement('textarea', {
                id: 'userPromptInput',
                ref: inputRef,
                className: getTextareaClasses(),
                rows: '4',
                placeholder: isImprovingPrompt ? '' : placeholderText,
                value: isImprovingPrompt ? improvingAnimationText + (cursorVisible ? '|' : '') : (isTyping ? displayValue + (cursorVisible ? '|' : '') : displayValue),
                onChange: isLoading ? undefined : onChange,
                onFocus: isLoading ? undefined : handleFocus,
                onBlur: isLoading ? undefined : handleBlur,
                disabled: isLocked || isLoading,
                'aria-label': isLoading ? 'Prompt input disabled during generation' : 'Enter your thumbnail prompt here',
                'aria-disabled': isLocked || isLoading,
                title: isLoading ? 'Please wait for generation to complete' : 'Enter your thumbnail prompt here',
                style: {
                    maxWidth: '100%',
                    width: '100%',
                    resize: 'none',
                    fontFamily: 'monospace',
                    opacity: isLoading ? 0.5 : 1,
                    cursor: isLoading ? 'not-allowed' : 'text'
                }
            }),
            
            // Cinematic glow effect during typewriter animation
            isTypewriterActive && React.createElement('div', {
                className: 'absolute inset-0 rounded-lg pointer-events-none',
                style: {
                    background: 'linear-gradient(45deg, transparent, rgba(139, 92, 246, 0.05), transparent)',
                    animation: 'pulse 3s ease-in-out infinite'
                }
            }),
            
            // Button container for both buttons
            React.createElement('div', {
                className: 'prompt-action-buttons absolute bottom-4 right-3 flex items-center gap-2',
                id: 'promptActionButtons'
            },
                // Variations button (first in order, show only for prompts with >=5 words)
                (displayValue && displayValue.trim().split(/\s+/).length >= 5) && React.createElement('div', {
                    className: 'relative group',
                    id: 'variationsButtonContainer',
                    style: { width: '36px', height: '36px' }
                },
                    React.createElement('button', {
                        type: 'button',
                        onClick: (isLocked || isTyping || isImprovingPrompt || isLoading || isGeneratingVariations) ? undefined : () => onPromptVariations && onPromptVariations(),
                        className: `prompt-variations-btn w-full h-full rounded-full transition-colors duration-200 backdrop-filter backdrop-blur-[8px] shadow-[0_2px_8px_rgba(0,0,0,0.15)] flex items-center justify-center ${
                            isLocked || isTyping || isImprovingPrompt || isLoading || isGeneratingVariations
                                ? 'bg-gray-750/90 border border-gray-700 text-gray-500 cursor-not-allowed opacity-50'
                                : `bg-gray-700/90 border border-gray-600 hover:border-blue-500/50 ${isGeneratingVariations ? 'text-purple-400' : 'text-gray-400 hover:text-purple-300'} focus:outline-none hover:bg-purple-500/10`
                        }`,
                        disabled: isLocked || isTyping || isImprovingPrompt || isLoading || isGeneratingVariations,
                        'aria-label': isLoading ? 'Prompt variations disabled during generation' : isGeneratingVariations ? 'Generating prompt variations...' : 'Get prompt variations',
                        id: 'promptVariationsBtn'
                    },
                        React.createElement('span', {
                            className: `variations-btn-icon iconify ${isGeneratingVariations ? 'animate-spin' : ''}`,
                            id: 'variations-btn-icon',
                            'data-icon': isGeneratingVariations ? 'solar:refresh-bold' : 'solar:file-text-linear',
                            style: { width: '18px', height: '18px' }
                        })
                    ),
                    // Tooltip
                    React.createElement('div', {
                        className: 'absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-150 delay-100 pointer-events-none z-20 shadow-lg border border-gray-700',
                        id: 'variationsTooltip'
                    }, isLoading ? 'Please wait...' : isGeneratingVariations ? 'Generating variations...' : 'Suggest Variants')
                ),
                
                // Improve Prompt button (second in order)
                React.createElement('div', {
                    className: 'relative group',
                    id: 'improveButtonContainer',
                    style: { width: '36px', height: '36px' }
                },
                    React.createElement('button', {
                        type: 'button',
                        onClick: (isLocked || isTyping || isImprovingPrompt || isLoading) ? undefined : () => onImprovePrompt(),
                        className: `prompt-improve-btn w-full h-full rounded-full transition-colors duration-200 backdrop-filter backdrop-blur-[8px] shadow-[0_2px_8px_rgba(0,0,0,0.15)] flex items-center justify-center ${
                            isLocked || isTyping || isImprovingPrompt || isLoading
                                ? 'bg-gray-750/90 border border-gray-700 text-gray-500 cursor-not-allowed opacity-50'
                                : `bg-gray-700/90 border border-gray-600 hover:border-blue-500/50 ${isImprovingPrompt ? 'text-purple-400' : 'text-gray-400 hover:text-purple-300'} focus:outline-none hover:bg-purple-500/10`
                        }`,
                        disabled: isLocked || isTyping || isImprovingPrompt || isLoading,
                        'aria-label': isLoading ? 'Improve prompt disabled during generation' : 'Improve prompt with AI',
                        id: 'promptImproveBtn'
                    },
                        React.createElement('span', {
                            className: 'improve-btn-icon iconify',
                            id: 'improve-btn-icon',
                            'data-icon': 'solar:stars-minimalistic-outline',
                            style: { width: '18px', height: '18px'  }
                        })
                    ),
                    // Tooltip
                    React.createElement('div', {
                        className: 'absolute bottom-full mb-2 right-0 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-150 delay-100 pointer-events-none z-20 shadow-lg border border-gray-700',
                        id: 'improveTooltip'
                    }, isLoading ? 'Please wait...' : 'Improve Prompt')
                )
            ),
            ),
            
            // Enhanced Variations Modal (Premium Design)
            showVariations && React.createElement('div', {
                className: `prompt-variations-overlay fixed inset-0 z-[1000] flex items-center justify-center px-4 py-4 bg-black/60 backdrop-blur-sm px-4 ${isVariationsClosing ? 'closing' : ''}`,
                id: 'promptVariationsOverlay',
                onClick: () => onSelectVariation && onSelectVariation(null),
                style: {
                    animation: isVariationsClosing ? 'fadeOut 200ms cubic-bezier(0.4, 0, 1, 1) forwards' : 'fadeIn 200ms cubic-bezier(0.16, 1, 0.3, 1) forwards'
                }
            },
                React.createElement('div', {
                    className: 'prompt-variations-modal bg-gray-800/95 border border-purple-500/20 rounded-2xl shadow-2xl p-4 md:p-6 w-full max-w-2xl backdrop-blur-xl',
                    id: 'promptVariationsModal',
                    onClick: (e) => e.stopPropagation(),
                    style: {
                        animation: 'slideInUp 200ms cubic-bezier(0.16, 1, 0.3, 1) forwards',
                        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(139, 92, 246, 0.1)'
                    }
                },
                    // Header with gradient accent
                    React.createElement('div', {
                        className: 'flex items-center justify-between mb-4 md:mb-6 pb-3 md:pb-4 border-b border-gray-700/50'
                    },
                        React.createElement('div', {
                            className: 'flex items-center gap-3'
                        },
                            React.createElement('div', {
                                className: 'w-10 h-10 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-white',
                                    'data-icon': 'solar:magic-stick-3-bold-duotone',
                                    style: { fontSize: '20px' }
                                })
                            ),
                            React.createElement('div', {},
                        React.createElement('h3', {
                                    className: 'prompt-variations-title text-lg font-semibold text-white',
                            id: 'promptVariationsTitle'
                                }, variations && variations.length > 0 ? 'Enhanced Prompt Variations' : 'Generating Variations'),
                                React.createElement('p', {
                                    className: 'text-sm text-gray-400'
                                }, variations && variations.length > 0 ? `${variations.length} cinematic variations generated` : (variationsLoadingMessage || 'Preparing your variations...'))
                            )
                        ),
                        React.createElement('button', {
                            onClick: () => onSelectVariation && onSelectVariation(null),
                            className: 'p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 rounded-lg transition-all duration-200',
                            'aria-label': 'Close variations'
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:close-circle-linear',
                                style: { fontSize: '24px' }
                            })
                        )
                    ),
                    
                    // Variations Grid or Loading State
                    React.createElement('div', {
                        className: 'space-y-3 max-h-96 overflow-y-auto custom-scrollbar pr-2'
                    },
                        // Show loading state when no variations are available
                        (!variations || variations.length === 0) ? React.createElement('div', {
                            className: 'flex flex-col items-center justify-center py-12 px-4'
                        },
                            // Animated loading spinner
                            React.createElement('div', {
                                className: 'relative mb-6'
                            },
                                React.createElement('div', {
                                    className: 'w-16 h-16 border-4 border-purple-500/20 border-t-purple-500 rounded-full animate-spin'
                                }),
                                React.createElement('div', {
                                    className: 'absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-500/50 rounded-full animate-spin',
                                    style: { animationDirection: 'reverse', animationDuration: '1.5s' }
                                })
                            ),
                            
                            // Loading message
                            React.createElement('div', {
                                className: 'text-center'
                            },
                                React.createElement('p', {
                                    className: 'text-lg font-medium text-white mb-2 variations-loading-message'
                                }, variationsLoadingMessage || 'Generating ideas...'),
                                React.createElement('p', {
                                    className: 'text-sm text-gray-400'
                                }, 'This may take a few seconds...')
                            ),
                            
                            // Animated dots
                            React.createElement('div', {
                                className: 'loading-dots'
                            },
                                React.createElement('div', {
                                    className: 'loading-dot'
                                }),
                                React.createElement('div', {
                                    className: 'loading-dot'
                                }),
                                React.createElement('div', {
                                    className: 'loading-dot'
                                })
                            )
                        ) : 
                        // Show variations when available
                        variations.map((variation, index) => 
                            React.createElement('div', {
                                key: index,
                                className: 'prompt-variation-card group relative bg-gradient-to-r from-gray-700/30 to-gray-800/30 hover:from-purple-900/20 hover:to-blue-900/20 border border-gray-600/50 hover:border-blue-500/50 rounded-xl p-4 cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10',
                                onClick: () => onSelectVariation && onSelectVariation(variation),
                                style: {
                                    animation: `slideInUp ${200 + index * 50}ms cubic-bezier(0.16, 1, 0.3, 1) forwards`
                                }
                            },
                                // Variation Text (no numbered badge, more padding on right for button)
                                React.createElement('p', {
                                    className: 'text-sm text-gray-200 leading-relaxed pr-16 mb-3'
                                }, variation),
                                
                                // Use Button (outline style with check icon)
                                React.createElement('button', {
                                    className: 'prompt-variation-use-btn absolute right-3 bottom-3 border border-purple-500/50 hover:border-purple-400 text-purple-300 hover:text-purple-200 text-xs font-medium rounded-lg px-3 py-1.5 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-purple-500/10 flex items-center gap-1.5',
                                    onClick: (e) => {
                                        e.stopPropagation();
                                        onSelectVariation && onSelectVariation(variation);
                                    }
                                },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:check-circle-linear',
                                        style: { fontSize: '14px' }
                                    }),
                                    'Use'
                                ),
                                
                                // Hover Glow Effect
                                React.createElement('div', {
                                    className: 'absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'
                                })
                        )
                        )
                    ),
                    
                    // Footer with tip or loading info
                    React.createElement('div', {
                        className: 'mt-6 pt-4 border-t border-gray-700/50 flex items-center gap-2 text-xs text-gray-400'
                    },
                        React.createElement('span', {
                            className: `iconify ${(!variations || variations.length === 0) ? 'text-blue-400' : 'text-purple-400'}`,
                            'data-icon': (!variations || variations.length === 0) ? 'solar:clock-circle-linear' : 'solar:lightbulb-minimalistic-linear',
                            style: { fontSize: '16px' }
                        }),
                        (!variations || variations.length === 0) ? 
                            'AI is analyzing your prompt and generating creative variations...' :
                        'Each variation is enhanced with professional cinematic keywords for better results'
                    )
                )
            ),

            isLocked && React.createElement('div', {
                id: 'prompt-lock-tooltip',
                className: 'absolute top-2 right-2 p-2 bg-yellow-500 text-black text-xs rounded-md shadow-md flex items-center gap-1 group',
                role: 'note'
            },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:lock-keyhole-bold',
                    style: { width: '16px', height: '16px' }
                }),
                'Locked',
                React.createElement('div', {
                    className: 'absolute bottom-full right-0 mb-2 w-72 bg-gray-800 text-gray-100 text-xs rounded-md p-2 shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 z-50',
                    role: 'tooltip'
                }, 'Prompt is locked while a template is active. Use the controls in the sidebar to customize the thumbnail. Clear the template to edit this prompt.')
            )
        )
    );
};

const LegacyControlPanel = ({
    includePerson,
    includeIcons,
    textOverlay,
    selectedExpression,
    handleToggleChange,
    setIncludePerson,
    setIncludeIcons,
    setTextOverlay,
    fitFullCanvas,
    setFitFullCanvas,
    showLayoutSimulator,
    setShowLayoutSimulator,
    showSafeZone,
    setShowSafeZone,
    overlayText,
    setOverlayText,
    isEditingOverlayText,
    setIsEditingOverlayText,
    textPosition,
    setTextPosition,
    selectedTextSize,
    setSelectedTextSize,
    selectedFontFamily,
    setSelectedFontFamily,
    selectedGender,
    setSelectedGender,
    primaryTextColor,
    setPrimaryTextColor,
    secondaryTextColor,
    setSecondaryTextColor,
    selectedPalette,
    setSelectedPalette,
    setSelectedExpression,
    customFaceImageUrl,
    setCustomFaceImageUrl,
    imageSourceType,
    handleImageSourceTypeChange,
    handleFileUpload,
    setErrorMsg,
    openImageRequirementsModal,
    userPrompt,
    smartTextSuggestion
}) => {
    const createToggle = (label, id, checked, setter, tooltip = null) => {
        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };
        return React.createElement('div', { className: 'toggle-row flex items-center justify-between py-2', id: `toggle-${id}-row` },
            React.createElement('div', { className: 'flex items-center gap-2' },
                React.createElement('label', { htmlFor: id, className: 'toggle-label text-sm font-medium text-gray-300 cursor-pointer' }, label),
                tooltip && React.createElement('div', {
                    className: 'relative group',
                    tabIndex: 0,
                    'aria-label': 'Information about this setting',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltip)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('button', {
                id,
                role: 'switch',
                'aria-checked': checked,
                onClick: () => handleToggleChange(setter),
                onKeyDown: handleKeyDown,
                tabIndex: '0',
                className: `toggle-switch ${checked ? 'bg-blue-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900`
            },
                React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                React.createElement('span', {
                    'aria-hidden': 'true',
                    className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
                })
            )
        );
    };

    const createFaceUploadSection = () => {
        // Use a ref for the file input to programmatically clear it
        const fileInputRef = React.useRef(null);

        // State for showing image requirements modal
        const [showRequirementsModal, setShowRequirementsModal] = React.useState(false);

        const currentTempUrl = imageSourceType === 'url' ? customFaceImageUrl : '';
        const [tempUrl, setTempUrl] = React.useState(currentTempUrl);

        React.useEffect(() => {
            if (imageSourceType === 'url' && customFaceImageUrl && customFaceImageUrl.startsWith('data:')) {
                setTempUrl('');
            } else if (imageSourceType === 'url') {
                setTempUrl(customFaceImageUrl || '');
            }
        }, [imageSourceType, customFaceImageUrl]);

        const handleSetFaceImageFromUrl = () => {
            if (tempUrl.trim() && imageSourceType === 'url') {
                const urlTrimmed = tempUrl.trim();
                if (!/^https?:\/\//i.test(urlTrimmed)) {
                    setErrorMsg("Invalid image URL. Must start with http:// or https://");
                    return;
                }
                setCustomFaceImageUrl(tempUrl.trim());
                setErrorMsg('');
            }
        };

        const handleRemoveFaceImage = () => {
            setCustomFaceImageUrl('');
            setTempUrl('');
            if (fileInputRef.current) {
                fileInputRef.current.value = null;
            }
            setErrorMsg('');
            
            // Show success notification for face image deletion
            setSuccessToast({
                isVisible: true,
                message: 'Face image removed successfully!',
                type: 'success'
            });
            
            // Auto-dismiss the notification after 2.5 seconds
            setTimeout(() => {
                setSuccessToast(prev => ({ ...prev, isVisible: false }));
            }, 2500);
        };

        const handleUrlInputChange = (e) => {
            setTempUrl(e.target.value);
        };

        const handleFileUploadClick = () => {
            if (fileInputRef.current) {
                fileInputRef.current.click();
            }
        };

        // Image Requirements Modal Component
        const renderImageRequirementsModal = () => {
            if (!showRequirementsModal) return null;

            return React.createElement('div', {
                className: 'fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4',
                onClick: () => setShowRequirementsModal(false)
            },
                React.createElement('div', {
                    className: 'bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto',
                    onClick: (e) => e.stopPropagation()
                },
                    // Modal Header
                    React.createElement('div', {
                        className: 'sticky top-0 bg-gray-800 border-b border-gray-700 p-6 flex items-center justify-between'
                    },
                        React.createElement('h2', {
                            className: 'text-2xl font-semibold text-white'
                        }, 'Image Requirements for Best Results'),
                        React.createElement('button', {
                            onClick: () => setShowRequirementsModal(false),
                            className: 'p-2 hover:bg-gray-700 rounded-lg transition-colors'
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:close-circle-linear',
                                style: { fontSize: '24px', color: '#9CA3AF' }
                            })
                        )
                    ),

                    // Modal Content
                    React.createElement('div', {
                        className: 'p-6 space-y-8'
                    },
                        // Quick Guidelines Section
                        React.createElement('div', {
                            className: 'bg-blue-900/20 border border-blue-700/50 rounded-xl p-5'
                        },
                            React.createElement('h3', {
                                className: 'text-lg font-semibold text-blue-300 mb-3 flex items-center gap-2'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': 'solar:star-bold-duotone',
                                    style: { fontSize: '20px' }
                                }),
                                'Quick Guidelines'
                            ),
                            React.createElement('ul', {
                                className: 'space-y-2 text-sm text-gray-300'
                            },
                                React.createElement('li', { className: 'flex items-start gap-2' },
                                    React.createElement('span', { className: 'text-green-400 mt-0.5' }, '✓'),
                                    'Use a clear, front-facing photo with good lighting'
                                ),
                                React.createElement('li', { className: 'flex items-start gap-2' },
                                    React.createElement('span', { className: 'text-green-400 mt-0.5' }, '✓'),
                                    'Face should fill 60-80% of the image'
                                ),
                                React.createElement('li', { className: 'flex items-start gap-2' },
                                    React.createElement('span', { className: 'text-green-400 mt-0.5' }, '✓'),
                                    'Minimum resolution: 512x512 pixels (1024x1024 recommended)'
                                ),
                                React.createElement('li', { className: 'flex items-start gap-2' },
                                    React.createElement('span', { className: 'text-green-400 mt-0.5' }, '✓'),
                                    'Supported formats: JPEG, PNG (max 2MB)'
                                )
                            )
                        ),

                        // Good vs Bad Examples
                        React.createElement('div', null,
                            React.createElement('h3', {
                                className: 'text-lg font-semibold text-white mb-4'
                            }, 'Good vs Bad Examples'),
                            React.createElement('div', {
                                className: 'grid md:grid-cols-2 gap-6'
                            },
                                // Good Examples
                                React.createElement('div', {
                                    className: 'bg-green-900/10 border border-green-700/30 rounded-xl p-5'
                                },
                                    React.createElement('h4', {
                                        className: 'text-green-400 font-medium mb-3 flex items-center gap-2'
                                    },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:check-circle-bold-duotone',
                                            style: { fontSize: '20px' }
                                        }),
                                        'Good Examples'
                                    ),
                                    React.createElement('ul', {
                                        className: 'space-y-3 text-sm text-gray-300'
                                    },
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-green-400' }, '✓'),
                                            'Professional headshots'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-green-400' }, '✓'),
                                            'Passport-style photos'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-green-400' }, '✓'),
                                            'Clear selfies with natural light'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-green-400' }, '✓'),
                                            'Even lighting, no harsh shadows'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-green-400' }, '✓'),
                                            'Neutral expression or slight smile'
                                        )
                                    )
                                ),

                                // Bad Examples
                                React.createElement('div', {
                                    className: 'bg-red-900/10 border border-red-700/30 rounded-xl p-5'
                                },
                                    React.createElement('h4', {
                                        className: 'text-red-400 font-medium mb-3 flex items-center gap-2'
                                    },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:close-circle-bold-duotone',
                                            style: { fontSize: '20px' }
                                        }),
                                        'Avoid These'
                                    ),
                                    React.createElement('ul', {
                                        className: 'space-y-3 text-sm text-gray-300'
                                    },
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-red-400' }, '✗'),
                                            'Blurry or pixelated images'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-red-400' }, '✗'),
                                            'Side profiles or extreme angles'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-red-400' }, '✗'),
                                            'Sunglasses or face coverings'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-red-400' }, '✗'),
                                            'Heavy filters or effects'
                                        ),
                                        React.createElement('li', { className: 'flex items-center gap-2' },
                                            React.createElement('span', { className: 'text-red-400' }, '✗'),
                                            'Multiple people in frame'
                                        )
                                    )
                                )
                            )
                        ),

                        // Technical Specifications
                        React.createElement('div', null,
                            React.createElement('h3', {
                                className: 'text-lg font-semibold text-white mb-4'
                            }, 'Technical Specifications'),
                            React.createElement('div', {
                                className: 'bg-gray-700/30 rounded-xl p-5'
                            },
                                React.createElement('div', {
                                    className: 'grid md:grid-cols-2 gap-6 text-sm'
                                },
                                    React.createElement('div', null,
                                        React.createElement('h4', {
                                            className: 'font-medium text-gray-300 mb-2'
                                        }, 'Image Requirements'),
                                        React.createElement('ul', {
                                            className: 'space-y-1 text-gray-400'
                                        },
                                            React.createElement('li', null, '• Format: JPEG or PNG'),
                                            React.createElement('li', null, '• Size: 200KB - 2MB'),
                                            React.createElement('li', null, '• Min Resolution: 512x512'),
                                            React.createElement('li', null, '• Recommended: 1024x1024+'),
                                            React.createElement('li', null, '• Aspect Ratio: 1:1 or 3:4')
                                        )
                                    ),
                                    React.createElement('div', null,
                                        React.createElement('h4', {
                                            className: 'font-medium text-gray-300 mb-2'
                                        }, 'Face Position'),
                                        React.createElement('ul', {
                                            className: 'space-y-1 text-gray-400'
                                        },
                                            React.createElement('li', null, '• Face Coverage: 60-80%'),
                                            React.createElement('li', null, '• Angle: Front-facing (0-15°)'),
                                            React.createElement('li', null, '• Eyes: Both visible & open'),
                                            React.createElement('li', null, '• Head: Minimal tilt'),
                                            React.createElement('li', null, '• Include: Neck/shoulders')
                                        )
                                    )
                                )
                            )
                        ),

                        // Tips Section
                        React.createElement('div', null,
                            React.createElement('h3', {
                                className: 'text-lg font-semibold text-white mb-4'
                            }, 'Pro Tips for Better Results'),
                            React.createElement('div', {
                                className: 'bg-purple-900/10 border border-purple-700/30 rounded-xl p-5'
                            },
                                React.createElement('ul', {
                                    className: 'space-y-3 text-sm text-gray-300'
                                },
                                    React.createElement('li', { className: 'flex items-start gap-3' },
                                        React.createElement('span', {
                                            className: 'iconify text-purple-400 mt-0.5',
                                            'data-icon': 'solar:lightbulb-bold-duotone',
                                            style: { fontSize: '16px' }
                                        }),
                                        React.createElement('span', null,
                                            React.createElement('strong', { className: 'text-purple-300' }, 'Lighting:'),
                                            ' Use natural daylight near a window for best results'
                                        )
                                    ),
                                    React.createElement('li', { className: 'flex items-start gap-3' },
                                        React.createElement('span', {
                                            className: 'iconify text-purple-400 mt-0.5',
                                            'data-icon': 'solar:camera-bold-duotone',
                                            style: { fontSize: '16px' }
                                        }),
                                        React.createElement('span', null,
                                            React.createElement('strong', { className: 'text-purple-300' }, 'Camera:'),
                                            ' Use rear camera instead of front for higher quality'
                                        )
                                    ),
                                    React.createElement('li', { className: 'flex items-start gap-3' },
                                        React.createElement('span', {
                                            className: 'iconify text-purple-400 mt-0.5',
                                            'data-icon': 'solar:face-scan-circle-bold-duotone',
                                            style: { fontSize: '16px' }
                                        }),
                                        React.createElement('span', null,
                                            React.createElement('strong', { className: 'text-purple-300' }, 'Expression:'),
                                            ' Keep a neutral or natural expression for best AI matching'
                                        )
                                    ),
                                    React.createElement('li', { className: 'flex items-start gap-3' },
                                        React.createElement('span', {
                                            className: 'iconify text-purple-400 mt-0.5',
                                            'data-icon': 'solar:gallery-minimalistic-bold-duotone',
                                            style: { fontSize: '16px' }
                                        }),
                                        React.createElement('span', null,
                                            React.createElement('strong', { className: 'text-purple-300' }, 'Background:'),
                                            ' Plain or uncluttered backgrounds work best'
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            );
        };

        // Show helpful message when Include Person is OFF
        if (!includePerson) {
            return React.createElement('div', {
                className: 'face-upload-disabled-message bg-gradient-to-r from-blue-900/30 to-purple-900/30 border-2 border-dashed border-blue-500/50 rounded-2xl p-8 mt-4 text-center',
                id: 'face-upload-disabled-section'
            },
                React.createElement('div', {
                    className: 'flex flex-col items-center gap-4'
                },
                    React.createElement('div', {
                        className: 'w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:user-plus-bold-duotone',
                            style: { fontSize: '32px', color: '#60A5FA' }
                        })
                    ),
                    React.createElement('h3', {
                        className: 'text-xl font-semibold text-white'
                    }, 'Custom Face Upload Available'),
                    React.createElement('p', {
                        className: 'text-gray-300 max-w-md mx-auto leading-relaxed'
                    }, 'Upload your own headshot to replace AI-generated faces in thumbnails. First, enable the "Include Person" toggle above to unlock this feature.'),
                    React.createElement('button', {
                        type: 'button',
                        onClick: () => {
                            console.log('Enable Include Person button clicked - scrolling to toggle');
                            setIncludePerson(true);
                            // Scroll to the include person toggle
                            const toggleElement = document.getElementById('togglePerson');
                            if (toggleElement) {
                                toggleElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        },
                        className: 'px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105',
                        'aria-label': 'Enable Include Person toggle to access face upload'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:power-bold-duotone',
                            style: { fontSize: '20px' }
                        }),
                        'Enable Include Person',
                        React.createElement('span', {
                            className: 'iconify ml-1',
                            'data-icon': 'solar:arrow-up-linear',
                            style: { fontSize: '16px' }
                        })
                    ),
                    // View Requirements Button (Always Visible)
                    React.createElement('button', {
                        type: 'button',
                        onClick: () => {

                            setShowRequirementsModal(true);
                        },
                        className: 'view-requirements-btn mt-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-700 text-gray-300 hover:text-white rounded-lg transition-colors flex items-center justify-center gap-2 text-sm font-medium border border-gray-600',
                        'aria-label': 'View detailed image requirements and guidelines'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:document-text-bold-duotone',
                            style: { fontSize: '18px' }
                        }),
                        'View Image Requirements'
                    )
                )
            );
        }

        return React.createElement('div', {
            className: 'face-upload-section bg-gray-800/50 border border-gray-700/50 rounded-2xl p-6 mt-4',
            id: 'face-upload-section'
        },
            // Header with title, Beta badge, and info icon
            React.createElement('div', {
                className: 'flex items-center justify-between mb-6'
            },
                React.createElement('h3', {
                    className: 'text-lg font-semibold text-white'
                }, 'Custom Face Upload'),
                React.createElement('div', {
                    className: 'flex items-center gap-3'
                },
                    // Beta badge
                    React.createElement('span', {
                        className: 'beta-badge bg-gray-700 text-gray-300 text-sm font-medium px-3 py-1 rounded-full'
                    }, 'Beta'),
                    // Info icon
                    React.createElement('div', {
                        className: 'relative group',
                        tabIndex: 0,
                        'aria-label': 'Information about custom face upload',
                        role: 'button'
                    },
                        React.createElement('span', {
                            className: 'iconify info-icon',
                            'data-icon': 'solar:info-circle-linear',
                            style: { fontSize: '20px', color: '#9CA3AF' }
                        }),
                        React.createElement('div', {
                            className: 'absolute bottom-full right-0 transform -translate-y-2 w-80 p-4 bg-gray-900 text-white text-xs rounded-lg shadow-xl z-20 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity border border-gray-700',
                            role: 'tooltip',
                            'aria-hidden': 'true'
                        },
                            React.createElement('div', { className: 'space-y-3' },
                                React.createElement('p', { className: 'font-medium text-sm' }, 'Replace AI-generated face with your own headshot.'),
                                React.createElement('ul', { className: 'text-xs space-y-1 text-gray-300' },
                                    React.createElement('li', { className: 'flex items-center gap-2' },
                                        React.createElement('span', { className: 'text-green-400' }, '✓'),
                                        'Clear, front-facing photo'
                                    ),
                                    React.createElement('li', { className: 'flex items-center gap-2' },
                                        React.createElement('span', { className: 'text-green-400' }, '✓'),
                                        'Good lighting, no shadows'
                                    ),
                                    React.createElement('li', { className: 'flex items-center gap-2' },
                                        React.createElement('span', { className: 'text-green-400' }, '✓'),
                                        '512x512 minimum resolution'
                                    ),
                                    React.createElement('li', { className: 'flex items-center gap-2' },
                                        React.createElement('span', { className: 'text-green-400' }, '✓'),
                                        'JPEG/PNG, max 2MB'
                                    )
                                ),
                                React.createElement('div', { className: 'bg-blue-900/50 rounded-lg p-3 border border-blue-700/50' },
                                    React.createElement('p', { className: 'text-blue-300 font-semibold text-sm flex items-center gap-2' },
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:arrow-down-linear',
                                            style: { fontSize: '16px' }
                                        }),
                                        'Click the blue "View Image Requirements" button below for complete guidelines!'
                                    )
                                )
                            ),
                            React.createElement('div', {
                                className: 'absolute left-full top-4 transform -translate-x-1 w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-gray-900'
                            })
                        )
                    )
                )
            ),

            // Tab buttons
            React.createElement('div', {
                className: 'tab-buttons bg-gray-700 rounded-2xl p-1 mb-6 grid grid-cols-2 gap-1'
            },
                // Upload tab button
                React.createElement('button', {
                    type: 'button',
                    onClick: () => handleImageSourceTypeChange('upload'),
                    className: `tab-button flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${imageSourceType === 'upload'
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-300 hover:text-white hover:bg-gray-600'
                        }`,
                    'aria-pressed': imageSourceType === 'upload',
                    id: 'upload-tab-button'
                },
                    React.createElement('span', {
                        className: 'iconify tab-icon',
                        'data-icon': 'solar:upload-linear',
                        style: { fontSize: '18px', color: 'currentColor' }
                    }),
                    'Upload'
                ),
                // URL tab button
                React.createElement('button', {
                    type: 'button',
                    onClick: () => handleImageSourceTypeChange('url'),
                    className: `tab-button flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${imageSourceType === 'url'
                        ? 'bg-blue-600 text-white shadow-md'
                        : 'text-gray-300 hover:text-white hover:bg-gray-600'
                        }`,
                    'aria-pressed': imageSourceType === 'url',
                    id: 'url-tab-button'
                },
                    React.createElement('span', {
                        className: 'iconify tab-icon',
                        'data-icon': 'solar:link-circle-linear',
                        style: { fontSize: '18px', color: 'currentColor' }
                    }),
                    'URL'
                )
            ),

            // View Requirements Button
            React.createElement('div', {
                className: 'mb-4',
                style: {
                    display: 'block',
                    visibility: 'visible',
                    opacity: 1,
                    position: 'relative',
                    zIndex: 10,
                    minHeight: '50px'
                }
            },
                React.createElement('button', {
                    type: 'button',
                    onClick: () => {

                        openImageRequirementsModal();
                    },
                    className: 'view-requirements-btn w-full py-3 px-4 bg-gradient-to-r from-blue-600/80 to-purple-600/80 hover:from-blue-600 hover:to-purple-600 text-white rounded-xl transition-all duration-300 flex items-center justify-center gap-2 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] border border-blue-500/30',
                    'aria-label': 'View detailed image requirements and guidelines',
                    id: 'view-image-requirements-btn'
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:document-text-bold-duotone',
                        style: { fontSize: '20px' }
                    }),
                    'View Image Requirements',
                    React.createElement('span', {
                        className: 'iconify ml-1',
                        'data-icon': 'solar:arrow-right-linear',
                        style: { fontSize: '16px' }
                    })
                )
            ),

            // Content area based on selected tab
            React.createElement('div', {
                className: 'tab-content mb-6'
            },
                // Upload content
                imageSourceType === 'upload' && React.createElement('div', {
                    className: 'upload-content'
                },
                    React.createElement('div', {
                        className: 'upload-dropzone bg-gray-800 border-2 border-dashed border-gray-600 rounded-2xl p-12 text-center hover:border-gray-500 hover:bg-gray-750 transition-all duration-200 cursor-pointer',
                        onClick: handleFileUploadClick,
                        role: 'button',
                        tabIndex: 0,
                        'aria-label': 'Click to upload image file',
                        onKeyDown: (e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                handleFileUploadClick();
                            }
                        }
                    },
                        React.createElement('div', {
                            className: 'upload-icon-container w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4 relative'
                        },
                            React.createElement('span', {
                                className: 'iconify upload-icon',
                                'data-icon': 'solar:upload-linear',
                                style: { fontSize: '28px', color: '#9CA3AF' }
                            }),
                            // Face guide overlay
                            React.createElement('div', {
                                className: 'absolute -inset-8 border-2 border-dashed border-gray-600 rounded-full opacity-30'
                            }),
                            React.createElement('div', {
                                className: 'absolute -top-10 left-1/2 transform -translate-x-1/2 text-xs text-gray-500'
                            }, '60-80%')
                        ),
                        React.createElement('p', {
                            className: 'text-gray-300 font-medium mb-2'
                        }, 'Click to upload or drag & drop'),
                        React.createElement('p', {
                            className: 'text-sm text-gray-500 mb-3'
                        }, 'Max 4MB, JPEG or PNG'),
                        React.createElement('div', {
                            className: 'flex items-center justify-center gap-2 text-xs text-gray-400'
                        },
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:face-scan-circle-linear',
                                style: { fontSize: '16px' }
                            }),
                            'Best: Clear, front-facing photo'
                        )
                    ),
                    // Hidden file input
                    React.createElement('input', {
                        type: 'file',
                        ref: fileInputRef,
                        accept: 'image/jpeg,image/jpg,image/png',
                        onChange: handleFileUpload,
                        className: 'hidden',
                        'aria-hidden': 'true',
                        id: 'face-upload-input'
                    })
                ),

                // URL content  
                imageSourceType === 'url' && React.createElement('div', {
                    className: 'url-content'
                },
                    React.createElement('div', {
                        className: 'url-input-container bg-gray-800 rounded-2xl p-6'
                    },
                        React.createElement('div', {
                            className: 'flex gap-3 items-center'
                        },
                            React.createElement('input', {
                                type: 'text',
                                placeholder: 'Paste image URL here...',
                                value: tempUrl,
                                onChange: handleUrlInputChange,
                                onKeyDown: (e) => {
                                    if (e.key === 'Enter') {
                                        handleSetFaceImageFromUrl();
                                    }
                                },
                                className: 'flex-1 bg-gray-700 text-gray-300 placeholder-gray-500 px-4 py-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                                'aria-label': 'Enter image URL'
                            }),
                            React.createElement('button', {
                                type: 'button',
                                onClick: handleSetFaceImageFromUrl,
                                className: 'px-6 py-3 bg-gray-600 hover:bg-gray-500 text-white rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 whitespace-nowrap',
                                'aria-label': 'Set image URL'
                            }, 'Set URL')
                        )
                    ),
                    React.createElement('div', {
                        className: 'mt-3 flex items-center gap-2 text-xs text-gray-400'
                    },
                        React.createElement('span', {
                            className: 'iconify',
                            'data-icon': 'solar:info-circle-linear',
                            style: { fontSize: '14px' }
                        }),
                        'Enter a direct link to an image file (JPEG/PNG).'
                    )
                )
            ),

            // Preview section (if image is uploaded/set)
            customFaceImageUrl && React.createElement('div', {
                className: 'preview-section bg-gray-700/30 border border-gray-600 rounded-xl p-4 mb-6'
            },
                React.createElement('div', {
                    className: 'flex items-center gap-4'
                },
                    React.createElement('img', {
                        src: customFaceImageUrl,
                        alt: 'Custom Face Preview',
                        className: 'w-16 h-16 rounded-full object-cover border-2 border-blue-500',
                        onError: (e) => {
                            e.target.style.display = 'none';
                            setErrorMsg('Failed to load image. Please check the URL or try a different image.');
                        }
                    }),
                    React.createElement('div', {
                        className: 'flex-1'
                    },
                        React.createElement('p', {
                            className: 'text-sm font-medium text-gray-300 mb-1'
                        }, 'Face Preview'),
                        React.createElement('p', {
                            className: 'text-xs text-gray-500'
                        }, imageSourceType === 'upload' ? 'Uploaded from device' : 'Loaded from URL')
                    ),
                    React.createElement('button', {
                        type: 'button',
                        onClick: handleRemoveFaceImage,
                        className: 'px-3 py-2 text-xs bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-red-500',
                        'aria-label': 'Remove uploaded image'
                    }, 'Remove')
                )
            ),

            // Warning message
            React.createElement('div', {
                className: 'warning-message text-sm text-gray-400 leading-relaxed italic'
            }, 'This feature is currently under development. Results may not be 100% accurate and could contain mistakes.'),

            // Render the modal
            renderImageRequirementsModal()
        );
    };

    const createMoodAndExpressionPicker = () => {
        if (!includePerson) return null;

        const expressionOptions = [
            { emoji: '🙂', label: 'Default', value: 'Default' },
            { emoji: '😊', label: 'Happy', value: 'Happy' },
            { emoji: '😳', label: 'Shocked', value: 'Shocked' },
            { emoji: '😍', label: 'Loved', value: 'Loved' },
            { emoji: '🤔', label: 'Thinking', value: 'Thinking' },
            { emoji: '😠', label: 'Angry', value: 'Angry' },
            { emoji: '😢', label: 'Crying', value: 'Crying' },
            { emoji: '😆', label: 'Laughing', value: 'Laughing' },
            { emoji: '😐', label: 'Neutral', value: 'Neutral' },
            { emoji: '😎', label: 'Proud', value: 'Proud' }
        ];

        const handleExpressionClick = (newValue) => {
            setSelectedExpression(newValue);
        };

        const handleExpressionKeyDown = (e, newValue) => {
            if (e.key === 'Enter' || e.key === ' ') {
                setSelectedExpression(newValue);
                e.preventDefault();
            }
        };

        const buttons = expressionOptions.map(option =>
            React.createElement('button', {
                key: option.value,
                type: 'button',
                onClick: () => handleExpressionClick(option.value),
                onKeyDown: (e) => handleExpressionKeyDown(e, option.value),
                title: option.label,
                className: `flex flex-col items-center justify-center p-2 rounded-lg border-2 text-center ${selectedExpression === option.value ? 'border-purple-500 bg-purple-700' : 'border-gray-600 bg-gray-700 hover:border-purple-400'} transition-all focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 w-full`,
                id: `expression-btn-${option.value.toLowerCase().replace(/\s+/g, '-')}`,
                'aria-label': `${option.emoji} ${option.label}`,
                'aria-pressed': selectedExpression === option.value
            },
                React.createElement('span', { 'aria-hidden': 'true', className: 'text-3xl' }, option.emoji),
                React.createElement('span', { className: 'block text-xs mt-1 text-gray-300' }, option.label)
            )
        );

        return React.createElement('div', { className: 'mood-expression-picker-section py-2', id: 'mood-expression-picker-section' },
            React.createElement('label', { className: 'mood-expression-picker-label block text-sm font-medium text-gray-300 mb-2' }, 'Mood & Expression:'),
            React.createElement('div', { className: 'mood-expression-picker-grid grid grid-cols-4 sm:grid-cols-4 gap-2' }, buttons)
        );
    };

    const createTextOverlayToggle = () => {
        const label = 'Thumbnail Overlay Text';
        const id = 'toggleText';
        const checked = textOverlay;
        const setter = setTextOverlay;
        const tooltip = "Edit the main text for your thumbnail. Use 2–3 impactful words per line, arranged in a pyramid shape for best results. Leave blank to auto-generate from your main prompt.";

        const handleKeyDown = (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                handleToggleChange(setter);
                e.preventDefault();
            }
        };

        // Function to highlight placeholders in the overlay text editor
        const renderHighlightedTextArea = () => {
            const placeholders = findPlaceholders(overlayText);

            if (placeholders.length === 0) {
                // No placeholders, just render the normal textarea
                return React.createElement('textarea', {
                    id: 'overlayTextInput',
                    className: 'overlay-textarea w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:ring-purple-500 focus:border-purple-500 text-gray-100 resize-none text-sm',
                    rows: '3',
                    placeholder: smartTextSuggestion || 'YOUR VIDEO\nTITLE\nHERE',
                    value: overlayText,
                    onChange: (e) => {
                        // Apply real-time sanitization while preserving user input experience
                        const rawValue = e.target.value;
                        const sanitized = sanitizeOverlayText(rawValue, {
                            preserveSpaces: true, // Preserve spaces during typing for better UX
                            removeEmojis: true,
                            removeNumbers: false,
                            convertToUppercase: true
                        });
                        setOverlayText(sanitized);
                    },
                    'aria-label': 'Edit custom overlay text (automatically converted to uppercase)'
                });
            }

            return React.createElement('div', { className: 'relative' },
                // The actual editable textarea (invisible but functional)
                React.createElement('textarea', {
                    id: 'overlayTextInput',
                    className: 'overlay-textarea w-full p-2 bg-gray-700 border border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:ring-purple-500 focus:border-purple-500 text-gray-100 resize-none text-sm relative z-10',
                    rows: '3',
                    placeholder: smartTextSuggestion || 'YOUR VIDEO\nTITLE\nHERE',
                    value: overlayText,
                    onChange: (e) => {
                        // Apply real-time sanitization while preserving user input experience
                        const rawValue = e.target.value;
                        const sanitized = sanitizeOverlayText(rawValue, {
                            preserveSpaces: true, // Preserve spaces during typing for better UX
                            removeEmojis: true,
                            removeNumbers: false,
                            convertToUppercase: true
                        });
                        setOverlayText(sanitized);
                    },
                    'aria-label': 'Edit custom overlay text with placeholders (automatically converted to uppercase)'
                }),

                // Info about placeholders
                placeholders.length > 0 && React.createElement('div', {
                    className: 'mt-1 text-xs text-yellow-400 flex items-center gap-1'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        strokeWidth: 1.5,
                        stroke: 'currentColor',
                        className: 'w-4 h-4'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            d: 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z'
                        })
                    ),
                    `Replace ${placeholders.length > 1 ? 'placeholders' : 'placeholder'} like ${placeholders.map(p => p.placeholder).join(', ')} with your content`
                )
            );
        };

        return React.createElement('div', { className: 'overlay-text-section flex flex-col gap-2', id: 'overlay-text-section' },
            React.createElement('div', { className: 'toggle-row flex items-center justify-between py-2', id: `toggle-${id}-row` },
                React.createElement('div', { className: 'flex items-center gap-2' },
                    React.createElement('label', { htmlFor: id, className: 'toggle-label text-sm font-medium text-gray-300 cursor-pointer' }, label),
                    tooltip && React.createElement('div', {
                        className: 'relative group',
                        tabIndex: 0,
                        'aria-label': 'Information about this setting',
                        role: 'button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            stroke: 'currentColor',
                            className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        ),
                        React.createElement('div', {
                            className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                            role: 'tooltip',
                            'aria-hidden': 'true'
                        },
                            React.createElement('div', {
                                className: 'flex items-start gap-2'
                            },
                                React.createElement('svg', {
                                    xmlns: 'http://www.w3.org/2000/svg',
                                    fill: 'none',
                                    viewBox: '0 0 24 24',
                                    stroke: 'currentColor',
                                    className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                                },
                                    React.createElement('path', {
                                        strokeLinecap: 'round',
                                        strokeLinejoin: 'round',
                                        strokeWidth: 2,
                                        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                    })
                                ),
                                React.createElement('p', null, tooltip)
                            ),
                            React.createElement('div', {
                                className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                            })
                        )
                    )
                ),
                React.createElement('div', { className: 'flex items-center gap-2' },
                    textOverlay && React.createElement('button', {
                        type: 'button',
                        onClick: () => setIsEditingOverlayText(prev => !prev),
                        className: `edit-overlay-btn px-2 py-1 text-xs rounded ${isEditingOverlayText ? 'bg-purple-700 text-white' : 'bg-gray-600 text-gray-300 hover:bg-purple-500'} transition-colors`,
                        id: 'edit-overlay-btn',
                        'aria-label': isEditingOverlayText ? 'Close Text Editor' : 'Edit Overlay Text'
                    }, isEditingOverlayText ? 'Close' : 'Edit'),
                    React.createElement('button', {
                        id,
                        role: 'switch',
                        'aria-checked': checked,
                        onClick: () => handleToggleChange(setter),
                        onKeyDown: handleKeyDown,
                        tabIndex: '0',
                        className: `toggle-switch ${checked ? 'bg-blue-600' : 'bg-gray-600'} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900`
                    },
                        React.createElement('span', { className: 'sr-only' }, 'Use setting'),
                        React.createElement('span', { 'aria-hidden': 'true', className: `toggle-circle ${checked ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out` })
                    )
                )
            ),
            textOverlay && isEditingOverlayText && React.createElement('div', { className: 'overlay-text-editor mt-1 w-full', id: 'overlay-text-editor' },
                React.createElement('label', { htmlFor: 'overlayTextInput', className: 'overlay-text-editor-label block text-xs font-medium text-gray-400 mb-1' }, 'Edit overlay text (pyramid shape recommended):'),
                renderHighlightedTextArea()
            )
        );
    };

    // Re-introduce the dropdown selector function
    const createPositionSelector = () => {
        if (!textOverlay) return null; // Only show if text overlay is enabled

        const positions = [
            "Top Left", "Top Center", "Top Right",
            "Center",
            "Bottom Left", "Bottom Center", "Bottom Right"
        ];

        const options = positions.map(pos =>
            React.createElement('option', { key: pos, value: pos }, pos)
        );

        const tooltipText = "Choose where the title text should appear. Top Right is standard. Center is good for dramatic titles.";

        return React.createElement('div', { className: 'text-position-selector py-2', id: 'text-position-selector' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' }, // Label and Tooltip
                React.createElement('label', { htmlFor: 'textPositionSelect', className: 'text-sm font-medium text-gray-300' }, 'Text Position:'),
                React.createElement('div', {
                    className: 'relative group',
                    tabIndex: 0,
                    'aria-label': 'Information about text positioning',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'glass-select-wrapper' },
                React.createElement('select', {
                    id: 'textPositionSelect',
                    value: textPosition,
                    onChange: (e) => setTextPosition(e.target.value),
                    className: 'glass-select'
                },
                    options
                ),
                React.createElement('span', {
                    className: 'glass-select-chevron iconify',
                    'data-icon': 'solar:alt-arrow-down-linear'
                })
            )
        );
    };

    const createTextSizeRadioButtons = () => {
        if (!textOverlay) return null;

        const sizes = ["Small", "Medium", "Large"];
        const tooltipText = "Choose the size of the title text. Small offers a minimal, subtle look. Medium provides a balanced look, while Large offers maximum impact, especially for mobile. 'Medium' is generally recommended for YouTube thumbnails.";

        return React.createElement('div', { className: 'text-size-radios py-2', id: 'text-size-radios' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, 'Text Size:'),
                React.createElement('div', {
                    className: 'relative group',
                    tabIndex: 0,
                    'aria-label': 'Information about text size options',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'flex items-center gap-4 mt-1' },
                sizes.map(size => React.createElement('label', { key: size, htmlFor: `textSize-${size}`, className: 'flex items-center cursor-pointer text-sm text-gray-300' },
                    React.createElement('input', {
                        type: 'radio',
                        id: `textSize-${size}`,
                        name: 'selectedTextSize',
                        value: size,
                        checked: selectedTextSize === size,
                        onChange: () => setSelectedTextSize(size),
                        className: 'form-radio h-4 w-4 text-purple-600 bg-gray-700 border-gray-600 focus:ring-purple-500 cursor-pointer'
                    }),
                    React.createElement('span', { className: 'ml-2' }, size)
                ))
            )
        );
    };

    const createFontSelector = () => {
        if (!textOverlay) return null;
        const fontOptions = ['Impact', 'Arial', 'Verdana', 'Georgia', 'Comic Sans MS']; // Added Comic Sans for fun
        const tooltipText = "Select the primary font family for the overlay text. Ensure it complements the thumbnail's style.";

        return React.createElement('div', { className: 'font-selector-section py-2', id: 'font-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { htmlFor: 'fontFamilySelect', className: 'text-sm font-medium text-gray-300' }, 'Font Family:'),
                React.createElement('div', {
                    className: 'relative group',
                    tabIndex: 0,
                    'aria-label': 'Information about font selection',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'glass-select-wrapper' },
                React.createElement('select', {
                    id: 'fontFamilySelect',
                    value: selectedFontFamily,
                    onChange: (e) => setSelectedFontFamily(e.target.value),
                    className: 'glass-select'
                },
                    fontOptions.map(font => React.createElement('option', { key: font, value: font }, font))
                ),
                React.createElement('span', {
                    className: 'glass-select-chevron iconify',
                    'data-icon': 'solar:alt-arrow-down-linear'
                })
            )
        );
    };

    const createGenderSelector = () => {
        if (!includePerson) return null;
        const genderOptions = [
            { emoji: '🤖', label: 'Auto', value: 'Auto' },
            { emoji: '👨', label: 'Male', value: 'Male' },
            { emoji: '👩', label: 'Female', value: 'Female' },
            { emoji: '🧑', label: 'Non-binary', value: 'Non-binary' }
        ];
        const tooltipText = "Specify the gender of the person in the thumbnail. 'Auto' lets the AI decide. This is only active if 'Include Person' is ON.";

        return React.createElement('div', { className: 'gender-selector-section py-2', id: 'gender-selector-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { htmlFor: 'genderSelect', className: 'text-sm font-medium text-gray-300' }, 'Preferred Gender:'),
                React.createElement('div', {
                    className: 'relative group',
                    tabIndex: 0,
                    'aria-label': 'Information about gender selection',
                    role: 'button'
                },
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        stroke: 'currentColor',
                        className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                        })
                    ),
                    React.createElement('div', {
                        className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                        role: 'tooltip',
                        'aria-hidden': 'true'
                    },
                        React.createElement('div', {
                            className: 'flex items-start gap-2'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                fill: 'none',
                                viewBox: '0 0 24 24',
                                stroke: 'currentColor',
                                className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                            },
                                React.createElement('path', {
                                    strokeLinecap: 'round',
                                    strokeLinejoin: 'round',
                                    strokeWidth: 2,
                                    d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                                })
                            ),
                            React.createElement('p', null, tooltipText)
                        ),
                        React.createElement('div', {
                            className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                        })
                    )
                )
            ),
            React.createElement('div', { className: 'grid grid-cols-4 gap-2 mt-2', role: 'radiogroup', 'aria-label': 'Gender selection' },
                genderOptions.map(gender => React.createElement('button', {
                    key: gender.value,
                    onClick: () => setSelectedGender(gender.value),
                    className: `gender-card flex flex-col items-center justify-center p-2 ${selectedGender === gender.value ? 'bg-purple-700 border-purple-500' : 'bg-gray-700 border-gray-600'} border rounded-md shadow-sm hover:bg-gray-600 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50`,
                    'aria-pressed': selectedGender === gender.value,
                    'aria-label': gender.label,
                    disabled: !includePerson
                },
                    React.createElement('span', { className: 'text-2xl mb-1' }, gender.emoji),
                    React.createElement('span', { className: 'text-xs font-medium' }, gender.label)
                ))
            )
        );
    };

    const createTextColorPickers = () => {
        if (!textOverlay) return null;
        const tooltipText = "Choose primary and secondary colors for the text overlay. High contrast is recommended for readability (e.g., light text on dark glow/shadow, or vice-versa).";

        // selectedPalette is managed by parent component via props

        const handleSelectPalette = (paletteId, colors) => {
            if (!colors || colors.length < 2) return;

            // Update the app state with the selected palette colors
            setPrimaryTextColor(colors[0]);
            setSecondaryTextColor(colors[1]);

            // Track which palette is selected
            setSelectedPalette(paletteId);
        };

        // When manual color picker changes, set to custom
        const handleManualColorChange = (isPrimary, color) => {
            if (isPrimary) {
                setPrimaryTextColor(color);
            } else {
                setSecondaryTextColor(color);
            }
            setSelectedPalette('custom');
        };

        return React.createElement('div', { className: 'text-color-pickers-section py-2', id: 'text-color-pickers-section' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, 'Text Colors:'),
                React.createElement('div', { className: 'tooltip-wrapper relative group' },
                    React.createElement('svg', {
                        className: 'tooltip-icon w-4 h-4 text-gray-400 hover:text-purple-400 cursor-help transition-colors',
                        xmlns: 'http://www.w3.org/2000/svg',
                        fill: 'none',
                        viewBox: '0 0 24 24',
                        strokeWidth: 1.5,
                        stroke: 'currentColor'
                    }, React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        d: 'M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z'
                    })),
                    React.createElement('div', {
                        className: 'tooltip-content absolute bottom-full left-1/2 z-50 mb-2 -translate-x-1/2 transform rounded-md bg-purple-900 px-3 py-2 text-xs text-white shadow-lg opacity-0 invisible transition-all duration-300 ease-in-out delay-150 group-hover:opacity-100 group-hover:visible',
                        style: { minWidth: '200px', maxWidth: '300px' }
                    },
                        React.createElement('div', { className: 'tooltip-arrow absolute -bottom-2 left-1/2 h-4 w-4 -translate-x-1/2 transform rotate-45 bg-purple-900' }),
                        React.createElement('div', { className: 'tooltip-text relative z-10 flex flex-col gap-1' },
                            React.createElement('p', { className: 'tooltip-title font-medium text-purple-200 mb-1' }, 'Text Color Customization'),
                            React.createElement('p', { className: 'tooltip-description leading-relaxed' }, tooltipText)
                        )
                    )
                )
            ),
            // Add the new color palette presets component
            React.createElement(ColorPalettePresets, {
                onSelectPalette: handleSelectPalette,
                currentPalette: selectedPalette,
                primaryColor: primaryTextColor,
                secondaryColor: secondaryTextColor
            }),
            React.createElement('div', { className: 'grid grid-cols-2 gap-3 mt-1' },
                React.createElement('div', null,
                    React.createElement('label', { htmlFor: 'primaryTextColorPicker', className: 'block text-xs font-medium text-gray-400 mb-1' }, 'Primary:'),
                    React.createElement('input', {
                        type: 'color',
                        id: 'primaryTextColorPicker',
                        value: primaryTextColor,
                        onChange: (e) => handleManualColorChange(true, e.target.value),
                        className: 'w-full h-10 p-1 bg-gray-700 border border-gray-600 rounded-md cursor-pointer focus:ring-purple-500 focus:border-purple-500'
                    })
                ),
                React.createElement('div', null,
                    React.createElement('label', { htmlFor: 'secondaryTextColorPicker', className: 'block text-xs font-medium text-gray-400 mb-1' }, 'Secondary:'),
                    React.createElement('input', {
                        type: 'color',
                        id: 'secondaryTextColorPicker',
                        value: secondaryTextColor,
                        onChange: (e) => handleManualColorChange(false, e.target.value),
                        className: 'w-full h-10 p-1 bg-gray-700 border border-gray-600 rounded-md cursor-pointer focus:ring-purple-500 focus:border-purple-500'
                    })
                )
            ),
            // Recent colors - Show the 3 most recently used color combinations
            React.createElement(RecentlyUsedColors, {
                onSelectColors: (primary, secondary) => {
                    setPrimaryTextColor(primary);
                    setSecondaryTextColor(secondary);
                    setSelectedPalette('custom');
                }
            })
        );
    };

    // Text Color Palette Presets Component
    const ColorPalettePresets = ({
        onSelectPalette,
        currentPalette,
        primaryColor,
        secondaryColor
    }) => {
        // Store element ref for scrolling
        const scrollContainerRef = React.useRef(null);

        // Track if we're in a custom state (user manually changed colors)
        const isCustomColor = React.useMemo(() => {
            if (!currentPalette) return true;
            if (currentPalette === 'custom') return true;

            // Find the selected palette
            const selectedPalette = TEXT_COLOR_PALETTES.find(p => p.id === currentPalette);
            if (!selectedPalette) return true;

            // Compare colors to see if they match the palette
            return !(
                selectedPalette.colors[0] === primaryColor &&
                selectedPalette.colors[1] === secondaryColor
            );
        }, [currentPalette, primaryColor, secondaryColor]);

        // Horizontal scroll with keyboard
        const handleKeyDown = (e) => {
            const container = scrollContainerRef.current;
            if (!container) return;

            const SCROLL_AMOUNT = 150;

            if (e.key === 'ArrowRight') {
                container.scrollLeft += SCROLL_AMOUNT;
                e.preventDefault();
            } else if (e.key === 'ArrowLeft') {
                container.scrollLeft -= SCROLL_AMOUNT;
                e.preventDefault();
            }
        };

        return React.createElement('div', { className: 'color-palette-presets mb-3' },
            React.createElement('div', { className: 'flex items-center justify-between mb-1' },
                React.createElement('label', { className: 'block text-xs font-medium text-gray-400' }, 'Color Palettes:'),
                isCustomColor && React.createElement('span', {
                    className: 'text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded'
                }, '🎨 Custom')
            ),
            React.createElement('div', {
                className: 'palette-scroll-container relative',
                onKeyDown: handleKeyDown,
                tabIndex: '0'
            },
                React.createElement('div', {
                    ref: scrollContainerRef,
                    className: 'flex overflow-x-auto pb-2 space-x-2 scroll-smooth',
                    'aria-label': 'Color palette presets',
                    style: { scrollbarWidth: 'thin' }
                },
                    TEXT_COLOR_PALETTES.map(palette => {
                        const isSelected = currentPalette === palette.id;
                        return React.createElement('button', {
                            key: palette.id,
                            onClick: () => onSelectPalette(palette.id, palette.colors),
                            className: `palette-button flex-shrink-0 rounded-md p-2 ${isSelected ? 'ring-2 ring-purple-500 bg-gray-700' : 'bg-gray-800 hover:bg-gray-700'} cursor-pointer transition-all focus:outline-none focus:ring-2 focus:ring-purple-400`,
                            'aria-label': `${palette.name} color palette`,
                            'aria-pressed': isSelected,
                            style: { minWidth: '90px' }
                        },
                            React.createElement('div', { className: 'color-chips-container flex justify-center mb-1 gap-1' },
                                palette.colors.slice(0, 4).map((color, i) =>
                                    React.createElement('div', {
                                        key: `${palette.id}-color-${i}`,
                                        className: 'color-chip rounded-full border border-gray-600 w-4 h-4',
                                        style: { backgroundColor: color }
                                    })
                                )
                            ),
                            React.createElement('div', { className: 'text-center text-xs text-gray-300' }, palette.name)
                        );
                    })
                ),
                // Scroll indicators
                React.createElement('div', {
                    className: 'absolute left-0 top-0 bottom-0 bg-gradient-to-r from-gray-800 to-transparent w-6 pointer-events-none opacity-75',
                    'aria-hidden': 'true'
                }),
                React.createElement('div', {
                    className: 'absolute right-0 top-0 bottom-0 bg-gradient-to-l from-gray-800 to-transparent w-6 pointer-events-none opacity-75',
                    'aria-hidden': 'true'
                })
            )
        );
    };

    // Recently Used Colors Component
    const RecentlyUsedColors = ({ onSelectColors }) => {
        const [recentColors, setRecentColors] = React.useState([]);

        // Load recent colors on component mount
        React.useEffect(() => {
            try {
                const savedColors = JSON.parse(localStorage.getItem('recentTextColors') || '[]');
                setRecentColors(savedColors.slice(0, 3)); // Show only the 3 most recent
            } catch (e) {
                // console.error('Error loading recent colors:', e);
            }
        }, []);

        if (recentColors.length === 0) return null;

        return React.createElement('div', { className: 'recent-colors-section mt-3 pt-2 border-t border-gray-700' },
            React.createElement('label', { className: 'block text-xs font-medium text-gray-400 mb-2' }, 'Recently Used:'),
            React.createElement('div', { className: 'flex gap-2 overflow-x-auto' },
                recentColors.map((colorPair, index) =>
                    React.createElement('button', {
                        key: `recent-${index}`,
                        onClick: () => onSelectColors(colorPair.primary, colorPair.secondary),
                        className: 'recent-color-pair flex-shrink-0 p-1 rounded hover:bg-gray-700 focus:outline-none focus:ring-1 focus:ring-purple-400',
                        'aria-label': `Select recent color combination ${index + 1}`
                    },
                        React.createElement('div', { className: 'flex rounded overflow-hidden border border-gray-600', style: { width: '50px', height: '20px' } },
                            React.createElement('div', { className: 'w-1/2 h-full', style: { backgroundColor: colorPair.primary } }),
                            React.createElement('div', { className: 'w-1/2 h-full', style: { backgroundColor: colorPair.secondary } })
                        )
                    )
                )
            )
        );
    };

    // Persist recently used text colors (runs once per render when colors change)
    React.useEffect(() => {
        if (!primaryTextColor || !secondaryTextColor) return;
        try {
            const recentColors = JSON.parse(localStorage.getItem('recentTextColors') || '[]');
            const existingIndex = recentColors.findIndex(pair => pair.primary === primaryTextColor && pair.secondary === secondaryTextColor);
            if (existingIndex > -1) recentColors.splice(existingIndex, 1);
            recentColors.unshift({ primary: primaryTextColor, secondary: secondaryTextColor, timestamp: Date.now() });
            if (recentColors.length > 5) recentColors.pop();
            localStorage.setItem('recentTextColors', JSON.stringify(recentColors));
        } catch (e) {
            // console.error('Error saving recent colors:', e);
        }
    }, [primaryTextColor, secondaryTextColor]);

    // Duplicate background handler functions removed (now provided via props)



    return React.createElement('div', { className: 'design-controls-panel p-4 bg-gray-800 rounded-lg shadow-md flex flex-col gap-3', id: 'design-controls' },
        React.createElement('h2', { className: 'design-controls-heading text-lg font-semibold text-purple-400 mb-2' }, 'Design Controls'),
        // Text Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Text & Overlay'),
            createTextOverlayToggle(),
            createPositionSelector(),
            createTextSizeRadioButtons(),
            createFontSelector(),
            createTextColorPickers()
        ),
        // Subject Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Subject & Scene'),
            createToggle('Include Person?', 'togglePerson', includePerson, (value) => handleToggleChange(setIncludePerson, true), "Including a person in your thumbnail can increase viewer engagement. Enable this option if your video features a presenter or reaction shots. This may increase generation complexity."),
            createFaceUploadSection(),
            createGenderSelector(),
            createMoodAndExpressionPicker(),
            createToggle('Include Icons?', 'toggleIcons', includeIcons, (value) => handleToggleChange(setIncludeIcons), "Add visual elements like icons, badges or symbols that enhance the thumbnail\'s message. Good for tech tutorials, comparisons, or step-by-step guides.")
        ),
        // Background Controls Group - UPDATED

        // Advanced & Preview Controls Group
        React.createElement('div', { className: 'control-group border border-gray-700 rounded-md p-3' },
            React.createElement('h3', { className: 'text-md font-semibold text-purple-300 mb-2' }, 'Layout & Preview'),
            createToggle('Fit Full Canvas', 'toggleFitFullCanvas', fitFullCanvas, setFitFullCanvas, "Ensures the image fills the entire thumbnail width (1280x720) without black side bars or padding. Automatically adjusts background and composition."),
            createToggle('Show Layout Simulator', 'toggleLayoutSim', showLayoutSimulator, setShowLayoutSimulator, "Shows approximate placement of elements before generation. For visualization only."),
            createToggle('Show Text-Safe Zone', 'toggleSafeZone', showSafeZone, setShowSafeZone, "Outlines the area where text will be fully visible on all devices, including mobile.")
        ),
        // Hidden toggles remain as they were
        React.createElement('div', { id: 'toggle-toggleTextPosition-row', style: { display: 'none' } },
            createToggle('Text Position', 'toggleTextPosition', textPosition === 'Top Right', setTextPosition, "Select the position of the text overlay on the thumbnail.")
        ),
    );
};

const ThumbnailPreview = ({
    imageURL,
    isLoading,
    showLayoutSimulator,
    showSafeZone,
    progress = 0,
    onOpenFullPreview,
    // Need these for simulator
    includePerson,
    includeIcons,
    textOverlay,
    overlayText,
    selectedFontFamily,
    selectedTextSize,
    primaryTextColor,
    secondaryTextColor
}) => {
    const placeholder = React.createElement(
        'div',
        { className: 'w-full h-full flex flex-col items-center justify-center text-gray-400', style: { gap: '0.55rem' } },
        isLoading
            ? React.createElement(
                'div',
                {
                    className: 'preview-loading-backdrop',
                    style: {
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: 'linear-gradient(135deg, rgba(30, 58, 138, 0.9) 0%, rgba(124, 58, 237, 0.8) 50%, rgba(6, 182, 212, 0.7) 100%)',
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)',
                        borderRadius: '8px',
                        border: '1px solid rgba(59, 130, 246, 0.3)',
                        boxShadow: 'inset 0 0 20px rgba(59, 130, 246, 0.2), 0 8px 32px rgba(0, 0, 0, 0.3)',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 10,
                    },
                },
                // Large percentage display - responsive desktop enhancement (25% larger at 768px+)
                React.createElement(
                    'div',
                    { 
                        className: 'loading-percentage text-white font-black mb-4',
                        style: {
                            fontSize: 'clamp(3.3rem, 8vw, 4.4rem)', // 10% bigger (4rem * 1.1 = 4.4rem) with responsive scaling
                            textShadow: '0 3px 12px rgba(255, 255, 255, 0.51), 0 1.5px 6px rgba(0, 0, 0, 0.765)', // White glow reduced by 15%
                            letterSpacing: '-0.02em',
                            color: '#FFFFFF' // Simple white color like before
                        }
                    },
                    `${Math.round(progress)}%`
                ),
                // Animated "Generating Thumbnail..." text with cursor thinking effect
                React.createElement(
                    'div',
                    {
                        className: 'cursor-thinking-text text-lg',
                        style: {
                            fontWeight: '500',
                            fontSize: '1rem',
                            color: 'transparent',
                            background: 'linear-gradient(90deg, rgba(181, 181, 181, 0.85) 20%, #FFFFFF 50%, rgba(181, 181, 181, 0.85) 80%)',
                            backgroundSize: '200% 100%',
                            WebkitBackgroundClip: 'text',
                            backgroundClip: 'text',
                            animation: 'cursorThinking 2.42s linear infinite'
                        }
                    },
                    'Generating Thumbnail...'
                )
            )
            : React.createElement(
                React.Fragment,
                null,
                React.createElement('img', {
                    src: '/assets/empty-states/Album.svg',
                    alt: 'Album',
                    style: {
                        width: '144px',
                        height: '113px',
                        objectFit: 'contain',
                        display: 'block',
                        margin: '0 auto'
                    },
                    className: 'mb-4'
                }),
                React.createElement('p', { 
                    className: 'cursor-thinking-text text-lg',
                    style: {
                        fontWeight: '500',
                        fontSize: '1rem',
                        color: 'transparent',
                        background: 'linear-gradient(90deg, rgba(181, 181, 181, 0.85) 20%, #FFFFFF 50%, rgba(181, 181, 181, 0.85) 80%)',
                        backgroundSize: '200% 100%',
                        WebkitBackgroundClip: 'text',
                        backgroundClip: 'text',
                        animation: 'cursorThinking 2.42s linear infinite'
                    }
                }, 'Create some Magic')
        )
    );

    // Determine if simulator should be shown (before generation)
    const shouldShowSimulator = showLayoutSimulator && !imageURL && !isLoading;
    // Determine if safe zone should be shown (after generation)
    const shouldShowSafeZone = showSafeZone && imageURL && !isLoading;

    return React.createElement('div', { 
        className: `preview-container ${isLoading ? 'preview-loading' : ''} ${imageURL ? 'has-image' : ''}`,
        style: {
            position: 'relative'
        }
    },
        // Image or Placeholder
        imageURL ?
            React.createElement('img', { src: imageURL, alt: 'Generated Thumbnail', className: 'generated-thumbnail' })
            : placeholder,
        
        // Maximize button (only show if imageURL exists AND generation is not in progress)
        imageURL && !isLoading && onOpenFullPreview && React.createElement('button', {
            className: 'thumbnail-maximize-btn',
            onClick: onOpenFullPreview,
            'aria-label': 'Open full-size preview',
            tabIndex: 0,
            title: 'View full size' // Tooltip for clarity
        },
            React.createElement('span', {
                className: 'iconify',
                'data-icon': 'solar:maximize-square-minimalistic-linear',
                style: { fontSize: '20px', color: '#fff' }
            })
        ),
        
        // Enhanced Progress bar overlay when loading - Much more visible
        isLoading && React.createElement('div', {
            className: 'preview-progress-overlay',
            style: {
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                height: '12px', // Increased height for better visibility
                backgroundColor: 'rgba(31, 41, 55, 0.9)', // Darker background for contrast
                borderRadius: '0 0 8px 8px',
                overflow: 'hidden',
                boxShadow: '0 -4px 20px rgba(59, 130, 246, 0.6)', // Enhanced blue glow
                border: '1px solid rgba(59, 130, 246, 0.3)' // Blue border for definition
            }
        },
            React.createElement('div', {
                className: 'preview-progress-bar',
                style: {
                    height: '100%',
                    background: 'linear-gradient(90deg, #3B82F6, #8B5CF6, #06B6D4)', // Vibrant gradient
                    width: `${progress}%`,
                    transition: 'width 0.3s ease-out',
                    borderRadius: '0 0 8px 8px',
                    boxShadow: '0 0 20px rgba(59, 130, 246, 0.8), inset 0 1px 0 rgba(255, 255, 255, 0.3)', // Enhanced glow + inner highlight
                    position: 'relative',
                    overflow: 'hidden'
                }
            },
                // Animated shimmer effect on the progress bar
                React.createElement('div', {
                    style: {
                        position: 'absolute',
                        top: 0,
                        left: '-100%',
                        width: '100%',
                        height: '100%',
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%)',
                        animation: 'progressShimmer 2s ease-in-out infinite'
                    }
                })
            )
        ),


        // Layout Simulator Overlay (shows before image)
        shouldShowSimulator && React.createElement(LayoutSimulatorOverlay, {
            includePerson,
            includeIcons,
            textOverlay,
            overlayText,
            selectedFontFamily,
            selectedTextSize,
            primaryTextColor,
            secondaryTextColor
        }),

        // Safe Zone Overlay (shows over generated image)
        shouldShowSafeZone && React.createElement(SafeZoneOverlay, null)
    );
};

// ================= Button Components =================
const DownloadButton = ({ imageURL, isLoading }) => {
    const handleDownloadClick = async () => {
        if (!imageURL || isLoading) return;

        try {
            await downloadThumbnailAt1280x720(imageURL, 'generated', imageURL);
        } catch (error) {
            console.error("Download failed:", error);
            setErrorMsg("Failed to download image.");
        }
    };

    const isDisabled = !imageURL || isLoading;

    // NEW: Icon-only oval button design matching the reference image
    return React.createElement('button', {
        onClick: handleDownloadClick,
        disabled: isDisabled,
        className: `
            group relative inline-flex items-center justify-center
            w-12 h-12
            text-white
            bg-gray-600 hover:bg-gray-500
            border border-gray-500/30
            rounded-full shadow-lg shadow-gray-500/25
            transition-all duration-200 ease-out
            ${isDisabled
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:shadow-xl hover:shadow-gray-500/30 hover:scale-[1.05] active:scale-[0.95]'
            }
            focus:outline-none focus:ring-2 focus:ring-gray-400/50 focus:ring-offset-2 focus:ring-offset-gray-900
            backdrop-blur-sm
        `.replace(/\s+/g, ' ').trim(),
        'aria-label': 'Download generated thumbnail',
        title: 'Download' // Tooltip for icon-only button
    },
        // Primary icon using Iconify (Solar icons) - larger for icon-only button
        React.createElement('span', {
            className: 'iconify download-icon',
            'data-icon': 'solar:download-line-duotone',
            style: { fontSize: '20px', color: 'currentColor' }
        }),
        // Fallback SVG icon if Iconify doesn't load
        React.createElement('svg', {
            className: 'fallback-download-icon w-[20px] h-[20px] hidden',
            xmlns: 'http://www.w3.org/2000/svg',
            fill: 'none',
            viewBox: '0 0 24 24',
            strokeWidth: 1.5,
            stroke: 'currentColor'
        },
            React.createElement('path', {
                strokeLinecap: 'round',
                strokeLinejoin: 'round',
                d: 'M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3'
            })
        ),
        // Subtle gradient overlay for depth
        React.createElement('div', {
            className: `
                absolute inset-0 rounded-full bg-gradient-to-t from-black/10 to-white/10 
                transition-opacity duration-200
                ${isDisabled ? 'opacity-0' : 'group-hover:opacity-100'}
            `.replace(/\s+/g, ' ').trim()
        })
    );
};

const GenerateButton = ({ onClick, isLoading, includePerson, textOverlay, includeIcons, isLimitReached = false }) => {
    const isCleanBackgroundMode = !includePerson && !textOverlay;
    const hasAnyDesignControl = includePerson || includeIcons || textOverlay;
    const isForbiddenState = !hasAnyDesignControl;

    // Enhanced icon rendering with proper circular spinner
    const getIcon = () => {
        if (isLoading) {
            // Professional circular spinner SVG
            return React.createElement('svg', {
                className: 'w-[18px] h-[18px] animate-spin',
                viewBox: '0 0 24 24',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
                React.createElement('circle', {
                    cx: '12',
                    cy: '12',
                    r: '10',
                    stroke: 'currentColor',
                    strokeWidth: '3',
                    strokeLinecap: 'round',
                    strokeDasharray: '31.416',
                    strokeDashoffset: '23.562',
                    className: 'opacity-25'
                }),
                React.createElement('circle', {
                    cx: '12',
                    cy: '12',
                    r: '10',
                    stroke: 'currentColor',
                    strokeWidth: '3',
                    strokeLinecap: 'round',
                    strokeDasharray: '15.708',
                    strokeDashoffset: '0',
                    className: 'opacity-75'
                })
            );
        }

        // Solar Iconify magic stick icon with fallback, wrapped in a single div
        return React.createElement('div', { // Parent div for stable structure
            className: 'w-[18px] h-[18px] flex items-center justify-center' // Ensures consistent sizing and centering
        },
            React.createElement('span', { // Iconify span
                className: 'iconify generate-magic-icon', // Iconify will use data-width/height
                'data-icon': 'solar:magic-stick-3-bold-duotone',
                'data-width': '18',
                'data-height': '18',
                style: { display: 'flex', alignItems: 'center', justifyContent: 'center' } // Ensure Iconify content is centered
            }),
            React.createElement('svg', { // Fallback SVG
                className: 'fallback-generate-magic-icon w-[18px] h-[18px] hidden', // Explicit size for fallback
                viewBox: '0 0 24 24',
                fill: 'none',
                xmlns: 'http://www.w3.org/2000/svg'
            },
                React.createElement('path', {
                    d: 'M15.312 13.312L4.5 24.125l-1.125-1.125L14.188 12.188M9.75 6.75L20.25 17.25m-3-15l3 3-3 3m0-6h3m-3 3v3',
                    stroke: 'currentColor',
                    strokeWidth: '1.5',
                    strokeLinecap: 'round',
                    strokeLinejoin: 'round'
                }),
                React.createElement('circle', { cx: '19', cy: '5', r: '0.75', fill: 'currentColor' }),
                React.createElement('circle', { cx: '21', cy: '3', r: '0.5', fill: 'currentColor' }),
                React.createElement('circle', { cx: '17', cy: '7', r: '0.5', fill: 'currentColor' })
            )
        );
    };

    const getText = () => {
        if (isLimitReached) {
            return 'Demo Limit Reached';
        }
        if (isForbiddenState) {
            return 'Enable Design Controls';
        }
        return isLoading ? 'Generating...' : 'Generate';
    };

    return React.createElement('div', { className: 'relative' },
        React.createElement('button', {
            onClick,
            disabled: isLoading || isLimitReached || isForbiddenState,
            className: [
                'group relative inline-flex items-center justify-center gap-2.5 px-5',
                'h-12', // Fixed 48px height
                'text-lg font-semibold text-white',
                'border border-blue-400/30',
                'rounded-xl shadow-lg shadow-blue-500/25',
                'transition-all duration-200 ease-out',
                isLimitReached || isForbiddenState
                    ? 'opacity-60 cursor-not-allowed bg-gray-600 border-gray-500/30'
                    : isLoading 
                        ? 'opacity-75 cursor-wait generating' 
                        : 'hover:brightness-110 hover:shadow-xl hover:shadow-blue-500/30 hover:scale-[1.02] active:scale-[0.98]',
                'focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:ring-offset-2 focus:ring-offset-gray-900',
                'backdrop-blur-sm min-w-[160px]'
            ].join(' '),
            style: {
                fontSize: '18px',
                backgroundColor: (isLimitReached || isForbiddenState) ? '#6B7280' : '#006FEE', // Gray when limited/forbidden, blue when available
                borderColor: (isLimitReached || isForbiddenState) ? 'rgba(107, 114, 128, 0.3)' : 'rgba(0, 111, 238, 0.3)'
            }
        },
            getIcon(),
            React.createElement('span', { className: 'font-medium' }, getText()),
            !isLoading && React.createElement('div', {
                className: 'absolute inset-0 rounded-xl bg-gradient-to-t from-black/10 to-white/10 transition-opacity duration-200 group-hover:opacity-100 opacity-0'
            })
        ),
        isCleanBackgroundMode && React.createElement('div', {
            className: 'absolute -top-2 -right-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-[10px] font-bold px-2 py-1 rounded-full shadow-lg shadow-blue-500/30 transform translate-x-1/2 -translate-y-1/2 whitespace-nowrap border border-blue-400/30 animate-pulse',
            title: 'Text Overlay and Include Person are both OFF'
        }, '🖼️ Clean')
    );
};

// ================= Overlay Components =================

const LayoutSimulatorOverlay = ({ includePerson, includeIcons, textOverlay, overlayText, selectedFontFamily, selectedTextSize, primaryTextColor, secondaryTextColor }) => {
    if (!includePerson && !includeIcons && !textOverlay) {
        return null; // Don't show if nothing is toggled
    }

    const overlayStyle = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none', // Allow clicks to pass through
    };

    const zoneStyle = {
        position: 'absolute',
        border: '2px dashed rgba(255, 255, 255, 0.5)',
        backgroundColor: 'rgba(0, 0, 0, 0.3)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '10px',
        color: 'rgba(255, 255, 255, 0.7)',
        padding: '2px 4px',
        borderRadius: '4px',
    };

    return React.createElement('div', { style: overlayStyle },
        // Person Zone (Example: Left side)
        includePerson && React.createElement('div', {
            style: { ...zoneStyle, top: '10%', left: '5%', width: '40%', height: '80%' }
        }, 'PERSON'),

        // Text Overlay Zone (Example: Top Right)
        textOverlay && React.createElement('div', {
            style: { ...zoneStyle, top: '10%', right: '5%', width: '45%', height: '25%' }
        }, 'TEXT OVERLAY'),

        // Icons Zone (Example: Floating around)
        includeIcons && React.createElement(React.Fragment, null,
            React.createElement('div', {
                style: { ...zoneStyle, top: '30%', right: '10%', width: '15%', height: '15%', borderRadius: '50%' }
            }, 'ICON'),
            React.createElement('div', {
                style: { ...zoneStyle, bottom: '15%', left: '40%', width: '10%', height: '10%', borderRadius: '50%' }
            }, 'ICON')
        )
    );
};

const SafeZoneOverlay = () => {
    const overlayStyle = {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        border: '2px solid rgba(255, 0, 0, 0.6)', // Outer edge for reference
    };

    const safeZoneStyle = {
        position: 'absolute',
        // 64px margin on a 1280x720 viewport roughly translates to percentages
        top: `${(64 / 720) * 100}%`,
        left: `${(64 / 1280) * 100}%`,
        width: `${100 - 2 * (64 / 1280) * 100}%`,
        height: `${100 - 2 * (64 / 720) * 100}%`,
        border: '3px dashed rgba(255, 255, 0, 0.8)', // Yellow dashed line for safe zone
        boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.3)', // Dim outside area
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
    };

    const labelStyle = {
        backgroundColor: 'rgba(255, 255, 0, 0.8)',
        color: 'black',
        padding: '2px 6px',
        fontSize: '10px',
        fontWeight: 'bold',
        borderRadius: '3px',
        marginTop: '5px'
    };

    return React.createElement('div', { style: overlayStyle },
        React.createElement('div', { style: safeZoneStyle },
            React.createElement('span', { style: labelStyle }, 'Text Safe Zone')
        )
    );
};

// ================= Quality Toggle Component =================
const QualityToggle = ({ isLowCostMode, onToggle }) => {
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
            onToggle();
            e.preventDefault();
        }
    };

    const bgColor = isLowCostMode ? 'bg-green-600' : 'bg-red-600';
    const hoverBgColor = isLowCostMode ? 'hover:bg-green-700' : 'hover:bg-red-700';
    const ringColor = isLowCostMode ? 'focus:ring-green-500' : 'focus:ring-red-500';

    return React.createElement('div', { className: 'flex items-center justify-between py-2 px-4 bg-gray-800 rounded-lg shadow-md mt-4 w-full' }, // Added w-full
        React.createElement('div', { className: 'flex items-center gap-2 group relative' }, // Group label and tooltip icon
            React.createElement('label', {
                htmlFor: 'qualityToggle',
                className: 'text-sm font-medium text-gray-300 cursor-pointer'
            }, isLowCostMode ? 'Low-Cost Mode (Testing)' : 'High-Quality Mode (Export)'),
            React.createElement('div', {
                className: 'relative group',
                tabIndex: 0,
                'aria-label': 'Information about quality settings',
                role: 'button'
            },
                React.createElement('svg', {
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    stroke: 'currentColor',
                    className: 'w-4 h-4 text-gray-400 hover:text-purple-400 transition-colors cursor-help'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                    })
                ),
                React.createElement('div', {
                    className: 'absolute bottom-full left-1/2 transform -translate-x-1/2 -translate-y-2 w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-lg z-10 opacity-0 group-hover:opacity-100 group-focus:opacity-100 pointer-events-none transition-opacity',
                    role: 'tooltip',
                    'aria-hidden': 'true'
                },
                    React.createElement('div', {
                        className: 'flex items-start gap-2'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            stroke: 'currentColor',
                            className: 'w-4 h-4 text-purple-400 flex-shrink-0 mt-0.5'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        ),
                        React.createElement('p', null, 'Toggle between low-cost mode for testing or high-quality mode for final exports.')
                    ),
                    React.createElement('div', {
                        className: 'absolute left-1/2 top-full transform -translate-x-1/2 -translate-y-1 w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-gray-900'
                    })
                )
            )
        ),
        // The actual toggle switch
        React.createElement('button', {
            id: 'qualityToggle',
            role: 'switch',
            'aria-checked': !isLowCostMode, // Aria checked corresponds to 'High Quality' being ON
            onClick: onToggle,
            onKeyDown: handleKeyDown,
            tabIndex: '0',
            className: `${bgColor} ${hoverBgColor} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 ${ringColor} focus:ring-offset-2 focus:ring-offset-gray-900`
        },
            React.createElement('span', { className: 'sr-only' }, 'Toggle Image Quality Mode'),
            React.createElement('span', {
                'aria-hidden': 'true',
                className: `${!isLowCostMode ? 'translate-x-5' : 'translate-x-0'} inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`
            })
        )
    );
};

// ================= PremadeTemplatesSection Component =================
const PremadeTemplatesSection = ({ templatesData, expandedTemplatesData, onTemplateSelect, activeCategory, setActiveCategory, setIsAnyTemplateModalOpen, onModalStateChange }) => {
    const [modalOpen, setModalOpen] = React.useState(false);
    const [selectedCategory, setSelectedCategory] = React.useState(null);
    const [showMoreModalOpen, setShowMoreModalOpen] = React.useState(false);
    const [expandedSelectedCategory, setExpandedSelectedCategory] = React.useState(null);
    
    // Add closing states for smooth animations
    const [isClosing, setIsClosing] = React.useState(false);
    const [isShowMoreClosing, setIsShowMoreClosing] = React.useState(false);
    
    const openCategoryModal = (category) => {
        setSelectedCategory(category);
        setModalOpen(true);
        setIsAnyTemplateModalOpen(true);
        // Notify parent component about modal state
        if (onModalStateChange) {
            onModalStateChange({
                modalOpen: true,
                selectedCategory: category,
                showMoreModalOpen: false,
                expandedSelectedCategory: null
            });
        }
    };

    const closeModal = () => {
        setModalOpen(false);
        setIsAnyTemplateModalOpen(false);
        if (onModalStateChange) {
            onModalStateChange({
                modalOpen: false,
                selectedCategory: null,
                showMoreModalOpen: showMoreModalOpen,
                expandedSelectedCategory: expandedSelectedCategory
            });
        }
    };

    const openShowMoreModal = () => {
        setShowMoreModalOpen(true);
        setIsAnyTemplateModalOpen(true);
        if (onModalStateChange) {
            onModalStateChange({
                modalOpen: false,
                selectedCategory: null,
                showMoreModalOpen: true,
                expandedSelectedCategory: expandedSelectedCategory
            });
        }
    };

    const closeShowMoreModal = () => {
        setShowMoreModalOpen(false);
        setIsAnyTemplateModalOpen(false);
        if (onModalStateChange) {
            onModalStateChange({
                modalOpen: modalOpen,
                selectedCategory: selectedCategory,
                showMoreModalOpen: false,
                expandedSelectedCategory: null
            });
        }
    };

    const openExpandedCategoryModal = (category) => {
        setExpandedSelectedCategory(category);
        if (onModalStateChange) {
            onModalStateChange({
                modalOpen: modalOpen,
                selectedCategory: selectedCategory,
                showMoreModalOpen: showMoreModalOpen,
                expandedSelectedCategory: category
            });
        }
    };

    // Render a category card with modern styling
    const renderCategoryCard = (category) => {
        const isActive = activeCategory === category.id;
        const unsplashUrl = getUnsplashImageUrl(category.id);

        return React.createElement('div', {
            key: category.id,
            className: `template-category-card ${isActive ? 'active' : ''}`,
            onClick: () => openCategoryModal(category),
            role: 'button',
            'aria-expanded': isActive,
            'aria-controls': `templates-${category.id}`,
            id: `template-category-card-${category.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    openCategoryModal(category);
                }
            }
        },
            // Card content with background image (matching Figma JSON structure)
            React.createElement('div', {
                className: 'template-category-content',
                style: {
                    backgroundImage: `url(${unsplashUrl})`
                }
            },
                // Category label overlay (matching Figma JSON label structure)
                React.createElement('div', {
                    className: 'template-category-label'
                },
                    React.createElement('span', {},
                        // Remove emoji from the name if present
                        category.name.replace(/[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '').trim()
                    )
                )
            )
        );
    };

    // Render an expanded category card in the "Show More" modal
    const renderExpandedCategoryCard = (category) => {
        const isSelected = expandedSelectedCategory && expandedSelectedCategory.id === category.id;
        const unsplashUrl = getUnsplashImageUrl(category.id);

        return React.createElement('div', {
            key: category.id,
            className: `expanded-category-card relative overflow-hidden rounded-xl transition-all duration-300 cursor-pointer border
                ${isSelected
                    ? 'border-purple-500 shadow-lg shadow-purple-500/30 scale-[1.02]'
                    : 'border-transparent hover:border-purple-400 hover:shadow-md hover:shadow-purple-400/20 hover:scale-[1.01]'}`,
            onClick: () => openExpandedCategoryModal(category),
            role: 'button',
            'aria-expanded': isSelected,
            'aria-controls': `expanded-templates-${category.id}`,
            id: `expanded-category-card-${category.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    openExpandedCategoryModal(category);
                }
            }
        },
            // Card background/image with Unsplash image
            React.createElement('div', {
                className: 'expanded-category-thumbnail relative aspect-video w-full h-full rounded-xl overflow-hidden',
            },
                // Background image from Unsplash
                React.createElement('img', {
                    src: unsplashUrl,
                    alt: category.name,
                    className: 'absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110',
                    loading: 'lazy'
                }),
                // Dark overlay gradient
                React.createElement('div', {
                    className: 'absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/30'
                }),
                // Centered category name
                React.createElement('div', {
                    className: 'absolute inset-0 flex items-center justify-center p-3',
                },
                    React.createElement('span', {
                        className: 'expanded-category-name font-semibold text-white text-xl tracking-wide text-center px-4 py-2 rounded-lg bg-black/40 backdrop-blur-sm'
                    }, category.name)
                )
            )
        );
    };

    // Function to get relevant Unsplash image URLs for each category
    const getUnsplashImageUrl = (categoryId) => {
        const imageMap = {
            // Travel & Vlog - Epic mountain landscape with dramatic sky and vibrant colors
            'travel-vlog': 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?auto=format&fit=crop&w=800&q=80',

            // Health Fitness - Energetic fitness scene with dumbbells and vibrant lighting
            'health-fitness': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?auto=format&fit=crop&w=800&q=80',

            // Business - Modern skyscraper with dynamic perspective and professional feel
            'business': 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=800&q=80',

            // Art & Tech - Colorful digital art workspace with creative tools and neon lighting
            
            // Tech - Circuit board with vibrant blue and green lighting
            'tech': 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80',

            // Reactions - Expressive woman with surprised/excited expression
            'reactions': 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=800&q=80',

            // Gaming - Dynamic gaming setup with RGB lighting and controllers
            'gaming': 'https://images.unsplash.com/photo-**********-adc38448a05e?auto=format&fit=crop&w=800&q=80',

            // Legacy categories (for backward compatibility)
            'education': 'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?auto=format&fit=crop&w=800&q=80',
            'vlogging': 'https://images.unsplash.com/photo-1616469829941-c7200edec809?auto=format&fit=crop&w=800&q=80',

            // Default fallback - Creative abstract background with vibrant colors
            'default': 'https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?auto=format&fit=crop&w=800&q=80'
        };

        return imageMap[categoryId] || imageMap['default'];
    };

    // Create an "Add New" card matching template card design
    const renderAddNewCard = () => {
        return React.createElement('div', {
            key: 'add-new',
            className: 'template-category-card add-new-card',
            onClick: () => {
                // Future functionality for adding new templates
                console.log('Add new template functionality coming soon');
            },
            role: 'button',
            id: 'template-add-new',
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    console.log('Add new template functionality coming soon');
                }
            }
        },
            // Card content with consistent structure
            React.createElement('div', {
                className: 'template-category-content',
                style: {
                    // Background matching other cards but with gray tone
                    backgroundColor: 'rgba(161, 161, 170, 0.10)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative'
                }
            },
                // Solar widget-add icon (centered)
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': 'solar:widget-add-bold-duotone',
                    style: {
                        fontSize: '32px', // Consistent icon size
                        color: 'rgb(113, 113, 122)', // Gray color
                        marginBottom: '8px'
                    }
                }),

                // Category label overlay (matching other cards)
                React.createElement('div', {
                    className: 'template-category-label'
                },
                    React.createElement('span', {}, 'More Soon')
                )
            )
        );
    };

    // Render expanded template item card
    const renderExpandedTemplateCard = (template) => {
        return React.createElement('div', {
            key: template.id,
            className: 'expanded-template-item-card group relative bg-gray-800 border border-gray-700 hover:border-blue-500 rounded-lg overflow-hidden transition-all hover:shadow-md cursor-pointer',
            onClick: () => {
                onTemplateSelect(template);
                closeShowMoreModal();
            },
            role: 'button',
            id: `expanded-template-item-${template.id}`,
            tabIndex: 0,
            onKeyDown: (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onTemplateSelect(template);
                    closeShowMoreModal();
                }
            }
        },
            // Template thumbnail - REPLACED WITH TemplatePreviewImage
            React.createElement(TemplatePreviewImage, {
                template: template,
                categoryId: expandedSelectedCategory?.id || 'default',
                className: 'expanded-template-item-thumbnail'
            }),
            // Apply button overlay on hover
            React.createElement('div', {
                className: 'expanded-template-item-hover-overlay absolute inset-0 backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity',
                style: {
                    background: 'linear-gradient(145deg, rgba(0, 111, 238, 0.70) 0%, rgba(0, 111, 238, 0.50) 100%)'
                }
            },
                                    React.createElement('button', {
                        className: 'expanded-template-item-apply-btn px-4 py-2 text-white rounded-full font-medium transform transition hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500',
                        style: {
                            backgroundColor: '#006FEE',
                            '&:hover': {
                                backgroundColor: '#0056CC'
                            }
                        },
                        onMouseEnter: (e) => {
                            e.target.style.backgroundColor = '#0056CC';
                        },
                        onMouseLeave: (e) => {
                            e.target.style.backgroundColor = '#006FEE';
                        }
                    }, 'Apply Template')
            ),
            // Template info
            React.createElement('div', {
                className: 'expanded-template-item-info p-3 space-y-1'
            },
                React.createElement('h4', {
                    className: 'expanded-template-item-title font-medium text-white text-sm'
                }, template.name),
                React.createElement('p', {
                    className: 'expanded-template-item-description text-xs text-gray-400'
                }, template.description)
            )
        );
    };

    // Template Modal Component
    const renderTemplateModal = () => {
        if (!selectedCategory) return null;

        return React.createElement('div', {
            className: `template-modal-container fixed inset-0 z-[10002] flex items-center justify-center px-4 py-4 ${modalOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`,
            id: 'template-selection-modal'
        },
            // Backdrop
            React.createElement('div', {
                className: `template-modal-backdrop absolute inset-0 bg-black/70 backdrop-blur-sm transition-opacity ${modalOpen ? 'opacity-100' : 'opacity-0'}`,
                onClick: closeModal
            }),
            // Modal content
            React.createElement('div', {
                className: `template-modal-content w-full max-w-4xl bg-gray-900 rounded-xl border border-gray-700 shadow-xl transform transition-all max-h-[90vh] overflow-hidden flex flex-col ${modalOpen ? 'scale-100' : 'scale-95'}`,
                role: 'dialog',
                'aria-modal': 'true',
                'aria-labelledby': `category-title-${selectedCategory.id}`
            },
                // Header
                React.createElement('div', {
                    className: 'template-modal-header flex items-center justify-between p-4 border-b border-gray-800'
                },
                    // Title
                    React.createElement('h3', {
                        id: `category-title-${selectedCategory.id}`,
                        className: 'template-modal-title text-xl font-semibold text-white flex items-center gap-2'
                    },
                        React.createElement('span', {
                            className: `template-modal-icon flex items-center justify-center w-8 h-8 rounded ${selectedCategory.categoryImagePlaceholder.bgColor} text-white font-bold text-base`
                        }, selectedCategory.name.split(' ')[0][0]),
                        selectedCategory.name
                    ),
                    // Close button
                    React.createElement('button', {
                        className: 'template-modal-close-btn w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                        onClick: closeModal,
                        'aria-label': 'Close modal',
                        id: 'template-modal-close-button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            viewBox: '0 0 24 24',
                            fill: 'none',
                            stroke: 'currentColor',
                            strokeWidth: '2',
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            className: 'w-5 h-5'
                        },
                            React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                            React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                        )
                    )
                ),
                // Grid of templates
                React.createElement('div', {
                    className: 'template-modal-body overflow-y-auto p-4 flex-grow',
                    style: {
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#6B7280 #374151'
                    }
                },
                    React.createElement('div', {
                        className: 'template-items-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                    },
                        selectedCategory.templates.map(template =>
                            React.createElement('div', {
                                key: template.id,
                                className: 'template-item-card group relative bg-gray-800 border border-gray-700 hover:border-blue-500 rounded-lg overflow-hidden transition-all hover:shadow-md cursor-pointer',
                                onClick: () => {
                                    onTemplateSelect(template);
                                    closeModal();
                                },
                                role: 'button',
                                id: `template-item-${template.id}`,
                                tabIndex: 0,
                                onKeyDown: (e) => {
                                    if (e.key === 'Enter' || e.key === ' ') {
                                        e.preventDefault();
                                        onTemplateSelect(template);
                                        closeModal();
                                    }
                                }
                            },
                                // Template thumbnail - REPLACED WITH TemplatePreviewImage
                                React.createElement(TemplatePreviewImage, {
                                    template: template,
                                    categoryId: selectedCategory?.id || 'default',
                                    className: 'template-item-thumbnail'
                                }),
                                // Apply button overlay on hover
                                React.createElement('div', {
                                    className: 'template-item-hover-overlay absolute inset-0  backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity'
                                },
                                    React.createElement('button', {
                                        className: 'template-item-apply-btn px-4 py-2 text-white rounded-full font-medium transform transition hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500',
                                        style: {
                                            backgroundColor: '#006FEE',
                                            '&:hover': {
                                                backgroundColor: '#0056CC'
                                            }
                                        },
                                        onMouseEnter: (e) => {
                                            e.target.style.backgroundColor = '#0056CC';
                                        },
                                        onMouseLeave: (e) => {
                                            e.target.style.backgroundColor = '#006FEE';
                                        }
                                    }, 'Apply Template')
                                ),
                                // Template info
                                React.createElement('div', {
                                    className: 'template-item-info p-3 space-y-1'
                                },
                                    React.createElement('h4', {
                                        className: 'template-item-title font-medium text-white text-sm'
                                    }, template.name),
                                    React.createElement('p', {
                                        className: 'template-item-description text-xs text-gray-400'
                                    }, template.description)
                                )
                            )
                        )
                    )
                ),
                // Footer
                React.createElement('div', {
                    className: 'template-modal-footer p-4 border-t border-gray-800 flex justify-end'
                },
                    React.createElement('button', {
                        className: 'template-modal-close-button px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium',
                        onClick: closeModal,
                        id: 'template-modal-footer-close-btn'
                    }, 'Close')
                )
            )
        );
    };

    // Show More Modal Component
    const renderShowMoreModal = () => {
        return React.createElement('div', {
            className: `show-more-modal-container fixed inset-0 z-[10002] flex items-center justify-center px-4 py-4 ${showMoreModalOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`,
            id: 'show-more-selection-modal'
        },
            // Backdrop
            React.createElement('div', {
                className: `show-more-modal-backdrop absolute inset-0 bg-black/70 backdrop-blur-sm transition-opacity ${showMoreModalOpen ? 'opacity-100' : 'opacity-0'}`,
                onClick: closeShowMoreModal
            }),
            // Modal content
            React.createElement('div', {
                className: `show-more-modal-content w-full max-w-6xl bg-gray-900 rounded-xl border border-gray-700 shadow-xl transform transition-all max-h-[90vh] overflow-hidden flex flex-col ${showMoreModalOpen ? 'scale-100' : 'scale-95'}`,
                role: 'dialog',
                'aria-modal': 'true',
                'aria-labelledby': 'show-more-title'
            },
                // Header
                React.createElement('div', {
                    className: 'show-more-modal-header flex items-center justify-between p-4 border-b border-gray-800'
                },
                    // Title
                    React.createElement('h3', {
                        id: 'show-more-title',
                        className: 'show-more-modal-title text-xl font-semibold text-white flex items-center gap-2'
                    },
                        React.createElement('span', {
                            className: 'show-more-modal-icon flex items-center justify-center w-8 h-8 rounded bg-blue-600 text-white font-bold text-base'
                        }, '+'),
                        'More Template Categories'
                    ),
                    // Close button
                    React.createElement('button', {
                        className: 'show-more-modal-close-btn w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                        onClick: closeShowMoreModal,
                        'aria-label': 'Close modal',
                        id: 'show-more-modal-close-button'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            viewBox: '0 0 24 24',
                            fill: 'none',
                            stroke: 'currentColor',
                            strokeWidth: '2',
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            className: 'w-5 h-5'
                        },
                            React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                            React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                        )
                    )
                ),

                // Content section - split into two columns on larger screens
                React.createElement('div', {
                    className: 'show-more-modal-body overflow-y-auto p-4 flex-grow flex flex-col lg:flex-row gap-4',
                    style: {
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#6B7280 #374151'
                    }
                },
                    // Categories column (left)
                    React.createElement('div', {
                        className: 'categories-column lg:w-1/3'
                    },
                        React.createElement('h4', {
                            className: 'text-sm font-medium text-gray-400 mb-4'
                        }, 'Browse Categories'),

                        // Grid of expanded categories
                        React.createElement('div', {
                            className: 'expanded-categories-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4'
                        }, expandedTemplatesData.map(renderExpandedCategoryCard))
                    ),

                    // Templates column (right)
                    React.createElement('div', {
                        className: 'templates-column lg:w-2/3 bg-gray-800/50 rounded-xl p-4'
                    },
                        // Selected category details
                        expandedSelectedCategory ? React.createElement('div', { className: 'selected-category-details mb-4' },
                            React.createElement('h3', {
                                className: 'text-xl font-semibold text-white'
                            }, expandedSelectedCategory.name),
                            React.createElement('p', {
                                className: 'text-sm text-gray-400'
                            }, expandedSelectedCategory.description)
                        ) : React.createElement('div', { className: 'no-selection-message flex flex-col items-center justify-center p-6 h-full' },
                            React.createElement('p', {
                                className: 'text-lg text-gray-500 text-center'
                            }, 'Select a category to explore templates')
                        ),

                        // Template grid (if a category is selected)
                        expandedSelectedCategory && React.createElement('div', {
                            className: 'expanded-templates-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                        }, expandedSelectedCategory.templates.map(renderExpandedTemplateCard))
                    )
                ),

                // Footer
                React.createElement('div', {
                    className: 'show-more-modal-footer p-4 border-t border-gray-800 flex justify-end'
                },
                    React.createElement('button', {
                        className: 'show-more-modal-close-button px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium',
                        onClick: closeShowMoreModal,
                        id: 'show-more-modal-footer-close-btn'
                    }, 'Close')
                )
            )
        );
    };

    // Main render method - render categories in a grid with max 3 visible + show more
    return React.createElement('section', {
        className: 'premade-templates-section mt-6 mb-8',
        'aria-labelledby': 'premade-templates-heading'
    },
        // Section heading with info icon
        React.createElement('div', {
            className: 'flex items-center gap-2 mb-4'
        },
            React.createElement('h2', {
                id: 'premade-templates-heading',
                className: 'text-xl font-semibold text-white flex items-center gap-2'
            },
                React.createElement('span', {
                    className: 'iconify w-5 h-5 text-blue-400',
                    'data-icon': 'solar:gallery-wide-outline'
                }),
                'Premade Templates'
            ),
            // Info icon with tooltip (hidden per user request)
            // createFixedTooltip(
            //     'These templates provide inspiration and starting points for your YouTube thumbnails. Select one to quickly generate a professional-looking design.',
            //     'premade-templates-info'
            // )
        ),

        // Grid of categories - 2 column layout
        React.createElement('div', {
            className: 'templates-grid grid grid-cols-2 gap-2 w-full max-w-[324px]'
        },
            // Display all 5 categories
            templatesData.map(category => renderCategoryCard(category)),

            // Add New card as the 6th item
            renderAddNewCard()
        )
    );
};

// Add this before the App component (around line 1525)
const ResetButton = ({ onClick, isLoading = false }) => {
    const isDisabled = isLoading;
    
    return React.createElement('button', {
        onClick: isDisabled ? undefined : onClick,
        disabled: isDisabled,
        className: `reset-button flex items-center gap-2 px-3 py-1.5 bg-transparent rounded-lg text-sm font-medium transition-all duration-200 ${
            isDisabled 
                ? 'opacity-50 cursor-not-allowed text-gray-500' 
                : 'hover:bg-gray-700/30 hover:text-gray-300 hover:border-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900'
        }`,
        'aria-label': isDisabled ? 'Reset disabled during generation' : 'Reset all settings',
        title: isDisabled ? 'Please wait for generation to complete' : 'Reset all settings'
    }, 
        React.createElement('span', {
            className: 'iconify',
            'data-icon': 'solar:refresh-bold-duotone',
            style: { 
                width: '16px', 
                height: '16px', 
                color: isDisabled ? '#6B7280' : '#F9C97C',
                transition: 'color 200ms ease-in-out'
            }
        }),
        'Reset'
    );
};

// Hero UI Kit compliant success toast component with enhanced close animation
const SuccessToast = ({ isVisible, message, type = 'success', onDismiss }) => {
    if (!isVisible) return null;

    const getToastConfig = () => {
        switch (type) {
            case 'success':
                return {
                    icon: 'solar:check-circle-bold',
                    iconColor: '#10B981', // green-500
                    bgColor: 'bg-gray-800',
                    borderColor: 'border-green-500/20',
                    textColor: 'text-green-400'
                };
            case 'info':
                return {
                    icon: 'solar:info-circle-bold',
                    iconColor: '#3B82F6', // blue-500
                    bgColor: 'bg-gray-800',
                    borderColor: 'border-blue-500/20',
                    textColor: 'text-blue-400'
                };
            case 'warning':
                return {
                    icon: 'solar:danger-triangle-bold',
                    iconColor: '#F59E0B', // amber-500
                    bgColor: 'bg-gray-800',
                    borderColor: 'border-amber-500/20',
                    textColor: 'text-amber-400'
                };
            case 'error':
                return {
                    icon: 'solar:close-circle-bold',
                    iconColor: '#EF4444', // red-500
                    bgColor: 'bg-gray-800',
                    borderColor: 'border-red-500/20',
                    textColor: 'text-red-400'
                };
            default:
                return {
                    icon: 'solar:check-circle-bold',
                    iconColor: '#10B981',
                    bgColor: 'bg-gray-800',
                    borderColor: 'border-green-500/20',
                    textColor: 'text-green-400'
                };
        }
    };

    const config = getToastConfig();

    // Enhanced liquid close handler with smooth animation
    const handleClose = () => {
        if (onDismiss) {
            // Add liquid glass slide-out animation
            const toastElement = document.querySelector('.success-toast-container');
            if (toastElement) {
                toastElement.style.animation = 'liquidSlideOutRight 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)';
                toastElement.style.opacity = '0';
            }
            
            // Call the dismiss handler after matching delay
            setTimeout(() => {
                onDismiss();
            }, 495); // Match the animation duration
        }
    };

    return React.createElement('div', {
        className: 'success-toast-container fixed top-6 right-6 z-[10001] transform transition-all ease-out',
        style: {
            animation: isVisible ? 'liquidSlideInRight 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)' : 'liquidSlideOutRight 495ms cubic-bezier(0.94, 0.45, 0.46, 0.25)',
            opacity: isVisible ? 1 : 0,
            transition: 'opacity 495ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            perspective: '1000px'
        }
    },
        React.createElement('div', {
            className: `liquid-glass-toast-inverted border backdrop-blur-md rounded-2xl p-5 flex items-center gap-4 min-w-[400px] max-w-[520px] transition-all duration-300 ease-out`,
            style: {
                backdropFilter: 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)', // Success toast backdrop filter
                WebkitBackdropFilter: 'blur(20px) saturate(180%) brightness(1.1) contrast(1.05)',
                background: 'linear-gradient(135deg, #16A34A 0%, #15803D 100%)', // Bold green gradient
                border: '1px solid rgba(255, 255, 255, 0.2)', // White border for contrast
                boxShadow: '0 25px 50px -12px rgba(22, 163, 74, 0.4), 0 8px 16px -8px rgba(22, 163, 74, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                borderRadius: '16px',
                position: 'relative'
            }
        },
            // Enhanced Icon with macOS styling
            React.createElement('div', {
                className: 'flex-shrink-0 liquid-toast-icon-container',
                style: {
                    width: '44px',
                    height: '44px',
                    borderRadius: '12px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255, 255, 255, 0.15)', // Semi-transparent white background
                    border: '1px solid rgba(255, 255, 255, 0.25)', // White border for contrast
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)'
                }
            },
                React.createElement('span', {
                    className: 'iconify',
                    'data-icon': config.icon,
                    style: {
                        fontSize: '22px', // Optimized for new container
                        color: '#ffffff' // Pure white for maximum contrast
                    }
                })
            ),
                
                // Enhanced Message content with improved typography
                React.createElement('div', {
                    className: 'flex-1 liquid-toast-content'
                },
                    React.createElement('p', {
                        className: `text-base font-semibold leading-6 tracking-tight text-white`,
                        style: {
                            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", system-ui, sans-serif',
                            fontSize: '15px',
                            fontWeight: '600',
                            lineHeight: '1.4',
                            letterSpacing: '-0.01em',
                            color: '#ffffff' // Pure white for maximum contrast and accessibility
                        }
                    }, message)
                ),
                
                // Enhanced liquid close button
                React.createElement('button', {
                    onClick: handleClose,
                    className: 'liquid-close-button flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2 focus:ring-offset-transparent',
                    'aria-label': 'Close notification',
                    style: {
                        width: '32px',
                        height: '32px',
                        borderRadius: '10px',
                        background: 'rgba(255, 255, 255, 0.12)',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)',
                        transition: 'all 300ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                        flexShrink: 0
                    },
                    onMouseEnter: (e) => {
                        e.target.style.transform = 'scale(1.05)';
                        e.target.style.background = 'rgba(255, 255, 255, 0.2)';
                        e.target.style.boxShadow = '0 4px 12px rgba(255, 255, 255, 0.15)';
                    },
                    onMouseLeave: (e) => {
                        e.target.style.transform = 'scale(1)';
                        e.target.style.background = 'rgba(255, 255, 255, 0.12)';
                        e.target.style.boxShadow = 'none';
                    }
                },
                    React.createElement('span', {
                        className: 'iconify',
                        'data-icon': 'solar:close-circle-bold',
                        style: {
                            fontSize: '18px',
                            color: '#ffffff',
                            transition: 'all 150ms ease-out'
                        }
                    })
                )
        )
    );
};

// Enhanced fixed positioning tooltip with macOS Liquid Glass styling
const createFixedTooltip = (tooltipText, tooltipId = '') => {
    const iconRef = React.useRef(null);
    const tooltipRef = React.useRef(null);
    const [isVisible, setIsVisible] = React.useState(false);
    const [isClosing, setIsClosing] = React.useState(false);
    const [position, setPosition] = React.useState({ top: 0, left: 0, placement: 'bottom' });

    const calculatePosition = React.useCallback(() => {
        if (!iconRef.current || !tooltipRef.current) return;

        const iconRect = iconRef.current.getBoundingClientRect();
        const tooltipRect = tooltipRef.current.getBoundingClientRect();
        
        const tooltipWidth = tooltipRect.width || 280;
        const tooltipHeight = tooltipRect.height || 60;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const padding = 12;
        
        let newPosition = { top: 0, left: 0, placement: 'bottom' };

        // Prefer positioning below the icon first
        if (iconRect.bottom + tooltipHeight + padding <= viewportHeight) {
            newPosition.top = iconRect.bottom + padding;
            newPosition.left = iconRect.left + (iconRect.width / 2) - (tooltipWidth / 2);
            newPosition.placement = 'bottom';
        }
        // If not enough space below, try above
        else if (iconRect.top - tooltipHeight - padding >= 0) {
            newPosition.top = iconRect.top - tooltipHeight - padding;
            newPosition.left = iconRect.left + (iconRect.width / 2) - (tooltipWidth / 2);
            newPosition.placement = 'top';
        }
        // Position to the right if needed
        else if (iconRect.right + tooltipWidth + padding <= viewportWidth) {
            newPosition.top = iconRect.top + (iconRect.height / 2) - (tooltipHeight / 2);
            newPosition.left = iconRect.right + padding;
            newPosition.placement = 'right';
        }
        // Last resort: position to the left
        else {
            newPosition.top = iconRect.top + (iconRect.height / 2) - (tooltipHeight / 2);
            newPosition.left = iconRect.left - tooltipWidth - padding;
            newPosition.placement = 'left';
        }

        // Viewport boundary checks
        if (newPosition.left < padding) {
            newPosition.left = padding;
        } else if (newPosition.left + tooltipWidth > viewportWidth - padding) {
            newPosition.left = viewportWidth - tooltipWidth - padding;
        }

        if (newPosition.top < padding) {
            newPosition.top = padding;
        } else if (newPosition.top + tooltipHeight > viewportHeight - padding) {
            newPosition.top = viewportHeight - tooltipHeight - padding;
        }

        setPosition(newPosition);
        
        // Recalculate if dimensions not available yet
        if (tooltipRect.width === 0 || tooltipRect.height === 0) {
            setTimeout(() => calculatePosition(), 50);
        }
    }, []);

    const handleMouseEnter = () => {
        setIsClosing(false);
        setIsVisible(true);
        setTimeout(() => calculatePosition(), 10);
    };

    const handleMouseLeave = () => {
        setIsClosing(true);
        setTimeout(() => {
            setIsVisible(false);
            setIsClosing(false);
        }, 250); // Match CSS transition duration
    };

    const handleFocus = () => {
        setIsClosing(false);
        setIsVisible(true);
        setTimeout(() => calculatePosition(), 10);
    };

    const handleBlur = () => {
        setIsClosing(true);
        setTimeout(() => {
            setIsVisible(false);
            setIsClosing(false);
        }, 250);
    };

    React.useEffect(() => {
        const handleUpdate = () => {
            if (isVisible && !isClosing) {
                calculatePosition();
            }
        };

        window.addEventListener('scroll', handleUpdate, true);
        window.addEventListener('resize', handleUpdate);

        return () => {
            window.removeEventListener('scroll', handleUpdate, true);
            window.removeEventListener('resize', handleUpdate);
        };
    }, [isVisible, isClosing, calculatePosition]);

    const getArrowClass = () => {
        switch (position.placement) {
            case 'bottom':
                return 'tooltip-arrow-top'; // Arrow points up when tooltip is below
            case 'top':
                return 'tooltip-arrow-bottom'; // Arrow points down when tooltip is above
            case 'right':
                return 'tooltip-arrow-left'; // Arrow points left when tooltip is to the right
            case 'left':
                return 'tooltip-arrow-right'; // Arrow points right when tooltip is to the left
            default:
                return 'tooltip-arrow-top';
        }
    };

    return React.createElement('div', {
        className: 'tooltip-icon-wrapper relative inline-block',
        onMouseEnter: handleMouseEnter,
        onMouseLeave: handleMouseLeave,
        onFocus: handleFocus,
        onBlur: handleBlur,
        tabIndex: 0,
        'aria-label': 'Information about this setting',
        role: 'button'
    },
        React.createElement('svg', {
            ref: iconRef,
            xmlns: 'http://www.w3.org/2000/svg',
            fill: 'none',
            viewBox: '0 0 24 24',
            stroke: 'currentColor',
            className: 'w-4 h-4 text-gray-400 hover:text-yellow-400 transition-colors cursor-help',
            style: {
                color: '#9CA3AF' // Default gray-400
            },
            onMouseEnter: (e) => {
                e.target.style.color = '#FFD700'; // Gold hover color
                e.target.style.transform = 'scale(1.1)';
            },
            onMouseLeave: (e) => {
                e.target.style.color = '#9CA3AF'; // Back to gray-400
                e.target.style.transform = 'scale(1)';
            }
        },
            React.createElement('path', {
                strokeLinecap: 'round',
                strokeLinejoin: 'round',
                strokeWidth: 2,
                d: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
            })
        ),
        // Fixed position tooltip
        isVisible && React.createElement('div', {
            ref: tooltipRef,
            className: `tooltip-fixed fixed text-xs rounded-lg shadow-xl p-3 transition-opacity duration-200 pointer-events-none text-white`,
            style: {
                top: `${position.top}px`,
                left: `${position.left}px`,
                zIndex: 9999,
                maxWidth: '320px',
                opacity: isVisible ? 1 : 0,
                visibility: isVisible ? 'visible' : 'hidden',
                backgroundColor: '#1A1A1A', // Dark background for high contrast
                border: '1px solid #4A4A4A' // Subtle border for definition
            },
            role: 'tooltip',
            'aria-hidden': !isVisible,
            id: tooltipId
        },
            React.createElement('div', {
                className: 'relative z-10',
                style: {
                    margin: 0,
                    padding: 0,
                    lineHeight: '1.4'
                }
            }, tooltipText),
            React.createElement('div', { className: `tooltip-arrow-fixed ${getArrowClass()}` })
        )
    );
};

// ================= Top Navigation Component =================
const TopNavigation = ({
    user = null,
    onSignOut = null,
    onNavigateToProfile = null,
    isUserDropdownOpen,
    isDropdownClosing,
    toggleUserDropdown,
    closeDropdownWithAnimation,
    onOpenSubscriptionModal = null
}) => {
    const [displayUser, setDisplayUser] = React.useState(user);

    // Add helper function to get consistent avatar URL
    const getConsistentAvatarUrl = (user) => {
        if (!user?.user_metadata) return null;
        
        const metadata = user.user_metadata;
        
        // If it's a Supabase-stored avatar, ensure we use the storage URL
        if (metadata.avatar_source === 'supabase' && metadata.avatar_path) {
            const { data } = supabase.storage
                .from('user-avatars')
                .getPublicUrl(metadata.avatar_path);
            return data?.publicUrl || metadata.avatar_url;
        }
        
        // Otherwise use the stored URL
        return metadata.avatar_url;
    };

    // Listen for avatar update events to refresh the UI without page reload
    React.useEffect(() => {
        const handleAvatarUpdate = (event) => {
            const { newUser } = event.detail;
            setDisplayUser(newUser);
        };

        window.addEventListener('userAvatarUpdated', handleAvatarUpdate);
        
        return () => {
            window.removeEventListener('userAvatarUpdated', handleAvatarUpdate);
        };
    }, []);

    // Update displayUser when user prop changes
    React.useEffect(() => {
        setDisplayUser(user);
    }, [user]);

    // Get the current avatar URL
    const avatarUrl = getConsistentAvatarUrl(displayUser);

    const [isPricingModalOpen, setIsPricingModalOpen] = useState(false);
    const [showNotifications, setShowNotifications] = useState(false);
    const [notificationUnreadCount, setNotificationUnreadCount] = useState(0);
    const [isModalClosing, setIsModalClosing] = useState(false); // New state for closing animation
    const [showUserMenu, setShowUserMenu] = useState(false); // Add state for user menu
    const dropdownRef = useRef(null);
    const avatarButtonRef = useRef(null);

    // Use actual user data or fallback to defaults
    const currentUser = user || {
        name: 'User',
        email: '<EMAIL>',
        avatar: null,
        plan: 'free',
        credits: 750,
        maxCredits: 1000
    };

    // Get user display info
    const getUserInfo = () => {
        if (displayUser) {
            return {
                name: displayUser.user_metadata?.full_name || getUserDisplayName(),
                email: displayUser.email || '<EMAIL>',
                plan: displayUser.app_metadata?.plan || 'free',
                credits: displayUser.app_metadata?.credits || 750,
                maxCredits: displayUser.app_metadata?.max_credits || 1000
            };
        }
        return currentUser;
    };

    const userInfo = getUserInfo();

    // Calculate credits percentage
    const creditsPercentage = (userInfo.credits / userInfo.maxCredits) * 100;

    // Handle demo sign out
    const handleDemoSignOut = () => {
        if (onSignOut) {
            onSignOut();
        }
    };

    const getUserDisplayName = () => {
        if (displayUser?.user_metadata?.full_name) {
            return displayUser.user_metadata.full_name;
        }
        if (displayUser?.email) {
            return displayUser.email.split('@')[0];
        }
        return 'User';
    };

    const getUserInitials = () => {
        const name = getUserDisplayName();
        if (!name) return 'U';
        return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    };



    // Pricing plans configuration
    const pricingPlans = [
        {
            id: 'free',
            name: 'Free Plan',
            price: '$0',
            period: '/month',
            description: 'Perfect for getting started',
            features: [
                '5 generations per month',
                'Basic templates',
                'Standard quality'
            ],
            buttonText: 'Current Plan',
            isCurrentPlan: userInfo.plan === 'free',
            highlight: false,
            color: '#10B981', // Green for current plan
            badgeText: 'Current Plan',
            badgeColor: 'success'
        },
        {
            id: 'basic',
            name: 'Basic Plan',
            price: '$19',
            period: '/month',
            description: 'Enhanced features for creators',
            features: [
                'Unlimited generations',
                'Premium templates',
                'Advanced text styling',
                'Priority support'
            ],
            buttonText: userInfo.plan === 'basic' ? 'Current Plan' : 'Upgrade to Basic',
            isCurrentPlan: userInfo.plan === 'basic',
            highlight: true,
            color: '#8B5CF6', // Purple
            mostPopular: true,
            badgeText: 'Most Popular',
            badgeColor: 'secondary'
        },
        {
            id: 'pro',
            name: 'Pro Plan',
            price: '$49',
            period: '/month',
            description: 'Complete toolkit for professionals',
            features: [
                'Everything in Basic',
                'AI recommendations',
                'Batch generation',
                'Custom presets',
                'White-label options'
            ],
            buttonText: userInfo.plan === 'pro' ? 'Current Plan' : 'Upgrade to Pro',
            isCurrentPlan: userInfo.plan === 'pro',
            highlight: false,
            color: '#F59E0B' // Amber/Gold
        }
    ];

    // Note: toggleUserDropdown and closeDropdownWithAnimation are now passed as props from App component

    // Ensure notifications close when dropdown opens
    useEffect(() => {
        if (isUserDropdownOpen && showNotifications) {
            setShowNotifications(false);
        }
    }, [isUserDropdownOpen]);

    // Handle pricing plan selection
    const handlePlanSelect = (planId) => {
        if (planId === userInfo.plan) return; // Already on this plan

        // Find the selected plan and open subscription modal
        const selectedPlan = pricingPlans.find(plan => plan.id === planId);
        if (selectedPlan && !selectedPlan.isCurrentPlan && onOpenSubscriptionModal) {
            onOpenSubscriptionModal(planId);
            setIsUserDropdownOpen(false);
            setIsPricingModalOpen(false);
        }
    };

    // Handle clicking outside to close dropdowns
    useEffect(() => {
        const handleClickOutside = (event) => {
            // Check user dropdown - only close if clicking outside the dropdown elements
            // and not on other UI components like tabs
            if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&
                avatarButtonRef.current && !avatarButtonRef.current.contains(event.target)) {

                // Additional check: don't close if clicking on main app UI elements
                const clickedElement = event.target;
                const isTabElement = clickedElement.closest('#editor-templates-tab-group') ||
                    clickedElement.closest('.main-tab-button') ||
                    clickedElement.closest('.main-tab-navigation-container');

                // Only close dropdown if not clicking on tabs or other important UI
                if (!isTabElement && isUserDropdownOpen) {
                    closeDropdownWithAnimation();
                }
            }
        };

        if (isUserDropdownOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('touchstart', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);
        };
    }, [isUserDropdownOpen]);




    return React.createElement('header', {
        className: 'top-navigation fixed top-0 left-0 right-0 z-50 backdrop-blur-sm border-b border-gray-700/50',
        style: { height: '62px', backgroundColor: 'rgba(33, 41, 54, 0.8)' } // Slightly transparent
    },
        React.createElement('div', {
            className: 'top-nav-container flex items-center h-full px-4 md:px-6 justify-between md:justify-between' // Responsive layout
        },
            // Left side - Empty spacer on mobile, logo container on desktop
            React.createElement('div', {
                className: 'top-nav-left flex items-center flex-1 md:flex-initial'
            }),
            // Center - Logo (Mobile centered, Desktop left-aligned)
            React.createElement('div', {
                className: 'top-nav-center flex items-center justify-center md:justify-start absolute md:relative left-1/2 md:left-0 transform -translate-x-1/2 md:transform-none md:order-first'
            },
                React.createElement('img', {
                    className: 'top-nav-logo-img',
                    src: '/assets/main-logo.svg',
                    alt: 'ThumbSpark Logo',
                    style: { 
                        width: 'auto', 
                        height: '42px',
                        maxHeight: '42px',
                        objectFit: 'contain',
                        filter: 'brightness(1)',
                        transition: 'filter 0.2s ease'
                    },
                    onError: (e) => {
                        console.error('Logo failed to load:', e.target.src);
                        e.target.style.display = 'none';
                    }
                })
            ),
            // Right side - User account elements
            React.createElement('div', {
                className: 'top-nav-user flex items-center gap-3 md:gap-4 flex-1 md:flex-initial justify-end' // Adjusted gap and positioning
            },
                // Real user authentication - demo sign out button removed

                // User-specific items (Notification and User Menu)
                user && React.createElement(React.Fragment, null,
                    // Notification Badge (using new NotificationBadge component) - wrapped in relative container
                    React.createElement('div', { className: 'relative' },
                        React.createElement(NotificationBadge, {
                            user: user,
                            onClick: () => setShowNotifications(prev => !prev),
                            unreadCount: notificationUnreadCount
                        }),
                        // Render NotificationSystem as dropdown
                        React.createElement(NotificationSystem, {
                            user,
                            isOpen: showNotifications,
                            onClose: () => setShowNotifications(false),
                            onNotificationUpdate: setNotificationUnreadCount,
                            onOpenDashboard: () => {
                                setShowNotifications(false);
                                if (onNavigateToProfile) {
                                    onNavigateToProfile();
                                }
                            }
                        })
                    ),

                    // User Menu (Avatar/Initials and Dropdown)
                    React.createElement('div', { className: 'relative', ref: dropdownRef },
                        React.createElement('button', {
                            ref: avatarButtonRef,
                            onClick: toggleUserDropdown,
                            className: 'flex items-center justify-center w-9 h-9 md:w-10 md:h-10 bg-gray-700 rounded-full text-white text-sm font-semibold hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-purple-500 overflow-hidden',
                            id: 'user-menu-button',
                            'aria-expanded': isUserDropdownOpen,
                            'aria-haspopup': 'true'
                        },
                            avatarUrl ? 
                                React.createElement('img', {
                                    src: `${avatarUrl}?t=${new Date().getTime()}`,
                                    alt: 'User avatar',
                                    className: 'w-full h-full object-cover'
                                }) :
                            React.createElement('span', {
                                className: 'iconify',
                                'data-icon': 'solar:user-bold',
                                style: { fontSize: '20px', color: 'white' }
                            })
                        ),
                        // Comprehensive Dropdown Menu
                        isUserDropdownOpen && React.createElement('div', {
                            className: 'user-dropdown-menu absolute top-full right-0 mt-2 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-50 overflow-hidden',
                            style: {
                                width: '320px',
                                maxHeight: '80vh',
                                overflowY: 'auto',
                                animation: isDropdownClosing ? 'slideUp 200ms ease-out forwards' : 'slideDown 200ms ease-out'
                            }
                        },
                            // User Profile Section
                            React.createElement('div', {
                                className: 'user-profile-section p-4 border-b border-gray-700'
                            },
                                React.createElement('div', {
                                    className: 'flex items-center gap-3'
                                },
                                    // User Avatar
                                    React.createElement('div', {
                                        className: 'user-avatar w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center overflow-hidden'
                                    },
                                        avatarUrl ? 
                                            React.createElement('img', {
                                                src: `${avatarUrl}?t=${new Date().getTime()}`,
                                                alt: 'User avatar',
                                                className: 'w-full h-full object-cover'
                                            }) :
                                        React.createElement('span', {
                                            className: 'iconify',
                                            'data-icon': 'solar:user-bold',
                                            style: { fontSize: '20px', color: 'white' }
                                        })
                                    ),
                                    // User Info
                                    React.createElement('div', {
                                        className: 'user-info flex-1'
                                    },
                                        React.createElement('h3', {
                                            className: 'user-name text-white font-medium text-base truncate'
                                        }, userInfo.name),
                                        React.createElement('p', {
                                            className: 'user-email text-gray-400 text-sm truncate'
                                        }, userInfo.email)
                                    )
                                )
                            ),

                            // Upgrade to Pro Section
                            React.createElement('div', {
                                className: 'upgrade-section p-4 bg-gradient-to-r from-purple-900/50 to-blue-900/50 border-b border-gray-700'
                            },
                                React.createElement('div', {
                                    className: 'flex items-center justify-between mb-2'
                                },
                                    React.createElement('span', {
                                        className: 'text-purple-400 font-medium'
                                    }, 'Upgrade to Pro'),
                                    React.createElement('span', {
                                        className: 'iconify text-yellow-400',
                                        'data-icon': 'solar:crown-bold',
                                        style: { fontSize: '18px' }
                                    })
                                ),
                                React.createElement('p', {
                                    className: 'text-gray-300 text-sm mb-3'
                                }, 'Get unlimited AI generations and premium features'),
                                React.createElement('button', {
                                    className: 'pro-upgrade-cta-btn w-full text-white py-2 px-4 rounded-lg transition-colors',
                                    id: 'pro-upgrade-cta-btn',
                                    onClick: () => {
                                        closeDropdownWithAnimation();
                                        setTimeout(() => setIsPricingModalOpen(true), 250); // Delay to allow dropdown to close first
                                    }
                                }, React.createElement('span', {
                                    className: 'pro-upgrade-cta-text',
                                    id: 'pro-upgrade-cta-text'
                                }, 'Upgrade Now'))
                            ),

                                        // Available Credits Section
                            React.createElement('div', {
                                className: 'credits-section p-4 border-b border-gray-700'
                            },
                                        React.createElement('div', {
                                            className: 'credits-header flex items-center justify-between mb-2'
                                        },
                                            React.createElement('span', {
                                                className: 'credits-label text-gray-400 text-sm'
                                            }, 'Available Credits'),
                                            React.createElement('span', {
                                                className: 'credits-count text-white font-medium'
                                            }, `${userInfo.credits}/${userInfo.maxCredits}`)
                                        ),
                                        // Credits Progress Bar
                                        React.createElement('div', {
                                            className: 'credits-progress-container w-full bg-gray-700 rounded-full h-2'
                                        },
                                            React.createElement('div', {
                                                className: 'credits-progress-bar bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300',
                                                style: { width: `${creditsPercentage}%` }
                                            })
                                )
                            ),

                            // Menu Items Section
                            React.createElement('div', {
                                className: 'menu-items-section py-2'
                            },
                                // Demo Info - DISABLED
                                /*
                                React.createElement('div', {
                                    className: 'demo-info-item flex items-center gap-3 px-4 py-3 bg-blue-900/30 border border-blue-500/30 mx-3 mb-2 rounded-lg'
                                },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:info-circle-bold',
                                        style: { fontSize: '18px', color: '#60A5FA' }
                                    }),
                                    React.createElement('span', {
                                        className: 'text-blue-300 text-sm font-medium'
                                    }, 'Demo Mode - Explore all features')
                                ),
                                */
                                // View Profile
                                React.createElement('button', {
                                    className: 'menu-item w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-700 transition-colors text-left',
                                    onClick: () => {
                                        closeDropdownWithAnimation();
                                        if (onNavigateToProfile) {
                                            setTimeout(() => onNavigateToProfile(), 250); // Delay to allow dropdown to close first
                                        }
                                    }
                                },
                                    React.createElement('span', {
                                        className: 'iconify menu-icon',
                                        'data-icon': 'solar:user-circle-linear',
                                        style: { fontSize: '18px', color: '#D4D4D8' }
                                    }),
                                    React.createElement('span', {
                                        className: 'menu-text font-medium',
                                        style: { color: '#D4D4D8' }
                                    }, 'View Profile')
                                ),
                                // Sign Out
                                React.createElement('button', {
                                    className: 'menu-item w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-700 transition-colors text-left',
                                    onClick: () => {
                                        closeDropdownWithAnimation();
                                        if (onSignOut) {
                                            setTimeout(() => onSignOut(), 250); // Delay to allow dropdown to close first
                                        }
                                    }
                                },
                                    React.createElement('span', {
                                        className: 'iconify menu-icon',
                                        'data-icon': 'solar:logout-3-linear',
                                        style: { fontSize: '18px', color: '#D4D4D8' }
                                    }),
                                    React.createElement('span', {
                                        className: 'menu-text font-medium',
                                        style: { color: '#D4D4D8' }
                                    }, 'Sign Out')
                                )
                            )
                        )
                    ) // End of React.Fragment for user items
                ),
                
                // Pricing Modal - MacOS Liquid Glass Design
                isPricingModalOpen && React.createElement('div', {
                    className: 'modal-backdrop',
                    style: { 
                        backgroundColor: 'rgba(0, 0, 0, 0.6)',
                        backdropFilter: 'blur(8px)',
                        WebkitBackdropFilter: 'blur(8px)',
                        position: 'fixed',
                        top: '0',
                        left: '0',
                        right: '0',
                        bottom: '0',
                        zIndex: 9999,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        padding: '1rem',
                        height: '100vh',
                        width: '100vw'
                    },
                    onClick: () => setIsPricingModalOpen(false)
                },
                    React.createElement('div', {
                        className: 'modal-liquid-glass-container',
                            onClick: (e) => e.stopPropagation()
                        },
                            // Modal Header
                            React.createElement('div', {
                            className: 'modal-glass-header'
                            },
                            // Close Button
                                React.createElement('button', {
                                    onClick: () => setIsPricingModalOpen(false),
                                className: 'modal-glass-close-button',
                                'aria-label': 'Close modal'
                                },
                                    React.createElement('span', {
                                        className: 'iconify',
                                        'data-icon': 'solar:close-circle-bold',
                                        style: { fontSize: '24px' }
                                    })
                                ),
                            
                            // Header Content
                            React.createElement('div', {
                                className: 'modal-glass-header-content'
                            },
                                React.createElement('h2', {
                                    className: 'modal-glass-title'
                                }, 'Choose Your Plan'),
                                React.createElement('p', {
                                    className: 'modal-glass-subtitle'
                                }, 'Unlock the full potential of AI thumbnail generation with premium features')
                            )
                            ),
                            
                        // Modal Body
                            React.createElement('div', {
                            className: 'modal-glass-body'
                            },
                                React.createElement('div', {
                                className: 'pricing-plans-grid'
                                },
                                    pricingPlans.map((plan, index) => 
                                        React.createElement('div', {
                                            key: plan.id,
                                        className: `pricing-plan-card ${
                                            plan.highlight ? 'highlighted-plan' : ''
                                        } ${
                                            plan.isCurrentPlan ? 'current-plan' : ''
                                        }`
                                    },
                                        // Plan Badge
                                            (plan.mostPopular || plan.isCurrentPlan) && React.createElement('div', {
                                            className: `plan-badge ${
                                                plan.mostPopular ? 'most-popular' : 'current-plan-badge'
                                            }`
                                            },
                                                React.createElement('div', {
                                                className: 'plan-badge-text'
                                            }, plan.badgeText || (plan.mostPopular ? 'MOST POPULAR' : 'CURRENT PLAN'))
                                            ),
                                            
                                            // Plan Content
                                            React.createElement('div', {
                                            className: 'plan-content'
                                        },
                                            // Plan Header
                                            React.createElement('div', {
                                                className: 'plan-header'
                                            },
                                                React.createElement('h3', {
                                                    className: 'plan-name'
                                                }, plan.name),
                                                React.createElement('div', {
                                                    className: 'plan-price-container'
                                                },
                                                    React.createElement('span', {
                                                        className: 'plan-price'
                                                    }, plan.price),
                                                    React.createElement('span', {
                                                        className: 'plan-period'
                                                    }, plan.period)
                                                ),
                                                React.createElement('p', {
                                                    className: 'plan-description'
                                                }, plan.description)
                                            ),
                                            
                                            // Plan Features
                                                React.createElement('ul', {
                                                className: 'plan-features'
                                                },
                                                    plan.features.map((feature, featureIndex) =>
                                                        React.createElement('li', {
                                                            key: featureIndex,
                                                        className: 'plan-feature'
                                                            },
                                                                React.createElement('span', {
                                                            className: 'feature-icon iconify',
                                                            'data-icon': 'solar:check-circle-bold'
                                                        }),
                                                            React.createElement('span', {
                                                            className: 'feature-text'
                                                            }, feature)
                                                    )
                                                )
                                            ),

                                            // Action Button
                                            React.createElement('button', {
                                                onClick: () => handlePlanSelect(plan.id),
                                                disabled: plan.isCurrentPlan,
                                                className: `plan-action-button ${
                                                    plan.isCurrentPlan ? 'disabled' : plan.highlight ? 'highlighted' : 'standard'
                                                }`
                                            }, plan.buttonText)
                                        )
                                    )
                                        )
                                    )
                                ),
                                
                        // Modal Footer
                                React.createElement('div', {
                            className: 'modal-glass-footer'
                                },
                                    React.createElement('div', {
                                className: 'footer-guarantees'
                                    },
                                        React.createElement('div', {
                                    className: 'guarantee-item'
                                        },
                                            React.createElement('span', {
                                        className: 'guarantee-icon iconify',
                                        'data-icon': 'solar:shield-check-bold'
                                            }),
                                    React.createElement('span', {
                                        className: 'guarantee-text'
                                    }, '30-day money back guarantee')
                                        ),
                                        React.createElement('div', {
                                    className: 'guarantee-item'
                                        },
                                            React.createElement('span', {
                                        className: 'guarantee-icon iconify',
                                        'data-icon': 'solar:card-bold'
                                            }),
                                    React.createElement('span', {
                                        className: 'guarantee-text'
                                    }, 'Secure payment processing')
                                        ),
                                        React.createElement('div', {
                                    className: 'guarantee-item'
                                        },
                                            React.createElement('span', {
                                        className: 'guarantee-icon iconify',
                                        'data-icon': 'solar:refresh-bold'
                                    }),
                                    React.createElement('span', {
                                        className: 'guarantee-text'
                                    }, 'Cancel anytime')
                                )
                            )
                        )
                    )
                )
            )
        )
    );
};

export const App = ({ user = null, onSignOut = null }) => {


    // State Management
    const [userPrompt, setUserPrompt] = useState(''); // full internal prompt
    const [visiblePrompt, setVisiblePrompt] = useState(''); // summary shown in UI
    const [includePerson, setIncludePerson] = useState(false); // Default to OFF
    const [includeIcons, setIncludeIcons] = useState(false);
    const [textOverlay, setTextOverlay] = useState(true); // Default to ON
    const [selectedExpression, setSelectedExpression] = useState('Default'); // New state
    const [imageURL, setImageURL] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [progress, setProgress] = useState(0); // NEW: Progress state for premium loading indicator
    const [finalPrompt, setFinalPrompt] = useState(''); // Store the built prompt
    const [errorMsg, setErrorMsg] = useState('');
    const [showLayoutSimulator, setShowLayoutSimulator] = useState(false); // State for layout simulator visibility
    const [showSafeZone, setShowSafeZone] = useState(false); // State for safe zone overlay visibility
    const [isLowCostMode, setIsLowCostMode] = useState(true); // Will be deprecated by selectedQuality but kept for UI
    const [selectedQuality, setSelectedQuality] = useState('normal'); // 'low', 'normal', 'hd'
    const [fitFullCanvas, setFitFullCanvas] = useState(true);
    const [overlayText, setOverlayText] = useState(''); // State for custom overlay text
    const [isEditingOverlayText, setIsEditingOverlayText] = useState(false); // State to track editing mode
    const [smartTextSuggestion, setSmartTextSuggestion] = useState(''); // NEW: State for smart text suggestions
    const [textPosition, setTextPosition] = useState('Top Right'); // State for text overlay position
    const [selectedTextSize, setSelectedTextSize] = useState('Medium'); // State for text overlay size, default Medium
    const [selectedFontFamily, setSelectedFontFamily] = useState('Impact'); // New state for font family
    const [selectedGender, setSelectedGender] = useState('Auto'); // New state for gender
    const [primaryTextColor, setPrimaryTextColor] = useState('#F0D000'); // New state for primary text color - changed from #FFFFFF to vibrant yellow
    const [secondaryTextColor, setSecondaryTextColor] = useState('#FFFF00'); // New state for secondary text color
    const [selectedPalette, setSelectedPalette] = useState('default'); // Color palette selection state
    const [activeTemplateCategory, setActiveTemplateCategory] = useState(null); // State for active template category
    const [customFaceImageUrl, setCustomFaceImageUrl] = useState(''); // New state for custom face image URL
    const [imageSourceType, setImageSourceType] = useState('url'); // 'url' or 'upload'
    const [showErrorBanner, setShowErrorBanner] = useState(false);
    const errorTimeoutRef = useRef(null); // To store timeout ID
    const [isTemplateActive, setIsTemplateActive] = useState(false); // New state to track template activity
    // Background customization feature


    const [isTyping, setIsTyping] = useState(false);
    const [cursorVisible, setCursorVisible] = useState(true);
    // Add state for tab selection
    const [activeTab, setActiveTab] = useState('editor'); // 'editor' or 'templates'

    // Protected tab setter to prevent interference from outside click handlers
    const setActiveTabProtected = useCallback((newTab) => {
        if (newTab === 'editor' || newTab === 'templates') {

            setActiveTab(newTab);
        }
    }, []);

    // Debug: Monitor activeTab changes
    useEffect(() => {

    }, [activeTab]);

    const [isImprovingPrompt, setIsImprovingPrompt] = useState(false);
    // In App component, add a new state for the improving animation text
    const [improvingAnimationText, setImprovingAnimationText] = useState('');
    const [isFullPreviewOpen, setIsFullPreviewOpen] = useState(false);

    // NEW: Prompt variations state
    const [showPromptVariations, setShowPromptVariations] = useState(false);
    const [isVariationsClosing, setIsVariationsClosing] = useState(false);
    const [promptVariations, setPromptVariations] = useState([]);
    const [isGeneratingVariations, setIsGeneratingVariations] = useState(false);
    const [variationsLoadingMessage, setVariationsLoadingMessage] = useState('');
    
    // Responsive sidebar state
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    // User dashboard state
    const [showUserDashboard, setShowUserDashboard] = useState(false);
    const [isDashboardClosing, setIsDashboardClosing] = useState(false);
    const [showNotifications, setShowNotifications] = useState(false);

    // NEW: Icon rendering mode state for intelligent icon rendering system
    const [iconRenderingMode, setIconRenderingMode] = useState('auto'); // 'auto', 'realistic', 'cartoonish'

    // NEW: Image Requirements modal state
    const [isImageRequirementsModalOpen, setIsImageRequirementsModalOpen] = useState(false);

    // NEW: Template modal state to hide tab navigation
    const [isAnyTemplateModalOpen, setIsAnyTemplateModalOpen] = useState(false);

    // NEW: User dropdown state lifted from TopNavigation
    const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
    const [isDropdownClosing, setIsDropdownClosing] = useState(false);
    
    // NEW: Success toast state for Hero UI compliant notifications
    const [successToast, setSuccessToast] = useState({
        isVisible: false,
        message: '',
        type: 'success' // 'success', 'info', 'warning', 'error'
    });
    
    // NEW: Confirmation modal state for reset button
    const [resetConfirmModal, setResetConfirmModal] = useState({
        isOpen: false,
        title: '',
        message: '',
        onConfirm: null
    });

    // NEW: Confirmation modal state for image source type change
    const [imageSourceConfirmModal, setImageSourceConfirmModal] = useState({
        isOpen: false,
        title: '',
        message: '',
        onConfirm: null
    });

    // NEW: Template modal state for rendering modals at root level
    const [templateModalState, setTemplateModalState] = useState({
        modalOpen: false,
        selectedCategory: null,
        showMoreModalOpen: false,
        expandedSelectedCategory: null
    });

    // NEW: Subscription checkout modal state
    const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);
    const [selectedSubscriptionPlan, setSelectedSubscriptionPlan] = useState(null);
    
    // NEW: Thumbnail preview modal state
    const [thumbnailPreviewModal, setThumbnailPreviewModal] = useState({
        isOpen: false,
        thumbnail: null,
        title: 'Generated Thumbnail',
        itemId: null,
        fullImageUrl: null
    });
    
    // Demo functionality removed - using real authentication

    // ================= Image Requirements Modal Function =================
    const openImageRequirementsModal = () => {
        setIsImageRequirementsModalOpen(true);
    };

    // ================= Responsive Sidebar Functions =================
    const openSidebar = () => {
        setIsSidebarOpen(true);
        document.body.classList.add('sidebar-open');
    };

    const closeSidebar = () => {
        setIsSidebarOpen(false);
        document.body.classList.remove('sidebar-open');
    };

    const toggleSidebar = () => {
        if (isSidebarOpen) {
            closeSidebar();
        } else {
            openSidebar();
        }
    };

    // ================= User Dropdown Functions =================
    const toggleUserDropdown = () => {
        if (isUserDropdownOpen) {
            // Start closing animation
            setIsDropdownClosing(true);
            setTimeout(() => {
                setIsUserDropdownOpen(false);
                setIsDropdownClosing(false);
            }, 200); // Match animation duration
        } else {
            setIsUserDropdownOpen(true);
        }
    };

    const closeDropdownWithAnimation = () => {
        if (isUserDropdownOpen) {
            setIsDropdownClosing(true);
            setTimeout(() => {
                setIsUserDropdownOpen(false);
                setIsDropdownClosing(false);
            }, 200);
        }
    };

    // ================= User Dashboard Navigation =================
    const handleNavigateToProfile = () => {
        // Enable access to user dashboard
        setShowUserDashboard(true);
        // Close any open dropdowns
        closeDropdownWithAnimation();
    };

    const handleExitUserDashboard = () => {
        setIsDashboardClosing(true);
        // Wait for fade-out animation to complete before hiding
        setTimeout(() => {
            setShowUserDashboard(false);
            setIsDashboardClosing(false);
        }, 200); // 200ms for snappy fade-out
    };

    // Helper function to show success toast notifications
    const handleShowSuccessToast = (message, type = 'success') => {
        setSuccessToast({
            isVisible: true,
            message,
            type
        });
        
        // Auto-dismiss the notification after 2.5 seconds
        setTimeout(() => {
            setSuccessToast(prev => ({ ...prev, isVisible: false }));
        }, 2500);
    };

    // Update user profile function - Enhanced for avatar consistency
    const handleUpdateUser = (updatedUserData) => {
        // Update the user state to reflect changes across all components
        if (user && updatedUserData) {
            // Create updated user object
            const newUser = {
                ...user,
                ...updatedUserData,
                user_metadata: {
                    ...user.user_metadata,
                    ...updatedUserData.user_metadata
                },
                app_metadata: {
                    ...user.app_metadata,
                    ...updatedUserData.app_metadata
                }
            };

            console.log('✅ User updated successfully:', newUser);
            
            // Update Supabase auth session with the new user data
            try {
                // Refresh the Supabase session to ensure consistency
                supabase.auth.getSession().then(({ data: { session } }) => {
                    if (session) {
                        // Force a React re-render by updating a state that triggers UI updates
                        // This ensures avatar changes are reflected in the top navigation without full reload
                        const forceUpdateEvent = new CustomEvent('userAvatarUpdated', {
                            detail: { newUser, avatarUrl: updatedUserData.user_metadata?.avatar_url }
                        });
                        window.dispatchEvent(forceUpdateEvent);
                        
                        console.log('🔄 Avatar update event dispatched - UI will update without reload');
                    }
                }).catch(error => {
                    console.warn('Session refresh failed (non-critical):', error);
                });
            } catch (error) {
                console.warn('Auth session update failed (non-critical):', error);
            }

            // If there's a parent update handler, call it
            if (onSignOut && typeof onSignOut === 'function') {
                console.log('🔗 Propagating user update to parent components');
                // Note: In a production app, you'd use proper state management here
                // For now, we avoid the problematic page reload that was causing the welcome screen redirect
            }
        }
    };
    
    // Demo limit management functions removed - using real authentication
    
    // ================= Subscription Modal Functions =================
    const handleOpenSubscriptionModal = (planId = 'pro') => {
        // Define available plans
        const availablePlans = [
            {
                id: 'basic',
                name: 'Basic Plan',
                price: '$19',
                period: '/month'
            },
            {
                id: 'pro',
                name: 'Pro Plan',
                price: '$49',
                period: '/month'
            }
        ];

        const selectedPlan = availablePlans.find(plan => plan.id === planId) || availablePlans[1]; // Default to Pro
        setSelectedSubscriptionPlan(selectedPlan);
        setIsSubscriptionModalOpen(true);
    };

    const handleCloseSubscriptionModal = () => {
        setIsSubscriptionModalOpen(false);
        setSelectedSubscriptionPlan(null);
    };
    
    // ================= Thumbnail Preview Modal Functions =================
    const handleOpenFullPreview = () => {
        if (imageURL) {
            setThumbnailPreviewModal({
                isOpen: true,
                thumbnail: imageURL,
                title: 'Generated Thumbnail',
                itemId: Date.now(),
                fullImageUrl: imageURL // For newly generated images, imageURL is the full-quality image
            });
        }
    };
    
    const handleCloseThumbnailPreview = () => {
        setThumbnailPreviewModal({
            isOpen: false,
            thumbnail: null,
            title: 'Generated Thumbnail',
            itemId: null,
            fullImageUrl: null
        });
    };

    // Keyboard event handler for Esc key
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Escape' && isSidebarOpen) {
                closeSidebar();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [isSidebarOpen]);

    // Notification panel is now controlled only by the notification button
    // No outside click detection - panel only closes when notification button is clicked again

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            document.body.classList.remove('sidebar-open');
        };
    }, []);



    // Effect to handle showing and auto-hiding the error banner
    useEffect(() => {
        if (errorMsg) {
            setShowErrorBanner(true);
            // Clear any existing timeout
            if (errorTimeoutRef.current) {
                clearTimeout(errorTimeoutRef.current);
            }
            // Set a new timeout to hide the banner after 5 seconds (increased from 3)
            errorTimeoutRef.current = setTimeout(() => {
                setShowErrorBanner(false);
                // Optionally clear errorMsg after animation completes, e.g., after 0.5s (transition duration)
                // setTimeout(() => setErrorMsg(''), 500);
            }, 5000); // Increased from 3000 to 5000 ms
        } else {
            setShowErrorBanner(false); // Ensure banner is hidden if errorMsg is cleared externally
        }

        // Cleanup timeout on component unmount or if errorMsg changes again
        return () => {
            if (errorTimeoutRef.current) {
                clearTimeout(errorTimeoutRef.current);
            }
        };
    }, [errorMsg]);

    const clearError = () => {
        setErrorMsg('');
        setShowErrorBanner(false);
        if (errorTimeoutRef.current) {
            clearTimeout(errorTimeoutRef.current);
        }
    };

    // Enhanced reset function with safety checks and comprehensive state reset
    const handleReset = () => {
        // Safety check: Don't allow reset during image generation
        if (isLoading) {
            setErrorMsg("Please wait for the current generation to complete before resetting.");
            return;
        }

        setResetConfirmModal({
            isOpen: true,
            title: 'Reset All Settings',
            message: 'Are you sure you want to reset all settings? This will clear your current thumbnail and all customizations.',
            onConfirm: () => {
                // Comprehensive state reset to default values
                setUserPrompt('');
                setVisiblePrompt('');
                setIncludePerson(false);
                setIncludeIcons(false);
                setTextOverlay(true);
                setSelectedExpression('Default');
                setImageURL('');
                setFinalPrompt('');
                setErrorMsg('');
                setShowLayoutSimulator(false);
                setShowSafeZone(false);
                setFitFullCanvas(true);
                setOverlayText('');
                setIsEditingOverlayText(false);
                setSmartTextSuggestion('');
                setTextPosition('Top Right');
                setSelectedTextSize('Medium');
                setSelectedFontFamily('Impact');
                setSelectedGender('Auto');
                setPrimaryTextColor('#F0D000');
                setSecondaryTextColor('#FFFF00');
                setSelectedPalette('default');
                setActiveTemplateCategory(null);
                setCustomFaceImageUrl('');
                setImageSourceType('url');
                setIsTemplateActive(false);
                setIsTyping(false);
                setCursorVisible(true);
                setActiveTab('editor');
                setIsImprovingPrompt(false);
                setImprovingAnimationText('');
                setShowPromptVariations(false);
                setIsVariationsClosing(false);
                setPromptVariations([]);
                setIsGeneratingVariations(false);
                setIconRenderingMode('auto');
                setProgress(0);
                
                // Clear any open modals/panels
                setIsFullPreviewOpen(false);
                setShowNotifications(false);
                setIsAnyTemplateModalOpen(false);
                
                // Clear thumbnails cache and session storage
                try {
                    // Clear any cached thumbnails or temporary data
                    sessionStorage.removeItem('temp_thumbnail_cache');
                    sessionStorage.removeItem('prompt_history');
                    
                    // Reset any canvas elements if they exist
                    const canvasElements = document.querySelectorAll('canvas[data-thumbnail-cache]');
                    canvasElements.forEach(canvas => {
                        const ctx = canvas.getContext('2d');
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                    });
                } catch (error) {
                    console.log('Cache clearing completed with minor issues:', error);
                }
                
                // Close the confirmation modal
                setResetConfirmModal({
                    isOpen: false,
                    title: '',
                    message: '',
                    onConfirm: null
                });
                
                // Show Hero UI compliant success toast
                setSuccessToast({
                    isVisible: true,
                    message: 'All settings reset successfully!',
                    type: 'success'
                });
                
                // Auto-dismiss toast after 3 seconds
                setTimeout(() => {
                    setSuccessToast(prev => ({
                        ...prev,
                        isVisible: false
                    }));
                }, 3000);

                // Clear toast completely after animation (accounting for slower 312ms exit)
                setTimeout(() => {
                    setSuccessToast({
                        isVisible: false,
                        message: '',
                        type: 'success'
                    });
                }, 3750);
            }
        });
    };

    // ====== Enhanced Prompt Variations Handler with Live Feedback ======
    const handlePromptVariations = async () => {
        if (!userPrompt || userPrompt.trim().length < 5) {
            setErrorMsg("Please enter a prompt first to get variations!");
            return;
        }
        
        // Check word count for minimum requirement
        const wordCount = userPrompt.trim().split(/\s+/).length;
        if (wordCount < 5) {
            setErrorMsg("Please use at least 5 words for better variations!");
            return;
        }

        // Prevent multiple simultaneous requests
        if (isGeneratingVariations) {
            return;
        }
        
        setIsGeneratingVariations(true);
        setErrorMsg(''); // Clear any existing errors

        // Show loading modal immediately with animated feedback
        setShowPromptVariations(true);
        setPromptVariations([]); // Clear previous variations to show loading state

        // Start animated loading messages
        const loadingMessages = [
            "Generating ideas...",
            "Thinking of new prompts...",
            "Crafting variations...",
            "Almost ready...",
            "Still working... hang tight!"
        ];

        let messageIndex = 0;
        setVariationsLoadingMessage(loadingMessages[0]);

        const loadingMessageInterval = setInterval(() => {
            if (messageIndex < loadingMessages.length - 1) {
                messageIndex++;
                setVariationsLoadingMessage(loadingMessages[messageIndex]);
            }
        }, 1200); // Change message every 1.2 seconds

        // Timeout handler for long requests (show final message after 5 seconds)
        const timeoutId = setTimeout(() => {
            if (isGeneratingVariations) {
                setVariationsLoadingMessage(loadingMessages[loadingMessages.length - 1]);
            }
        }, 5000);

        try {
            // Generate variations asynchronously
            const variations = await generateThumbnailPromptVariations(userPrompt);
            
            // Clear loading intervals and timeouts
            clearInterval(loadingMessageInterval);
            clearTimeout(timeoutId);
            
            if (!variations || variations.length === 0) {
                setErrorMsg("Couldn't generate variations. Try a different prompt or check your connection.");
                setShowPromptVariations(false);
                setVariationsLoadingMessage('');
                return;
            }

            // Show completion message briefly before displaying results
            setVariationsLoadingMessage('Ready! 🎉');
            setTimeout(() => {
                setPromptVariations(variations);
                setVariationsLoadingMessage('');
            }, 300);

        } catch (error) {
            console.error('Error generating prompt variations:', error);
            clearInterval(loadingMessageInterval);
            clearTimeout(timeoutId);
            setErrorMsg("Failed to generate variations. Please try again.");
            setShowPromptVariations(false);
            setVariationsLoadingMessage('');
        } finally {
            setIsGeneratingVariations(false);
        }
    };

    const handleSelectVariation = (variation) => {
        // Start fade-out animation
        setIsVariationsClosing(true);
        
        // Wait for fade-out animation to complete before hiding
        setTimeout(() => {
            if (variation) {
        setUserPrompt(variation);
        setVisiblePrompt(variation);
                setIsTemplateActive(false); // Unlock when user selects a variation
            }
        setShowPromptVariations(false);
            setIsVariationsClosing(false);
        setPromptVariations([]);
        setVariationsLoadingMessage(''); // Clear loading message
        }, 200); // 200ms for smooth fade-out
    };

    // ====== Smart Prompt Rewrite (OpenAI-powered) ======

    const handleImprovePrompt = async () => {
        if (!userPrompt) {
            setErrorMsg("Write a prompt first, then I can improve it!");
            return;
        }

        // Store original prompt for potential restoration
        const originalPrompt = userPrompt;

        setIsImprovingPrompt(true);
        setImprovingAnimationText('');
        setIsTyping(true);
        setCursorVisible(true);
        setErrorMsg(''); // Clear any existing errors

        // Enhanced thinking animation with contextual messages
        let thinkingDots = 0;
        let messageIndex = 0;
        const thinkingMessages = [
            'Analyzing prompt',
            'Adding visual details', 
            'Enhancing composition',
            'Applying cinematic effects',
            'Finalizing enhancement'
        ];
        
        const thinkingInterval = setInterval(() => {
            thinkingDots = (thinkingDots + 1) % 4;
            const dots = '.'.repeat(thinkingDots);
            const currentMessage = thinkingMessages[messageIndex % thinkingMessages.length];
            setImprovingAnimationText(`${currentMessage}${dots}`);
            
            // Change message every 1.4 seconds (30% faster than 2 seconds)
            if (thinkingDots === 0) {
                messageIndex++;
            }
        }, 350); // Reduced by 30% from 500ms (500 * 0.7 = 350)
        
        try {
            // Detect video topic for better contextual enhancement
            const detectedCategory = detectVideoCategory(userPrompt);
            
            // Use new enhanced prompt improvement with engagement variations
            const enhancementResult = await getEnhancedPromptWithEngagement(userPrompt, detectedCategory);
            clearInterval(thinkingInterval); // Stop thinking animation
            
            if (enhancementResult.enhancedPrompt && enhancementResult.enhancedPrompt.trim()) {
                // Log the enhancement strategy for debugging
                console.log(`[Enhanced Prompt] Strategy: ${enhancementResult.strategy}, Explanation: ${enhancementResult.explanation}`);
                
                // Use typewriter animation to display the improved prompt
                const typewriterController = startImproveTypewriter(
                    enhancementResult.enhancedPrompt,
                    setImprovingAnimationText,
                    async (finalText) => {
                        // This callback runs when typewriter completes
                        setUserPrompt(finalText);
                        setVisiblePrompt(finalText);
                        setIsTyping(false);
                        setCursorVisible(false);
                        setIsImprovingPrompt(false);
                        
                        // AUTO-REGENERATE TEXT OVERLAY: When prompt is improved, update overlay text with new context
                        if (textOverlay) {
                            try {
                                // Generate new overlay text based on the improved prompt
                                const newOverlaySuggestion = await generateSmartTextSuggestion(finalText, detectedCategory, true); // Force new generation
                                if (newOverlaySuggestion && newOverlaySuggestion.trim() !== '') {
                                    const sanitizedSuggestion = sanitizeOverlayText(newOverlaySuggestion, {
                                        preserveSpaces: false,
                                        removeEmojis: true,
                                        removeNumbers: false,
                                        convertToUppercase: true
                                    });
                                    setOverlayText(sanitizedSuggestion);
                                    setSmartTextSuggestion(newOverlaySuggestion);
                                    
                                    // Debug logging for auto-regeneration
                                    console.log(`[Auto-Regeneration] Updated text overlay after prompt improvement: "${sanitizedSuggestion}"`);
                                }
                            } catch (error) {
                                console.error('Failed to regenerate text overlay after prompt improvement:', error);
                                // Keep existing overlay text if regeneration fails
                            }
                        }
                        
                        // Show success feedback about the enhancement
                        if (enhancementResult.explanation) {
                            // Could show a brief tooltip or toast about the enhancement strategy
                            console.log(`[Enhancement Complete] ${enhancementResult.explanation}`);
                        }
                    },
                    setIsTyping,
                    setCursorVisible
                );

                // Don't set timeout here - let the typewriter handle completion

            } else {
                // Handle case where no improvement was generated
                setUserPrompt(originalPrompt);
                setVisiblePrompt(originalPrompt);
                setErrorMsg("Couldn't improve prompt. Please try again.");
                setIsTyping(false);
                setCursorVisible(false);
                setIsImprovingPrompt(false);
            }

        } catch (error) {
            clearInterval(thinkingInterval); // Stop thinking animation on error
            // Restore original prompt on error
            setUserPrompt(originalPrompt);
            setVisiblePrompt(originalPrompt);
            console.error("Error during prompt improvement:", error);
            setErrorMsg("Couldn't improve prompt. Please try again.");
            setIsTyping(false);
            setCursorVisible(false);
            setIsImprovingPrompt(false);
        }
    };

    // Helper function to detect video category for contextual enhancement
    const detectVideoCategory = (prompt) => {
        const categoryKeywords = {
            'gaming': ['game', 'gaming', 'player', 'battle', 'fight', 'win', 'level', 'score', 'fortnite', 'minecraft', 'cod'],
            'business': ['business', 'money', 'profit', 'success', 'growth', 'revenue', 'startup', 'entrepreneur'],
            'tech': ['tech', 'app', 'software', 'code', 'ai', 'innovation', 'device', 'iphone', 'android'],
            'food': ['food', 'recipe', 'cooking', 'nutrition', 'meal', 'ingredient', 'chef', 'kitchen'],
            'fitness': ['fitness', 'workout', 'exercise', 'health', 'training', 'muscle', 'gym', 'diet'],
            'education': ['learn', 'tutorial', 'guide', 'tips', 'how to', 'study', 'teach', 'explain']
        };
        
        const lowerPrompt = prompt.toLowerCase();
        for (const [category, keywords] of Object.entries(categoryKeywords)) {
            if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
                return category;
            }
        }
        return 'general';
    };

    // Handler Functions
    const handlePromptChange = (event) => {
        const val = event.target.value;
        setUserPrompt(val);
        setVisiblePrompt(val);
        setIsTemplateActive(false); // Unlock when user types in prompt input
        setErrorMsg(''); // Clear error on new input
    };

    const handleImageSourceTypeChange = (newSourceType) => {
        // Prevent switching to upload mode - it's under development
        if (newSourceType === 'upload') {
            setErrorMsg('Local upload is currently under development. Please use the URL option.');
            return;
        }

        if (customFaceImageUrl && newSourceType !== imageSourceType) {
            setImageSourceConfirmModal({
                isOpen: true,
                title: 'Switch Input Type',
                message: 'Switching input type will remove your current image. Continue?',
                onConfirm: () => {
                    setCustomFaceImageUrl(''); // Clear current image
                    setImageSourceType(newSourceType);
                    // Close the modal
                    setImageSourceConfirmModal({
                        isOpen: false,
                        title: '',
                        message: '',
                        onConfirm: null
                    });
                    // TODO: Clear file input visually if newSourceType is 'url' and old was 'upload'
                }
            });
        } else {
            setImageSourceType(newSourceType);
        }
    };

    const handleFileUpload = (event) => {
        // Disable file upload - under development
        setErrorMsg('Local upload is currently under development. Please use the URL option.');
        return;

        // Note: The code below is commented out as file upload is under development
        /*
        const file = event.target.files[0];
        
        if (!file) {
            setCustomFaceImageUrl(''); // Clear image if no file is selected (e.g., user cancels file dialog)
            return;
        }

        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!allowedTypes.includes(file.type)) {
            console.error('Invalid file type:', file.type);
            setErrorMsg('Invalid file type. Please upload JPEG or PNG.');
            event.target.value = null; // Clear the file input
            setCustomFaceImageUrl('');
            return;
        }

        const maxSizeInBytes = 2 * 1024 * 1024;
        if (file.size > maxSizeInBytes) {
            console.error('File too large:', file.size);
            setErrorMsg('File too large. Maximum size is 2MB.');
            event.target.value = null; // Clear the file input
            setCustomFaceImageUrl('');
            return;
        }

        const reader = new FileReader();
        reader.onloadend = () => {
            setCustomFaceImageUrl(reader.result); 
            setImageSourceType('upload'); 
            setErrorMsg(''); 
        };
        reader.onerror = () => {
            console.error('FileReader error:', reader.error);
            setErrorMsg('Error reading file. Please try again.');
            console.error("Error reading file:", reader.error);
            setCustomFaceImageUrl('');
            event.target.value = null;
        };
        
        try {
            reader.readAsDataURL(file);
        } catch (error) {
            console.error('Error starting file read:', error);
            setErrorMsg('Error processing file. Please try again.');
            setCustomFaceImageUrl('');
            event.target.value = null;
        }
        */
    };

    const handleToggleChange = (setter, isPersonToggle = false) => {
        // Use functional setter to get the current value correctly
        setter(prev => {
            const newValue = !prev;
            
            // Handle person toggle specific logic
            if (isPersonToggle) {
                if (newValue) {
            setSelectedExpression('Default');
                } else {
            setCustomFaceImageUrl('');
        }
            }
            
            return newValue;
        });

        // Clear template category and errors after any toggle
        if (activeTemplateCategory) setActiveTemplateCategory(null);
        setErrorMsg('');
    };

    const handleTemplateSelect = (template) => {
        // Context7MCP: Clear face image state on template change to prevent cache conflicts
        if (customFaceImageUrl) {
            console.log('[Context7MCP] Template change detected - clearing face image cache for consistency');
            setCustomFaceImageUrl(''); // Clear any cached face image
            // Also clear any temporary URL states that might be cached
            if (imageSourceType === 'url') {
                // Force refresh of URL input if using URL mode
                setTimeout(() => {
                    const urlInput = document.querySelector('input[placeholder="https://example.com/my-face.jpg"]');
                    if (urlInput) {
                        urlInput.value = '';
                    }
                }, 100);
            }
        }

        const fullPrompt = template.promptBase + CINEMATIC_SUFFIX; // Use template prompt + cinematic guidance
        setUserPrompt(fullPrompt); // store internal (not shown)
        // Show only the template name in the prompt input area
        setVisiblePrompt(template.name);
        setIsTemplateActive(true); // Lock the prompt input

        // Apply other settings from the template
        if (template.settingsToApply) {
            if (template.settingsToApply.includePerson !== undefined) {
                setIncludePerson(template.settingsToApply.includePerson);
                // If setting includePerson to true, also reset expression to default (or template specified)
                if (template.settingsToApply.includePerson) {
                    setSelectedExpression(template.settingsToApply.selectedExpression || 'Default');
                } else {
                    setSelectedExpression('Default'); // Or based on what makes sense when person is off
                }
            }

            if (template.settingsToApply.textOverlay !== undefined) {
                setTextOverlay(template.settingsToApply.textOverlay);
            }
            
            // Handle overlay text from the template (AUTO-UPPERCASE)
            if (template.settingsToApply.overlayText) {
                setOverlayText(template.settingsToApply.overlayText.toUpperCase());
                // Open the editor to make the placeholders immediately visible and editable
                setIsEditingOverlayText(true);
            }

            // Additional settings
            if (template.settingsToApply.textPosition) {
                setTextPosition(template.settingsToApply.textPosition);
            }

            if (template.settingsToApply.selectedTextSize) {
                setSelectedTextSize(template.settingsToApply.selectedTextSize);
            }

            if (template.settingsToApply.includeIcons !== undefined) {
                setIncludeIcons(template.settingsToApply.includeIcons);
            }
        }

        // Close all modals and reset template state
        setActiveTemplateCategory(null); // Collapse templates section
        setErrorMsg(''); // Clear any errors

        // Switch to the Editor tab to show the applied template settings
        setActiveTab('editor');

        // Scroll to the overlay text editor if it's open
        setTimeout(() => {
            if (template.settingsToApply?.overlayText) {
                document.getElementById('overlayTextInput')?.focus();
            } else {
                document.getElementById('userPromptInput')?.focus();
            }
        }, 100);
    };

    const handleQualityChange = (newQuality) => {
        setSelectedQuality(newQuality);
        setErrorMsg(''); // Clear errors when changing quality
    };

    // Toggle between low-cost test mode and high-quality export
    const handleQualityToggle = () => {
        setIsLowCostMode(prev => !prev);
        setSelectedQuality(prev => prev === 'low' ? 'normal' : 'low');
    };

    // Prompt Building Logic (remains the same, used as input for GPT-4V)
    const buildFullPrompt = () => {
        // For face image uploads, we need to handle them specially
        let faceDescriptionValue = null;
        if (customFaceImageUrl) {
            if (customFaceImageUrl.startsWith('data:')) {
                // This is an uploaded image - we'll use a placeholder in the prompt
                // The actual image data would need to be sent separately in a real implementation
                faceDescriptionValue = '[uploaded face image]';

            } else {
                // This is a URL
                faceDescriptionValue = customFaceImageUrl;

            }
        }

        const prompt = buildPrompt({
            userPrompt,
            includePerson,
            mood: selectedExpression, // Pass selectedExpression as mood
            includeIcons,
            textOverlay,
            selectedExpression,
            faceDescription: faceDescriptionValue,
            overlayText: textOverlay ? overlayText : '',
            overlayPosition: textOverlay ? textPosition : '',
            selectedTextSize,
            selectedFontFamily,
            primaryTextColor,
            secondaryTextColor,
            fitFullCanvas,
            selectedGender,
            // NEW: Pass background style states to buildPrompt

            // NEW: Pass icon rendering mode to buildPrompt
            iconRenderingMode: iconRenderingMode
        });

        return prompt;
    };

    // DESIGN CONTROLS VALIDATION: Check if at least one design control is enabled
    const validateDesignControls = () => {
        const hasAnyDesignControl = includePerson || includeIcons || textOverlay;
        return hasAnyDesignControl;
    };

    // Generate Thumbnail Feedback Handler (GPT-4 Vision Call)
    const handleGenerateClick = async () => {
        if (!userPrompt.trim()) {
            setErrorMsg("Oops, forgot the prompt!");
            return;
        }
        
        // DESIGN CONTROLS MINIMUM REQUIREMENT CHECK
        if (!validateDesignControls()) {
            setErrorMsg("Please turn on at least one option to continue.");
            return;
        }
        
        // Context7MCP: Force refresh face image validation on generation
        if (customFaceImageUrl && !customFaceImageUrl.startsWith('data:')) {
            console.log('[Context7MCP] Re-validating face image URL before generation:', customFaceImageUrl);
            // This ensures we're using the latest face image, not cached data
        }
        
        // Demo limit check removed - using real authentication and credits system

        const basePrompt = buildFullPrompt();

        let finalApiPrompt = basePrompt;
        let apiQualityValue = "medium"; // Default for normal

        if (selectedQuality === 'low') {
            const lowCostSuffix = "\\n\\nIMPORTANT: Generate a simplified draft version of the thumbnail at reduced resolution (like 512x288). Focus on layout, icon/text placement, and design logic only — not on fine detail or lighting. This version is for internal testing and does not require production-quality rendering.";
            finalApiPrompt = basePrompt + lowCostSuffix;
            apiQualityValue = "low";
        } else if (selectedQuality === 'hd') {
            apiQualityValue = "high";
        } // 'normal' uses "medium" by default setup above

        setIsLoading(true);
        setImageURL('');
        setErrorMsg('');
        setFinalPrompt(finalApiPrompt);



        try {
            const response = await fetch('https://api.openai.com/v1/images/generations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_API_KEY}`
                },
                body: JSON.stringify({
                    model: "gpt-image-1",
                    prompt: finalApiPrompt,
                    n: 1, 
                    size: "1536x1024", // Use 1536x1024 (3:2 aspect ratio) - supported by OpenAI
                    quality: apiQualityValue,
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                console.error("OpenAI API Error:", errorData);
                setErrorMsg(`Image Gen Error (${response.status}): ${errorData.error?.message || 'Unknown error'}`);
            } else {
                const data = await response.json();
                const b64 = data.data[0]?.b64_json;
                if (b64) {
                    const imageUrl = `data:image/png;base64,${b64}`;
                    
                    // Always resize/crop to 1280x720 before setting preview
                    let resizedImageUrl;
                    try {
                        resizedImageUrl = await resizeImageTo1280x720(imageUrl);
                        setImageURL(resizedImageUrl);
                    } catch (error) {
                        console.warn("Failed to resize image, using original:", error);
                        resizedImageUrl = imageUrl; // Fallback to original
                        setImageURL(imageUrl);
                    }
    
                    // Convert data URL to Blob for Supabase upload
                    const dataURLToBlob = (dataURL) => {
                        const arr = dataURL.split(',');
                        const mime = arr[0].match(/:(.*?);/)[1];
                        const bstr = atob(arr[1]);
                        let n = bstr.length;
                        const u8arr = new Uint8Array(n);
                        while(n--){
                            u8arr[n] = bstr.charCodeAt(n);
                        }
                        return new Blob([u8arr], {type:mime});
                    };
                    
                    // Create a small thumbnail for storage (150x85 pixels to save space)
                    const createSmallThumbnail = (imageDataUrl) => {
                        return new Promise((resolve) => {
                            const img = new Image();
                            img.onload = () => {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');

                                // Set small thumbnail dimensions
                                canvas.width = 150;
                                canvas.height = 85;

                                // Draw and compress the image
                                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                                // Convert to compressed JPEG (quality 0.7)
                                const compressedData = canvas.toDataURL('image/jpeg', 0.7);
                                resolve(compressedData);
                            };
                            img.src = imageDataUrl;
                        });
                    };

                    // Generate small thumbnail and save
                    createSmallThumbnail(imageUrl).then(async (smallThumbnail) => {
                        const generationData = {
                            id: Date.now().toString(),
                            timestamp: new Date().toISOString(),
                            date: new Date().toISOString(),
                            prompt: userPrompt.trim(),
                            title: userPrompt.trim(),
                            thumbnail: smallThumbnail, // Store compressed small image
                            quality: selectedQuality === 'low' ? 'Low' : selectedQuality === 'hd' ? 'HD' : 'Medium',
                            credits: selectedQuality === 'low' ? 1 : selectedQuality === 'hd' ? 5 : 3,
                            finalPrompt: finalApiPrompt.length > 200 ? finalApiPrompt.substring(0, 200) + '...' : finalApiPrompt
                        };

                        // Get existing history and add new generation
                        try {
                            const existingHistory = JSON.parse(localStorage.getItem('user_generation_history') || '[]');
                            const updatedHistory = [generationData, ...existingHistory].slice(0, 50); // Reduced to 50 to save space
                            localStorage.setItem('user_generation_history', JSON.stringify(updatedHistory));
                            
                            // Demo increment code removed - using real authentication and credits system
            
                        } catch (error) {
                            console.error("Error saving generation to history:", error);
                            // Fallback: try to clear some old entries and retry
                            try {
                                const existingHistory = JSON.parse(localStorage.getItem('user_generation_history') || '[]');
                                const reducedHistory = [generationData, ...existingHistory].slice(0, 20);
                                localStorage.setItem('user_generation_history', JSON.stringify(reducedHistory));

                            } catch (fallbackError) {
                                console.error("Failed to save even with reduced history:", fallbackError);
                            }
                        }

                        // Save to Supabase (with localStorage as fallback)
                        try {
                            const fullImageBlob = dataURLToBlob(resizedImageUrl);
                            const supabaseGenerationData = {
                                title: userPrompt.trim(),
                                prompt: userPrompt.trim(),
                                final_prompt: finalApiPrompt,
                                quality: selectedQuality === 'low' ? 'Low' : selectedQuality === 'hd' ? 'HD' : 'Medium',
                                credits: selectedQuality === 'low' ? 1 : selectedQuality === 'hd' ? 5 : 3,
                                metadata: {
                                    includePerson,
                                    includeIcons,
                                    textOverlay,
                                    overlayText,
                                    selectedExpression,
                                    selectedGender,
                                    selectedTextSize,
                                    selectedFontFamily,
                                    primaryTextColor,
                                    secondaryTextColor
                                }
                            };
                            
                            await saveGeneration(supabaseGenerationData, fullImageBlob);
                        } catch (supabaseError) {
                            console.warn('⚠️ Supabase save failed, falling back to localStorage:', supabaseError.message);
                        }
                    });
                } else {
                    throw new Error("No base64 image data found in gpt-image-1 response");
                }
            }

        } catch (e) {
            console.error("Error calling Image Generation API:", e);

            setErrorMsg('Something went sideways calling Image Generation...');
        } finally {
            setIsLoading(false);
        }
    };

    // Cursor blink effect when typing
    useEffect(() => {
        if (!isTyping) {
            setCursorVisible(false);
            return;
        }
        const blink = setInterval(() => {
            setCursorVisible(prev => !prev);
        }, 500);
        return () => clearInterval(blink);
    }, [isTyping]);

    // Smart text overlay suggestion effect with auto-population
    useEffect(() => {
        // Only generate suggestion if:
        // 1. Text overlay is enabled
        // 2. User has a prompt
        // 3. No custom overlay text is already provided
        if (textOverlay && userPrompt && !overlayText) {
            // Debounce the suggestion generation
            const delayTimer = setTimeout(async () => {
                try {
                    // Generate initial suggestion for auto-population (not forcing new - use cache if available)
                    const suggestion = await generateSmartTextSuggestion(userPrompt, 'general', false);
                    setSmartTextSuggestion(suggestion);
                    
                    // AUTO-POPULATE: Set the overlay text with the suggestion to make it the single source of truth (AUTO-UPPERCASE + SANITIZATION)
                    if (suggestion && suggestion.trim() !== '') {
                        const sanitizedSuggestion = sanitizeOverlayText(suggestion, {
                            preserveSpaces: false,
                            removeEmojis: true,
                            removeNumbers: false,
                            convertToUppercase: true
                        });
                        setOverlayText(sanitizedSuggestion);
                        
                        // Debug logging for auto-population
                        console.log(`[Auto-Population] Generated text overlay: "${sanitizedSuggestion}" (${sanitizedSuggestion.split(' ').length} words)`);
                    }
                } catch (error) {
                    console.error('Failed to generate text suggestion:', error);
                    // Use a simple fallback
                    const words = userPrompt.toUpperCase().split(/[\s:!?.,;]+/);
                    const keyWords = words.filter(word =>
                        word.length > 3 &&
                        !['THE', 'AND', 'WITH', 'FOR', 'YOUR', 'THIS', 'THAT'].includes(word)
                    ).slice(0, 2);
                    const fallback = keyWords.length > 0 ? keyWords.join(' ') + '!' : 'AMAZING!';
                    setSmartTextSuggestion(fallback);
                    
                    // AUTO-POPULATE: Set the overlay text with the fallback (AUTO-UPPERCASE + SANITIZATION)
                    const sanitizedFallback = sanitizeOverlayText(fallback, {
                        preserveSpaces: false,
                        removeEmojis: true,
                        removeNumbers: false,
                        convertToUppercase: true
                    });
                    setOverlayText(sanitizedFallback);
                }
            }, 500); // Wait 500ms after user stops typing

            return () => clearTimeout(delayTimer);
        } else if (!textOverlay) {
            // Clear both suggestion and overlay text if text overlay is disabled
            setSmartTextSuggestion('');
            setOverlayText('');
        }
        // Note: Don't clear when overlayText exists - let user keep their custom text
    }, [textOverlay, userPrompt, overlayText]);

    // Premium loading indicator progress simulation
    useEffect(() => {
        let intervalId;
        if (isLoading) {
            setProgress(10); // Start at 10%
            // Adjust timing based on quality mode
            const updateInterval = selectedQuality === 'hd' ? 350 : 250; // Slower for HD

            intervalId = setInterval(() => {
                setProgress(prevProgress => {
                    // HD mode has slower progression
                    const hdMultiplier = selectedQuality === 'hd' ? 0.7 : 1;

                    // Stage 1: 10% to 25% (fast)
                    if (prevProgress < 25) {
                        const increment = (Math.random() * 3 + 1.5) * hdMultiplier; // 1.5-4.5%
                        return Math.min(prevProgress + increment, 25);
                    }
                    // Stage 2: 25% to 80% (moderate)
                    if (prevProgress < 80) {
                        const increment = (Math.random() * 2 + 0.5) * hdMultiplier; // 0.5-2.5%
                        return Math.min(prevProgress + increment, 80);
                    }
                    // Stage 3: 80% to 99% (slow)
                    if (prevProgress < 99) {
                        const increment = (Math.random() * 0.5 + 0.1) * hdMultiplier; // 0.1-0.6%
                        return Math.min(prevProgress + increment, 99);
                    }
                    // Hold at 99% until image is ready - but keep it animated
                    return prevProgress;
                });
            }, updateInterval);
        } else {
            setProgress(0);
            if (intervalId) clearInterval(intervalId);
        }
        return () => clearInterval(intervalId);
    }, [isLoading, selectedQuality]);

    // Complete progress when image is ready
    useEffect(() => {
        if (imageURL && progress < 100) {
            setProgress(100); // Instantly set to 100%
        }
    }, [imageURL]); // Only depend on imageURL for instant completion

    // CONDITIONAL RENDERING FOR ADMIN DASHBOARD
    const [currentPath, setCurrentPath] = useState(window.location.pathname);

    useEffect(() => {
        const handleLocationChange = () => {
            const newPath = window.location.pathname;
            setCurrentPath(newPath);

            // Update user dashboard state based on URL
            if (newPath === '/profile') {
                setShowUserDashboard(true);
            } else {
                setShowUserDashboard(false);
            }
        };

        window.addEventListener('popstate', handleLocationChange);
        // Also handle clicks on links that change the path without a full page reload (if any)
        // For simplicity, we're only handling popstate. For full SPA routing, a library is better.

        return () => {
            window.removeEventListener('popstate', handleLocationChange);
        };
    }, []);

    // Initialize Geist fonts on component mount
    useEffect(() => {
        const initializeFonts = async () => {
            try {
                await loadGeistFontsWithFallback();
                console.log('🎨 Geist fonts loaded successfully in Thumbspark');
            } catch (error) {
                console.warn('⚠️ Could not load Geist fonts, falling back to system fonts:', error);
            }
        };

        const initializeOpenMoji = async () => {
            try {
                const { initOpenMoji } = await import('./utils/openmojiPreloader.js');
                initOpenMoji();
                console.log('✅ OpenMoji initialization started');
            } catch (error) {
                console.warn('⚠️ Could not initialize OpenMoji:', error);
            }
        };

        const initializeStorageManagement = () => {
            try {
                // Check storage usage on app startup
                const storageStats = checkStorageUsage();
                
                // If storage is near limit, suggest cleanup
                if (storageStats.isNearLimit) {
                    console.warn('⚠️ localStorage is near capacity:', storageStats);
                    
                    // Auto cleanup if storage is critically full
                    if (storageStats.isFull) {
                        console.log('🚨 Storage critically full, performing emergency cleanup...');
                        emergencyCleanup();
                    }
                }
                
                // Make functions globally available for debugging
                window.emergencyCleanup = emergencyCleanup;
                window.checkStorageUsage = checkStorageUsage;
                
                console.log('✅ Storage management initialized');
                
            } catch (error) {
                console.warn('Storage management initialization failed:', error);
            }
        };

        initializeFonts();
        initializeOpenMoji();
        initializeStorageManagement();
        
        // Listen for avatar success/error toast events from UserDashboard
        const handleShowSuccessToast = (event) => {
            const { message, type } = event.detail;
            setSuccessToast({
                isVisible: true,
                message,
                type: type || 'success'
            });
        };

        window.addEventListener('showSuccessToast', handleShowSuccessToast);
        
        return () => {
            window.removeEventListener('showSuccessToast', handleShowSuccessToast);
        };
    }, []);

    // FIXED: Body class management for scrolling control based on dashboard state
    useEffect(() => {
        if (showUserDashboard) {
            // Dashboard is active - remove main-app-active class to allow scrolling
            document.body.classList.remove('main-app-active');
        } else {
            // Main app is active - add main-app-active class to prevent scrolling
            document.body.classList.add('main-app-active');
        }
        
        // Cleanup function to remove classes when component unmounts
        return () => {
            document.body.classList.remove('main-app-active');
        };
    }, [showUserDashboard]);

    // Image Requirements Carousel (rendered at app level)
    const renderImageRequirementsModal = () => {
        return React.createElement(ImageRequirementsCarousel, {
            isOpen: isImageRequirementsModalOpen,
            onClose: () => setIsImageRequirementsModalOpen(false)
        });
    };

    if (showUserDashboard) {
        return React.createElement(UserDashboard, {
            user: user,
            onExitDashboard: handleExitUserDashboard,
            onUpdateUser: handleUpdateUser,
            onOpenSubscriptionModal: handleOpenSubscriptionModal,
            isClosing: isDashboardClosing
        });
    }

    // Original main app content
    return (
        React.createElement(Fragment, null,
            // Top Navigation Header
            React.createElement(TopNavigation, { 
            user, 
            onSignOut, 
            onNavigateToProfile: handleNavigateToProfile,
            isUserDropdownOpen,
            isDropdownClosing,
            toggleUserDropdown,
            closeDropdownWithAnimation,
            onOpenSubscriptionModal: handleOpenSubscriptionModal
        }),
            
            // Global Backdrop for user dropdown menu
            isUserDropdownOpen && React.createElement(React.Fragment, null,
                // Mobile backdrop with blur effect
                React.createElement('div', {
                    className: 'dropdown-backdrop fixed inset-0 md:hidden', // Only show on mobile
                    style: {
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        backdropFilter: 'blur(4px)',
                        WebkitBackdropFilter: 'blur(4px)',
                        animation: isDropdownClosing ? 'fadeOut 200ms ease-out forwards' : 'fadeIn 200ms ease-out',
                        zIndex: 45, // Below the dropdown (z-50) but above main content
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        width: '100vw',
                        height: '100vh',
                        pointerEvents: 'auto'
                    },
                    onClick: closeDropdownWithAnimation
                }),
                // Desktop backdrop - invisible, just for click detection
                React.createElement('div', {
                    className: 'dropdown-backdrop-desktop hidden md:block', // Only show on desktop
                    style: {
                        position: 'fixed',
                        inset: 0,
                        zIndex: 45,
                        background: 'transparent',
                        pointerEvents: 'auto'
                    },
                    onClick: closeDropdownWithAnimation
                })
            ),

            // Error Banner (unchanged position)
            errorMsg && React.createElement('div', {
                className: 'error-banner-container fixed top-0 left-0 right-0 z-50 flex justify-center items-start pt-4 pointer-events-none',
                style: { width: '100%' }
            },
                React.createElement('div', {
                    className: `error-banner bg-red-600 text-white px-6 py-3 rounded-lg shadow-lg flex items-center justify-between ${showErrorBanner ? 'animate-slide-down opacity-100' : 'opacity-0 -translate-y-full'} transition-all duration-300 ease-in-out pointer-events-auto`,
                    role: 'alert',
                    'aria-live': 'assertive',
                    style: {
                        width: 'clamp(300px, 90%, 800px)',
                        maxWidth: '90%',
                        transform: showErrorBanner ? 'translateY(0)' : 'translateY(-100%)'
                    }
                },
                    React.createElement('div', { className: 'flex items-center gap-3' },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            strokeWidth: 1.5,
                            stroke: 'currentColor',
                            className: 'w-6 h-6 flex-shrink-0'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                d: 'M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z'
                            })
                        ),
                        React.createElement('span', { className: 'font-medium' }, errorMsg)
                    ),
                    React.createElement('button', {
                        onClick: clearError,
                        className: 'ml-4 text-white hover:text-red-200 focus:outline-none focus:ring-2 focus:ring-red-300 p-1 rounded-full',
                        'aria-label': 'Close error message'
                    },
                        React.createElement('svg', {
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            strokeWidth: 1.5,
                            stroke: 'currentColor',
                            className: 'w-5 h-5'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                d: 'M6 18L18 6M6 6l12 12'
                            })
                        )
                    )
                )
            ),

            // Sidebar Toggle Button (Tablet/Mobile Only)
            React.createElement('button', {
                className: `hamburger-menu-btn ${isSidebarOpen ? 'sidebar-open' : ''}`,
                onClick: toggleSidebar,
                'aria-label': isSidebarOpen ? 'Close navigation menu' : 'Open navigation menu',
                'aria-expanded': isSidebarOpen,
            },
                // Primary icon using Iconify - changes based on sidebar state
                React.createElement('span', {
                    className: 'iconify sidebar-icon',
                    'data-icon': isSidebarOpen ? 'solar:close-circle-line-duotone' : 'solar:sidebar-minimalistic-line-duotone',
                    style: { fontSize: '24px', color: 'currentColor' }
                }),
                // Fallback SVG icon if Iconify doesn't load
                React.createElement('svg', {
                    className: 'fallback-sidebar-icon w-6 h-6',
                    xmlns: 'http://www.w3.org/2000/svg',
                    fill: 'none',
                    viewBox: '0 0 24 24',
                    strokeWidth: 1.5,
                    stroke: 'currentColor',
                    style: { display: 'none' }
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        d: 'M4 6.5h16M4 12h16M4 17.5h10'
                    })
                )
            ),
            // Sidebar Backdrop (Tablet/Mobile Only)
            React.createElement('div', {
                className: `sidebar-backdrop ${isSidebarOpen ? 'open' : ''}`,
                onClick: closeSidebar,
                'aria-hidden': 'true'
            }),



            // Three-Panel Professional Layout
            React.createElement('div', { className: 'app-container' },
                // Left Sidebar - Controls
                React.createElement('div', {
                    className: `left-sidebar custom-scrollbar ${isSidebarOpen ? 'open' : ''}`, // Added custom-scrollbar and responsive classes
                    id: 'left-sidebar-main-controls'
                },
                    // Close Button (Mobile/Tablet Only)
                    React.createElement('button', {
                        className: 'sidebar-close-btn',
                        onClick: closeSidebar,
                        'aria-label': 'Close navigation menu',
                        title: 'Close menu'
                    },
                        // Primary close icon using Iconify
                        React.createElement('span', {
                            className: 'iconify close-icon',
                            'data-icon': 'solar:close-circle-bold-duotone',
                            style: { fontSize: '20px', color: 'currentColor' }
                        }),
                        // Fallback SVG icon if Iconify doesn't load
                        React.createElement('svg', {
                            className: 'fallback-close-icon w-5 h-5',
                            xmlns: 'http://www.w3.org/2000/svg',
                            fill: 'none',
                            viewBox: '0 0 24 24',
                            strokeWidth: 2,
                            stroke: 'currentColor',
                            style: { display: 'none' }
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                d: 'M6 18L18 6M6 6l12 12'
                            })
                        )
                    ),
                    // Tab group for Editor/Templates
                    React.createElement('div', {
                        className: `main-tab-navigation-container flex justify-center mb-4 flex-shrink-0`,
                        id: 'main-tab-navigation'
                    },
                        React.createElement('div', {
                            className: `main-tab-group ${activeTab === 'templates' ? 'templates-active' : ''}`,
                            id: 'editor-templates-tab-group',
                            role: 'tablist',
                            'aria-label': 'Main navigation tabs'
                        },
                            // Editor Tab Button
                            React.createElement('button', {
                                id: 'editor-tab-button',
                                className: `main-tab-button ${activeTab === 'editor' ? 'active-tab' : 'inactive-tab'} focus:z-10`,
                                onClick: (e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    setActiveTabProtected('editor');
                                },
                                onMouseDown: (e) => {
                                    // Prevent any interference during the click process
                                    e.stopPropagation();
                                },
                                role: 'tab',
                                'aria-selected': activeTab === 'editor',
                                'aria-controls': 'editor-tab-content'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': 'solar:pen-2-bold-duotone'
                                }),
                                React.createElement('span', null, 'Editor')
                            ),
                            // Templates Tab Button
                            React.createElement('button', {
                                id: 'templates-tab-button',
                                className: `main-tab-button ${activeTab === 'templates' ? 'active-tab' : 'inactive-tab'} focus:z-10`,
                                onClick: (e) => {
                                    e.stopPropagation();
                                    e.preventDefault();
                                    setActiveTabProtected('templates');
                                },
                                onMouseDown: (e) => {
                                    // Prevent any interference during the click process
                                    e.stopPropagation();
                                },
                                role: 'tab',
                                'aria-selected': activeTab === 'templates',
                                'aria-controls': 'templates-tab-content'
                            },
                                React.createElement('span', {
                                    className: 'iconify',
                                    'data-icon': 'solar:library-bold-duotone'
                                }),
                                React.createElement('span', null, 'Templates')
                            )
                        )
                    ),

                    // NEW: Scrollable content area for the active tab's content
                    React.createElement('div', {
                        className: 'flex-grow overflow-y-auto overflow-x-visible custom-scrollbar pr-1', // Added overflow-x-visible to allow tooltips to show
                        style: { position: 'relative', zIndex: 1 }, // MODIFIED: Removed minHeight constraint
                        onClick: (e) => {
                            // Prevent any outside click handlers from affecting tab content
                            e.stopPropagation();
                        }
                    },
                        activeTab === 'editor' && React.createElement('div', {
                            id: 'editor-tab-content',
                            role: 'tabpanel',
                            'aria-labelledby': 'editor-tab-button',
                            style: {
                                display: 'block',
                                visibility: 'visible',
                                opacity: 1
                            },
                            onClick: (e) => {
                                // Ensure clicks within tab content don't bubble up
                                e.stopPropagation();
                            }
                        },
                            React.createElement(CollapsibleControlPanel, {
                                // ...all existing props for CollapsibleControlPanel (ensure they are all passed)
                                includePerson,
                                setIncludePerson,
                                includeIcons,
                                setIncludeIcons,
                                textOverlay,
                                setTextOverlay,
                                selectedExpression,
                                setSelectedExpression,
                                handleToggleChange,
                                fitFullCanvas,
                                setFitFullCanvas,
                                showLayoutSimulator,
                                setShowLayoutSimulator,
                                showSafeZone,
                                setShowSafeZone,
                                overlayText,
                                setOverlayText,
                                isEditingOverlayText,
                                setIsEditingOverlayText,
                                textPosition,
                                setTextPosition,
                                selectedTextSize,
                                setSelectedTextSize,
                                selectedFontFamily,
                                setSelectedFontFamily,
                                selectedGender,
                                setSelectedGender,
                                primaryTextColor,
                                setPrimaryTextColor,
                                secondaryTextColor,
                                setSecondaryTextColor,
                                selectedPalette,
                                setSelectedPalette,
                                customFaceImageUrl,
                                setCustomFaceImageUrl,
                                imageSourceType,
                                handleImageSourceTypeChange,
                                handleFileUpload,
                                setErrorMsg,



                                iconRenderingMode,
                                setIconRenderingMode,
                                // Add missing props for modal and headshot detection
                                openImageRequirementsModal,
                                userPrompt,
                                smartTextSuggestion,
                                // Add success toast function
                                onShowSuccessToast: handleShowSuccessToast
                            })
                        ),

                        activeTab === 'templates' && React.createElement('div', {
                            id: 'templates-tab-content',
                            role: 'tabpanel',
                            'aria-labelledby': 'templates-tab-button',
                            style: {
                                display: 'block',
                                visibility: 'visible',
                                opacity: 1
                            },
                            onClick: (e) => {
                                // Ensure clicks within tab content don't bubble up
                                e.stopPropagation();
                            }
                        },
                            React.createElement(PremadeTemplatesSection, {
                                templatesData: premadeTemplatesData,
                                expandedTemplatesData: expandedTemplatesData,
                                onTemplateSelect: handleTemplateSelect,
                                activeCategory: activeTemplateCategory,
                                setActiveCategory: setActiveTemplateCategory,
                                setIsAnyTemplateModalOpen: setIsAnyTemplateModalOpen,
                                onModalStateChange: setTemplateModalState
                            })
                        )
                    )
                ),

                // === Center Panel (Prompt & Actions) ===
                React.createElement('div', {
                    className: 'main-center-panel flex flex-col gap-2 p-6 overflow-hidden',
                    id: 'center-panel-main'
                },
                    // Preview Workspace Section
                    React.createElement('div', {
                        className: 'preview-workspace-section flex-grow',
                        id: 'preview-workspace'
                    },
                        // Header with title and reset button
                        React.createElement('div', {
                            className: 'workspace-header flex items-center justify-between mb-3'
                        },
                            React.createElement('h2', {
                                className: 'text-xl font-semibold text-white'
                            }, 'Preview Workspace'),
                            React.createElement(ResetButton, { onClick: handleReset, isLoading: isLoading || isImprovingPrompt })
                        ),
                        // Preview container
                        React.createElement('div', {
                            className: 'preview-wrapper',
                            id: 'thumbnail-preview-wrapper'
                        },
                            React.createElement(ThumbnailPreview, {
                                imageURL: imageURL,
                                isLoading: isLoading,
                                showLayoutSimulator: showLayoutSimulator,
                                showSafeZone: showSafeZone,
                                includePerson: includePerson,
                                includeIcons: includeIcons,
                                textOverlay: textOverlay,
                                overlayText: overlayText,
                                selectedFontFamily: selectedFontFamily,
                                selectedTextSize: selectedTextSize,
                                primaryTextColor: primaryTextColor,
                                secondaryTextColor: secondaryTextColor,
                                progress: progress,
                                onOpenFullPreview: handleOpenFullPreview
                            })
                        )
                    ),

                    // Prompt and Action Controls Section
                    React.createElement('div', {
                        className: 'prompt-controls-section',
                        id: 'prompt-controls'
                    },
                        React.createElement(PromptInput, {
                            displayValue: visiblePrompt, // Only show template name or user input, never the full formula
                            onChange: handlePromptChange,
                            isLocked: isTemplateActive,
                            onImprovePrompt: handleImprovePrompt,
                            onPromptVariations: handlePromptVariations,
                            isTyping: isTyping,
                            cursorVisible: cursorVisible,
                            isImprovingPrompt: isImprovingPrompt,
                            improvingAnimationText: improvingAnimationText,
                            showVariations: showPromptVariations,
                            isVariationsClosing: isVariationsClosing,
                            variations: promptVariations,
                            isLoading: isLoading,
                            isGeneratingVariations: isGeneratingVariations,
                            variationsLoadingMessage: variationsLoadingMessage,
                            onSelectVariation: (variation) => {
                                if (variation === null) {
                                    // Close button clicked - trigger fade-out
                                    handleSelectVariation(null);
                                } else {
                                    handleSelectVariation(variation);
                                }
                            }
                        }),
                        // Action buttons and quality controls row  
                        React.createElement('div', {
                            className: 'controls-bottom-row flex items-center justify-end gap-4 mt-8'
                        },
                            // Combined controls container - Quality selector and action buttons
                            React.createElement('div', {
                                className: 'combined-controls flex items-center gap-4',
                                id: 'combined-controls'
                            },
                                // Quality selector
                                React.createElement('div', {
                                    className: 'quality-selector flex items-center gap-2'
                                },
                                    React.createElement('span', { 
                                        className: `text-sm transition-colors duration-200 ${isLoading ? 'text-gray-500' : 'text-gray-400'}`
                                    }, 'Quality:'),
                                    React.createElement('div', {
                                        className: `quality-options relative grid grid-cols-3 rounded-xl overflow-visible transition-all duration-200 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`,
                                        style: {
                                            backgroundColor: isLoading ? 'rgb(55, 55, 62)' : 'rgb(63, 63, 70)',
                                            borderRadius: '13.4px',
                                            height: '44px',
                                            width: '219px',
                                            position: 'relative',
                                            padding: '3px'
                                        }
                                    },
                                        // Animated pill behind buttons
                                        React.createElement('div', {
                                            className: `absolute transition-all duration-300 ease-in-out h-full w-1/3 ${isLoading ? 'bg-gray-600' : 'bg-blue-600'}`,
                                            style: {
                                                transform: `translateX(${selectedQuality === 'low' ? '0%' : selectedQuality === 'normal' ? '100%' : '200%'})`,
                                                borderRadius: '13.4px'
                                            }
                                        }),
                                        // Buttons (inline in grid flow)
                                        ['low', 'normal', 'hd'].map((qual, idx) =>
                                            React.createElement('button', {
                                                key: qual,
                                                onClick: isLoading ? undefined : () => handleQualityChange(qual),
                                                disabled: isLoading,
                                                className: `relative z-10 text-sm font-semibold transition-all duration-200 flex items-center justify-center ${
                                                    isLoading 
                                                        ? 'text-gray-500 cursor-not-allowed' 
                                                        : selectedQuality === qual 
                                                            ? 'text-white' 
                                                            : 'text-gray-400 hover:text-gray-300'
                                                }`,
                                                style:{
                                                    outline:'none',
                                                    minHeight: '36px',
                                                    padding: '8px 12px',
                                                    lineHeight: '1.2'
                                                },
                                                'aria-pressed': selectedQuality === qual,
                                                'aria-label': isLoading ? `${qual === 'normal' ? 'Medium' : qual.toUpperCase()} quality disabled during generation` : `Select ${qual === 'normal' ? 'Medium' : qual.toUpperCase()} quality`,
                                                title: isLoading ? 'Please wait for generation to complete' : `Select ${qual === 'normal' ? 'Medium' : qual.toUpperCase()} quality`
                                            }, qual==='normal'?'Medium':qual.toUpperCase())
                                        )
                                    )
                                ),
                                // Action buttons
                                React.createElement('div', {
                                    className: 'action-buttons flex gap-3',
                                    id: 'action-buttons-row'
                                },
                                    React.createElement(GenerateButton, {
                                        onClick: handleGenerateClick,
                                        isLoading: isLoading || isImprovingPrompt, // Disable during both generation AND AI enhancement
                                        includePerson: includePerson,
                                        textOverlay: textOverlay,
                                        includeIcons: includeIcons
                                    }),
                                    React.createElement(DownloadButton, {
                                        imageURL: imageURL,
                                        isLoading: isLoading || isImprovingPrompt // Disable during both generation AND AI enhancement
                                    })
                                )
                            )
                        )
                    )
                ),

                // === Right Sidebar (Thumbnail Preview) ===
                React.createElement('div', {
                    className: 'right-sidebar p-6',
                    id: 'right-sidebar-preview'
                },
                    // Coming Soon placeholder
                    React.createElement('div', {
                        className: 'coming-soon-container flex flex-col items-center justify-center h-full text-center'
                    },
                        React.createElement('div', {
                            className: 'coming-soon-icon w-28 h-28 flex items-center justify-center mb-4'
                        },
                            React.createElement('img', {
                                src: '/public/assets/empty-states/Deploy.svg',
                                alt: 'Deploy illustration',
                                className: 'w-28 h-28 text-gray-500 mb-4',
                                style: { transform: 'scale(2.5)' }
                            })
                        ),
                        React.createElement('h3', {
                            className: 'text-lg font-medium text-gray-300 mb-2'
                        }, 'Coming Soon'),
                        React.createElement('p', {
                            className: 'text-sm text-gray-500 max-w-xs'
                        }, 'This space is reserved for future features like generation history, export options, and advanced settings.')
                    )
                )
            ),

            // Render the Image Requirements Modal at the app root level
            renderImageRequirementsModal(),

            // Render the Reset Confirmation Modal
            React.createElement(ConfirmationModal, {
                isOpen: resetConfirmModal.isOpen,
                onClose: () => setResetConfirmModal({
                    isOpen: false,
                    title: '',
                    message: '',
                    onConfirm: null
                }),
                onConfirm: resetConfirmModal.onConfirm,
                title: resetConfirmModal.title,
                message: resetConfirmModal.message
            }),

            // Render the Image Source Change Confirmation Modal
            React.createElement(ConfirmationModal, {
                isOpen: imageSourceConfirmModal.isOpen,
                onClose: () => setImageSourceConfirmModal({
                    isOpen: false,
                    title: '',
                    message: '',
                    onConfirm: null
                }),
                onConfirm: imageSourceConfirmModal.onConfirm,
                title: imageSourceConfirmModal.title,
                message: imageSourceConfirmModal.message
            }),

            // Render Template Modals at root level for proper z-index handling
            // Template Selection Modal
            templateModalState.modalOpen && templateModalState.selectedCategory && React.createElement('div', {
                className: `template-modal-container fixed inset-0 z-[10002] flex items-center justify-center px-4 py-4 opacity-100`,
                id: 'template-selection-modal',
                style: {
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 10002
                }
            },
                // Backdrop
                React.createElement('div', {
                    className: `template-modal-backdrop absolute inset-0 bg-black/70 backdrop-blur-sm transition-opacity opacity-100`,
                    onClick: () => setTemplateModalState(prev => ({ ...prev, modalOpen: false, selectedCategory: null }))
                }),
                // Modal content
                React.createElement('div', {
                    className: `template-modal-content w-full max-w-4xl bg-gray-900 rounded-xl border border-gray-700 shadow-xl transform transition-all max-h-[90vh] overflow-hidden flex flex-col scale-100`,
                    role: 'dialog',
                    'aria-modal': 'true',
                    'aria-labelledby': `category-title-${templateModalState.selectedCategory.id}`
                },
                    // Header
                    React.createElement('div', {
                        className: 'template-modal-header flex items-center justify-between p-4 border-b border-gray-800'
                    },
                        // Title
                        React.createElement('h3', {
                            id: `category-title-${templateModalState.selectedCategory.id}`,
                            className: 'template-modal-title text-xl font-semibold text-white flex items-center gap-2'
                        },
                            React.createElement('span', {
                                className: `template-modal-icon flex items-center justify-center w-8 h-8 rounded ${templateModalState.selectedCategory.categoryImagePlaceholder.bgColor} text-white font-bold text-base`
                            }, templateModalState.selectedCategory.name.split(' ')[0][0]),
                            templateModalState.selectedCategory.name
                        ),
                        // Close button
                        React.createElement('button', {
                            className: 'template-modal-close-btn w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-800 hover:text-white transition-colors',
                            onClick: () => setTemplateModalState(prev => ({ ...prev, modalOpen: false, selectedCategory: null })),
                            'aria-label': 'Close modal',
                            id: 'template-modal-close-button'
                        },
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                viewBox: '0 0 24 24',
                                fill: 'none',
                                stroke: 'currentColor',
                                strokeWidth: '2',
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                className: 'w-5 h-5'
                            },
                                React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                                React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                            )
                        )
                    ),
                    // Grid of templates
                    React.createElement('div', {
                        className: 'template-modal-body overflow-y-auto p-4 flex-grow',
                        style: {
                            scrollbarWidth: 'thin',
                            scrollbarColor: '#6B7280 #374151'
                        }
                    },
                        React.createElement('div', {
                            className: 'template-items-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                        },
                            templateModalState.selectedCategory.templates.map(template =>
                                React.createElement('div', {
                                    key: template.id,
                                    className: 'template-item-card group relative bg-gray-800 border border-gray-700 hover:border-blue-500 rounded-lg overflow-hidden transition-all hover:shadow-md cursor-pointer',
                                    onClick: () => {
                                        handleTemplateSelect(template);
                                        setTemplateModalState(prev => ({ ...prev, modalOpen: false, selectedCategory: null }));
                                    },
                                    role: 'button',
                                    id: `template-item-${template.id}`,
                                    tabIndex: 0,
                                    onKeyDown: (e) => {
                                        if (e.key === 'Enter' || e.key === ' ') {
                                            e.preventDefault();
                                            handleTemplateSelect(template);
                                            setTemplateModalState(prev => ({ ...prev, modalOpen: false, selectedCategory: null }));
                                        }
                                    }
                                },
                                    // Template thumbnail
                                    React.createElement(TemplatePreviewImage, {
                                        template: template,
                                        categoryId: templateModalState.selectedCategory?.id || 'default',
                                        className: 'template-item-thumbnail'
                                    }),
                                    // Apply button overlay on hover
                                    React.createElement('div', {
                                        className: 'template-item-hover-overlay absolute inset-0  backdrop-blur-sm flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity'
                                    },
                                        React.createElement('button', {
                                            className: 'template-item-apply-btn px-4 py-2 bg-blue-600 hover:bg-purple-700 text-white rounded-full font-medium transform transition hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-purple-500'
                                        }, 'Apply Template')
                                    ),
                                    // Template info
                                    React.createElement('div', {
                                        className: 'template-item-info p-3 space-y-1'
                                    },
                                        React.createElement('h4', {
                                            className: 'template-item-title font-medium text-white text-sm'
                                        }, template.name),
                                        React.createElement('p', {
                                            className: 'template-item-description text-xs text-gray-400'
                                        }, template.description)
                                    )
                                )
                            )
                        )
                    ),
                    // Footer
                    React.createElement('div', {
                        className: 'template-modal-footer p-4 border-t border-gray-800 flex justify-end'
                    },
                        React.createElement('button', {
                            className: 'template-modal-close-button px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-md font-medium',
                            onClick: () => setTemplateModalState(prev => ({ ...prev, modalOpen: false, selectedCategory: null })),
                            id: 'template-modal-footer-close-btn'
                        }, 'Close')
                    )
                )
            ),

            // Render Subscription Checkout Modal at root level for proper z-index handling
            React.createElement(SubscriptionCheckoutModal, {
                isOpen: isSubscriptionModalOpen,
                onClose: handleCloseSubscriptionModal,
                selectedPlan: selectedSubscriptionPlan
            }),
            
            // Render Thumbnail Preview Modal at root level for proper z-index handling
            React.createElement(ThumbnailPreviewModal, {
                isOpen: thumbnailPreviewModal.isOpen,
                onClose: handleCloseThumbnailPreview,
                thumbnail: thumbnailPreviewModal.thumbnail,
                title: thumbnailPreviewModal.title,
                itemId: thumbnailPreviewModal.itemId,
                fullImageUrl: thumbnailPreviewModal.fullImageUrl
            }),
            
            // Render Success Toast for reset and other operations with enhanced smooth close
            React.createElement(SuccessToast, {
                isVisible: successToast.isVisible,
                message: successToast.message,
                type: successToast.type,
                onDismiss: () => {
                    // Trigger smooth fade-out first
                    setSuccessToast(prev => ({ ...prev, isVisible: false }));
                    
                    // Clear toast completely after the slide-out animation completes
                    setTimeout(() => {
                        setSuccessToast({
                            isVisible: false,
                            message: '',
                            type: 'success'
                        });
                    }, 362); // 312ms animation + 50ms delay
                }
            })
        )
    );
};
