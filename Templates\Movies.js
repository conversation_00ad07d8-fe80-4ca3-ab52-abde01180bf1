export default [
  {
    id: "movie-review",
    name: "[MOVIE] Review: Worth Watching?",
    description: "For movie reviews and entertainment content.",
    promptBase: "Create a cinematic YouTube thumbnail for '[MOVIE] Review: Worth Watching?'. Show a reviewer with a thinking expression and a movie poster. Text overlay: '[MOVIE] WORTH WATCHING?'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Thinking', textOverlay: true, overlayText: "[MOVIE]\nWORTH\nWATCHING?" },
    templateImagePlaceholder: { text: "Movie Review", bgColor: "bg-red-500" }
  },
  {
    id: "movie-vs",
    name: "[MOVIE 1] vs [MOVIE 2] Showdown",
    description: "Versus-style comparison for two movies.",
    promptBase: "Design a split-screen thumbnail for '[MOVIE 1] vs [MOVIE 2]'. Use dramatic lighting, film icons, and bold text overlay: '[MOVIE 1] VS [MOVIE 2]'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "[MOVIE 1] VS [MOVIE 2]" },
    templateImagePlaceholder: { text: "Showdown", bgColor: "bg-red-400" }
  },
  {
    id: "box-office-hit",
    name: "[MOVIE] Box Office Hit!",
    description: "Highlight a blockbuster movie's success.",
    promptBase: "Create a bold thumbnail for '[MOVIE] Box Office Hit!'. Show a person celebrating with confetti and a trophy icon. Text overlay: 'BOX OFFICE HIT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', includeIcons: true, textOverlay: true, overlayText: "BOX OFFICE\nHIT!" },
    templateImagePlaceholder: { text: "Box Office", bgColor: "bg-red-300" }
  },
  {
    id: "movie-explained",
    name: "[MOVIE] Ending Explained",
    description: "For deep-dive or analysis content.",
    promptBase: "Design a mysterious thumbnail for '[MOVIE] Ending Explained'. Use a person with a curious expression, question mark icon, and cinematic background. Text overlay: 'ENDING EXPLAINED'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Curious', includeIcons: true, textOverlay: true, overlayText: "ENDING\nEXPLAINED" },
    templateImagePlaceholder: { text: "Explained", bgColor: "bg-red-200" }
  },
  {
    id: "movie-tier-list",
    name: "[GENRE] Movie Tier List!",
    description: "Ranking movies in a genre.",
    promptBase: "Create a fun tier list thumbnail for '[GENRE] Movie Tier List!'. Use film reel and star icons, and a person pointing at a ranking chart. Text overlay: 'TIER LIST!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Pointing', includeIcons: true, textOverlay: true, overlayText: "TIER\nLIST!" },
    templateImagePlaceholder: { text: "Tier List", bgColor: "bg-red-100" }
  },
  {
    id: "movie-trailer-reaction",
    name: "[MOVIE] Trailer Reaction!",
    description: "For trailer reaction videos.",
    promptBase: "Design a reaction thumbnail for '[MOVIE] Trailer Reaction!'. Show a person with a surprised expression, popcorn and clapperboard icons, and bold text overlay: 'TRAILER REACTION!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "TRAILER\nREACTION!" },
    templateImagePlaceholder: { text: "Trailer Reaction", bgColor: "bg-red-50" }
  }
]; 