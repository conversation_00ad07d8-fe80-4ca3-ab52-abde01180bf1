---
description: 
globs: 
alwaysApply: false
---
- ---
ruleId: face-upload-match-enhancement
description: Enhance the "Custom Face Upload" feature for accurate AI face generation by leveraging face detection, robust embedding, and precise prompt conditioning to ensure the generated output strongly resembles the uploaded face image.
ruleType: always
appliesTo:
  - /src/utils/promptBuilder.ts # For prompt conditioning logic
  - /src/hooks/usePromptEnhancer.ts # If a hook is used for prompt modification
  - /src/components/ControlPanel.jsx # Where face upload UI/logic resides
  - /src/utils/faceMatcher.js # NEW: Utility for face detection/embedding
  - /src/utils/imageProcessor.js # NEW: Utility for image preprocessing
---

## 📸 Face Upload Matching Enhancement

### Purpose
Ensure that AI-generated images, when a custom face is uploaded or a URL is provided, accurately and consistently reflect the facial features, expression, and likeness of the original input face.

### Implementation Notes

1.  **Robust Face Detection & Embedding:**
    -   Implement a utility (e.g., in `src/utils/faceMatcher.js`) to detect faces within the uploaded image or URL.
    -   Extract high-quality facial embeddings using a suitable model (e.g., FaceNet, ArcFace). This model should be optimized for likeness recognition.
    -   Ensure the embedding process captures key facial attributes like shape, expression, skin tone, and distinguishing features.

2.  **Image Preprocessing Pipeline:**
    -   Before embedding, implement preprocessing steps (in `src/utils/imageProcessor.js` or `src/utils/faceMatcher.js`).
    -   This should include:
        -   **Cropping:** Automatically crop to focus on the detected face.
        -   **Alignment:** Standardize face orientation (e.g., rotate to upright, align eyes).
        -   **Resizing:** Resize the cropped face to an optimal input dimension for the embedding model.
        -   **Normalization/Enhancement (Optional but Recommended):** Apply basic image enhancements (e.g., contrast adjustment, light denoising) to improve feature extraction quality, if beneficial.

3.  **Advanced Prompt Conditioning (in `src/utils/promptBuilder.ts`):**
    -   Modify the AI image generation prompt to explicitly instruct the model to use the uploaded face as the primary reference for likeness.
    -   **Pass Embeddings/References (if API supports):** If the AI image generation API allows direct passing of facial embeddings or reference images, prioritize this method.
    -   **Textual Prompt Augmentation:** If direct passing isn't fully supported or for additional emphasis, augment the textual prompt with strong directives:
        -   "Generate a person whose face *exactly* matches the uploaded reference image."
        -   "Strictly preserve facial structure, expression, unique features, and skin tone from the provided photo."
        -   "The generated face must be *unmistakably* the same person as in the uploaded image."
        -   "Avoid generic faces; prioritize likeness over stylistic variations."

4.  **Error & Fallback Handling:**
    -   Implement clear user feedback if face detection fails (e.g., "Could not detect a clear face. Please try a different photo.").
    -   Define a graceful fallback if the AI model cannot fully replicate the likeness (e.g., generate a person with similar general characteristics, but inform the user of limitations).

5.  **Comprehensive Testing & Validation:**
    -   Conduct thorough testing with a diverse set of real-world face images, including variations in:
        -   Age, gender, ethnicity
        -   Lighting conditions (front-lit, back-lit, shadowed)
        -   Expressions (neutral, smiling, surprised)
        -   Head poses (slight turns, tilts)
    -   Qualitatively and quantitatively evaluate the likeness accuracy between generated and input faces.

### Sample Transformed AI Prompt Output

**User Input (Implied):** User uploads a photo of "John Doe"

**Transformed AI Image Generation Prompt:**