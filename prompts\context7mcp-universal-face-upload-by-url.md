# Context7MCP Universal Face Upload by URL – Advanced Multi-Service Support

Design a cinematic YouTube thumbnail at 1280x720 resolution, using the following face image provided by <PERSON><PERSON> as the main subject:

**Face Image URL:** [PASTE_USER_URL_HERE]

## Context7MCP Universal Face Upload Optimization - Critical Requirements

### UNIVERSAL URL SUPPORT & TRANSFORMATION:
- **Imgur Support**: Automatically handle all Imgur formats (i.imgur.com, imgur.com/gallery, imgur.com/a/, m.imgur.com)
- **Google Drive Support**: Transform sharing URLs to direct access URLs (drive.google.com/file/d/*/view → uc?export=view)
- **Dropbox Support**: Convert sharing URLs to direct download URLs (?dl=0 → ?raw=1)
- **Discord CDN Support**: Handle Discord attachment URLs (cdn.discordapp.com, media.discordapp.net)
- **GitHub Support**: Transform repository URLs to raw.githubusercontent.com direct links
- **Direct Image URLs**: Support all .jpg, .jpeg, .png, .webp formats from any domain
- **CORS Handling**: Automatically handle cross-origin image loading with proper fallbacks

### COMPREHENSIVE FORMAT SUPPORT:
- **JPEG/JPG**: Full support for all quality levels and variants
- **PNG**: Support for transparent and non-transparent PNG images
- **WebP**: Modern format support with fallback handling
- **Format Detection**: Automatically detect and validate image formats from URLs
- **Fallback Strategy**: If primary format fails, attempt alternative formats (jpg→png→webp)

### ENHANCED FACE DETECTION & EXTRACTION:
- **Multi-Service Compatibility**: Ensure face detection works regardless of image source (Imgur, Google Drive, etc.)
- **Always detect and extract the most prominent face** from the provided URL image
- **If multiple faces are present** in the URL image, use the largest, most frontal face
- **PRIORITY REPLACEMENT:** The face from the URL must ALWAYS replace the original face in the thumbnail - never ignore or omit
- **URL Accessibility**: Ensure proper loading and accessibility of the provided image URL across all supported services

### SEAMLESS BLENDING & MATCHING:
- **Preserve EXACT facial identity** from the URL: bone structure, nose shape, eye shape and spacing, mouth shape, jawline, forehead proportions
- **Match ear shape** if visible, any unique facial features (moles, freckles, scars) from the reference image
- **EXACT skin tone and texture replication** from URL image - avoid mismatched colors or harsh edges
- **Hair color and style** must match the URL face exactly
- **Automatically harmonize lighting** between URL face and thumbnail scene for natural integration
- **Service-Agnostic Quality**: Maintain consistent quality regardless of image source (Imgur vs Google Drive vs direct URL)

### FACIAL ALIGNMENT & ORIENTATION:
- **Align the URL face** to match the pose, angle, and expression requirements of the thumbnail
- **Adjust orientation** as needed for perfect fit while maintaining the URL face's identity
- **Expression adaptation:** If needed, adapt the URL face to match required expression while preserving identity
- **Pose matching:** Ensure the face fits naturally with the intended body pose and camera angle
- **Resolution Independence**: Handle both high and low resolution source images effectively

### EDGE CASE HANDLING:
- **Partial occlusion:** If URL face is partially occluded, in shadow, or at unusual angle, enhance and correct for best integration
- **Lighting compensation:** Compensate for different lighting conditions between URL source and thumbnail target
- **Compression artifacts:** Handle images with JPEG compression or other artifacts gracefully
- **Aspect ratio variations:** Work with square, portrait, landscape, and unusual aspect ratios
- **Service-specific issues:** Handle rate limiting, temporary unavailability, or service-specific image processing

### QUALITY ASSURANCE:
- **Validation Success**: Only proceed if URL validation and accessibility tests pass
- **Error Handling**: Provide clear feedback for invalid URLs, unsupported formats, or inaccessible images
- **Performance Optimization**: Efficient loading and processing regardless of image source
- **Consistency Standards**: Maintain identical quality standards across all supported services

### TECHNICAL SPECIFICATIONS:
- **Maximum Resolution**: Handle images up to 4K resolution efficiently
- **Minimum Resolution**: Work with images as small as 200x200 pixels
- **File Size Range**: Support images from 50KB to 10MB
- **Loading Timeout**: Maximum 10 seconds for image accessibility testing
- **CORS Compliance**: Proper cross-origin handling for all supported services

## Expected Output

A thumbnail with the uploaded face from the provided URL perfectly swapped in, seamlessly blended, and visually harmonious with the rest of the image, following all Context7MCP Universal Face Upload optimization requirements. The system should work flawlessly regardless of whether the URL is from Imgur, Google Drive, Dropbox, Discord, GitHub, or any direct image URL.

## Supported URL Examples

### Imgur:
- https://i.imgur.com/abc123.jpg
- https://imgur.com/abc123
- https://imgur.com/gallery/abc123
- https://m.imgur.com/abc123

### Google Drive:
- https://drive.google.com/file/d/abc123/view
- https://drive.google.com/open?id=abc123

### Dropbox:
- https://www.dropbox.com/s/abc123/image.jpg?dl=0
- https://dropbox.com/s/abc123/image.jpg

### Discord:
- https://cdn.discordapp.com/attachments/123/456/image.jpg
- https://media.discordapp.net/attachments/123/456/image.png

### GitHub:
- https://github.com/user/repo/blob/main/image.jpg
- https://raw.githubusercontent.com/user/repo/main/image.jpg

### Direct URLs:
- https://example.com/image.jpg
- https://example.com/image.png
- https://example.com/image.webp

---

**Note**: This system automatically detects the service type, transforms URLs as needed, validates accessibility, and ensures 100% face application rate across all supported platforms and formats. 