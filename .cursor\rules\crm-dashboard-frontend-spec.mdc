---
description: 
globs: 
alwaysApply: false
---
ruleId: crm-dashboard-frontend-spec-en-v3
description: Product Requirements Document for the CRM (Customer Relationship Management) Dashboard Front-End, incorporating custom naming conventions.
ruleType: design_spec
appliesTo:
  - /src/pages/admin/AdminDashboard.jsx # Main container for the CRM
  - /src/components/admin/ # New directory for CRM-specific components
  - /src/styles/admin.css # Specific styles for the admin dashboard if needed
---

## CRM (Customer Relationship Management) Dashboard: Front-End Product Requirements Document (PRD)

**Version:** 1.2 (Updated for Custom Naming Rules & Skip to Login/Signup button)
**Date:** {{CURRENT_DATE}}
**Author:** Gemini Assistant

### 1. Introduction

#### 1.1 Purpose
The purpose of this document is to define the requirements for the front-end design and functionality of an administrative CRM (Customer Relationship Management) Dashboard for the "GPT-4 Vision Thumbnail Generator MVP". This dashboard will serve as the central interface for application monitoring, content management, and basic user oversight. It will also include a quick navigation option to the main application's login/signup pages. **All UI elements, blocks, divs, classes, and IDs must adhere to the naming conventions defined in `custom-naming.mdc`.**

#### 1.2 Goals
-   Provide an intuitive interface to display key application metrics.
-   Enable administrators to easily manage thumbnail templates and background presets.
-   Offer the ability to monitor basic user activity and API usage.
-   Include a simple "Skip to Login/Signup" button for users who may need to access the main application's authentication.
-   Ensure all UI components and containers are uniquely and descriptively named according to `custom-naming.mdc` for enhanced inspectability and maintainability.
-   Focus solely on the front-end UI/UX for the initial phase, without backend dependencies.

#### 1.3 Scope
-   **In Scope:**
    -   Complete front-end design and UI/UX for the CRM Dashboard.
    -   Design of the following key sections: Dashboard Overview, User Management, Template Management, Background Management, API Usage Analytics.
    -   Inclusion of a "Skip to Login/Signup" button in the header or a prominent top position.
    -   Visual representation of UI elements using mock data.
    -   Visual consistency with the existing application style (Hero UI, Tailwind CSS, Dark Mode).
    -   **Strict adherence to `custom-naming.mdc` for all `className` and `id` attributes on divs, blocks, features, and components.**
-   **Out of Scope:**
    -   Any backend implementation, database integration, or API development.
    -   Advanced user roles and permissions.
    -   Real-time data updates (at this stage).
    -   Authentication logic for the CRM dashboard itself (assumed admin access for design purposes).

#### 1.4 Reference Documents
-   `project-guidelines.mdc`: Core project guidelines, styling, and structure.
-   `custom-naming.mdc`: **Defines mandatory naming conventions for all UI elements, classes, and IDs.**
-   `background-templates-modal.mdc`: Reference for background selector UI.
-   `context7.mcp`: Overall project context.

### 2. Target Users
-   **Administrator / Project Owner:** Users responsible for managing the application, updating content, and monitoring usage.
-   **(Implicit) Users landing on admin page by mistake:** Users who might need a quick redirect to the main application's login/signup.

### 3. Features and Functionality

*(Sections 3.1 to 3.6 remain largely the same but assume that all created elements like containers for Metric Cards, table wrappers, modal content areas, etc., will receive unique and descriptive classNames/IDs as per `custom-naming.mdc`.)*

#### 3.1 Overall Layout
-   **Navigation:** Fixed left sidebar (e.g., `id="admin-sidebar-nav"`, `className="admin-sidebar-navigation"`).
    -   Links: "Dashboard Overview", "User Management", "Template Management", "Background Management", "API Analytics", "Settings".
-   **Header:** Simple top bar (e.g., `id="admin-header-main"`, `className="admin-main-header"`).
    -   Contains the application name/logo.
    -   **"Skip to Login/Signup" button/link:** Positioned visibly (e.g., `id="skip-to-auth-btn"`), allowing users to quickly navigate to the main application's login or signup page.
    -   Potentially an admin user profile/logout icon on the right.
-   **Main Content Area:** Dynamically renders content based on the sidebar selection (e.g., `id="admin-main-content-area"`, `className="admin-content-wrapper"`).

*(Similar detailed naming should be mentally applied to all sub-features like metric card containers, table wrappers, modal divs, form field containers, etc. For brevity, not all are explicitly listed here but are implied by the `custom-naming.mdc` requirement.)*

#### 3.2 Dashboard Overview (`/admin/dashboard`)
-   **Metric Cards Container** (e.g., `className="admin-dashboard-metric-cards-container"`):
    -   Individual Metric Cards (e.g., `className="metric-card revenue-metric" id="metric-card-total-revenue"`)
-   **Charts Container** (e.g., `className="admin-dashboard-charts-container"`)
-   **Quick Actions Section** (e.g., `className="admin-dashboard-quick-actions"`)

#### 3.3 Template Management (`/admin/templates`)
-   **Template Table Wrapper** (e.g., `className="admin-templates-table-wrapper"`)
-   **Add/Edit Template Modal Container** (e.g., `id="template-editor-modal-container"`)

#### 3.4 Background Management (`/admin/backgrounds`)
-   **Background Grid/Table Wrapper** (e.g., `className="admin-backgrounds-list-container"`)
-   **Add/Edit Background Modal Container** (e.g., `id="background-editor-modal-container"`)

#### 3.5 User Management (Basic - `/admin/users`)
-   **User Table Wrapper** (e.g., `className="admin-users-table-wrapper"`)

#### 3.6 API Usage Analytics (`/admin/api-usage`)
-   **API Charts Container** (e.g., `className="admin-api-analytics-charts-area"`)
-   **API Usage Table Wrapper** (e.g., `className="admin-api-usage-table-wrapper"`)


### 4. Design & UI/UX Considerations

#### 4.1 Styling & Components
-   **Hero UI & Tailwind CSS:** Strictly use Hero UI components and Tailwind CSS utility classes (as per `project-guidelines.mdc`).
-   **Custom Naming Conventions:** **All UI elements, containers, blocks, features, divs, etc., must be assigned unique and descriptive `className` and/or `id` attributes following the rules outlined in `custom-naming.mdc`. This is critical for inspectability and maintainability.**
-   **Dark Mode:** Maintain dark mode consistency with the main application.
-   **Heroicons:** Use Heroicons for all iconography.
-   **Responsive Design:** Ensure usability on various screen sizes, though a desktop-first approach is acceptable for admin panels.
-   The "Skip to Login/Signup" button should be styled to be noticeable but not overly intrusive, fitting the overall header design.

#### 4.2 Usability
-   Clear and concise information display.
-   Easy navigation and intuitive action flows.
-   Proper validation and feedback messages in forms (front-end validation only).
-   The "Skip to Login/Signup" button provides an obvious and immediate way to return to the main application's entry points.

### 5. Technical Considerations (Front-End Only)

#### 5.1 Framework & Libraries
-   **React:** Functional components, `useState`, `useEffect` hooks.
-   **React Router (or similar for front-end routing):** To handle navigation for the "Skip to Login/Signup" button if it directs to different routes within the SPA, or standard `<a>` tags if linking to external/separate pages.
-   **Hero UI (CDN):** For UI components.
-   **Heroicons (CDN):** For icons.
-   (Optional) Charting library (Chart.js, Recharts, etc.) as visual placeholders or for simple static charts.

#### 5.2 Component Structure
-   `/src/pages/admin/AdminDashboard.jsx`: Main CRM layout and routing logic. All primary sections within will adhere to `custom-naming.mdc`.
-   `/src/components/admin/`: CRM-specific reusable components. Component-level class names should be prefixed (e.g., `className="admin-metric-card"` for a `<MetricCard />` component's root div) as per `custom-naming.mdc`.
-   Separate page components for each major section.

#### 5.3 Data Handling
-   All data will be mocked at this stage.
-   Design appropriate data structures for displaying data.

#### 5.4 State Management
-   `useState` for page-level state.
-   Simple prop-drilling if necessary.

### 6. Future Considerations (Brief Mention)
-   Secure authentication and authorization system for the admin dashboard itself.
-   Fetching and posting real data via API integration.
-   Advanced filtering, sorting, and pagination.
-   Error handling and notification system.
-   Audit logs.

### 7. Acceptance Criteria (For Front-End Design)
-   All specified pages and sections of the CRM Dashboard are created using Hero UI and Tailwind CSS.
-   The UI is in dark mode and consistent with the main application's visual style.
-   Navigation is clear, allowing easy access to all major sections.
-   A "Skip to Login/Signup" button is present and functional (mock navigation acceptable).
-   **All major UI elements, containers, blocks, and features have unique and descriptive `className` and/or `id` attributes applied, following the guidelines in `custom-naming.mdc`.**
-   Forms and tables are displayed correctly (with mock data).
-   The UI concept for template and background management is clearly demonstrated.
-   All basic controls and information areas required for the MVP are included in the design.