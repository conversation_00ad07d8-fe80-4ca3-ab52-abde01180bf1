{"name": "thumbspark-thumbnail-generator", "version": "1.0.0", "type": "module", "main": "src/main.jsx", "scripts": {"dev": "vite --port 3000", "build": "vite build --mode production", "preview": "vite preview --port 3000", "start": "vite --port 3000", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist/stats.html"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@iconify/react": "^6.0.0", "@lottiefiles/dotlottie-react": "^0.14.0", "@solar-icons/react": "^1.0.1", "@supabase/supabase-js": "^2.50.2", "face-api.js": "^0.22.2", "geist": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@vitejs/plugin-react": "^4.4.1", "vite": "^6.3.4"}}