export default [
  {
    id: "fortnite-highlight",
    name: "Fortnite Epic Highlight!",
    description: "For Fortnite highlight and gameplay videos.",
    promptBase: "Create a dynamic YouTube thumbnail for 'Fortnite Epic Highlight!'. Show an action-packed battle scene with neon effects and a player celebrating a victory royale. Text overlay: 'EPIC HIGHLIGHT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Excited', textOverlay: true, overlayText: "EPIC\nHIGHLIGHT!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/FTHightlit.webp" }
  },
  {
    id: "fortnite-vs",
    name: "Fortnite 1v1 Showdown",
    description: "Versus-style 1v1 battle in Fortnite.",
    promptBase: "Design a split-screen thumbnail for 'Fortnite 1v1 Showdown'. Use Fortnite icons, dramatic lighting, and bold text overlay: '1v1 SHOWDOWN'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "1v1\nSHOWDOWN" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/fortnite-vs.webp" }
  },
  {
    id: "valorant-loadout",
    name: "Best Valorant Loadout!",
    description: "For best weapons/gear guides in Valorant.",
    promptBase: "Create a bold thumbnail for 'Best Valorant Loadout!'. Show a player holding top-tier weapons, with gun and shield icons. Text overlay: 'BEST LOADOUT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Confident', includeIcons: true, textOverlay: true, overlayText: "BEST\nLOADOUT!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/valorant-loadout.webp" }
  },
  {
    id: "valorant-tips-tricks",
    name: "Valorant Tips & Tricks!",
    description: "For tips and tricks in Valorant gameplay.",
    promptBase: "Create an informative thumbnail for 'Valorant Tips & Tricks!'. Highlight key strategies and techniques with detailed visuals. Use icons like crosshairs and strategy maps, and position bold text overlay: 'TIPS & TRICKS' at the center.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "TIPS & TRICKS", textPosition: 'center' },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/valorant-tips-tricks.webp" }
  },
  {
    id: "clash-ultimate-townhall",
    name: "Ultimate Town Hall 16 Base Design",
    description: "For designing the ultimate Town Hall 16 base in Clash of Clans.",
    promptBase: "Create a strategic thumbnail for 'Ultimate Town Hall 16 Base Design'. Use cartoonish Clash of Clans icons and a blue text overlay: 'ULTIMATE BASE DESIGN'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "ULTIMATE\nBASE DESIGN", textColor: 'blue' },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/clash-ultimate-townhall.webp" }
  },
  {
    id: "clash-legend-base",
    name: "This Base Design Made Me a Legend in the League!",
    description: "For showcasing a legendary base design in Clash of Clans.",
    promptBase: "Design a legendary thumbnail for 'This Base Design Made Me a Legend in the League!'. Incorporate cartoonish Clash of Clans icons and theme style with bold text overlay: 'LEGENDARY BASE!'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "LEGENDARY\nBASE!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/clash-legend-base.webp" }
  },
  {
    id: "clash-queen-walk",
    name: "Perfect Queen Walk Strategy",
    description: "For mastering the Queen Walk strategy in Clash of Clans.",
    promptBase: "Create a tactical thumbnail for 'Perfect Queen Walk Strategy'. Use cartoonish Clash of Clans icons and a green text overlay: 'QUEEN WALK STRATEGY'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "QUEEN WALK\nSTRATEGY", textColor: 'green' },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/clash-queen-walk.webp" }
  },
  {
    id: "pubg-guide",
    name: "PUBG Pro Guide!",
    description: "For pro tips and strategy guides in PUBG.",
    promptBase: "Create a pro guide thumbnail for 'PUBG Pro Guide!'. Show a tactical soldier wearing PUBG military gear with helmet and combat suit, pointing at strategic elements. Include map and trophy icons, with text overlay: 'PRO GUIDE!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Pointing', includeIcons: true, textOverlay: true, overlayText: "PRO\nGUIDE!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/pubg-guide.webp" }
  },
  {
    id: "pubg-update",
    name: "PUBG Update News!",
    description: "For patch/update news and reactions in PUBG.",
    promptBase: "Design a news-style thumbnail for 'PUBG Update News!'. Show a soldier wearing a PUBG military helmet and combat war suit with a normal expression and slight smile, update and lightning icons, and bold text overlay: 'UPDATE NEWS!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Normal', includeIcons: true, textOverlay: true, overlayText: "UPDATE\nNEWS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/pubg-update.webp" }
  },
  {
    id: "warzone-highlight",
    name: "Warzone Epic Highlight!",
    description: "For Warzone highlight and gameplay videos.",
    promptBase: "Create a military-themed YouTube thumbnail for 'Warzone Epic Highlight!'. Show a soldier in tactical gear amidst a battlefield with military vehicles and explosions. Text overlay: 'EPIC HIGHLIGHT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Normal', textOverlay: true, overlayText: "EPIC\nHIGHLIGHT!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/warzone-highlight.webp" }
  },
  {
    id: "warzone-vs",
    name: "Warzone 1v1 Showdown",
    description: "Versus-style 1v1 battle in Warzone.",
    promptBase: "Design a split-screen thumbnail for 'Warzone 1v1 Showdown'. Use Warzone icons, dramatic lighting, and bold text overlay: '1v1 SHOWDOWN'.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "1v1\nSHOWDOWN" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/warzone-vs.webp" }
  },
  {
    id: "lol-guide",
    name: "League of Legends Pro Guide!",
    description: "For pro tips and strategy guides in League of Legends.",
    promptBase: "Create a pro guide thumbnail for 'League of Legends Pro Guide!'. Use a player pointing, map and trophy icons, and text overlay: 'PRO GUIDE!'.",
    settingsToApply: { includePerson: false, selectedExpression: 'Pointing', includeIcons: true, textOverlay: true, overlayText: "PRO\nGUIDE!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/lol-guide.webp" }
  },
  {
    id: "lol-update",
    name: "League of Legends Update News!",
    description: "For patch/update news and reactions in League of Legends.",
    promptBase: "Design a news-style thumbnail for 'League of Legends Update News!'. Show a player with a surprised expression, update and lightning icons, and bold text overlay: 'UPDATE NEWS!'.",
    settingsToApply: { includePerson: false, selectedExpression: 'Surprised', includeIcons: true, textOverlay: true, overlayText: "UPDATE\nNEWS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/lol-update.webp" }
  },
  {
    id: "gtav-reaction",
    name: "GTAV Code Cheats!",
    description: "For funny or surprising moments in GTAV.",
    promptBase: "Create a mysterious thumbnail for 'GTAV Code Cheats!'. Show a player with a shocked expression, question mark icons, and cards with weird symbols. Include blurred weapons in the background and bold text overlay: 'CODE CHEATS!'.",
    settingsToApply: { includePerson: false, selectedExpression: 'Shocked', includeIcons: true, textOverlay: true, overlayText: "CODE\nCHEATS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/gtav-reaction.webp" }
  },
  {
    id: "gtav-highlight",
    name: "GTAV Epic Highlight!",
    description: "For GTAV highlight and gameplay videos featuring mods, cars, and challenging scenarios.",
    promptBase: "Create a dynamic YouTube thumbnail for 'GTAV Epic Highlight!'. Show an action-packed scene with neon effects and a player with a thinking expression amidst mods and cars. Text overlay: 'EPIC HIGHLIGHT!'.",
    settingsToApply: { includePerson: true, selectedExpression: 'Thinking', textOverlay: true, overlayText: "EPIC\nHIGHLIGHT!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Gaming/gtav-highlight.webp" }
  }
];