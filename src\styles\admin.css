/* ================= ADMIN DASHBOARD STYLES ================= */

/* Admin Dashboard Container */
.admin-dashboard-container {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Admin Sidebar Navigation */
.admin-sidebar-navigation {
    min-height: 100vh;
    position: sticky;
    top: 0;
}

/* Admin Navigation Items */
.admin-nav-item:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* Admin Table Hover Effects */
.admin-table-row:hover {
    background-color: #374151; /* gray-700 */
}

/* Admin Metric Cards */
.admin-metric-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

/* Admin Chart Placeholders */
.admin-chart-placeholder {
    position: relative;
}

.admin-chart-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.1) 50%, transparent 70%);
    border-radius: 0.5rem;
    pointer-events: none;
}

/* Template Modal Styles */
.template-modal-overlay {
    backdrop-filter: blur(4px);
}

.template-modal-content {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Template Setting Toggles */
.template-setting-toggle:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* Background Card Hover Effects */
.admin-background-card:hover .admin-background-actions-overlay {
    opacity: 1;
}

/* API Usage Analytics */
.admin-api-stat-card {
    position: relative;
    overflow: hidden;
}

.admin-api-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981);
}

/* User Status Badges */
.user-status-badge {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Quick Action Cards */
.admin-quick-action-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
}

/* Admin Form Focus States */
.admin-template-search-input:focus,
.admin-template-category-select:focus,
.admin-user-search-input:focus,
.admin-background-search-input:focus,
.admin-background-category-select:focus,
.admin-api-time-range-select:focus {
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .admin-dashboard-container {
        flex-direction: column;
    }
    
    .admin-sidebar-navigation {
        position: relative;
        width: 100%;
        min-height: auto;
    }
    
    .admin-main-content-wrapper {
        width: 100%;
    }
}

/* Loading States */
.admin-loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Accessibility Improvements */
.admin-nav-item:focus-visible,
.template-modal-close-btn:focus-visible,
.admin-add-template-btn:focus-visible,
.admin-add-background-btn:focus-visible {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .admin-sidebar-navigation,
    .admin-header-main,
    .template-modal-overlay {
        display: none !important;
    }
    
    .admin-main-content-wrapper {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* Dark Mode Enhancements */
.admin-dashboard-container {
    color-scheme: dark;
}

/* Scrollbar Styling for Admin Areas */
.admin-content-wrapper::-webkit-scrollbar,
.template-modal-content::-webkit-scrollbar {
    width: 8px;
}

.admin-content-wrapper::-webkit-scrollbar-track,
.template-modal-content::-webkit-scrollbar-track {
    background: #1f2937;
}

.admin-content-wrapper::-webkit-scrollbar-thumb,
.template-modal-content::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

.admin-content-wrapper::-webkit-scrollbar-thumb:hover,
.template-modal-content::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
} 