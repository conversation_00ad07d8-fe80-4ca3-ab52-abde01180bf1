<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        input[type="url"] {
            width: 70%;
            padding: 10px;
            margin: 10px 0;
            background-color: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #7c3aed;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background-color: #6d28d9;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background-color: #333;
            border-radius: 5px;
            border-left: 4px solid #7c3aed;
        }
        .error {
            border-left-color: #ef4444;
        }
        .success {
            border-left-color: #10b981;
        }
        .warning {
            border-left-color: #f59e0b;
        }
        img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Face Upload Validation Test</h1>
    <p>Test the face upload functionality without face-api.js models</p>
    
    <div class="test-container">
        <h2>Test URLs</h2>
        <div>
            <label>Enter image URL:</label><br>
            <input type="url" id="testUrl" placeholder="https://example.com/image.jpg" value="https://i.imgur.com/example.jpg">
            <button onclick="testValidation()">Test Validation</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="imagePreview" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>Sample Test URLs</h2>
        <button onclick="testSampleUrl('https://i.imgur.com/7kZwQpS.jpg')">Test MrBeast Image</button>
        <button onclick="testSampleUrl('https://picsum.photos/400/400')">Test Random Portrait</button>
        <button onclick="testSampleUrl('https://picsum.photos/800/600')">Test Landscape</button>
        <button onclick="testSampleUrl('https://invalid-url.com/nonexistent.jpg')">Test Invalid URL</button>
    </div>

    <script type="module">
        import { validateImageUrlComprehensive } from './src/utils/imageUrlValidator.js';
        
        window.testValidation = async function() {
            const url = document.getElementById('testUrl').value;
            const resultDiv = document.getElementById('result');
            const imagePreview = document.getElementById('imagePreview');
            
            if (!url.trim()) {
                showResult('Please enter a URL', 'error');
                return;
            }
            
            showResult('Validating...', 'info');
            
            try {
                const validation = await validateImageUrlComprehensive(url);
                
                if (validation.isValid && validation.isAccessible) {
                    showResult(`✅ Success! Image loaded from ${validation.service || 'direct URL'}`, 'success');
                    showImage(validation.transformedUrl || validation.url);
                } else {
                    showResult(`❌ Failed: ${validation.error}`, 'error');
                    hideImage();
                }
            } catch (error) {
                showResult(`❌ Error: ${error.message}`, 'error');
                hideImage();
            }
        };
        
        window.testSampleUrl = function(url) {
            document.getElementById('testUrl').value = url;
            testValidation();
        };
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function showImage(url) {
            const imagePreview = document.getElementById('imagePreview');
            imagePreview.innerHTML = `<img src="${url}" alt="Preview" onload="console.log('Image loaded successfully')" onerror="console.error('Image failed to load')">`;
            imagePreview.style.display = 'block';
        }
        
        function hideImage() {
            const imagePreview = document.getElementById('imagePreview');
            imagePreview.style.display = 'none';
        }
    </script>
</body>
</html> 