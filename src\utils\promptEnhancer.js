export const enhancePrompt = (raw) => {
  if (!raw) return '';

  // 1. Strip emojis and non-ASCII pictographs
  let txt = raw.replace(/[\p{Extended_Pictographic}]/gu, '').trim();

  // 2. Lowercase for easier replacement
  txt = txt.toLowerCase();

  // 3. Remove personal pronouns / filler verbs
  const stopWords = [
    'i', 'my', 'me', 'mine', 'we', 'our', 'ours', 'you', 'your',
    'use', 'using', 'show', 'shows', 'showing', 'explain', 'explaining',
    'react', 'reacting', 'study', 'studying', 'learn', 'learning',
    'talk', 'talking', 'give', 'giving', 'take', 'taking', 'make', 'making',
    'about', 'the', 'a', 'an', 'of', 'to', 'for', 'on', 'in' // common fillers
  ];
  stopWords.forEach(w => {
    const re = new RegExp(`\\b${w}\\b`, 'gi');
    txt = txt.replace(re, ' ');
  });

  // Collapse multiple spaces and trim
  txt = txt.replace(/\s{2,}/g, ' ').trim();

  if (!txt) return '';

  // 4. Keyword→scene mapping
  const mappings = [
    {
      keywords: ['website', 'websites', 'site'],
      scene: 'A modern workspace with a laptop displaying popular study websites, books and a cup of coffee on the desk. Soft daylight, clean background, cinematic composition.'
    },
    {
      keywords: ['tool', 'tools', 'software', 'app', 'apps'],
      scene: 'A sleek tech setup showcasing various AI tools on floating holographic screens, neon accents, dark gradient background.'
    },
    {
      keywords: ['iphone', 'smartphone', 'phone'],
      scene: 'A glowing smartphone centered on a minimalist surface with subtle reflections and dramatic rim lighting.'
    },
    {
      keywords: ['purchase', 'purchases', 'buy', 'buys'],
      scene: 'A dramatic tabletop scene with assorted gadgets under a single spotlight against a dark backdrop.'
    },
    {
      keywords: ['tiktok', 'tiktoks', 'video', 'videos'],
      scene: 'A retro theatre marquee background with film reels and colorful light leaks in a nostalgic style.'
    }
  ];

  for (const map of mappings) {
    if (map.keywords.some(k => txt.includes(k))) {
      return map.scene;
    }
  }

  // 5. Fallback – convert remaining keywords into generic cinematic scene
  return `A cinematic scene illustrating ${txt}, clean background, dramatic lighting.`;
};

export function extractContextualVisuals(userPrompt) {
    const lower = userPrompt.toLowerCase();
    let contextualInstructions = "";

    const keywordsMap = {
        "video games": "Include visual elements like game controllers, pixel art icons, or arcade lights.",
        "toys": "Use playful or colorful backgrounds, bold cartoon-style icons.",
        "cards": "Use playful or colorful backgrounds, bold cartoon-style icons, or stacks of collectible cards.",
        "apps": "Show a futuristic interface, glowing dashboards, or stylized app icons with glass reflections.",
        "ai tools": "Show a futuristic interface, glowing dashboards, or stylized app icons with glass reflections.",
        "google": "Use bold Google colors (red, yellow, green, blue) and round, friendly tech icons in the Material Design style.",
        "gaming": "Include a gamepad, neon lights, or console silhouettes in the background.",
        "lego": "Incorporate a background of colorful LEGO bricks or a minifigure character.",
        "marvel": "Use a comic-book style background with halftone dots and action lines, possibly with the Marvel logo subtly integrated.",
        "adobe": "Feature a clean, creative studio background with subtle hints of Adobe's software UIs or logos.",
        "steam": "Show a background of a digital game library or the Steam client interface, with dark, techy colors.",
        "nostalgia": "Use a retro or vintage aesthetic, with elements like old CRT screens, 8-bit graphics, or aged paper textures.",
        "cringe": "Use chaotic, vibrant, and slightly jarring visuals. Think meme-like elements, distorted colors, or awkward stock photos.",
        "productivity": "Feature a clean, organized desk setup, calendar icons, or abstract representations of focus and progress.",
        "retro cameras": "Showcase vintage camera bodies, film rolls, and a warm, analog color tone.",
        "trading cards": "Visualize collectible cards with dramatic, high-contrast lighting, possibly fanned out on a surface or with a holographic foil effect.",
        "ai chip": "Depict a glowing, futuristic microprocessor with intricate, illuminated circuits on a dark, high-tech background."
    };

    for (const keyword in keywordsMap) {
        if (lower.includes(keyword)) {
            contextualInstructions += `\n- Contextual Visuals for '${keyword}': ${keywordsMap[keyword]}`;
        }
    }

    if (contextualInstructions) {
        return `\n\nContext-Aware Visual Instructions:${contextualInstructions}\n`;
    }

    return "";
}

// === GAME-CONTEXTUAL ICON & OBJECT GENERATION SYSTEM ===

/**
 * Detects if the prompt is about video games and returns the specific game(s)
 * @param {string} userPrompt - The user's prompt
 * @returns {Object} - Detection result with games and confidence
 */
export function detectGameContext(userPrompt) {
    const lower = userPrompt.toLowerCase();
    
    // Comprehensive game detection with aliases
    const gameDatabase = {
        // Battle Royale Games
        'fortnite': {
            aliases: ['fortnite', 'fort nite', 'fortnight'],
            category: 'battle-royale',
            artStyle: 'cartoon-vibrant',
            confidence: 0.95
        },
        'apex-legends': {
            aliases: ['apex legends', 'apex', 'apex leg'],
            category: 'battle-royale',
            artStyle: 'sci-fi-realistic',
            confidence: 0.9
        },
        'pubg': {
            aliases: ['pubg', 'playerunknown', 'battlegrounds', 'pub g'],
            category: 'battle-royale',
            artStyle: 'military-realistic',
            confidence: 0.85
        },
        'warzone': {
            aliases: ['warzone', 'war zone', 'cod warzone'],
            category: 'battle-royale',
            artStyle: 'military-realistic',
            confidence: 0.9
        },
        
        // FPS Games
        'call-of-duty': {
            aliases: ['call of duty', 'cod', 'modern warfare', 'black ops'],
            category: 'fps',
            artStyle: 'military-realistic',
            confidence: 0.9
        },
        'valorant': {
            aliases: ['valorant', 'val', 'valor ant'],
            category: 'fps',
            artStyle: 'stylized-neon',
            confidence: 0.95
        },
        'cs2': {
            aliases: ['cs2', 'counter strike', 'cs:go', 'csgo', 'counter-strike'],
            category: 'fps',
            artStyle: 'realistic',
            confidence: 0.9
        },
        'overwatch': {
            aliases: ['overwatch', 'overwatch 2', 'ow', 'ow2'],
            category: 'fps',
            artStyle: 'cartoon-stylized',
            confidence: 0.9
        },
        'halo': {
            aliases: ['halo', 'master chief', 'halo infinite'],
            category: 'fps',
            artStyle: 'sci-fi-realistic',
            confidence: 0.85
        },
        'doom': {
            aliases: ['doom', 'doom eternal', 'doom slayer'],
            category: 'fps',
            artStyle: 'dark-realistic',
            confidence: 0.8
        },
        
        // MOBA/Strategy
        'league-of-legends': {
            aliases: ['league of legends', 'lol', 'league', 'riot games'],
            category: 'moba',
            artStyle: 'fantasy-stylized',
            confidence: 0.9
        },
        'dota2': {
            aliases: ['dota 2', 'dota', 'dota2'],
            category: 'moba',
            artStyle: 'fantasy-realistic',
            confidence: 0.85
        },
        'starcraft': {
            aliases: ['starcraft', 'starcraft 2', 'sc2'],
            category: 'rts',
            artStyle: 'sci-fi-realistic',
            confidence: 0.8
        },
        'age-of-empires': {
            aliases: ['age of empires', 'aoe', 'aoe2', 'aoe4'],
            category: 'rts',
            artStyle: 'historical-realistic',
            confidence: 0.75
        },
        
        // Sandbox/Creative
        'minecraft': {
            aliases: ['minecraft', 'mine craft', 'mc'],
            category: 'sandbox',
            artStyle: 'pixelated-blocky',
            confidence: 0.95
        },
        'roblox': {
            aliases: ['roblox', 'robux', 'rblx'],
            category: 'sandbox',
            artStyle: 'blocky-simple',
            confidence: 0.9
        },
        'terraria': {
            aliases: ['terraria', 'terraria game'],
            category: 'sandbox',
            artStyle: 'pixelated-2d',
            confidence: 0.85
        },
        
        // RPG/Adventure
        'elden-ring': {
            aliases: ['elden ring', 'eldenring', 'from software'],
            category: 'rpg',
            artStyle: 'dark-fantasy',
            confidence: 0.9
        },
        'genshin-impact': {
            aliases: ['genshin impact', 'genshin', 'mihoyo'],
            category: 'rpg',
            artStyle: 'anime-stylized',
            confidence: 0.9
        },
        'world-of-warcraft': {
            aliases: ['world of warcraft', 'wow', 'warcraft'],
            category: 'mmorpg',
            artStyle: 'fantasy-stylized',
            confidence: 0.85
        },
        'skyrim': {
            aliases: ['skyrim', 'elder scrolls', 'tes'],
            category: 'rpg',
            artStyle: 'fantasy-realistic',
            confidence: 0.8
        },
        'witcher': {
            aliases: ['witcher', 'witcher 3', 'geralt'],
            category: 'rpg',
            artStyle: 'dark-fantasy',
            confidence: 0.8
        },
        'dark-souls': {
            aliases: ['dark souls', 'darksouls', 'souls'],
            category: 'rpg',
            artStyle: 'dark-fantasy',
            confidence: 0.85
        },
        'pokemon': {
            aliases: ['pokemon', 'pokémon', 'pkmn'],
            category: 'rpg',
            artStyle: 'anime-cartoon',
            confidence: 0.9
        },
        'zelda': {
            aliases: ['zelda', 'legend of zelda', 'breath of the wild', 'tears of the kingdom'],
            category: 'adventure',
            artStyle: 'cartoon-stylized',
            confidence: 0.9
        },
        
        // Sports/Racing
        'fifa': {
            aliases: ['fifa', 'fifa 24', 'ea sports'],
            category: 'sports',
            artStyle: 'realistic-sports',
            confidence: 0.85
        },
        'nba2k': {
            aliases: ['nba 2k', 'nba2k', '2k basketball'],
            category: 'sports',
            artStyle: 'realistic-sports',
            confidence: 0.8
        },
        'gran-turismo': {
            aliases: ['gran turismo', 'gt', 'gran turismo 7'],
            category: 'racing',
            artStyle: 'realistic',
            confidence: 0.75
        },
        'forza': {
            aliases: ['forza', 'forza horizon', 'forza motorsport'],
            category: 'racing',
            artStyle: 'realistic',
            confidence: 0.8
        },
        
        // Mobile Games
        'clash-royale': {
            aliases: ['clash royale', 'clash', 'supercell'],
            category: 'mobile-strategy',
            artStyle: 'cartoon-colorful',
            confidence: 0.85
        },
        'brawl-stars': {
            aliases: ['brawl stars', 'brawl', 'brawlstars'],
            category: 'mobile-action',
            artStyle: 'cartoon-vibrant',
            confidence: 0.85
        },
        'clash-of-clans': {
            aliases: ['clash of clans', 'coc', 'clash clans'],
            category: 'mobile-strategy',
            artStyle: 'cartoon-colorful',
            confidence: 0.8
        },
        'candy-crush': {
            aliases: ['candy crush', 'candy crush saga'],
            category: 'mobile-puzzle',
            artStyle: 'colorful-candy',
            confidence: 0.8
        },
        'pokemon-go': {
            aliases: ['pokemon go', 'pokémon go', 'pogo'],
            category: 'mobile-ar',
            artStyle: 'anime-realistic',
            confidence: 0.85
        },
        
        // Fighting Games
        'street-fighter': {
            aliases: ['street fighter', 'sf6', 'street fighter 6'],
            category: 'fighting',
            artStyle: 'anime-stylized',
            confidence: 0.8
        },
        'tekken': {
            aliases: ['tekken', 'tekken 8', 'tekken 7'],
            category: 'fighting',
            artStyle: 'realistic-anime',
            confidence: 0.75
        },
        'mortal-kombat': {
            aliases: ['mortal kombat', 'mk', 'mk1'],
            category: 'fighting',
            artStyle: 'dark-realistic',
            confidence: 0.8
        },
        
        // Platform/Indie
        'among-us': {
            aliases: ['among us', 'amongus', 'sus'],
            category: 'social-deduction',
            artStyle: 'simple-cartoon',
            confidence: 0.9
        },
        'fall-guys': {
            aliases: ['fall guys', 'fallguys', 'bean guys'],
            category: 'party',
            artStyle: 'colorful-cute',
            confidence: 0.85
        },
        'rocket-league': {
            aliases: ['rocket league', 'rl', 'car soccer'],
            category: 'sports-arcade',
            artStyle: 'stylized-realistic',
            confidence: 0.85
        }
    };
    
    const detectedGames = [];
    
    // Check for each game
    for (const [gameKey, gameData] of Object.entries(gameDatabase)) {
        for (const alias of gameData.aliases) {
            if (lower.includes(alias)) {
                detectedGames.push({
                    key: gameKey,
                    name: alias,
                    category: gameData.category,
                    artStyle: gameData.artStyle,
                    confidence: gameData.confidence
                });
                break; // Only add once per game
            }
        }
    }
    
    return {
        isGaming: detectedGames.length > 0,
        games: detectedGames,
        primaryGame: detectedGames.length > 0 ? detectedGames[0] : null
    };
}

/**
 * Generates game-specific icon and object instructions
 * @param {Object} gameContext - Game detection result
 * @param {string} userPrompt - Original user prompt
 * @returns {string} - Detailed icon generation instructions
 */
export function generateGameSpecificIconInstructions(gameContext, userPrompt) {
    if (!gameContext.isGaming || !gameContext.primaryGame) {
        return "";
    }
    
    const game = gameContext.primaryGame;
    const lower = userPrompt.toLowerCase();
    
    // Game-specific object and icon databases
    const gameIconDatabase = {
        'fortnite': {
            weapons: ['Assault Rifle', 'Shotgun', 'Sniper Rifle', 'Pickaxe', 'SMG', 'Rocket Launcher'],
            items: ['Shield Potion', 'Med Kit', 'Chug Jug', 'Launch Pad', 'Building Materials', 'Treasure Chest'],
            ui: ['Victory Royale Banner', 'Elimination Counter', 'Storm Circle', 'Map Marker'],
            characters: ['Default Skin', 'Battle Pass Skins', 'Emotes', 'Back Bling'],
            scenarios: {
                '1v1': ['Build Battle Ramps', 'Editing Structures', 'High Ground Advantage'],
                'win': ['Victory Royale Crown', '#1 Badge', 'Celebration Emote'],
                'build': ['Wood/Brick/Metal Materials', 'Building Grid', 'Edit Mode']
            }
        },
        'valorant': {
            weapons: ['Vandal', 'Phantom', 'Operator', 'Sheriff', 'Spectre', 'Knife'],
            items: ['Ability Orbs', 'Spike', 'Armor Plates', 'Credits'],
            ui: ['Agent Select Screen', 'Minimap', 'Ability Icons', 'Rank Badges'],
            characters: ['Jett', 'Reyna', 'Sage', 'Phoenix', 'Sova', 'Omen'],
            scenarios: {
                'ace': ['5 Kill Badge', 'HEADSHOT Text', 'Multi-kill Icon'],
                'rank': ['Rank Badges', 'RR Points', 'Leaderboard'],
                'clutch': ['Last Alive Indicator', 'Clutch Badge', 'Timer']
            }
        },
        'minecraft': {
            weapons: ['Diamond Sword', 'Bow', 'Crossbow', 'Trident', 'Axe'],
            items: ['Diamond', 'Redstone', 'TNT', 'Crafting Table', 'Furnace', 'Ender Pearl'],
            ui: ['Inventory Grid', 'Health Hearts', 'Hunger Bar', 'XP Bar'],
            characters: ['Steve', 'Alex', 'Creeper', 'Enderman', 'Villager'],
            scenarios: {
                'build': ['Blocks', 'Blueprint Grid', 'Creative Mode'],
                'redstone': ['Redstone Dust', 'Pistons', 'Repeaters', 'Logic Gates'],
                'survival': ['Mob Spawner', 'Cave Entrance', 'Night Sky']
            }
        },
        'apex-legends': {
            weapons: ['R-301', 'Wingman', 'Kraber', 'Mastiff', 'Devotion'],
            items: ['Shield Battery', 'Phoenix Kit', 'Respawn Beacon', 'Loot Boxes'],
            ui: ['Squad Status', 'Ring Timer', 'Kill Counter', 'Damage Numbers'],
            characters: ['Wraith', 'Octane', 'Lifeline', 'Pathfinder', 'Bloodhound'],
            scenarios: {
                'win': ['Champion Banner', 'Squad Elimination', '#1 Squad'],
                'third party': ['Multiple Squad Icons', 'Chaos Indicators'],
                'ranked': ['RP Points', 'Tier Badges', 'Ranked Emblems']
            }
        },
        'call-of-duty': {
            weapons: ['AK-47', 'M4A1', 'Sniper Rifle', 'Pistol', 'Grenade', 'Knife'],
            items: ['Killstreaks', 'UAV', 'Care Package', 'Dog Tags'],
            ui: ['Kill Feed', 'Minimap', 'Score Display', 'Prestige Emblems'],
            characters: ['Operator Skins', 'Military Gear', 'Tactical Equipment'],
            scenarios: {
                'nuke': ['Nuclear Symbol', 'Tactical Nuke', 'Countdown Timer'],
                'killstreak': ['Kill Chain', 'Helicopter', 'Airstrike'],
                'multiplayer': ['Team Emblems', 'Match Score', 'Leaderboard']
            }
        },
        'league-of-legends': {
            weapons: ['Sword', 'Staff', 'Bow', 'Magical Items'],
            items: ['Health Potion', 'Wards', 'Gold Coins', 'Runes'],
            ui: ['Champion Icons', 'Ability Cooldowns', 'Map Objectives', 'Score Display'],
            characters: ['Champion Portraits', 'Skins', 'Emotes'],
            scenarios: {
                'pentakill': ['Pentakill Banner', '5 Kill Icon', 'Legendary Text'],
                'ranked': ['League Emblems', 'LP Points', 'Tier Icons'],
                'teamfight': ['Multiple Champions', 'Ability Effects', 'Ultimate Icons']
            }
        },
        'cs2': {
            weapons: ['AK-47', 'M4A4', 'AWP', 'Desert Eagle', 'Knife', 'Grenades'],
            items: ['Bomb/Defuse Kit', 'Armor', 'Money Symbol', 'Weapon Skins'],
            ui: ['Round Timer', 'Score Display', 'Radar', 'Buy Menu'],
            characters: ['CT/T Models', 'Player Skins', 'Gloves'],
            scenarios: {
                'ace': ['5K Badge', 'Ace Text', 'Kill Feed'],
                'clutch': ['1vX Indicator', 'Clutch Badge', 'Bomb Timer'],
                'headshot': ['Headshot Icon', 'Precision Badge', 'Aim Crosshair']
            }
        }
    };
    
    const gameData = gameIconDatabase[game.key];
    if (!gameData) {
        return `\n=== GENERIC GAMING ICONS ===\nGenerate authentic gaming-related icons appropriate for ${game.name} in ${game.artStyle} art style.\n`;
    }
    
    let instructions = `\n=== GAME-CONTEXTUAL ICON & OBJECT GENERATION: ${game.name.toUpperCase()} ===\n`;
    instructions += `Art Style: ${game.artStyle}\nCategory: ${game.category}\n\n`;
    
    // Analyze prompt for specific scenarios
    let detectedScenario = null;
    for (const [scenario, items] of Object.entries(gameData.scenarios || {})) {
        if (lower.includes(scenario)) {
            detectedScenario = { scenario, items };
            break;
        }
    }
    
    instructions += `STRICT REQUIREMENTS:\n`;
    instructions += `- Generate ONLY objects, icons, and UI elements that exist in ${game.name}\n`;
    instructions += `- Use the official ${game.artStyle} art style and color palette\n`;
    instructions += `- NO generic emojis, random 3D shapes, or unrelated icons\n`;
    instructions += `- Every element must be instantly recognizable to ${game.name} players\n\n`;
    
    if (detectedScenario) {
        instructions += `SCENARIO-SPECIFIC OBJECTS (${detectedScenario.scenario}):\n`;
        detectedScenario.items.forEach(item => {
            instructions += `- ${item}\n`;
        });
        instructions += `\n`;
    }
    
    instructions += `AUTHENTIC GAME ELEMENTS TO INCLUDE:\n`;
    instructions += `Core Weapons: ${gameData.weapons.slice(0, 3).join(', ')}\n`;
    instructions += `Essential Items: ${gameData.items.slice(0, 3).join(', ')}\n`;
    instructions += `UI Elements: ${gameData.ui.slice(0, 2).join(', ')}\n`;
    instructions += `Characters: ${gameData.characters.slice(0, 2).join(', ')}\n\n`;
    
    instructions += `VISUAL QUALITY STANDARDS:\n`;
    instructions += `- Render in authentic ${game.name} visual style\n`;
    instructions += `- Use official color schemes and materials\n`;
    instructions += `- Apply appropriate lighting and effects from the game\n`;
    instructions += `- Maintain game-accurate proportions and details\n`;
    instructions += `- Position elements to support the thumbnail narrative\n\n`;
    
    instructions += `COMPOSITION GUIDELINES:\n`;
    instructions += `- Arrange objects in dynamic, action-oriented poses\n`;
    instructions += `- Use depth and layering for visual hierarchy\n`;
    instructions += `- Balance iconic weapons/items with UI elements\n`;
    instructions += `- Ensure all elements are clearly visible and recognizable\n`;
    instructions += `- Support the overall thumbnail story and click appeal\n\n`;
    
    return instructions;
}

/**
 * Enhanced contextual visuals that includes game-specific enhancements
 * @param {string} userPrompt - The user's prompt
 * @returns {string} - Enhanced contextual visual instructions
 */
export function extractEnhancedContextualVisuals(userPrompt) {
    const gameContext = detectGameContext(userPrompt);
    let contextualInstructions = extractContextualVisuals(userPrompt);
    
    // If gaming content is detected, add game-specific enhancements
    if (gameContext.isGaming) {
        const gameInstructions = generateGameSpecificIconInstructions(gameContext, userPrompt);
        contextualInstructions += gameInstructions;
    }
    
    return contextualInstructions;
} 