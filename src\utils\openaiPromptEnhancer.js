// Enhanced utility to call OpenAI Chat Completion for smart prompt enhancement
// Now integrates contextual-engagement-prompt-variation-generator AND uniqueness tracking
import { OPENAI_API_KEY } from '/config.js';
import { enhancePromptWithEngagementVariations } from './contextualEngagementVariationGenerator.js';
import { promptUniquenessTracker } from './promptUniquenessTracker.js'; // NEW: Import uniqueness tracker

// Named export so callers can import { getSmartTitle }
export const getSmartTitle = async (rawPrompt) => {
    if (!rawPrompt) return '';

    // Safety: trim and limit to 200 characters to keep request concise
    const userIdea = rawPrompt.trim().slice(0, 200);

    // For Netlify deployment, check if we're in a browser environment
    // and use a different approach for API calls
    const isNetlify = window.location.hostname.includes('netlify.app');
    
    if (isNetlify) {
        console.log('Running on Netlify - using client-side only approach');
        // Return a simulated enhanced title for Netlify deployment
        // This is a fallback since we can't use the API key directly on Netlify
        return `Enhanced: ${userIdea.slice(0, 50)}${userIdea.length > 50 ? '...' : ''}`;
    }

    const payload = {
        model: 'gpt-3.5-turbo-1106', // cost-effective model for short copywriting
        messages: [
            {
                role: 'system',
                content: [
                    'You are an expert YouTube copywriter. ',
                    'Rewrite the user\'s rough video idea into a SINGLE professional, catchy, YouTube-ready title. ',
                    'Key rules:\n',
                    '• Keep it under 90 characters.\n',
                    '• Use Title Case capitalization.\n',
                    '• Do NOT wrap the title in quotes or markdown.\n',
                    '• The title must stay faithful to the user\'s original topic and intent.\n',
                    '• Return ONLY the title text.'
                ].join('')
            },
            {
                role: 'user',
                content: userIdea
            }
        ],
        temperature: 0.7,
        max_tokens: 20,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
    };

    try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_API_KEY}`
            },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`OpenAI API error: ${response.status} ${errorText}`);
        }

        const data = await response.json();
        const rawTitle = data.choices?.[0]?.message?.content || '';

        // Clean up any stray quotes/newlines
        return rawTitle.trim().replace(/^"|"$/g, '').replace(/^'|'$/g, '');
    } catch (err) {
        console.error('Failed to generate smart title:', err);
        // Return a fallback title based on the user's input
        return `Enhanced: ${userIdea.slice(0, 50)}${userIdea.length > 50 ? '...' : ''}`;
    }
};

/**
 * Enhanced prompt improvement using uniqueness tracking for diverse variations
 * Ensures each enhancement attempt produces significantly different results
 * Follows comprehensive-thumbnail-prompt-enhancement rules with variety enforcement
 */
export const getEnhancedPromptWithEngagement = async (rawPrompt, category = 'general') => {
    if (!rawPrompt) return { enhancedPrompt: '', explanation: '', strategy: 'general' };

    // Safety: trim and validate input
    const userIdea = rawPrompt.trim();
    if (userIdea.length < 5) {
        return { 
            enhancedPrompt: userIdea, 
            explanation: 'Prompt too short for enhancement', 
            strategy: 'general' 
        };
    }

    // UNIQUENESS TRACKING: Generate diverse variation using tracked strategies
    console.log('[AI Enhancement] Using uniqueness tracker for diverse variations');
    
    // Check current enhancement stats for debugging
    const stats = promptUniquenessTracker.getEnhancementStats(userIdea);
    console.log(`[AI Enhancement] Stats for "${userIdea}":`, stats);

    // For Netlify deployment, use uniqueness tracker with fallback enhancement
    const isNetlify = window.location.hostname.includes('netlify.app');
    
    if (isNetlify || !OPENAI_API_KEY) {
        console.log('Using uniqueness tracker with fallback enhancement (Netlify deployment or no API key)');
        // Use uniqueness tracker even for fallback to ensure variety
        const uniqueEnhancement = promptUniquenessTracker.generateUniqueVariation(userIdea, category);
        return uniqueEnhancement;
    }

    try {
        // APPROACH 1: Try AI enhancement with uniqueness tracking
        // Generate unique variation strategy first
        const uniqueVariation = promptUniquenessTracker.generateUniqueVariation(userIdea, category);
        
        // If we have a unique strategy, use it to guide AI enhancement
        if (uniqueVariation.variation) {
            console.log(`[AI Enhancement] Applying unique strategy: ${uniqueVariation.variation.name}`);
            
            // Create a guided prompt for the AI that incorporates the unique variation strategy
            const guidedPrompt = `${userIdea} [STYLE GUIDANCE: Use ${uniqueVariation.variation.shotType}, ${uniqueVariation.variation.colorGrading}, ${uniqueVariation.variation.positioning}]`;
            
            // Try enhanced AI generation with the guided prompt
            const enhancementResult = await enhancePromptWithEngagementVariations(guidedPrompt, category);
            
            if (enhancementResult.enhancedPrompt && enhancementResult.enhancedPrompt !== userIdea) {
                // Combine AI result with uniqueness strategy info
                return {
                    enhancedPrompt: enhancementResult.enhancedPrompt,
                    explanation: `${uniqueVariation.explanation} + AI enhancement`,
                    strategy: uniqueVariation.strategy,
                    variation: uniqueVariation.variation,
                    isUnique: true
                };
            }
        }
        
        // APPROACH 2: Fallback to uniqueness tracker if AI fails
        console.log('AI enhancement failed, using uniqueness tracker fallback');
        return uniqueVariation;
        
    } catch (error) {
        console.error('Error in getEnhancedPromptWithEngagement:', error);
        // Final fallback: Use uniqueness tracker
        console.log('Using uniqueness tracker as final fallback');
        return promptUniquenessTracker.generateUniqueVariation(userIdea, category);
    }
};

/**
 * Enhanced fallback prompt improvement with visual focus only
 * Excludes text overlay content to maintain separation
 */
const createFallbackEnhancement = (userIdea, category) => {
    // Remove any existing text overlay references from the prompt
    let cleanPrompt = userIdea;
    
    // Strip text overlay references using the sanitization system
    const textOverlayPatterns = [
        /\b(text overlay|title|headline|bold text|large text)\b:?\s*['"]?[^'"]*['"]?/gi,
        /\bwith text\b\s*['"]?[^'"]*['"]?/gi,
        /\bsaying\b\s*['"]?[^'"]*['"]?/gi,
        /\bdisplaying\b\s*['"]?[^'"]*['"]?/gi
    ];
    
    textOverlayPatterns.forEach(pattern => {
        cleanPrompt = cleanPrompt.replace(pattern, '');
    });
    
    // Clean up spacing
    cleanPrompt = cleanPrompt.replace(/\s+/g, ' ').trim();
    
    // Enhanced fallback strategies focusing only on visual elements
    const enhancementStrategies = {
        gaming: {
            visual: "Dynamic gaming setup with RGB lighting, multiple monitors, and competitive atmosphere with cinematic depth of field and vibrant color grading",
            composition: "Rule of thirds composition with dramatic lighting and high-contrast neon effects"
        },
        business: {
            visual: "Professional workspace with modern equipment, clean lines, and sophisticated lighting with warm color palette and minimal aesthetic",
            composition: "Centered composition with premium materials and elegant shadows"
        },
        tech: {
            visual: "Sleek technology showcase with futuristic elements, holographic displays, and cool blue-teal color grading with rim lighting",
            composition: "Asymmetric layout with depth layering and modern visual hierarchy"
        },
        food: {
            visual: "Appetizing food presentation with natural lighting, vibrant colors, and steam effects with warm orange-yellow color grading",
            composition: "Close-up composition with shallow depth of field and natural textures"
        },
        fitness: {
            visual: "Dynamic fitness scene with energy and movement, dramatic lighting, and motivational atmosphere with high-contrast color grading",
            composition: "Action-focused composition with motion blur and powerful visual impact"
        },
        general: {
            visual: "Cinematic scene with professional lighting, balanced composition, and enhanced visual appeal with rich color grading",
            composition: "Balanced layout with depth and visual interest"
        }
    };
    
    const strategy = enhancementStrategies[category] || enhancementStrategies.general;
    
    // Build enhanced prompt focusing ONLY on visual elements
    const enhancedPrompt = `${cleanPrompt} rendered as a cinematic YouTube thumbnail with ${strategy.visual} using ${strategy.composition} and professional visual effects`;
    
    return {
        enhancedPrompt,
        explanation: `Enhanced with ${category}-specific visual elements and cinematic effects`,
        strategy: 'visual-focused-fallback'
    };
};

/**
 * Detect video topic from prompt for contextual enhancement
 */
const detectVideoTopic = (prompt) => {
    const topicKeywords = {
        'gaming': ['game', 'gaming', 'player', 'battle', 'fight', 'win', 'level', 'score', 'esports', 'gamer', 'console', 'pc', 'mobile', 'strategy', 'fps', 'mmo', 'rpg', 'pvp', 'clan', 'guild', 'tournament', 'champion', 'boss', 'quest', 'achievement', 'stream', 'twitch', 'youtube gaming'],
        'business': ['business', 'money', 'profit', 'success', 'growth', 'revenue', 'startup', 'entrepreneur', 'investment', 'finance', 'marketing', 'sales', 'corporate', 'company', 'economy', 'trade', 'commerce', 'market', 'stock', 'cryptocurrency', 'bitcoin', 'wealth', 'income', 'passive income', 'hustle', 'side business'],
        'tech': ['tech', 'app', 'software', 'code', 'ai', 'innovation', 'device', 'programming', 'developer', 'technology', 'digital', 'computer', 'internet', 'web', 'mobile', 'android', 'ios', 'machine learning', 'blockchain', 'cybersecurity', 'cloud', 'data', 'algorithm', 'automation', 'robotics'],
        'food': ['food', 'recipe', 'cooking', 'nutrition', 'meal', 'ingredient', 'chef', 'kitchen', 'baking', 'dessert', 'healthy', 'diet', 'vegan', 'vegetarian', 'restaurant', 'cuisine', 'flavor', 'taste', 'delicious', 'foodie', 'gastronomy', 'culinary', 'organic', 'fresh', 'homemade'],
        'fitness': ['fitness', 'workout', 'exercise', 'health', 'training', 'muscle', 'gym', 'bodybuilding', 'cardio', 'strength', 'weight loss', 'nutrition', 'protein', 'wellness', 'yoga', 'pilates', 'crossfit', 'running', 'marathon', 'athletic', 'sports', 'transformation', 'healthy lifestyle', 'diet', 'supplements'],
        'education': ['learn', 'tutorial', 'guide', 'tips', 'how to', 'study', 'education', 'teaching', 'school', 'university', 'course', 'lesson', 'knowledge', 'skill', 'training', 'academic', 'research', 'student', 'professor', 'online learning', 'certification', 'degree', 'exam', 'test', 'homework']
    };
    
    for (const [topic, keywords] of Object.entries(topicKeywords)) {
        if (keywords.some(keyword => prompt.includes(keyword))) {
            return topic;
        }
    }
    return 'general';
};

/**
 * Get contextual color grading based on video topic
 */
const getContextualColorGrading = (topic) => {
    const lutMappings = {
        'gaming': [
            'Apply gaming-glow LUT with neon lighting effects, purple and electric blue tones with digital bloom',
            'Use cyberpunk-neon LUT with magenta-cyan color grade and holographic lighting effects',
            'Apply retro-arcade LUT with pixel-perfect contrast and nostalgic warm glow',
            'Use dark-gaming LUT with moody shadows and bright accent highlights'
        ],
        'business': [
            'Use cinematic-warm LUT with orange-teal color grading, warm shadows and cool highlights',
            'Apply corporate-clean LUT with neutral tones and professional lighting balance',
            'Use success-gold LUT with luxurious warm tones and elegant contrast',
            'Apply modern-corporate LUT with cool blues and sophisticated gray tones'
        ],
        'tech': [
            'Apply cold-drama LUT with blue-gray cinematic filter and desaturated highlights',
            'Use futuristic-blue LUT with electric cyan tones and digital enhancement',
            'Apply minimal-tech LUT with clean whites and subtle blue accents',
            'Use innovation-purple LUT with deep purples and bright white highlights'
        ],
        'food': [
            'Use soft-calm LUT with pastel color grade, muted tones and soft ambient lighting',
            'Apply warm-delicious LUT with appetizing orange-red tones and golden highlights',
            'Use fresh-natural LUT with vibrant greens and natural sunlight warmth',
            'Apply cozy-kitchen LUT with warm yellows and inviting home-style lighting'
        ],
        'fitness': [
            'Apply viral-energy LUT with high-contrast vivid tone, saturated reds and yellows',
            'Use strong-power LUT with bold contrast and dramatic lighting effects',
            'Apply motivational-orange LUT with energetic warm tones and dynamic shadows',
            'Use athletic-blue LUT with cool tones and sharp contrast for intensity'
        ],
        'education': [
            'Use sunset-mood LUT with soft peach and orange gradient overlay, warm lowlight ambience',
            'Apply knowledge-warm LUT with inviting yellows and comfortable learning atmosphere',
            'Use academic-clean LUT with balanced neutrals and clear, focused lighting',
            'Apply inspiring-bright LUT with uplifting tones and motivational color grade'
        ],
        'general': [
            'Apply cinematic-warm LUT with balanced color grading for professional visual appeal',
            'Use versatile-teal LUT with modern orange-teal color scheme and balanced contrast',
            'Apply classic-film LUT with timeless color grading and cinematic depth',
            'Use dynamic-contrast LUT with bold highlights and rich shadow detail'
        ]
    };
    
    return lutMappings[topic] || lutMappings.general;
};