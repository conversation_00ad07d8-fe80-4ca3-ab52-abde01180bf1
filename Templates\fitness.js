export default [
  // Fitness Templates
  {
    id: "fitness-abs-workout",
    name: "Intense Abs Workout",
    description: "For showcasing challenging abdominal exercises and routines.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person with visible determination performing an intense ab exercise like crunches or planks, showcasing core strength. Pose: Mid-exercise, focused. Emotion: Determined, focused. Text Overlay: 'ABS ON FIRE!' in bold, uppercase, high-contrast font (e.g., yellow on dark) with a slight glow, positioned top-right with 40px margin. Visual Focus: Clear shot of the person's core engagement. Background: Dynamic, energetic gym setting or abstract motion blur with vibrant lighting. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Determined', textOverlay: true, overlayText: "ABS ON FIRE!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-abs-workout.webp" }
  },
  {
    id: "fitness-full-body",
    name: "Ultimate Full Body Blast",
    description: "For full-body workout routines and fitness challenges.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person in a dynamic full-body exercise pose (e.g., lunge, squat, burpee). Pose: Action shot, mid-movement. Emotion: Energetic, powerful. Text Overlay: 'FULL BODY BLAST!' in bold, uppercase, high-contrast font, center-right with 40px margin. Visual Focus: Emphasis on the entire body engagement. Background: Energetic, vibrant background with motion lines or abstract patterns, strong color contrast. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Energetic', textOverlay: true, overlayText: "FULL BODY\nBLAST!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-full-body.webp" }
  },
  {
    id: "fitness-home-workout",
    name: "No-Equipment Home Fitness",
    description: "For accessible home-based workout guides.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person exercising effectively in a comfortable living room setting without visible gym equipment. Pose: Performing a bodyweight exercise. Emotion: Positive, motivated. Text Overlay: 'HOME WORKOUT HERO' in bold, uppercase, friendly high-contrast font, top-right with 40px margin. Visual Focus: Relatable home environment. Background: Bright and inviting home interior, possibly with soft, natural lighting. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Happy', textOverlay: true, overlayText: "HOME WORKOUT\nHERO" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-home-workout.webp" }
  },
  {
    id: "fitness-gym-motivation",
    name: "Beast Mode: Gym Motivation",
    description: "For motivational content targeting gym enthusiasts.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A muscular person looking strong and focused in a modern gym. Pose: Lifting weights or an empowering stance. Emotion: Intense, confident. Text Overlay: 'BEAST MODE ON' in aggressive, bold, uppercase font (e.g., metallic red with black outline), center-right with 40px margin. Visual Focus: Subject's physique and intensity. Background: Gritty gym environment with dramatic lighting, possibly with lens flare. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Confident', textOverlay: true, overlayText: "BEAST MODE\nON" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-gym-motivation.webp" }
  },
  {
    id: "fitness-yoga-flexibility",
    name: "Morning Yoga Flow",
    description: "For yoga, stretching, and flexibility routines.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person in a graceful yoga pose (e.g., warrior pose, tree pose). Pose: Serene, balanced. Emotion: Calm, peaceful. Text Overlay: 'YOGA BLISS' in elegant, bold, uppercase font, top-right with 40px margin. Visual Focus: The beauty of the pose and serene atmosphere. Background: Peaceful, natural setting (e.g., beach at sunrise, tranquil garden) or minimalist studio with soft lighting. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Calm', textOverlay: true, overlayText: "YOGA BLISS" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-yoga-flexibility.webp" }
  },
  {
    id: "fitness-cardio-challenge",
    name: "Ultimate Cardio Challenge",
    description: "For high-intensity cardio workouts and endurance tests.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person engaged in a high-energy cardio activity (e.g., running on a treadmill, sprinting outdoors, jump rope). Pose: Dynamic action shot, showing effort. Emotion: Determined, energetic. Text Overlay: 'CARDIO KING/QUEEN' in dynamic, bold, uppercase font, center-right with 40px margin. Visual Focus: Movement and intensity. Background: Motion blur effects, vibrant streaks of light, or an outdoor track/path. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Energetic', textOverlay: true, overlayText: "CARDIO\nCHALLENGE" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-cardio-challenge.webp" }
  },
  {
    id: "fitness-strength-training",
    name: "Lift Heavy, Get Strong",
    description: "For strength training, weightlifting, and muscle-building guides.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person lifting a significant amount of weight (dumbbells, barbell) with proper form. Pose: Mid-lift, showing strength. Emotion: Focused, powerful. Text Overlay: 'LIFT HEAVY' in strong, bold, uppercase font (e.g., blocky, impactful), top-right with 40px margin. Visual Focus: Muscles engaged, weight being lifted. Background: A rugged gym environment or dark, textured background with spotlights. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Powerful', textOverlay: true, overlayText: "LIFT HEAVY" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/fitness-strength-training.webp" }
  },

  // Health Templates
  {
    id: "health-mental-wellness",
    name: "Find Your Inner Calm",
    description: "For mental health, meditation, and mindfulness content.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person meditating peacefully or looking serene and relaxed. Pose: Eyes closed, gentle smile, or calm contemplative look. Emotion: Peaceful, content. Text Overlay: 'MENTAL RESET' in calming, bold, uppercase font, center-right with 40px margin. Visual Focus: Subject's tranquil expression. Background: Soft, peaceful nature scene (e.g., misty forest, calm lake) or an abstract background with soothing colors. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Calm', textOverlay: true, overlayText: "MENTAL RESET" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-mental-wellness.webp" }
  },
  {
    id: "health-healthy-habits",
    name: "7 Days to a Healthier You",
    description: "For promoting healthy lifestyle changes and habits.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A vibrant and positive person surrounded by subtle icons representing healthy habits (e.g., water droplet, apple, sleep moon, dumbbell). Pose: Smiling, looking energetic. Emotion: Happy, vibrant. Text Overlay: 'HEALTHY HABITS KICKSTART' in clean, bold, uppercase font, top-right with 40px margin. Visual Focus: Overall positive and healthy vibe. Background: Bright, abstract background with cheerful colors and soft gradients. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Happy', includeIcons: true, textOverlay: true, overlayText: "HEALTHY HABITS\nKICKSTART" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-healthy-habits.webp" }
  },
  {
    id: "health-sleep-better",
    name: "Unlock Deep Sleep Secrets",
    description: "For tips and advice on improving sleep quality.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person sleeping soundly and peacefully in a comfortable bed, appearing as if they are among the clouds in the sky. Pose: Relaxed sleep. Emotion: Peaceful, restful. Text Overlay: 'SLEEP WELL TONIGHT' in soft, bold, uppercase font, center-right with 40px margin. Visual Focus: The ethereal blend of serene sleep and sky-like surroundings. Background: Dreamy night sky with clouds gently enveloping the scene, moonlit ambiance, or abstract dark blue/purple hues with subtle stardust effects. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Peaceful', textOverlay: true, overlayText: "SLEEP WELL\nTONIGHT" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-sleep-better.webp" }
  },
  {
    id: "health-stress-relief",
    name: "Beat Stress & Anxiety Now",
    description: "For techniques and advice on managing stress and anxiety.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person looking visibly relieved and calm, perhaps taking a deep breath. Pose: Relaxed shoulders, gentle smile. Emotion: Relieved, serene. Text Overlay: 'STRESS FREE ZONE' in reassuring, bold, uppercase font, top-right with 40px margin. Visual Focus: The feeling of relief and calm. Background: Calming abstract imagery, soft light, or a peaceful outdoor scene like a quiet park. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Relieved', textOverlay: true, overlayText: "STRESS FREE\nZONE" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-stress-relief.webp" }
  },
  {
    id: "health-medical-info",
    name: "Doctor Explains: [Condition]",
    description: "For informative content about medical conditions or health topics.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A friendly, approachable doctor figure in professional attire. Pose: Explaining, pointing to an illustrative graphic (if applicable). Emotion: Knowledgeable, trustworthy. Text Overlay: '[CONDITION] EXPLAINED' in clear, bold, uppercase font, center-right with 40px margin. Visual Focus: The doctor and clarity of information. Background: Clean, professional background, perhaps with subtle medical iconography or abstract blue/green tones. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Knowledgeable', textOverlay: true, overlayText: "[TOPIC]\nEXPLAINED" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-medical-info.webp" }
  },
  {
    id: "health-self-care",
    name: "My Relaxing Self-Care Routine",
    description: "For content about self-care practices and relaxation.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person visibly enjoying a self-care activity (e.g., applying a face mask, sipping tea, reading). Pose: Relaxed, content. Emotion: Pampered, happy. Text Overlay: 'SELF-CARE SUNDAY' in soft, elegant, bold, uppercase font, top-right with 40px margin. Visual Focus: The enjoyable self-care moment. Background: Cozy home environment, spa-like setting with soft lighting and pleasing textures. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Happy', textOverlay: true, overlayText: "SELF-CARE\nSUNDAY" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-self-care.webp" }
  },
  {
    id: "health-hydration-reminder",
    name: "Stay Hydrated: Drink More Water!",
    description: "For promoting the benefits of hydration.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A healthy, energetic person smiling and holding a glass of clear water, or a dynamic splash of water. Pose: Active or refreshed. Emotion: Vibrant, healthy. Text Overlay: 'STAY HYDRATED!' in refreshing, bold, uppercase font (e.g., blue with white outline), center-right with 40px margin. Visual Focus: Clear water, refreshed look. Background: Bright, clean background with water ripple effects or abstract blue/cyan patterns. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Refreshed', textOverlay: true, overlayText: "STAY\nHYDRATED!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/health-hydration-reminder.webp" }
  },

  // Nutrition Templates
  {
    id: "nutrition-healthy-recipe",
    name: "Easy & Delicious [Meal] Recipe",
    description: "For showcasing healthy recipes and cooking tutorials.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A beautifully plated healthy meal (e.g., vibrant salad, hearty soup, colorful stir-fry). No person, focus on the food. Text Overlay: 'HEALTHY [MEAL] RECIPE' in appetizing, bold, uppercase font, top-right with 40px margin. Visual Focus: Delicious-looking food, textures, and colors. Background: Bright, clean kitchen counter or rustic wooden table, overhead shot perspective often works well. High sharpness and saturation.",
    settingsToApply: { includePerson: false, textOverlay: true, overlayText: "HEALTHY\n[MEAL] RECIPE" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-healthy-recipe.webp" }
  },
  {
    id: "nutrition-meal-prep",
    name: "Meal Prep Like a Pro This Week",
    description: "For meal preparation guides and tips.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: Neatly organized meal prep containers filled with colorful, healthy food. Optionally, a person happily arranging them. Pose: Organized, efficient. Emotion (if person): Satisfied, proud. Text Overlay: 'MEAL PREP SUNDAY' in organized, bold, uppercase font, center-right with 40px margin. Visual Focus: Abundance and organization of prepared meals. Background: Clean kitchen workspace or a colorful flat lay. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Satisfied', textOverlay: true, overlayText: "MEAL PREP\nLIKE A PRO" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-meal-prep.webp" }
  },
  {
    id: "nutrition-diet-explained",
    name: "Understanding the [Diet Name] Diet",
    description: "For explaining specific diets (Keto, Paleo, Vegan, etc.).",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A conceptual graphic representing the key food groups or principles of the '[Diet Name]' diet. Could include a person looking thoughtful or informed. Pose (if person): Pointing to graphic, or curious. Emotion (if person): Curious, informed. Text Overlay: 'THE [DIET NAME] DIET' in clear, informative, bold, uppercase font, top-right with 40px margin. Visual Focus: Key elements of the diet, easy to understand. Background: Clean, infographic-style background with relevant food icons or color coding. High sharpness and saturation.",
    settingsToApply: { includePerson: false, includeIcons: true, textOverlay: true, overlayText: "THE [DIET NAME]\nDIET" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-diet-explained.webp" }
  },
  {
    id: "nutrition-superfoods",
    name: "Power Up with [Superfood]",
    description: "For highlighting the benefits of specific superfoods.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A vibrant, close-up shot of the featured '[Superfood]' (e.g., blueberries, avocado, spinach) with a subtle glowing effect. Text Overlay: '[SUPERFOOD] BENEFITS!' in energetic, bold, uppercase font, center-right with 40px margin. Visual Focus: The superfood itself, looking fresh and appealing. Background: Abstract background with colors complementing the superfood, possibly with radiating lines or energy effects. High sharpness and saturation.",
    settingsToApply: { includePerson: false, textOverlay: true, overlayText: "[SUPERFOOD]\nBENEFITS!" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-superfoods.webp" }
  },
  {
    id: "nutrition-what-i-eat",
    name: "What I Eat In A Day (Healthy Edition)",
    description: "For 'What I Eat In A Day' vlogs focusing on healthy eating.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A happy and energetic person, possibly with a small collage or display of 2-3 healthy meals they ate. Pose: Smiling, holding a healthy dish, or pointing to meal examples. Emotion: Vibrant, healthy. Text Overlay: 'WHAT I EAT (HEALTHY!)' in friendly, bold, uppercase font, top-right with 40px margin. Visual Focus: The person's energy and appealing healthy food. Background: Bright, clean kitchen or home setting, or a colorful graphic background. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Vibrant', textOverlay: true, overlayText: "WHAT I EAT\n(HEALTHY!)" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-what-i-eat.webp" }
  },
  {
    id: "nutrition-weight-loss-tips",
    name: "My Top 5 Weight Loss Secrets",
    description: "For sharing weight loss tips and transformation stories.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A confident, smiling person on the left. Optionally, a subtle before/after silhouette or graphic element indicating transformation. Pose: Empowered, happy. Emotion: Confident, inspiring. Text Overlay: 'WEIGHT LOSS JOURNEY' in motivational, bold, uppercase font, right-aligned with 40px margin. Visual Focus: The person's positive transformation or confidence. Background: Bright, uplifting background, perhaps with an arrow or graph indicating progress. High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Confident', textOverlay: true, overlayText: "WEIGHT LOSS\nJOURNEY" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-weight-loss-tips.webp" }
  },
  {
    id: "nutrition-supplement-review",
    name: "[Supplement Name] Review: Worth It?",
    description: "For reviewing nutritional supplements.",
    promptBase: "Create a cinematic YouTube thumbnail, 1280x720. Subject: A person looking thoughtful or analytical, holding or pointing to the '[Supplement Name]' product. Pose: Inquisitive, examining. Emotion: Curious, skeptical, or convinced. Text Overlay: '[SUPPLEMENT] REVIEW: HONEST TRUTH!' in bold, attention-grabbing, uppercase font, top-right with 40px margin. Visual Focus: The product and the reviewer's expression. Background: Clean, modern background, possibly with a spotlight on the product or a split background (pro/con). High sharpness and saturation.",
    settingsToApply: { includePerson: true, selectedExpression: 'Thoughtful', textOverlay: true, overlayText: "[SUPPLEMENT]\nREVIEW" },
    templateImagePlaceholder: { backgroundImage: "/background-templates/Fitness/nutrition-supplement-review.webp" }
  }
]; 