/* ================= TOAST ANIMATION STYLES ================= */

/* Liquid Glass Toast Animations */
@keyframes liquidSlideInRight {
    0% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(15deg);
        filter: blur(4px);
    }
    60% {
        opacity: 0.8;
        transform: translateX(0) scale(1.02) rotateY(0deg);
        filter: blur(1px);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
}

@keyframes liquidSlideOutRight {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
        filter: blur(0px);
    }
    40% {
        opacity: 0.8;
        transform: translateX(0) scale(0.98) rotateY(5deg);
        filter: blur(1px);
    }
    100% {
        opacity: 0;
        transform: translateX(120%) scale(0.9) rotateY(-15deg);
        filter: blur(4px);
    }
}

/* Toast Container Styles */
.toast-container {
    pointer-events: none;
    z-index: 10002;
}

.toast-item {
    pointer-events: auto;
    perspective: 1000px;
}

/* Liquid Glass Effect for Toast */
.liquid-glass-toast-inverted {
    backdrop-filter: blur(20px) saturate(180%) brightness(1.1) contrast(1.05);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(1.1) contrast(1.05);
    position: relative;
    overflow: hidden;
}

/* Toast Icon Container */
.toast-icon-container {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Toast Close Button */
.toast-close-button {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    transition: all 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.toast-close-button:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.2) !important;
}

.toast-close-button:active {
    transform: scale(0.95);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .toast-item,
    .toast-icon-container,
    .toast-close-button {
        transition: none !important;
        animation: none !important;
    }
    
    @keyframes liquidSlideInRight {
        0% { opacity: 0; transform: translateX(100%); }
        100% { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes liquidSlideOutRight {
        0% { opacity: 1; transform: translateX(0); }
        100% { opacity: 0; transform: translateX(100%); }
    }
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .toast-container {
        top: 1rem !important;
        right: 1rem !important;
        left: 1rem !important;
        width: calc(100% - 2rem) !important;
    }
    
    .liquid-glass-toast-inverted {
        min-width: auto !important;
        max-width: none !important;
        width: 100% !important;
        padding: 1rem !important;
    }
    
    .toast-icon-container {
        width: 36px !important;
        height: 36px !important;
    }
    
    .toast-icon-container .iconify {
        font-size: 18px !important;
    }
    
    .toast-close-button {
        width: 24px !important;
        height: 24px !important;
    }
    
    .toast-close-button .iconify {
        font-size: 14px !important;
    }
}

/* Tablet Adjustments */
@media (max-width: 1024px) and (min-width: 769px) {
    .toast-container {
        top: 1.25rem !important;
        right: 1.25rem !important;
    }
    
    .liquid-glass-toast-inverted {
        min-width: 360px !important;
        max-width: 440px !important;
    }
}

/* High DPI Screen Optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .liquid-glass-toast-inverted {
        border-width: 0.5px;
    }
} 