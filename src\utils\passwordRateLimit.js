// Password Change Rate Limiting Utility
// Implements NIST-compliant password change restrictions with optimal UX

export const PASSWORD_CHANGE_CONFIG = {
    // Rate limiting rules
    MAX_CHANGES_PER_MONTH: 3,
    MIN_INTERVAL_HOURS: 1,
    ROLLING_WINDOW_DAYS: 30,
    
    // Emergency override settings
    EMERGENCY_COOLDOWN_HOURS: 24,
    SECURITY_INCIDENT_BYPASS: true,
    
    // Progressive penalties
    PENALTY_MULTIPLIER: 2, // Doubles wait time for violations
    MAX_PENALTY_HOURS: 72, // Maximum penalty duration
    
    // Grace periods
    FIRST_CHANGE_GRACE: true, // No restrictions on first password change
    NEW_USER_GRACE_DAYS: 7, // New users get 7 days without restrictions
};

export class PasswordRateLimiter {
    constructor(supabaseClient) {
        this.supabase = supabaseClient;
    }

    /**
     * Check if user can change password based on rate limiting rules
     * @param {string} userId - User ID
     * @returns {Promise<Object>} - { canChange, reason, nextAllowed, changesRemaining, timeUntilNext }
     */
    async checkPasswordChangeEligibility(userId) {
        try {
            const userMetadata = await this.getUserPasswordMetadata(userId);
            const now = new Date();
            
            // Check if user is in grace period (new user or first change)
            if (this.isInGracePeriod(userMetadata, now)) {
                return {
                    canChange: true,
                    reason: 'grace_period',
                    changesRemaining: PASSWORD_CHANGE_CONFIG.MAX_CHANGES_PER_MONTH,
                    isFirstChange: userMetadata.passwordChanges.length === 0
                };
            }

            // Check minimum interval between changes
            const intervalCheck = this.checkMinimumInterval(userMetadata, now);
            if (!intervalCheck.allowed) {
                return {
                    canChange: false,
                    reason: 'minimum_interval',
                    timeUntilNext: intervalCheck.timeRemaining,
                    nextAllowed: intervalCheck.nextAllowed
                };
            }

            // Check monthly limit
            const monthlyCheck = this.checkMonthlyLimit(userMetadata, now);
            if (!monthlyCheck.allowed) {
                return {
                    canChange: false,
                    reason: 'monthly_limit_exceeded',
                    changesRemaining: 0,
                    nextAllowed: monthlyCheck.nextReset,
                    oldestChangeWillExpire: monthlyCheck.oldestExpiry
                };
            }

            // Check for active penalties
            const penaltyCheck = this.checkActivePenalties(userMetadata, now);
            if (!penaltyCheck.allowed) {
                return {
                    canChange: false,
                    reason: 'penalty_active',
                    timeUntilNext: penaltyCheck.timeRemaining,
                    nextAllowed: penaltyCheck.penaltyEnd,
                    penaltyReason: penaltyCheck.reason
                };
            }

            // All checks passed
            return {
                canChange: true,
                reason: 'allowed',
                changesRemaining: monthlyCheck.changesRemaining,
                lastChange: userMetadata.lastPasswordChange
            };

        } catch (error) {
            console.error('Error checking password change eligibility:', error);
            // Fail open for better UX - allow change but log error
            return {
                canChange: true,
                reason: 'error_fallback',
                error: error.message
            };
        }
    }

    /**
     * Record a successful password change
     * @param {string} userId - User ID
     * @param {Object} changeInfo - Additional change information
     */
    async recordPasswordChange(userId, changeInfo = {}) {
        try {
            const userMetadata = await this.getUserPasswordMetadata(userId);
            const now = new Date();

            const newChange = {
                timestamp: now.toISOString(),
                ip: changeInfo.ip || 'unknown',
                userAgent: changeInfo.userAgent || 'unknown',
                source: changeInfo.source || 'manual',
                ...changeInfo
            };

            // Add new change to history
            const updatedChanges = [...userMetadata.passwordChanges, newChange];
            
            // Keep only changes within the rolling window
            const cutoffDate = new Date(now.getTime() - (PASSWORD_CHANGE_CONFIG.ROLLING_WINDOW_DAYS * 24 * 60 * 60 * 1000));
            const filteredChanges = updatedChanges.filter(change => 
                new Date(change.timestamp) > cutoffDate
            );

            // Update user metadata
            const updatedMetadata = {
                ...userMetadata,
                passwordChanges: filteredChanges,
                lastPasswordChange: now.toISOString(),
                totalPasswordChanges: (userMetadata.totalPasswordChanges || 0) + 1
            };

            await this.updateUserPasswordMetadata(userId, updatedMetadata);

            // Ensure the metadata is properly updated in Supabase user_metadata
            const { data: updatedUser, error: updateError } = await this.supabase.auth.updateUser({
                data: {
                    passwordChangeTracking: updatedMetadata
                }
            });

            if (updateError) {
                console.error("Failed to update password change tracking:", updateError);
                throw updateError;
            }

            console.log("✅ Password change recorded successfully. Changes remaining:", 
                PASSWORD_CHANGE_CONFIG.MAX_CHANGES_PER_MONTH - filteredChanges.length);            return { success: true };

        } catch (error) {
            console.error('Error recording password change:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Apply penalty for rate limit violations
     * @param {string} userId - User ID
     * @param {string} violationType - Type of violation
     */
    async applyPenalty(userId, violationType) {
        try {
            const userMetadata = await this.getUserPasswordMetadata(userId);
            const now = new Date();
            
            let penaltyHours = PASSWORD_CHANGE_CONFIG.MIN_INTERVAL_HOURS * PASSWORD_CHANGE_CONFIG.PENALTY_MULTIPLIER;
            
            // Progressive penalties for repeat violations
            const recentViolations = userMetadata.penalties?.filter(p => 
                new Date(p.timestamp) > new Date(now.getTime() - (24 * 60 * 60 * 1000))
            ) || [];
            
            penaltyHours = Math.min(
                penaltyHours * Math.pow(2, recentViolations.length),
                PASSWORD_CHANGE_CONFIG.MAX_PENALTY_HOURS
            );

            const penalty = {
                timestamp: now.toISOString(),
                type: violationType,
                duration: penaltyHours,
                expiresAt: new Date(now.getTime() + (penaltyHours * 60 * 60 * 1000)).toISOString()
            };

            const updatedMetadata = {
                ...userMetadata,
                penalties: [...(userMetadata.penalties || []), penalty],
                lastViolation: now.toISOString()
            };

            await this.updateUserPasswordMetadata(userId, updatedMetadata);

            // Ensure the metadata is properly updated in Supabase user_metadata
            const { data: updatedUser, error: updateError } = await this.supabase.auth.updateUser({
                data: {
                    passwordChangeTracking: updatedMetadata
                }
            });

            if (updateError) {
                console.error("Failed to update password change tracking:", updateError);
                throw updateError;
            }

            console.log("✅ Password change recorded successfully. Changes remaining:", 
                PASSWORD_CHANGE_CONFIG.MAX_CHANGES_PER_MONTH - filteredChanges.length);            return { success: true, penaltyHours };

        } catch (error) {
            console.error('Error applying penalty:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get user password change metadata from Supabase
     */
    async getUserPasswordMetadata(userId) {
        try {
            const { data: { user }, error } = await this.supabase.auth.getUser();
            if (error) throw error;

            const metadata = user.user_metadata?.passwordChangeTracking || {};
            
            return {
                passwordChanges: metadata.passwordChanges || [],
                lastPasswordChange: metadata.lastPasswordChange || null,
                totalPasswordChanges: metadata.totalPasswordChanges || 0,
                penalties: metadata.penalties || [],
                lastViolation: metadata.lastViolation || null,
                accountCreated: user.created_at,
                firstPasswordChange: metadata.firstPasswordChange || null
            };

        } catch (error) {
            console.error('Error getting user password metadata:', error);
            return {
                passwordChanges: [],
                lastPasswordChange: null,
                totalPasswordChanges: 0,
                penalties: [],
                lastViolation: null,
                accountCreated: new Date().toISOString(),
                firstPasswordChange: null
            };
        }
    }

    /**
     * Update user password change metadata in Supabase
     */
    async updateUserPasswordMetadata(userId, metadata) {
        try {
            const { data, error } = await this.supabase.auth.updateUser({
                data: {
                    passwordChangeTracking: metadata
                }
            });

            if (error) throw error;
            return { success: true, data };

        } catch (error) {
            console.error('Error updating user password metadata:', error);
            throw error;
        }
    }

    /**
     * Check if user is in grace period
     */
    isInGracePeriod(userMetadata, now) {
        // First password change grace
        if (PASSWORD_CHANGE_CONFIG.FIRST_CHANGE_GRACE && userMetadata.passwordChanges.length === 0) {
            return true;
        }

        // New user grace period
        if (PASSWORD_CHANGE_CONFIG.NEW_USER_GRACE_DAYS > 0) {
            const accountAge = now.getTime() - new Date(userMetadata.accountCreated).getTime();
            const gracePeriod = PASSWORD_CHANGE_CONFIG.NEW_USER_GRACE_DAYS * 24 * 60 * 60 * 1000;
            return accountAge < gracePeriod;
        }

        return false;
    }

    /**
     * Check minimum interval between password changes
     */
    checkMinimumInterval(userMetadata, now) {
        if (!userMetadata.lastPasswordChange) {
            return { allowed: true };
        }

        const lastChange = new Date(userMetadata.lastPasswordChange);
        const timeSinceLastChange = now.getTime() - lastChange.getTime();
        const minimumInterval = PASSWORD_CHANGE_CONFIG.MIN_INTERVAL_HOURS * 60 * 60 * 1000;

        if (timeSinceLastChange < minimumInterval) {
            const timeRemaining = minimumInterval - timeSinceLastChange;
            const nextAllowed = new Date(lastChange.getTime() + minimumInterval);
            
            return {
                allowed: false,
                timeRemaining,
                nextAllowed
            };
        }

        return { allowed: true };
    }

    /**
     * Check monthly password change limit
     */
    checkMonthlyLimit(userMetadata, now) {
        const cutoffDate = new Date(now.getTime() - (PASSWORD_CHANGE_CONFIG.ROLLING_WINDOW_DAYS * 24 * 60 * 60 * 1000));
        const recentChanges = userMetadata.passwordChanges.filter(change => 
            new Date(change.timestamp) > cutoffDate
        );

        const changesRemaining = PASSWORD_CHANGE_CONFIG.MAX_CHANGES_PER_MONTH - recentChanges.length;

        if (changesRemaining <= 0) {
            // Find when the oldest change will expire
            const oldestChange = recentChanges.sort((a, b) => 
                new Date(a.timestamp) - new Date(b.timestamp)
            )[0];
            
            const oldestExpiry = new Date(
                new Date(oldestChange.timestamp).getTime() + 
                (PASSWORD_CHANGE_CONFIG.ROLLING_WINDOW_DAYS * 24 * 60 * 60 * 1000)
            );

            return {
                allowed: false,
                changesRemaining: 0,
                nextReset: oldestExpiry,
                oldestExpiry
            };
        }

        return {
            allowed: true,
            changesRemaining
        };
    }

    /**
     * Check for active penalties
     */
    checkActivePenalties(userMetadata, now) {
        const activePenalties = userMetadata.penalties?.filter(penalty => 
            new Date(penalty.expiresAt) > now
        ) || [];

        if (activePenalties.length > 0) {
            const latestPenalty = activePenalties.sort((a, b) => 
                new Date(b.expiresAt) - new Date(a.expiresAt)
            )[0];

            const timeRemaining = new Date(latestPenalty.expiresAt).getTime() - now.getTime();

            return {
                allowed: false,
                timeRemaining,
                penaltyEnd: new Date(latestPenalty.expiresAt),
                reason: latestPenalty.type
            };
        }

        return { allowed: true };
    }
}

/**
 * Format time remaining for user display
 * @param {number} milliseconds - Time remaining in milliseconds
 * @returns {string} - Formatted time string
 */
export const formatTimeRemaining = (milliseconds) => {
    const totalMinutes = Math.ceil(milliseconds / (1000 * 60));
    
    if (totalMinutes < 60) {
        return `${totalMinutes} minute${totalMinutes !== 1 ? 's' : ''}`;
    }
    
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    if (hours < 24) {
        if (minutes === 0) {
            return `${hours} hour${hours !== 1 ? 's' : ''}`;
        }
        return `${hours}h ${minutes}m`;
    }
    
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    
    if (remainingHours === 0) {
        return `${days} day${days !== 1 ? 's' : ''}`;
    }
    return `${days}d ${remainingHours}h`;
};

/**
 * Get user-friendly message for rate limit status
 * @param {Object} eligibilityResult - Result from checkPasswordChangeEligibility
 * @returns {Object} - { title, message, type, actionable }
 */
export const getRateLimitMessage = (eligibilityResult) => {
    const { canChange, reason, timeUntilNext, changesRemaining, nextAllowed } = eligibilityResult;

    if (canChange) {
        if (reason === 'grace_period') {
            return {
                title: 'Password Change Available',
                message: eligibilityResult.isFirstChange 
                    ? 'Set up your secure password'
                    : `${changesRemaining} changes remaining this month`,
                type: 'success',
                actionable: true
            };
        }
        
        return {
            title: 'Password Change Available',
            message: `${changesRemaining} change${changesRemaining !== 1 ? 's' : ''} remaining this month`,
            type: 'success',
            actionable: true
        };
    }

    switch (reason) {
        case 'minimum_interval':
            return {
                title: 'Password Recently Changed',
                message: `Please wait ${formatTimeRemaining(timeUntilNext)} before changing again`,
                type: 'warning',
                actionable: false,
                nextAvailable: nextAllowed
            };

        case 'monthly_limit_exceeded':
            return {
                title: 'Monthly Limit Reached',
                message: `You've used all 3 password changes this month. Next change available ${nextAllowed.toLocaleDateString()}`,
                type: 'info',
                actionable: false,
                nextAvailable: nextAllowed
            };

        case 'penalty_active':
            return {
                title: 'Temporary Restriction',
                message: `Password changes temporarily restricted. Available in ${formatTimeRemaining(timeUntilNext)}`,
                type: 'error',
                actionable: false,
                nextAvailable: nextAllowed
            };

        default:
            return {
                title: 'Password Change Unavailable',
                message: 'Please try again later or contact support if this persists',
                type: 'error',
                actionable: false
            };
    }
}; 