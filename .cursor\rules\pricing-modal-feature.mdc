---
description: 
globs: 
alwaysApply: false
---
@pricing-modal-feature
ruleId: pricing-modal-feature
description: >
  Implement a professional, responsive pricing modal with 3-column layout that follows HeroUI design guidelines and maintains consistency with existing dark theme UI components. The modal displays Free, Basic (Most Popular), and Pro pricing tiers with smooth animations and full accessibility support.

appliesTo:
  - /src/components/modals/PricingModal.jsx # Main pricing modal component
  - /src/components/modals/PricingPlanCard.jsx # Individual pricing plan card component
  - /src/components/ui/Modal.jsx # Enhanced base modal wrapper (if reused)
  - /src/contexts/AppContext.jsx # For pricing modal state management
  - /src/styles/pricing-modal.css # Dedicated pricing modal styles
  - /src/App.jsx # Integration with main app state

ruleType: always

implementationNotes: |
  ## Modal Structure & Layout
  - **PricingModal.jsx**: Main modal container with backdrop and 3-column grid
  - **PricingPlanCard.jsx**: Reusable card component for each pricing tier
  - **Responsive Grid**: CSS Grid layout (3 columns desktop, 1 column mobile)
  - **HeroUI Integration**: Use HeroUI button components, spacing utilities, and design tokens

  ## Pricing Plans Configuration
  ```javascript
  const pricingPlans = [
    {
      id: 'free',
      name: 'Free Plan',
      price: '$0',
      period: '/month',
      description: 'Perfect for getting started',
      features: [
        'Basic thumbnail generation',
        '5 generations per month',
        'Standard templates',
        'Basic text overlays'
      ],
      buttonText: 'Current Plan',
      buttonDisabled: true,
      highlight: false,
      currentPlan: true
    },
    {
      id: 'basic',
      name: 'Basic Plan',
      price: '$19',
      period: '/month',
      description: 'Enhanced features for creators',
      features: [
        'Unlimited thumbnail generation',
        'Premium templates',
        'Advanced text styling',
        'Icon library access',
        'Custom backgrounds',
        'Priority support'
      ],
      buttonText: 'Upgrade to Basic',
      buttonDisabled: false,
      highlight: true,
      mostPopular: true,
      savings: 'Most Popular'
    },
    {
      id: 'pro',
      name: 'Pro Plan',
      price: '$49',
      period: '/month',
      description: 'Complete toolkit for professionals',
      features: [
        'Everything in Basic',
        'AI-powered recommendations',
        'Batch generation',
        'Custom brand presets',
        'Advanced analytics',
        'White-label options',
        'Dedicated support'
      ],
      buttonText: 'Upgrade to Pro',
      buttonDisabled: false,
      highlight: false,
      gradient: true
    }
  ];
  ```

  ## HeroUI Component Integration
  - **Modal Backdrop**: Use HeroUI Modal component with backdrop blur
  - **Buttons**: HeroUI Button components with proper variants (primary, secondary, disabled)
  - **Cards**: HeroUI Card components with consistent padding and shadows
  - **Typography**: Follow HeroUI text sizing and weight scale
  - **Spacing**: Use HeroUI spacing tokens (p-4, gap-6, etc.)
  - **Colors**: Adhere to HeroUI dark theme color palette

  ## Responsive Design Specifications
  ```css
  .pricing-modal-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }

  @media (max-width: 1024px) {
    .pricing-modal-grid {
      grid-template-columns: repeat(2, 1fr);
      max-width: 800px;
    }
  }

  @media (max-width: 640px) {
    .pricing-modal-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
      padding: 1rem;
    }
  }
  ```

  ## Animation & UX Features
  - **Modal Fade-in**: 300ms ease-out transition for backdrop and content
  - **Car