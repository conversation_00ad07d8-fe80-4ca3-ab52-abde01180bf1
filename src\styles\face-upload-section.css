/* ====== ENHANCED FACE UPLOAD SECTION STYLES ====== */
.face-upload-section {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(55, 65, 81, 0.5) !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.beta-badge {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.8), rgba(55, 65, 81, 0.9));
    border: 1px solid rgba(107, 114, 128, 0.3);
    font-weight: 600;
    letter-spacing: 0.025em;
}

.tab-buttons {
    background: rgba(55, 65, 81, 0.8) !important;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.tab-button {
    font-weight: 600;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

.tab-button.bg-blue-600 {
    background: linear-gradient(135deg, #3B82F6, #2563EB) !important;
    box-shadow: 0 4px 12px rgba(229, 231, 235, 0.2);
}

.tab-button:hover:not(.bg-blue-600) {
    background: rgba(75, 85, 99, 0.6) !important;
}

.tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.tab-button:hover::before {
    left: 100%;
}

.upload-dropzone {
    background: rgba(31, 41, 55, 0.8) !important;
    border-color: rgba(75, 85, 99, 0.6) !important;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-dropzone:hover {
    background: rgba(31, 41, 55, 0.95) !important;
    border-color: rgba(107, 114, 128, 0.8) !important;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.upload-dropzone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
}

.upload-dropzone:hover::before {
    opacity: 1;
}

.upload-icon-container {
    background: linear-gradient(135deg, rgba(75, 85, 99, 0.8), rgba(55, 65, 81, 0.9)) !important;
    border: 1px solid rgba(107, 114, 128, 0.3);
    transition: all 0.3s ease;
}

.upload-dropzone:hover .upload-icon-container {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2)) !important;
    border-color: rgba(229, 231, 235, 0.2);
    transform: scale(1.05);
}

.upload-dropzone:hover .upload-icon {
    color: #60A5FA !important;
}

.url-input-container {
    background: rgba(31, 41, 55, 0.8) !important;
    border: 1px solid rgba(75, 85, 99, 0.5);
    transition: all 0.3s ease;
}

.url-input-container:focus-within {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.preview-section {
    background: rgba(75, 85, 99, 0.2) !important;
    border: 1px solid rgba(107, 114, 128, 0.3) !important;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.warning-message {
    font-style: italic;
    opacity: 0.8;
}

.info-icon {
    transition: all 0.2s ease;
    cursor: help;
}

.info-icon:hover {
    color: #60A5FA !important;
    transform: scale(1.1);
}

/* Mobile responsiveness for face upload section */
@media (max-width: 640px) {
    .face-upload-section {
        padding: 1rem !important;
        margin-top: 1rem !important;
    }

    .tab-buttons {
        padding: 0.5rem !important;
    }

    .tab-button {
        padding: 0.75rem 1rem !important;
        font-size: 0.875rem;
    }

    .upload-dropzone {
        padding: 2rem 1rem !important;
    }

    .upload-icon-container {
        width: 3rem !important;
        height: 3rem !important;
    }

    .upload-icon {
        font-size: 1.5rem !important;
    }

    .url-input-container {
        padding: 1rem !important;
    }
} 