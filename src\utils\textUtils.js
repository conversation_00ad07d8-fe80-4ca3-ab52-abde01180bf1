/**
 * Text utilities for the thumbnail generator
 */

/**
 * Find placeholders in a string formatted as [PLACEHOLDER]
 * @param {string} text - Text to search for placeholders
 * @returns {Array} - Array of placeholder objects with info
 */
export const findPlaceholders = (text) => {
    if (!text) return [];
    const placeholderRegex = /\[([A-Z0-9_\s]+)\]/g;
    const matches = [...(text.matchAll(placeholderRegex) || [])];
    return matches.map(match => ({
        placeholder: match[0],
        text: match[1],
        start: match.index,
        end: match.index + match[0].length
    }));
};

/**
 * Generate a random ID for elements
 * @returns {string} Random ID
 */
export const generateRandomId = () => {
    return Math.random().toString(36).substring(2, 9);
};

export default {
    findPlaceholders,
    generateRandomId
}; 