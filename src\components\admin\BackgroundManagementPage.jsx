const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;

export const BackgroundManagementPage = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState('all');

    // Mock background data
    const [backgrounds, setBackgrounds] = useState([
        {
            id: 'bg-1',
            name: 'Cinematic Bokeh',
            category: 'Cinematic',
            previewPath: '/assets/backgrounds/cinematic/bokeh.webp',
            dateAdded: '2024-01-15'
        },
        {
            id: 'bg-2',
            name: 'Gaming Neon',
            category: 'Gaming',
            previewPath: '/assets/backgrounds/gaming/neon.webp',
            dateAdded: '2024-01-14'
        },
        {
            id: 'bg-3',
            name: 'Tech Circuit',
            category: 'Tech',
            previewPath: '/assets/backgrounds/tech/circuit.webp',
            dateAdded: '2024-01-13'
        },
        {
            id: 'bg-4',
            name: 'Abstract Gradient',
            category: 'Abstract',
            previewPath: '/assets/backgrounds/abstract/gradient.webp',
            dateAdded: '2024-01-12'
        }
    ]);

    const categories = ['all', 'Cinematic', 'Gaming', 'Tech', 'Abstract', 'Business'];

    const handleAddBackground = () => {
        console.log('Add new background');
        // This would open a modal for adding backgrounds
    };

    const handleEditBackground = (background) => {
        console.log('Edit background:', background.id);
        // This would open a modal for editing backgrounds
    };

    const handleDeleteBackground = (backgroundId) => {
        if (window.confirm('Are you sure you want to delete this background?')) {
            setBackgrounds(prev => prev.filter(bg => bg.id !== backgroundId));
        }
    };

    const filteredBackgrounds = backgrounds.filter(background => {
        const matchesSearch = background.name.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesCategory = selectedCategory === 'all' || background.category === selectedCategory;
        return matchesSearch && matchesCategory;
    });

    const createBackgroundCard = (background) => {
        return React.createElement('div', {
            key: background.id,
            className: 'admin-background-card bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-purple-500 transition-colors',
            id: `background-card-${background.id}`
        },
            // Preview Image
            React.createElement('div', {
                className: 'admin-background-preview-wrapper relative h-32 bg-gray-700'
            },
                React.createElement('div', {
                    className: 'admin-background-preview-placeholder w-full h-full flex items-center justify-center'
                },
                    React.createElement('span', {
                        className: 'admin-background-preview-text text-gray-400 text-sm'
                    }, 'Preview Image')
                ),
                // Actions overlay
                React.createElement('div', {
                    className: 'admin-background-actions-overlay absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center gap-2'
                },
                    React.createElement('button', {
                        className: 'admin-background-edit-btn p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors',
                        onClick: () => handleEditBackground(background),
                        title: 'Edit background'
                    },
                        React.createElement('svg', {
                            className: 'admin-background-edit-icon w-4 h-4',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'm16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10'
                            })
                        )
                    ),
                    React.createElement('button', {
                        className: 'admin-background-delete-btn p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors',
                        onClick: () => handleDeleteBackground(background.id),
                        title: 'Delete background'
                    },
                        React.createElement('svg', {
                            className: 'admin-background-delete-icon w-4 h-4',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'm14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0'
                            })
                        )
                    )
                )
            ),
            
            // Card Content
            React.createElement('div', {
                className: 'admin-background-card-content p-4'
            },
                React.createElement('h3', {
                    className: 'admin-background-name text-lg font-semibold text-white mb-2'
                }, background.name),
                React.createElement('div', {
                    className: 'admin-background-meta flex items-center justify-between'
                },
                    React.createElement('span', {
                        className: 'admin-background-category-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800'
                    }, background.category),
                    React.createElement('span', {
                        className: 'admin-background-date text-gray-400 text-sm'
                    }, new Date(background.dateAdded).toLocaleDateString())
                )
            )
        );
    };

    return React.createElement('div', {
        className: 'admin-background-management-page',
        id: 'admin-background-management-content'
    },
        // Page Header
        React.createElement('div', {
            className: 'admin-background-management-header flex justify-between items-center mb-8',
            id: 'admin-background-management-header'
        },
            React.createElement('div', {
                className: 'admin-background-header-left'
            },
                React.createElement('h1', {
                    className: 'admin-background-management-title text-3xl font-bold text-white mb-2'
                }, 'Background Preset Management'),
                React.createElement('p', {
                    className: 'admin-background-management-subtitle text-gray-400'
                }, 'Manage background presets and styles for thumbnail generation')
            ),
            React.createElement('button', {
                className: 'admin-add-background-btn bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2',
                id: 'admin-add-background-btn',
                onClick: handleAddBackground
            },
                React.createElement('svg', {
                    className: 'admin-add-background-icon w-5 h-5',
                    fill: 'none',
                    stroke: 'currentColor',
                    viewBox: '0 0 24 24'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: 'M12 4.5v15m7.5-7.5h-15'
                    })
                ),
                'Add New Background'
            )
        ),

        // Filters and Search
        React.createElement('div', {
            className: 'admin-background-filters-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6',
            id: 'admin-background-filters-section'
        },
            React.createElement('div', {
                className: 'admin-background-filters-grid grid grid-cols-1 md:grid-cols-2 gap-4'
            },
                // Search Input
                React.createElement('div', {
                    className: 'admin-background-search-wrapper'
                },
                    React.createElement('label', {
                        className: 'admin-background-search-label block text-sm font-medium text-gray-300 mb-2'
                    }, 'Search Backgrounds'),
                    React.createElement('input', {
                        type: 'text',
                        className: 'admin-background-search-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                        id: 'admin-background-search-input',
                        placeholder: 'Search by name...',
                        value: searchTerm,
                        onChange: (e) => setSearchTerm(e.target.value)
                    })
                ),
                
                // Category Filter
                React.createElement('div', {
                    className: 'admin-background-category-wrapper'
                },
                    React.createElement('label', {
                        className: 'admin-background-category-label block text-sm font-medium text-gray-300 mb-2'
                    }, 'Filter by Category'),
                    React.createElement('select', {
                        className: 'admin-background-category-select w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                        id: 'admin-background-category-select',
                        value: selectedCategory,
                        onChange: (e) => setSelectedCategory(e.target.value)
                    }, categories.map(category => 
                        React.createElement('option', {
                            key: category,
                            value: category
                        }, category === 'all' ? 'All Categories' : category)
                    ))
                )
            )
        ),

        // Backgrounds Grid
        React.createElement('div', {
            className: 'admin-backgrounds-list-container',
            id: 'admin-backgrounds-grid-section'
        },
            filteredBackgrounds.length > 0 ? 
                React.createElement('div', {
                    className: 'admin-backgrounds-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6',
                    id: 'admin-backgrounds-grid'
                }, filteredBackgrounds.map(background => createBackgroundCard(background))) :
                React.createElement('div', {
                    className: 'admin-backgrounds-empty-state bg-gray-800 border border-gray-700 rounded-lg p-12 text-center',
                    id: 'admin-backgrounds-empty-state'
                },
                    React.createElement('svg', {
                        className: 'admin-backgrounds-empty-icon w-16 h-16 text-gray-500 mx-auto mb-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'm2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z'
                        })
                    ),
                    React.createElement('h3', {
                        className: 'admin-backgrounds-empty-title text-lg font-medium text-gray-400 mb-2'
                    }, 'No Backgrounds Found'),
                    React.createElement('p', {
                        className: 'admin-backgrounds-empty-message text-gray-500'
                    }, 'No background presets match your search criteria. Try adjusting your filters or add a new background.')
                )
        )
    );
}; 