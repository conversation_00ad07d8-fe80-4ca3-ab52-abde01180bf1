/**
 * Template Image Utilities
 * Universal system for handling template preview images
 * Supports .webp images with fallback to solid color placeholders
 */

/**
 * Generate preview image path for a template
 * @param {string} categoryId - Category identifier (e.g., 'tech', 'fitness', 'gaming')
 * @param {string} templateId - Template identifier (e.g., 'tech-review', 'fitness-abs-workout')
 * @returns {string} Path to preview image
 */
export const getTemplatePreviewPath = (categoryId, templateId) => {
  return `/background-templates/${categoryId}/${templateId}.webp`;
};

/**
 * Generate fallback solid color placeholder
 * @param {object} placeholder - Placeholder object with text and bgColor
 * @returns {object} Placeholder configuration
 */
export const getFallbackPlaceholder = (placeholder) => {
  return {
    text: placeholder.text,
    bgColor: placeholder.bgColor,
    useSolidColor: true
  };
};

/**
 * Check if preview image exists and handle loading
 * @param {string} imagePath - Path to preview image
 * @param {function} onError - Callback for image load error
 * @returns {Promise<boolean>} Whether image loaded successfully
 */
export const checkImageExists = (imagePath, onError = () => {}) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => {
      onError();
      resolve(false);
    };
    img.src = imagePath;
  });
};

/**
 * Get complete template preview configuration
 * @param {string} categoryId - Category identifier
 * @param {object} template - Template object
 * @returns {object} Complete preview configuration
 */
export const getTemplatePreviewConfig = (categoryId, template) => {
  const previewPath = getTemplatePreviewPath(categoryId, template.id);
  
  return {
    id: template.id,
    name: template.name,
    description: template.description,
    previewImage: previewPath,
    fallbackPlaceholder: getFallbackPlaceholder(template.templateImagePlaceholder),
    category: categoryId
  };
};

/**
 * Map category names to their folder identifiers
 */
export const CATEGORY_FOLDER_MAP = {
  'tech': 'tech',
  'gaming': 'gaming', 

  'fitness': 'fitness',
  'health-fitness': 'fitness', // Combined category uses fitness folder
  'reaction': 'reaction',
  'vlogging': 'vlogging',
  'travel': 'travel',
  'business': 'business',
  'art-design': 'art-design',
  'finance': 'finance',
  'education': 'education'
};

/**
 * Get folder name for category
 * @param {string} categoryId - Category identifier
 * @returns {string} Folder name for assets
 */
export const getCategoryFolder = (categoryId) => {
  return CATEGORY_FOLDER_MAP[categoryId] || categoryId;
}; 