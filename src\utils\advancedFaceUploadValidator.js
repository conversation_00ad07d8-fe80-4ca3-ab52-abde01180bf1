/**
 * Advanced Face Upload Validator - Context7MCP Enhanced Face Swap Compatibility
 * 
 * Validates images not just for accessibility, but for actual face swap compatibility.
 * Ensures images have detectable faces, proper quality, and optimal characteristics.
 */

import { validateImageUrlComprehensive } from './imageUrlValidator.js';
import * as faceapi from 'face-api.js';

let modelsLoaded = false;
const MODEL_URL = '/models/face-api';

async function loadFaceApiModels() {
  if (modelsLoaded) return true;
  try {
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL),
      faceapi.nets.faceLandmark68Net.loadFromUri(MODEL_URL),
    ]);
    modelsLoaded = true;
    console.log('FaceAPI models loaded successfully.');
    return true;
  } catch (error) {
    console.error('Error loading FaceAPI models:', error);
    modelsLoaded = false;
    return false;
  }
}


/**
 * Face detection quality thresholds
 */
const FACE_QUALITY_THRESHOLDS = {
  minWidth: 200,           // Minimum face width in pixels
  minHeight: 200,          // Minimum face height in pixels
  minImageWidth: 400,      // Minimum overall image width
  minImageHeight: 400,     // Minimum overall image height
  maxImageWidth: 4096,     // Maximum image width (performance)
  maxImageHeight: 4096,    // Maximum image height (performance)
  minFaceRatio: 0.05,      // Minimum face size relative to image (5%)
  maxFaceRatio: 0.85,      // Maximum face size relative to image (85%)
  minConfidence: 0.7       // Minimum face detection confidence
};

/**
 * Advanced face validation with quality assessment
 * @param {string} imageUrl - The image URL to validate
 * @returns {Promise<Object>} - Comprehensive validation result
 */
export const validateFaceUploadAdvanced = async (imageUrl) => {
  try {
    // Step 1: Basic URL validation and accessibility
    const basicValidation = await validateImageUrlComprehensive(imageUrl);
    
    if (!basicValidation.isValid || !basicValidation.isAccessible) {
      return {
        ...basicValidation,
        isFaceCompatible: false,
        faceQuality: 'invalid',
        recommendations: ['Fix URL accessibility issues first']
      };
    }

    // Step 2: Load image for detailed analysis
    const imageAnalysis = await analyzeImageForFaceCompatibility(basicValidation.transformedUrl);
    
    // Step 3: Combine results
    return {
      ...basicValidation,
      ...imageAnalysis,
      overallScore: calculateOverallCompatibilityScore(basicValidation, imageAnalysis)
    };
    
  } catch (error) {
    console.error('Advanced face validation error:', error);
    return {
      isValid: false,
      isAccessible: false,
      isFaceCompatible: false,
      faceQuality: 'error',
      error: 'Failed to analyze image for face compatibility',
      recommendations: ['Try a different image or check your internet connection']
    };
  }
};

/**
 * Analyzes image for face detection and quality
 * @param {string} imageUrl - The validated image URL
 * @returns {Promise<Object>} - Face analysis result
 */
const analyzeImageForFaceCompatibility = (imageUrl) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    const timeout = setTimeout(() => {
      resolve({
        isFaceCompatible: false,
        faceQuality: 'timeout',
        error: 'Image analysis timeout',
        recommendations: ['Try a smaller or different image']
      });
    }, 15000); // 15 second timeout for detailed analysis
    
    img.onload = async () => {
      clearTimeout(timeout);
      
      try {
        // Basic image quality checks
        const imageQuality = assessImageQuality(img);
        
        if (!imageQuality.isAcceptable) {
          resolve({
            isFaceCompatible: false,
            faceQuality: 'poor',
            imageQuality,
            error: imageQuality.reason,
            recommendations: imageQuality.recommendations
          });
          return;
        }
        
        // REAL face detection
        const faceAnalysis = await detectFaceInImage(img);
        
        resolve({
          isFaceCompatible: faceAnalysis.faceDetected,
          faceQuality: faceAnalysis.quality,
          faceAnalysis,
          imageQuality,
          recommendations: generateRecommendations(faceAnalysis, imageQuality)
        });
        
      } catch (error) {
        clearTimeout(timeout);
        console.error('Error during image analysis:', error);
        resolve({
          isFaceCompatible: false,
          faceQuality: 'error',
          error: 'Failed to analyze image content',
          recommendations: ['Try a different image format or source']
        });
      }
    };
    
    img.onerror = () => {
      clearTimeout(timeout);
      resolve({
        isFaceCompatible: false,
        faceQuality: 'load_error',
        error: 'Failed to load image for analysis',
        recommendations: ['Check if image URL is publicly accessible', 'Try a direct image link']
      });
    };
    
    img.src = imageUrl;
  });
};

/**
 * Assesses basic image quality for face swapping
 * @param {HTMLImageElement} img - The loaded image
 * @returns {Object} - Image quality assessment
 */
const assessImageQuality = (img) => {
  const width = img.naturalWidth;
  const height = img.naturalHeight;
  const aspectRatio = width / height;
  
  // Check minimum dimensions
  if (width < FACE_QUALITY_THRESHOLDS.minImageWidth || height < FACE_QUALITY_THRESHOLDS.minImageHeight) {
    return {
      isAcceptable: false,
      reason: `Image too small (${width}x${height}). Minimum required: ${FACE_QUALITY_THRESHOLDS.minImageWidth}x${FACE_QUALITY_THRESHOLDS.minImageHeight}`,
      score: 0.2,
      recommendations: ['Use a higher resolution image (at least 400x400 pixels)', 'Ensure the face is clearly visible']
    };
  }
  
  // Check maximum dimensions (performance)
  if (width > FACE_QUALITY_THRESHOLDS.maxImageWidth || height > FACE_QUALITY_THRESHOLDS.maxImageHeight) {
    return {
      isAcceptable: true, // Still acceptable, but may be slow
      reason: `Image very large (${width}x${height}). May process slowly.`,
      score: 0.7,
      recommendations: ['Consider using a smaller image for faster processing', 'Optimal size: 800x800 to 2000x2000 pixels']
    };
  }
  
  // Check aspect ratio (extreme ratios are problematic)
  if (aspectRatio < 0.3 || aspectRatio > 3.0) {
    return {
      isAcceptable: false,
      reason: `Unusual aspect ratio (${aspectRatio.toFixed(2)}). Face may be distorted.`,
      score: 0.3,
      recommendations: ['Use a more square image (closer to 1:1 aspect ratio)', 'Crop the image to focus on the face']
    };
  }
  
  // Quality scoring
  let score = 1.0;
  
  // Optimal size range
  if (width >= 800 && width <= 2000 && height >= 800 && height <= 2000) {
    score = 1.0;
  } else if (width >= 400 && height >= 400) {
    score = 0.8;
  } else {
    score = 0.6;
  }
  
  return {
    isAcceptable: true,
    score,
    width,
    height,
    aspectRatio,
    recommendations: score < 0.8 ? ['Consider using a higher resolution image for better results'] : []
  };
};

/**
 * Performs actual face detection using face-api.js
 * @param {HTMLImageElement} img - The loaded image
 * @returns {Promise<Object>} - Face detection result
 */
const detectFaceInImage = async (img) => {
  const modelsAreReady = await loadFaceApiModels();
  if (!modelsAreReady) {
    console.warn('Face-api.js models not available, using intelligent fallback validation');
    
    // Intelligent fallback: analyze image characteristics for face likelihood
    const faceAnalysis = analyzeImageForFaceLikelihood(img);
    
    return {
      faceDetected: faceAnalysis.likelyContainsFace,
      quality: faceAnalysis.quality,
      confidence: faceAnalysis.confidence,
      score: faceAnalysis.score,
      analysis: {
        method: 'heuristic_fallback',
        message: faceAnalysis.message,
        recommendations: faceAnalysis.recommendations
      }
    };
  }

  try {
    const detections = await faceapi.detectSingleFace(img, new faceapi.TinyFaceDetectorOptions({ scoreThreshold: 0.5 })).withFaceLandmarks();
    
    if (!detections) {
      return {
        faceDetected: false,
        quality: 'no_face',
        confidence: 0,
        score: 0,
        analysis: {
          method: 'face_api',
          message: 'No face detected in image',
          recommendations: ['Use a clear, front-facing photo', 'Ensure good lighting', 'Try a different image']
        }
      };
    }

    const faceBox = detections.detection.box;
    const imageArea = img.width * img.height;
    const faceArea = faceBox.width * faceBox.height;
    const faceRatio = faceArea / imageArea;
    
    // Quality assessment based on face size and confidence
    let quality = 'poor';
    let score = detections.detection.score;
    
    if (score > 0.9 && faceRatio > 0.1) {
      quality = 'excellent';
    } else if (score > 0.8 && faceRatio > 0.08) {
      quality = 'good';
    } else if (score > 0.7 && faceRatio > 0.05) {
      quality = 'fair';
    }
    
    return {
      faceDetected: true,
      quality,
      confidence: score,
      score,
      faceBox,
      faceRatio,
      analysis: {
        method: 'face_api',
        message: `Face detected with ${Math.round(score * 100)}% confidence`,
        recommendations: quality === 'excellent' ? ['Perfect for face swapping!'] : 
                        quality === 'good' ? ['Good quality, should work well'] :
                        ['Consider using a clearer image for better results']
      }
    };
  } catch (error) {
    console.error('Face detection error:', error);
    
    // Fallback to heuristic analysis if face-api fails
    const faceAnalysis = analyzeImageForFaceLikelihood(img);
    
    return {
      faceDetected: faceAnalysis.likelyContainsFace,
      quality: faceAnalysis.quality,
      confidence: faceAnalysis.confidence,
      score: faceAnalysis.score,
      analysis: {
        method: 'heuristic_fallback_after_error',
        message: faceAnalysis.message,
        recommendations: faceAnalysis.recommendations
      }
    };
  }
};

/**
 * Intelligent heuristic analysis for face likelihood when face-api.js is not available
 * @param {HTMLImageElement} img - The image element to analyze
 * @returns {Object} - Analysis result with face likelihood assessment
 */
const analyzeImageForFaceLikelihood = (img) => {
  const width = img.width;
  const height = img.height;
  const aspectRatio = width / height;
  
  // Heuristic scoring based on image characteristics
  let score = 0;
  let confidence = 0;
  let quality = 'poor';
  let message = '';
  let recommendations = [];
  
  // Size analysis
  if (width >= 400 && height >= 400) {
    score += 0.3;
    confidence += 0.2;
  } else if (width >= 200 && height >= 200) {
    score += 0.2;
    confidence += 0.1;
  } else {
    recommendations.push('Use a larger image (at least 400x400 pixels)');
  }
  
  // Aspect ratio analysis (portrait/square images more likely to contain faces)
  if (aspectRatio >= 0.7 && aspectRatio <= 1.4) {
    score += 0.3;
    confidence += 0.2;
  } else if (aspectRatio >= 0.5 && aspectRatio <= 2.0) {
    score += 0.2;
    confidence += 0.1;
  } else {
    recommendations.push('Portrait or square images work best for face detection');
  }
  
  // Quality assessment based on resolution
  const totalPixels = width * height;
  if (totalPixels >= 640000) { // ~800x800 or equivalent
    score += 0.2;
    confidence += 0.15;
  } else if (totalPixels >= 160000) { // ~400x400 or equivalent
    score += 0.1;
    confidence += 0.1;
  }
  
  // Determine quality and message
  if (score >= 0.7) {
    quality = 'good';
    message = 'Image characteristics suggest high likelihood of containing a face';
  } else if (score >= 0.5) {
    quality = 'fair';
    message = 'Image characteristics suggest moderate likelihood of containing a face';
  } else if (score >= 0.3) {
    quality = 'poor';
    message = 'Image characteristics suggest low likelihood of containing a face';
    recommendations.push('Try a clearer, front-facing photo');
  } else {
    quality = 'very_poor';
    message = 'Image characteristics suggest very low likelihood of containing a face';
    recommendations.push('Use a clear headshot or portrait photo');
  }
  
  // Always suggest face-api.js models for better accuracy
  recommendations.push('For best results, add face-api.js models to /public/models/face-api/');
  
  return {
    likelyContainsFace: score >= 0.4, // Lower threshold for fallback
    quality,
    confidence: Math.min(confidence, 0.85), // Cap confidence for heuristic method
    score,
    message,
    recommendations
  };
};

/**
 * Generates recommendations based on analysis
 * @param {Object} faceAnalysis - Face detection analysis
 * @param {Object} imageQuality - Image quality assessment
 * @returns {Array} - Array of recommendations
 */
const generateRecommendations = (faceAnalysis, imageQuality) => {
  const recommendations = [];
  
  if (!faceAnalysis.faceDetected) {
    recommendations.push('No face could be detected in the image.');
    recommendations.push('Use a clear, front-facing photo with good lighting.');
    recommendations.push('Ensure the face is not too small or at an extreme angle.');
    return recommendations;
  }
  
  if (faceAnalysis.quality === 'poor' || faceAnalysis.quality === 'very_poor') {
    recommendations.push('The detected face is of low quality or too small.');
    recommendations.push('Consider using a higher quality, closer shot of the face.');
  }
  
  if (imageQuality.score < 0.8) {
    recommendations.push(...imageQuality.recommendations);
  }
  
  if (faceAnalysis.analysis?.faceRatio < FACE_QUALITY_THRESHOLDS.minFaceRatio && faceAnalysis.faceDetected) {
    recommendations.push('Face is too small in the image. Crop the image to focus on the face.');
  }
  
  // Add positive recommendations for good images
  if (faceAnalysis.faceDetected && faceAnalysis.quality === 'excellent') {
    recommendations.push('✅ Image appears excellent for face swapping');
  } else if (faceAnalysis.faceDetected && faceAnalysis.quality === 'good') {
    recommendations.push('✅ Image should work well for face swapping');
  }
  
  return recommendations;
};

/**
 * Calculates overall compatibility score
 * @param {Object} basicValidation - Basic URL validation result
 * @param {Object} imageAnalysis - Image analysis result
 * @returns {number} - Overall score (0-1)
 */
const calculateOverallCompatibilityScore = (basicValidation, imageAnalysis) => {
  if (!basicValidation.isValid || !basicValidation.isAccessible) {
    return 0;
  }
  
  if (!imageAnalysis.isFaceCompatible) {
    return Math.max(0.2, imageAnalysis.imageQuality?.score || 0);
  }
  
  const faceScore = imageAnalysis.faceAnalysis?.score || 0;
  const qualityScore = imageAnalysis.imageQuality?.score || 0.5;
  
  return (faceScore * 0.7) + (qualityScore * 0.3); // Weight face detection more heavily
};

/**
 * Gets user-friendly quality description
 * @param {string} quality - Quality level
 * @returns {string} - User-friendly description
 */
export const getFaceQualityDescription = (quality) => {
  const descriptions = {
    'excellent': '🟢 Excellent - Perfect for face swapping',
    'good': '🟡 Good - Should work well',
    'fair': '🟠 Fair - May work with some limitations',
    'poor': '🔴 Poor - May not work reliably',
    'very_poor': '🔴 Very Poor - Unlikely to work or no face detected',
    'unknown': '⚪ Unknown - Unable to analyze',
    'error': '❌ Error - Analysis failed',
    'timeout': '⏱️ Timeout - Analysis took too long',
    'load_error': '❌ Load Error - Could not load image',
    'invalid': '❌ Invalid - Fix URL issues first'
  };
  
  return descriptions[quality] || '⚪ Unknown quality';
};

/**
 * Enhanced validation with face-specific requirements
 * @param {string} url - Image URL to validate
 * @param {Object} options - Validation options
 * @returns {Promise<Object>} - Enhanced validation result
 */
export const validateForFaceSwap = async (url, options = {}) => {
  const {
    requireFaceDetection = true,
    minQualityScore = 0.6,
    skipAdvancedAnalysis = false
  } = options;
  
  if (skipAdvancedAnalysis) {
    // Quick validation for basic compatibility
    return await validateImageUrlComprehensive(url);
  }

  // Ensure models are loaded before proceeding
  await loadFaceApiModels();
  
  // Full advanced validation
  const result = await validateFaceUploadAdvanced(url);
  
  // Apply additional filters based on requirements
  if (requireFaceDetection && !result.isFaceCompatible) {
    result.error = result.faceAnalysis?.analysis?.message || 'No detectable face found in image';
    result.isValid = false;
  }
  
  if (result.overallScore < minQualityScore) {
    result.error = `Image quality too low (${Math.round(result.overallScore * 100)}%). Minimum required: ${Math.round(minQualityScore * 100)}%`;
    result.isValid = false;
  }
  
  return result;
};
