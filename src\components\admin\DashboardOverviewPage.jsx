const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;
import { MetricCard } from './MetricCard.jsx';

export const DashboardOverviewPage = () => {
    // Mock data for metrics
    const metricsData = [
        {
            id: 'total-revenue',
            title: 'Total Revenue',
            value: '$12,847.50',
            change: '+12.5%',
            changeType: 'positive',
            icon: 'currency-dollar'
        },
        {
            id: 'total-sales',
            title: 'Total Sales',
            value: '1,247',
            change: '+8.2%',
            changeType: 'positive',
            icon: 'shopping-cart'
        },
        {
            id: 'active-users',
            title: 'Active Users',
            value: '342',
            change: '+15.3%',
            changeType: 'positive',
            icon: 'users'
        },
        {
            id: 'total-api-calls',
            title: 'Total API Calls',
            value: '28,456',
            change: '+22.1%',
            changeType: 'positive',
            icon: 'cpu-chip'
        },
        {
            id: 'site-traffic',
            title: 'Site Traffic (30d)',
            value: '15,892',
            change: '-2.4%',
            changeType: 'negative',
            icon: 'chart-bar'
        }
    ];

    const quickActions = [
        {
            id: 'add-template',
            title: 'Add New Template',
            description: 'Create a new thumbnail template',
            icon: 'plus',
            action: () => console.log('Add new template')
        },
        {
            id: 'add-background',
            title: 'Add New Background',
            description: 'Upload a new background preset',
            icon: 'photo',
            action: () => console.log('Add new background')
        }
    ];

    const createQuickActionCard = (action) => {
        return React.createElement('div', {
            key: action.id,
            className: 'admin-quick-action-card bg-gray-800 border border-gray-700 rounded-lg p-6 hover:border-purple-500 transition-colors cursor-pointer',
            id: `quick-action-${action.id}`,
            onClick: action.action
        },
            React.createElement('div', {
                className: 'admin-quick-action-icon-wrapper flex items-center justify-center w-12 h-12 bg-purple-600 rounded-lg mb-4'
            },
                React.createElement('svg', {
                    className: 'admin-quick-action-icon w-6 h-6 text-white',
                    fill: 'none',
                    stroke: 'currentColor',
                    viewBox: '0 0 24 24'
                },
                    React.createElement('path', {
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        strokeWidth: 2,
                        d: action.icon === 'plus' 
                            ? 'M12 4.5v15m7.5-7.5h-15'
                            : 'm2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z'
                    })
                )
            ),
            React.createElement('h3', {
                className: 'admin-quick-action-title text-lg font-semibold text-white mb-2'
            }, action.title),
            React.createElement('p', {
                className: 'admin-quick-action-description text-gray-400 text-sm'
            }, action.description)
        );
    };

    const createChartPlaceholder = (title, type, id) => {
        return React.createElement('div', {
            className: 'admin-chart-placeholder bg-gray-800 border border-gray-700 rounded-lg p-6',
            id: `chart-placeholder-${id}`
        },
            React.createElement('h3', {
                className: 'admin-chart-title text-lg font-semibold text-white mb-4'
            }, title),
            React.createElement('div', {
                className: 'admin-chart-content h-64 bg-gray-700 rounded-lg flex items-center justify-center',
                id: `chart-content-${id}`
            },
                React.createElement('div', {
                    className: 'admin-chart-placeholder-content text-center'
                },
                    React.createElement('svg', {
                        className: 'admin-chart-placeholder-icon w-16 h-16 text-gray-500 mx-auto mb-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: type === 'line' 
                                ? 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
                                : 'M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z'
                        })
                    ),
                    React.createElement('p', {
                        className: 'admin-chart-placeholder-text text-gray-500 text-sm'
                    }, `${title} Chart Coming Soon`)
                )
            )
        );
    };

    return React.createElement('div', {
        className: 'admin-dashboard-overview-page',
        id: 'admin-dashboard-overview-content'
    },
        // Page Header
        React.createElement('div', {
            className: 'admin-dashboard-overview-header mb-8',
            id: 'admin-dashboard-overview-header'
        },
            React.createElement('h1', {
                className: 'admin-dashboard-overview-title text-3xl font-bold text-white mb-2'
            }, 'Dashboard Overview'),
            React.createElement('p', {
                className: 'admin-dashboard-overview-subtitle text-gray-400'
            }, 'Monitor your thumbnail generator performance and manage content')
        ),

        // Metrics Cards Section
        React.createElement('div', {
            className: 'admin-dashboard-metric-cards-container mb-8',
            id: 'admin-dashboard-metrics-section'
        },
            React.createElement('h2', {
                className: 'admin-metrics-section-title text-xl font-semibold text-white mb-4'
            }, 'Key Metrics'),
            React.createElement('div', {
                className: 'admin-metrics-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6',
                id: 'admin-metrics-grid'
            }, metricsData.map(metric => 
                React.createElement(MetricCard, {
                    key: metric.id,
                    ...metric
                })
            ))
        ),

        // Charts Section
        React.createElement('div', {
            className: 'admin-dashboard-charts-container mb-8',
            id: 'admin-dashboard-charts-section'
        },
            React.createElement('h2', {
                className: 'admin-charts-section-title text-xl font-semibold text-white mb-4'
            }, 'Analytics'),
            React.createElement('div', {
                className: 'admin-charts-grid grid grid-cols-1 lg:grid-cols-2 gap-6',
                id: 'admin-charts-grid'
            },
                createChartPlaceholder('Revenue Over Time', 'line', 'revenue-over-time'),
                createChartPlaceholder('Top Users by API Usage', 'bar', 'top-users-api')
            )
        ),

        // Quick Actions Section
        React.createElement('div', {
            className: 'admin-dashboard-quick-actions mb-8',
            id: 'admin-dashboard-quick-actions-section'
        },
            React.createElement('h2', {
                className: 'admin-quick-actions-title text-xl font-semibold text-white mb-4'
            }, 'Quick Actions'),
            React.createElement('div', {
                className: 'admin-quick-actions-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
                id: 'admin-quick-actions-grid'
            }, quickActions.map(action => createQuickActionCard(action)))
        )
    );
}; 