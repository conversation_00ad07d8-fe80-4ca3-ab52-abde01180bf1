# Project Change Log

This file tracks all updates, fixes, features, and style changes (CSS/JS) for developer team review and communication.

---

## [2024-06-10] Refactor: Email Icon Container Custom Naming Standardization
- **Type:** Refactor
- **Files:** src/pages/ForgotPassword.jsx
- **Description:** 
  - Applied custom naming convention `email-icon-container` to the email input icon container div following @custom-naming.mdc rules.
  - Standardized naming across authentication screens for consistent, descriptive, and easily inspectable class names.
  - Improves maintainability, debugging capabilities, and aligns with project's custom naming guidelines.
- **Author:** [Your Name]

---

## [2024-06-10] Fix: Email Field Label Standardization
- **Type:** Fix
- **Files:** src/pages/ForgotPassword.jsx
- **Description:** 
  - Standardized email input label to use the same custom class (`email-field-label`) as the main login page.
  - Fixes a bug where the icon label would disappear after focusing/clicking on the email input field.
  - Aligns with custom naming conventions for better maintainability and consistency across authentication screens.
- **Author:** [Your Name]

---

## [2024-06-10] Feature: Responsive Mood Icon Sizing & Templates Grid
- **Type:** Feature
- **Files:** src/styles/layout.css
- **Description:** 
  - Added a media query for screens 600px–768px to set `.templates-grid` to 4 columns.
  - Increased `.mood-expression-picker-section img` size by 20% (from 40px to 48px) in the same breakpoint.
- **Author:** [Your Name]

---

*Add new entries below for every change!* 