---
description: 
globs: 
alwaysApply: false
---
@free-tier-upgrade-banners
ruleId: free-tier-upgrade-banners
description: >
  Implements a multi-scenario upgrade/limit notification system for free tier users, including user menu, dashboard, and notification dropdown banners. Enforces a strict 3 HD image generation limit for free users, disables medium/low quality, and provides debug toggling for testing.

appliesTo:
  - /src/components/UserDashboard.jsx
  - /src/components/NotificationSystem.jsx
  - /src/components/UserProfileSection.jsx
  - /src/styles/dashboard.css
  - /src/styles/user-profile-section.css

ruleType: always

implementationNotes: |
  - Free tier users can generate a maximum of 3 HD images. No medium or low quality allowed.
  - After 3 HD generations, all generation actions are disabled for free users.
  - User menu always shows an “Upgrade to Pro” banner with a crown/star icon and CTA.
  - When the free limit is reached, a prominent dashboard banner appears with a warning/info icon, headline, and upgrade CTA.
  - Notification dropdown shows an info/warning notification when the free plan is exhausted, with a CTA to upgrade.
  - All banners and notifications use Hero UI and Solar icons, with clear color coding (info: blue, warning: orange/yellow).
  - Debug Mode: When enabled, allows toggling banners/notifications for testing regardless of usage state.
  - All upgrade CTAs must route to the subscription page or billing tab.
  - All UI must be responsive and accessible, with proper ARIA labels and keyboard navigation.

acceptanceCriteria:
  - Free users are limited to 3 HD generations, with no access to medium/low quality.
  - User menu always displays the upgrade banner for free users.
  - Dashboard banner appears only when the free limit is reached.
  - Notification dropdown shows an upgrade notification when the free plan is exhausted.
  - All banners/notifications are styled consistently with Hero UI and use appropriate icons.
  - Debug mode allows manual toggling of all banners/notifications for QA.
  - All upgrade CTAs function and route correctly.

sampleUI: |
  - User menu: “Upgrade to Pro” card with crown/star icon and purple gradient.
  - Dashboard: “You’ve reached your monthly limit!” banner with warning icon and orange gradient.
  - Notification: “Your free plan has expired. Upgrade to continue.” with info/warning icon and blue/orange accent.
  - Debug toggle: When ON, all banners/notifications can be shown/hidden for testing.

relatedIcons:
  - User menu: solar:crown-bold, solar:star-bold
  - Dashboard banner: solar:stop-bold-duotone, solar:info-circle-bold-duotone
  - Notification: solar:info-circle-bold-duotone, solar:warning-bold-duotone

debugToggle:
  - Expose a toggle in the dashboard for “Debug Mode: ON/OFF”
  - When ON, allow manual display/hide of all upgrade banners and notifications for QA/testing.
  - Debug Mode is ON, you can test unlimited generation and all UI states without restriction.
  

---

# Usage Scenarios

1. **Free user, <3 HD generations:**  
   - User menu shows upgrade banner.  
   - No dashboard or notification banner.

2. **Free user, 3/3 HD generations used:**  
   - User menu shows upgrade banner.  
   - Dashboard shows “monthly limit reached” banner.  
   - Notification dropdown shows “plan expired” notification.

3. **Debug Mode ON:**  
   - All banners/notifications can be toggled for testing, regardless of usage.

---

# Maintenance

- All logic for free tier limits, banners, and notifications must be centralized and easy to update.
- All icons and color tokens must use Hero UI/Solar standards.
- All new CSS must be responsive and accessible.
