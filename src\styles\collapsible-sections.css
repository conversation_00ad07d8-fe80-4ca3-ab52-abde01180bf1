/**
 * collapsible-sections.css
 * Styles for collapsible sections in the sidebar
 */

/* 
 * Main collapsible section container
 */
.collapsible-section {
  position: relative;
  width: 100%;
}

/* 
 * Header styles for collapsible sections
 */
.collapsible-section [role="button"] {
  padding: 0.5rem 0;
  transition: background-color 0.2s ease;
  border-radius: 0.25rem;
}

.collapsible-section [role="button"]:hover {
  background-color: rgba(107, 114, 128, 0.1); /* Gray with opacity */
}

.collapsible-section [role="button"]:focus-visible {
  outline: 2px solid rgb(168, 85, 247); /* Purple outline on focus */
  outline-offset: 2px;
}

/* 
 * Heading text for collapsible sections 
 */
.collapsible-section h3 {
  transition: color 0.2s ease;
  user-select: none;
}

.collapsible-section [aria-expanded="true"] h3 {
  color: rgb(216, 180, 254); /* Purple-300 with slight brightness */
}

.collapsible-section [role="button"]:hover h3 {
  color: rgb(233, 213, 255); /* Lighter purple on hover */
}

/* 
 * Icon styling for chevron indicator
 */
.collapsible-section .iconify[data-icon="solar:alt-arrow-down-linear"] {
  display: flex !important;
  align-items: center;
  justify-content: center;
  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-origin: center !important;
  will-change: transform;
}

/* Ensure rotation states work properly */
.glass-collapsible-header .iconify[data-icon="solar:alt-arrow-down-linear"] {
  transform-origin: center !important;
}

/* 
 * Content area animation 
 */
.collapsible-content {
  will-change: max-height, opacity;
  overflow: hidden; /* Ensure content is clipped */
}

/* Animation for expanding/collapsing */
@keyframes collapseUp {
  from {
    max-height: var(--max-height);
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

@keyframes expandDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: var(--max-height);
    opacity: 1;
  }
}

/* 
 * Accessibility and screen reader support
 */
.collapsible-section [aria-hidden="true"] {
  display: block; /* Keep it in the DOM for animation */
  pointer-events: none;
}

/* 
 * Responsive adjustments
 */
@media (max-width: 640px) {
  .collapsible-section [role="button"] {
    padding: 0.75rem 0;
  }
} 