# A/B Test: Light/Dark Theme Toggle for Auth Pages

## Overview
Add a prominent toggle button to all authentication pages that allows users to instantly switch between a light and dark theme. This enables A/B testing and visual comparison of the login UI and buttons in both themes.

## Requirements
- **Toggle Button:** Fixed at top-right, labeled "A/B Test: Light/Dark Theme" (with sun/moon icon).
- **Theme Switching:** Instantly switches all auth UI (background, cards, buttons, text) between light and dark.
- **Button Styles:** Google and other buttons adapt to the selected theme (light for light, dark for dark).
- **Persistence:** Remembers the last selected theme for the session.
- **Responsiveness:** Works on all screen sizes.
- **Accessibility:** Fully keyboard and screen reader accessible.
- **Visual Cues:** Clearly indicate which theme is active.

## Technical Implementation
- **State Management:** Use React state with localStorage persistence
- **CSS Classes:** Toggle between `.auth-light-theme` and `.auth-dark-theme` body classes
- **Button Variants:** Google button switches between `auth-glass-google-btn` (light) and `auth-glass-google-btn-dark` classes
- **Icons:** Use Heroicons for sun/moon toggle icons
- **Transitions:** Smooth 300ms transitions for all theme changes
- **Z-Index:** Ensure toggle button stays above all other elements (z-index: 9999)

## Theme Variations

### Dark Theme (Current)
- Background: Dark glass with purple/blue gradients
- Cards: Semi-transparent dark with glass blur
- Google Button: Dark gradient (`#2a2a2a` to `#252525`)
- Text: White/light gray
- Borders: Semi-transparent white

### Light Theme (New)
- Background: Light gradient with soft whites/grays
- Cards: Semi-transparent light with glass blur
- Google Button: Light neutral (`#F5F7FA`)
- Text: Dark gray/black
- Borders: Semi-transparent dark

## Acceptance Criteria
- Toggle is always visible on all auth pages.
- Theme switches instantly and smoothly.
- All UI elements, including Google button, match the selected theme.
- No layout shift or flicker.
- Implementation is visually polished and consistent.
- Works on Welcome.jsx, WelcomeGlass.jsx, SignUp.jsx, ForgotPassword.jsx, ResetPassword.jsx

## Example UI

```
🌙 Dark Theme                           [☀️ Light Theme Toggle] (top-right)
┌───────────────────────────────┐
│     🔗 Continue with Google    │ ← Dark button
│              or               │
│     📧 [Email Field]          │
│     🔒 [Password Field]       │
│     [Sign In Button]          │
└───────────────────────────────┘

↓ (After toggle click)

☀️ Light Theme                          [🌙 Dark Theme Toggle] (top-right)  
┌───────────────────────────────┐
│     🔗 Continue with Google    │ ← Light button
│              or               │
│     📧 [Email Field]          │
│     🔒 [Password Field]       │
│     [Sign In Button]          │
└───────────────────────────────┘
```

## Files to Modify
- `src/pages/Welcome.jsx` - Add toggle to original auth page
- `src/pages/WelcomeGlass.jsx` - Add toggle to glass auth page  
- `src/pages/SignUp.jsx` - Add toggle to signup page
- `src/pages/ForgotPassword.jsx` - Add toggle to forgot password page
- `src/pages/ResetPassword.jsx` - Add toggle to reset password page
- `src/styles/auth-glass-v2.css` - Add light theme styles
- `src/Entry.jsx` - Add theme state management

## Implementation Priority
1. Create light theme CSS variants
2. Add toggle button component  
3. Implement theme switching logic
4. Add to all auth pages
5. Test responsiveness and accessibility