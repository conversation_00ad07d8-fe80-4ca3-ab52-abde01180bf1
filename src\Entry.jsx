// Import React from the installed packages
import React, { useState, useEffect } from 'react'
import { App } from './App.jsx'
import { Welcome } from './pages/Welcome.jsx'
import { WelcomeGlass } from './pages/WelcomeGlass.jsx'
import SignUp from './pages/SignUp.jsx'
import ForgotPassword from './pages/ForgotPassword.jsx'
import { authAPI, supabase } from './utils/supabase.mjs'
import { enhancedAuth, rememberMeAuth } from './utils/auth.js'
import { ToastProvider, ToastContainer, useToast } from './contexts/ToastContext.jsx'
import { needsProfileInitialization, initializeUserProfile } from './utils/userProfileUtils.js'

// Create a wrapper component that provides toast functionality to the App
const AppWithToast = ({ user, onSignOut }) => {
    const { showSuccess } = useToast();

    // Handle login success toast
    useEffect(() => {
        if (user) {
            const hasShownLoginToast = sessionStorage.getItem('login_toast_shown');
            if (!hasShownLoginToast) {
                // Show login success toast
                showSuccess('Login successful! Welcome back.', 3000);
                sessionStorage.setItem('login_toast_shown', 'true');
            }
        } else {
            // Clear the flag when user logs out
            sessionStorage.removeItem('login_toast_shown');
        }
    }, [user, showSuccess]);

    return React.createElement(React.Fragment, null,
        React.createElement(App, { user, onSignOut }),
        React.createElement(ToastContainer)
    );
};

/**
 * Entry component that manages real authentication state with Supabase
 * This component decides whether to show the Welcome (login) screen, SignUp, ForgotPassword, or the main App
 */
const Entry = () => {
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [hasCheckedAuth, setHasCheckedAuth] = useState(false);
    const [user, setUser] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [currentPage, setCurrentPage] = useState('login'); // 'login', 'signup', 'forgot-password'
    
    // Glass Auth V2 is now the default - no more A/B testing
    const useGlassAuth = true;

    // A/B Testing state for light/dark theme toggle (kept for future implementation)
    const [isLightTheme, setIsLightTheme] = useState(() => {
        return localStorage.getItem('auth-theme') === 'light';
    });

    // Clear any invalid tokens on component mount
    useEffect(() => {
        // Make clearInvalidTokens available globally for debugging
        window.clearInvalidTokens = () => {
            console.log('🧹 Manually clearing invalid tokens...');
            authAPI.clearInvalidTokens();
            window.location.reload();
        };

        // Check for common signs of invalid tokens and clear them
        const checkForInvalidTokens = () => {
            try {
                const keys = Object.keys(localStorage);
                const hasSupabaseTokens = keys.some(key => key.startsWith('sb-') || key.includes('auth-token'));
                
                if (hasSupabaseTokens) {
                    console.log('🔍 Found existing auth tokens, will validate during session check');
                    console.log('💡 If you see auth errors, run: clearInvalidTokens()');
                }
            } catch (error) {
                console.warn('Error checking for invalid tokens:', error);
            }
        };

        checkForInvalidTokens();

        // Cleanup
        return () => {
            if (window.clearInvalidTokens) {
                delete window.clearInvalidTokens;
            }
        };
    }, []);

    // Check for existing Supabase session on component mount
    useEffect(() => {
        let isMounted = true;
        let authSubscription = null;

        const checkSupabaseSession = async () => {
            try {
                setIsLoading(true);
                
                // First, check for saved session (Remember Me)
                const savedSessionResult = await enhancedAuth.checkSavedSession(supabase);
                
                if (!isMounted) return; // Component was unmounted, don't update state
                
                if (savedSessionResult.success && savedSessionResult.user) {
                    // Auto-login successful from saved session
                    let finalUser = savedSessionResult.user;
                    
                    // Initialize profile if needed
                    try {
                        if (needsProfileInitialization(savedSessionResult.user)) {
                            console.log('🔧 Initializing profile for returning user...');
                            finalUser = await initializeUserProfile(supabase, savedSessionResult.user);
                        }
                    } catch (error) {
                        console.error('Error initializing user profile:', error);
                    }
                    
                    setUser(finalUser);
                    setIsAuthenticated(true);
                    console.log('🚀 Auto-login successful from saved session:', finalUser.email);
                    return;
                }
                
                // If no saved session, check current Supabase session
                const sessionResult = await authAPI.getCurrentSession();
                
                if (!isMounted) return; // Component was unmounted, don't update state
                
                if (sessionResult.success && sessionResult.session?.user) {
                    // User is authenticated from current session
                    let finalUser = sessionResult.session.user;
                    
                    // Initialize profile if needed
                    try {
                        if (needsProfileInitialization(sessionResult.session.user)) {
                            console.log('🔧 Initializing profile for existing session user...');
                            finalUser = await initializeUserProfile(supabase, sessionResult.session.user);
                        }
                    } catch (error) {
                        console.error('Error initializing user profile:', error);
                    }
                    
                    setUser(finalUser);
                    setIsAuthenticated(true);
                    console.log('✅ User authenticated from existing session:', finalUser.email);
                } else {
                    // No valid session found
                    setUser(null);
                    setIsAuthenticated(false);
                    console.log('ℹ️ No existing session found');
                }
            } catch (error) {
                console.error('❌ Error checking session:', error);
                
                // If there's an invalid refresh token error, clear tokens
                if (error.message && error.message.includes('Invalid Refresh Token')) {
                    console.log('🧹 Clearing invalid tokens due to refresh token error');
                    authAPI.clearInvalidTokens();
                    rememberMeAuth.clearSavedSession(); // Also clear saved session data
                }
                
                if (isMounted) {
                    setUser(null);
                    setIsAuthenticated(false);
                }
            } finally {
                if (isMounted) {
                    setIsLoading(false);
                    setHasCheckedAuth(true);
                }
            }
        };

        // Setup auth state listener
        const setupAuthListener = () => {
            authSubscription = authAPI.onAuthStateChange((event, session) => {
                if (!isMounted) return; // Component was unmounted, ignore
                
                console.log('🔄 Auth state changed:', event, session?.user?.email || 'no user');
                
                // Only process meaningful auth state changes
                if (event === 'SIGNED_IN' && session?.user) {
                    // Initialize profile if needed for new sign-ins
                    (async () => {
                        let finalUser = session.user;
                        try {
                            if (needsProfileInitialization(session.user)) {
                                console.log('🔧 Initializing profile for newly signed in user...');
                                finalUser = await initializeUserProfile(supabase, session.user);
                            }
                        } catch (error) {
                            console.error('Error initializing user profile:', error);
                        }
                        setUser(finalUser);
                    })();
                    
                    setIsAuthenticated(true);
                    setCurrentPage('login'); // Reset to login page after successful auth
                } else if (event === 'SIGNED_OUT') {
                    setUser(null);
                    setIsAuthenticated(false);
                } else if (event === 'TOKEN_REFRESHED' && session?.user) {
                    // Update user data on token refresh
                    (async () => {
                        let finalUser = session.user;
                        try {
                            if (needsProfileInitialization(session.user)) {
                                console.log('🔧 Initializing profile during token refresh...');
                                finalUser = await initializeUserProfile(supabase, session.user);
                            }
                        } catch (error) {
                            console.error('Error initializing user profile during token refresh:', error);
                        }
                        setUser(finalUser);
                        setIsAuthenticated(true);
                    })();
                }
                // Ignore INITIAL_SESSION events to prevent duplicate processing
            });
        };

        // Start the initialization process
        checkSupabaseSession().then(() => {
            if (isMounted) {
                setupAuthListener();
            }
        });

        // Cleanup function
        return () => {
            isMounted = false;
            if (authSubscription) {
                try {
                    authSubscription();
                } catch (error) {
                    console.warn('Error unsubscribing from auth changes:', error);
                }
            }
        };
    }, []);

    // Theme management - Apply theme class to body
    useEffect(() => {
        if (isLightTheme) {
            document.body.classList.add('auth-light-theme');
            document.body.classList.remove('auth-dark-theme');
        } else {
            document.body.classList.add('auth-dark-theme');
            document.body.classList.remove('auth-light-theme');
        }
        
        // Persist theme preference
        localStorage.setItem('auth-theme', isLightTheme ? 'light' : 'dark');
    }, [isLightTheme]);

    // CONTEXT7MCP FIX: Body class management for authentication pages scrolling control
    useEffect(() => {
        if (!isAuthenticated && hasCheckedAuth) {
            // User is on authentication pages - add auth-page-active class
            document.body.classList.add('auth-page-active');
            document.body.classList.remove('main-app-active');
            
            // Add specific class for signup page to enable scrolling
            if (currentPage === 'signup') {
                document.body.classList.add('auth-page-signup');
            } else {
                document.body.classList.remove('auth-page-signup');
            }
        } else if (isAuthenticated) {
            // User is authenticated and in main app - remove auth-page-active class
            document.body.classList.remove('auth-page-active');
            document.body.classList.remove('auth-page-signup');
            // main-app-active class is managed by App.jsx
        }
        
        // Cleanup function to remove classes when component unmounts
        return () => {
            document.body.classList.remove('auth-page-active');
            document.body.classList.remove('auth-page-signup');
        };
    }, [isAuthenticated, hasCheckedAuth, currentPage]);

    // Handle successful authentication from any page
    const handleAuthenticated = async (authenticatedUser) => {
        console.log('🎉 Authentication successful:', authenticatedUser.email);
        
        // Initialize profile for new Gmail users
        try {
            if (needsProfileInitialization(authenticatedUser)) {
                console.log('🔧 Initializing profile for new user...');
                const initializedUser = await initializeUserProfile(supabase, authenticatedUser);
                setUser(initializedUser);
            } else {
                setUser(authenticatedUser);
            }
        } catch (error) {
            console.error('Error initializing user profile:', error);
            setUser(authenticatedUser); // Continue with original user data
        }
        
        setIsAuthenticated(true);
        setCurrentPage('login'); // Reset to default page
    };

    // Handle sign out
    const handleSignOut = async () => {
        try {
            // Use enhanced auth to sign out and clear all saved data
            await enhancedAuth.signOut(supabase);
            setUser(null);
            setIsAuthenticated(false);
            setCurrentPage('login');
            console.log('👋 User signed out (including Remember Me data cleared)');
        } catch (error) {
            console.error('❌ Error signing out:', error);
        }
    };

    // Navigation handlers
    const navigateToSignUp = () => setCurrentPage('signup');
    const navigateToForgotPassword = () => setCurrentPage('forgot-password');
    const navigateToLogin = () => setCurrentPage('login');
    
    // Theme toggle handler
    const toggleTheme = () => {
        const newValue = !isLightTheme;
        setIsLightTheme(newValue);
        console.log(`🎨 Theme switched to: ${newValue ? 'Light' : 'Dark'} mode`);
    };

    // Show loading spinner while checking authentication
    if (isLoading || !hasCheckedAuth) {
        return (
            <div className="flex min-h-screen bg-gray-900 text-white items-center justify-center">
                <div className="flex flex-col items-center gap-4">
                    <div className="w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
                    <p className="text-gray-400">Loading...</p>
                </div>
            </div>
        );
    }

    // If authenticated, show the main app with toast provider
    if (isAuthenticated && user) {
        return React.createElement(ToastProvider, null,
            React.createElement(AppWithToast, { user, onSignOut: handleSignOut })
        );
    }

    // CONTEXT7MCP: Wrap authentication pages with ToastProvider for signup success toast
    // Glass Auth V2 is now the default for all authentication pages
    const renderAuthPage = () => {
        switch (currentPage) {
            case 'signup':
                return React.createElement(SignUp, {
                    onAuthenticated: handleAuthenticated,
                    onNavigateToLogin: navigateToLogin,
                    onNavigateToForgotPassword: navigateToForgotPassword,
                    isLightTheme,
                    onToggleTheme: toggleTheme
                });
            
            case 'forgot-password':
                return React.createElement(ForgotPassword, {
                    onBackToLogin: navigateToLogin,
                    onNavigateToSignUp: navigateToSignUp,
                    isLightTheme,
                    onToggleTheme: toggleTheme
                });
            
            case 'login':
            default:
                // Always use Glass Auth V2 (WelcomeGlass) as the default
                return React.createElement(WelcomeGlass, {
                    onAuthenticated: handleAuthenticated,
                    onNavigateToSignUp: navigateToSignUp,
                    onNavigateToForgotPassword: navigateToForgotPassword,
                    isLightTheme,
                    onToggleTheme: toggleTheme
                });
        }
    };

    // Not authenticated - show appropriate auth page with toast support
    return React.createElement(ToastProvider, null,
        React.createElement(React.Fragment, null,
            renderAuthPage(),
            React.createElement(ToastContainer)
        )
    );
};

export { Entry };