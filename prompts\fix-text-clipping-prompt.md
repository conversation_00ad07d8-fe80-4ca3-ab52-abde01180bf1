# 🛠️ GPT-Image-1 Thumbnail Prompt: Fix Clipped Text Overlay

## 🧠 Purpose
Ensure that all text overlays rendered in GPT-image-1 thumbnails are fully visible, never cut off, and maintain proper alignment within a 1280×720 (16:9) safe display area.

---

## ✅ Updated Prompt Rules for Text Overlay

When generating a thumbnail that includes any text (titles, reactions, bold phrases), always include:

- "Ensure the text overlay fits safely **within the visible 1280x720 frame**."
- "Apply **a minimum of 50px horizontal padding** from both left and right sides."
- "Keep the text aligned inside a safe zone for YouTube thumbnails — never place text edge-to-edge."
- "Use balanced text placement (e.g. top-right or center-right), and avoid bottom corners."
- "The entire title should be readable even when viewed at small scale (mobile)."
- "Avoid extra-long single lines; break long phrases into two lines if needed."

---

## 🖼️ Composition & Layout Enhancements

- Frame the subject and object to leave enough space on the right for title text.
- Use rule-of-thirds: subject on left, text on right.
- Do not overlap glowing effects or arrows onto text area.
- If needed, slightly scale down the subject to give space for text without distortion.

---

## ✨ Text Style Reminders

- Font: bold, sans-serif, high contrast (e.g. yellow + white with glow)
- Title case: ALL CAPS or emphasized first words
- Text effects: drop shadow + slight 3D bevel
- Colors: avoid white-on-white — ensure separation from the background

---

## 📏 Output Requirements

- Final resolution: 1280×720 px
- Aspect ratio: strict 16:9
- Thumbnail must preview properly with **no text cropped** on any device

---

## 💬 Example Prompt Fragment (for injection)

> "Add a bold title text in the top-right with at least 50px margin from both sides. Ensure the full phrase is visible inside the safe area of a 1280x720 YouTube thumbnail and not clipped. Prioritize readability on mobile screens."

---

## 🔁 Use This Prompt When:
- Text is part of the thumbnail (e.g. "THIS TOOL IS INSANE!")
- User enables text_overlay
- Title overlays risk being too long or too close to the edge

---

## 🧪 Testing Recommendation
After generating an image:
- Preview in both mobile and desktop mockup frames
- Flag any cropped or low-contrast text as a UX issue 