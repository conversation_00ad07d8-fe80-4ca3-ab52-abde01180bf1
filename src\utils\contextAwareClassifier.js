/**
 * Context-Aware Keyword Disambiguation System
 * 
 * This utility analyzes user prompts to distinguish between gaming/FPS contexts 
 * and non-gaming contexts when encountering ambiguous keywords.
 * 
 * Examples:
 * - "code review best practices" → Tech (not gaming)
 * - "COD tournament highlights" → Gaming (Call of Duty)
 * - "battle cancer with new treatments" → Health (not gaming)
 * - "React vs Vue comparison" → Tech (React = atom logo, not infinity)
 */

// Explicit Call of Duty phrases that must be present to trigger COD gaming logic
const EXPLICIT_COD_PHRASES = [
    'call of duty',
    'cod warzone', 
    'cod battleroyale',
    'cod battle royale',
    'cod of duty',
    'warzone cod',
    'call duty', // Allow slight variations
    'cod: warzone',
    'cod tournament',
    'cod championship',
    'cod esports',
    'cod pro',
    'cod gameplay',
    'cod montage'
];

/**
 * Checks if the prompt contains explicit Call of Duty phrases
 * Only these phrases should trigger Call of Duty gaming logic
 * 
 * @param {string} prompt - The user's input prompt
 * @returns {boolean} True if explicit COD phrases are found
 */
export function hasExplicitCallOfDutyPhrases(prompt) {
    if (!prompt || typeof prompt !== 'string') return false;
    
    const normalizedPrompt = prompt.toLowerCase().trim();
    
    return EXPLICIT_COD_PHRASES.some(phrase => {
        // Allow for minor spacing and punctuation variations
        const flexiblePhrase = phrase.replace(/\s+/g, '\\s*').replace(/[:.]/g, '[:.\\s]*');
        const regex = new RegExp(flexiblePhrase, 'i');
        return regex.test(normalizedPrompt);
    });
}

// Ambiguous keywords that could be gaming or non-gaming
const AMBIGUOUS_KEYWORDS = {
    'code': {
        gaming: ['cod', 'call of duty', 'tournament', 'gameplay', 'warzone', 'multiplayer', 'killstreaks', 'loadout'],
        nonGaming: ['review', 'practices', 'javascript', 'python', 'programming', 'development', 'software', 'clean', 'standards', 'interview', 'bootcamp', 'certification', 'languages', 'challenges', 'postal', 'dress', 'medical', 'color', 'organization']
    },
    'cod': {
        gaming: ['tournament', 'championship', 'warzone', 'multiplayer', 'zombies', 'campaign', 'black ops', 'modern warfare', 'killstreaks', 'weapon', 'map', 'esports', 'pro player', 'gameplay', 'montage', 'update', 'patch', 'console', 'pc'],
        nonGaming: ['fish', 'recipes', 'atlantic', 'population', 'liver oil', 'benefits', 'fishing', 'techniques', 'fresh', 'market', 'prices', 'jobs', 'company', 'career', 'work', 'employment', 'position', 'hiring']
    },
    'make': {
        // Brand/platform context (Make.com)
        brand: ['make.com', 'automation', 'workflow', 'integromat', 'no-code', 'zapier', 'integration', 'connector', 'scenario', 'webhook', 'api integration', 'automate', 'trigger', 'action'],
        // General verb context (common usage)
        general: ['tutorial', 'how to', 'guide', 'create', 'build', 'design', 'develop', 'tips', 'money', 'profit', 'income', 'business', 'video', 'content', 'app', 'website', 'recipe', 'diy', 'craft', 'art', 'music', 'game', 'project', 'plan', 'decision', 'choice', 'change', 'difference', 'improvement', 'better', 'perfect', 'amazing', 'awesome', 'easy', 'simple', 'fast', 'quick', 'best', 'top', 'ultimate', 'complete', 'step by step', 'beginner', 'advanced', 'professional']
    },
    'battle': {
        gaming: ['royale', 'arena', 'deathmatch', 'pvp', 'versus', 'tournament', 'competitive', 'ranked', 'match'],
        nonGaming: [
            'cancer', 'disease', 'gettysburg', 'history', 'war', 'market share', 'business', 'legal', 'patents', 'court',
            'mobile app', 'app design', 'ui', 'ux', 'design', 'coding', 'code', 'development', 'tutorial', 'guide', 'workflow'
        ]
    },
    'rank': {
        gaming: ['ranked', 'competitive', 'tier', 'ladder', 'leaderboard', 'elo', 'mmr', 'season', 'placement'],
        nonGaming: ['employees', 'performance', 'military', 'structure', 'search', 'results', 'seo', 'higher']
    },
    'match': {
        gaming: ['deathmatch', 'competitive', 'ranked', 'tournament', 'versus', 'pvp', '1v1', 'arena'],
        nonGaming: ['colors', 'design', 'tennis', 'schedule', 'sports', 'job', 'candidates', 'recruiting', 'dating']
    },
    'vs': {
        gaming: ['1v1', 'pvp', 'tournament', 'competitive', 'showdown', 'duel', 'arena', 'ranked'],
        nonGaming: ['code', 'extensions', 'lakers', 'warriors', 'democracy', 'autocracy', 'comparison', 'review']
    },
    'level': {
        gaming: ['up', 'progression', 'xp', 'experience', 'unlock', 'tier', 'rank', 'character'],
        nonGaming: ['skills', 'professional', 'development', 'beginner', 'advanced', 'education']
    },
    'character': {
        gaming: ['build', 'class', 'stats', 'abilities', 'skills', 'customization', 'progression'],
        nonGaming: ['development', 'novels', 'literature', 'story', 'personality', 'traits']
    },
    'spawn': {
        gaming: ['respawn', 'timer', 'point', 'camping', 'kill', 'death'],
        nonGaming: ['processes', 'programming', 'system', 'administration', 'thread', 'task']
    },
    'react': {
        // Tech context: React.js programming library
        tech: ['js', 'javascript', 'library', 'framework', 'component', 'jsx', 'tsx', 'hooks', 'props', 'state', 'redux', 'nextjs', 'next.js', 'vue', 'angular', 'frontend', 'web', 'development', 'programming', 'code', 'coding', 'app', 'website', 'vs', 'comparison', 'tutorial', 'guide', 'learn', 'course', 'native', 'typescript', 'npm', 'yarn', 'webpack', 'babel', 'create-react-app', 'api', 'rest', 'graphql', 'node', 'express', 'database', 'mongodb', 'sql', 'firebase', 'auth', 'routing', 'deployment', 'build', 'bundle', 'performance', 'optimization', 'testing', 'jest', 'cypress'],
        // Reaction/Entertainment context: emotional response or video reactions
        reaction: ['to', 'reaction', 'reacting', 'trailer', 'video', 'first time', 'watching', 'shocked', 'surprised', 'emotional', 'crying', 'laughing', 'funny', 'epic', 'amazing', 'incredible', 'wow', 'omg', 'wtf', 'crazy', 'insane', 'unbelievable', 'mind blown', 'speechless', 'stunned', 'jaw dropping', 'goosebumps', 'chills', 'tears', 'moved', 'touched', 'heartwarming', 'heartbreaking', 'emotional', 'feels', 'sobbing', 'bawling', 'weeping', 'movie', 'film', 'show', 'series', 'episode', 'scene', 'moment', 'clip', 'music', 'song', 'album', 'performance', 'concert', 'live', 'cover', 'remix', 'mashup', 'parody', 'meme', 'tiktok', 'vine', 'youtube', 'streamer', 'twitch', 'content', 'creator', 'influencer', 'celebrity', 'interview', 'podcast', 'review', 'commentary', 'analysis', 'breakdown', 'discussion', 'opinion', 'thoughts', 'feelings', 'emotions', 'experience', 'journey', 'adventure', 'story', 'narrative', 'plot', 'character', 'actor', 'actress', 'director', 'producer', 'writer', 'script', 'dialogue', 'soundtrack', 'score', 'cinematography', 'editing', 'effects', 'special', 'visual', 'sound', 'audio', 'graphics', 'animation', 'cartoon', 'anime', 'manga', 'comic', 'book', 'novel', 'literature', 'poetry', 'art', 'painting', 'drawing', 'sculpture', 'photography', 'fashion', 'style', 'beauty', 'makeup', 'hair', 'outfit', 'trend', 'viral', 'trending', 'popular', 'famous', 'celebrity', 'star', 'icon', 'legend', 'hero', 'villain', 'protagonist', 'antagonist', 'supporting', 'cast', 'ensemble', 'crew', 'team', 'group', 'band', 'duo', 'solo', 'collaboration', 'featuring', 'guest', 'cameo', 'appearance', 'debut', 'premiere', 'release', 'launch', 'announcement', 'reveal', 'teaser', 'preview', 'sneak peek', 'behind the scenes', 'making of', 'documentary', 'biography', 'memoir', 'autobiography', 'history', 'timeline', 'chronology', 'era', 'period', 'decade', 'century', 'millennium', 'ancient', 'modern', 'contemporary', 'classic', 'vintage', 'retro', 'nostalgic', 'throwback', 'old school', 'new wave', 'cutting edge', 'innovative', 'revolutionary', 'groundbreaking', 'game changing', 'paradigm shifting', 'disruptive', 'transformative', 'evolutionary', 'progressive', 'advanced', 'sophisticated', 'complex', 'simple', 'elegant', 'beautiful', 'gorgeous', 'stunning', 'breathtaking', 'magnificent', 'spectacular', 'extraordinary', 'remarkable', 'outstanding', 'exceptional', 'phenomenal', 'sensational', 'fantastic', 'wonderful', 'marvelous', 'superb', 'excellent', 'perfect', 'flawless', 'impeccable', 'pristine', 'immaculate', 'spotless', 'polished', 'refined', 'sophisticated', 'classy', 'stylish', 'chic', 'trendy', 'fashionable', 'hip', 'cool', 'awesome', 'epic', 'legendary', 'iconic', 'timeless', 'eternal', 'everlasting', 'permanent', 'enduring', 'lasting', 'durable', 'resilient', 'robust', 'strong', 'powerful', 'mighty', 'intense', 'extreme', 'radical', 'wild', 'crazy', 'mad', 'insane', 'bonkers', 'nuts', 'bananas', 'bizarre', 'weird', 'strange', 'odd', 'peculiar', 'unusual', 'unique', 'rare', 'special', 'exclusive', 'limited', 'scarce', 'precious', 'valuable', 'priceless', 'invaluable', 'worthless', 'cheap', 'expensive', 'costly', 'affordable', 'budget', 'premium', 'luxury', 'high end', 'top tier', 'elite', 'professional', 'amateur', 'beginner', 'novice', 'expert', 'master', 'guru', 'legend', 'icon', 'star', 'celebrity', 'influencer', 'creator', 'artist', 'performer', 'entertainer', 'comedian', 'actor', 'musician', 'singer', 'dancer', 'athlete', 'sports', 'game', 'competition', 'contest', 'tournament', 'championship', 'league', 'season', 'match', 'race', 'event', 'show', 'concert', 'festival', 'party', 'celebration', 'gathering', 'meetup', 'conference', 'summit', 'convention', 'expo', 'fair', 'market', 'bazaar', 'sale', 'discount', 'deal', 'offer', 'promotion', 'campaign', 'advertisement', 'commercial', 'marketing', 'branding', 'publicity', 'hype', 'buzz', 'excitement', 'anticipation', 'expectation', 'hope', 'dream', 'wish', 'desire', 'want', 'need', 'crave', 'long', 'yearn', 'ache', 'hunger', 'thirst', 'appetite', 'craving', 'urge', 'impulse', 'instinct', 'intuition', 'feeling', 'emotion', 'sentiment', 'mood', 'atmosphere', 'vibe', 'energy', 'spirit', 'soul', 'heart', 'mind', 'body', 'physical', 'mental', 'emotional', 'spiritual', 'psychological', 'philosophical', 'intellectual', 'academic', 'scholarly', 'scientific', 'technical', 'practical', 'theoretical', 'abstract', 'concrete', 'tangible', 'intangible', 'visible', 'invisible', 'obvious', 'hidden', 'secret', 'mysterious', 'enigmatic', 'puzzling', 'confusing', 'clear', 'simple', 'complex', 'complicated', 'difficult', 'easy', 'hard', 'tough', 'challenging', 'demanding', 'rigorous', 'strict', 'loose', 'flexible', 'adaptable', 'versatile', 'dynamic', 'static', 'stable', 'unstable', 'volatile', 'unpredictable', 'random', 'chaotic', 'organized', 'structured', 'systematic', 'methodical', 'logical', 'rational', 'reasonable', 'sensible', 'practical', 'realistic', 'idealistic', 'optimistic', 'pessimistic', 'positive', 'negative', 'neutral', 'balanced', 'unbiased', 'objective', 'subjective', 'personal', 'individual', 'collective', 'group', 'team', 'community', 'society', 'culture', 'tradition', 'custom', 'habit', 'routine', 'pattern', 'trend', 'fashion', 'style', 'approach', 'method', 'technique', 'strategy', 'tactic', 'plan', 'scheme', 'plot', 'conspiracy', 'theory', 'hypothesis', 'idea', 'concept', 'notion', 'thought', 'opinion', 'view', 'perspective', 'angle', 'point', 'position', 'stance', 'attitude', 'approach', 'manner', 'way', 'method', 'means', 'tool', 'instrument', 'device', 'gadget', 'machine', 'equipment', 'apparatus', 'system', 'network', 'platform', 'service', 'product', 'item', 'object', 'thing', 'stuff', 'material', 'substance', 'element', 'component', 'part', 'piece', 'fragment', 'section', 'segment', 'portion', 'chunk', 'bit', 'byte', 'data', 'information', 'knowledge', 'wisdom', 'understanding', 'comprehension', 'awareness', 'consciousness', 'perception', 'sensation', 'feeling', 'experience']
    }
};

// Context indicators for different domains
const CONTEXT_INDICATORS = {
    gaming: [
        // Gaming-specific terms
        'gameplay', 'multiplayer', 'single player', 'co-op', 'pvp', 'pve',
        'tournament', 'esports', 'competitive', 'ranked', 'casual',
        'fps', 'rpg', 'mmo', 'moba', 'rts', 'battle royale',
        'console', 'pc gaming', 'mobile gaming', 'nintendo', 'xbox', 'playstation',
        'steam', 'epic games', 'origin', 'uplay',
        'streamer', 'twitch', 'youtube gaming',
        'boss fight', 'raid', 'dungeon', 'quest', 'mission',
        'loadout', 'weapon', 'armor', 'equipment', 'inventory',
        'map', 'level', 'stage', 'world', 'campaign',
        'patch', 'update', 'dlc', 'expansion', 'season pass',
        'achievement', 'trophy', 'unlock', 'progression',
        'leaderboard', 'scoreboard', 'stats', 'k/d', 'win rate',
        'noob', 'pro', 'gamer', 'player', 'teammate'
    ],
    tech: [
        // Programming and development
        'programming', 'development', 'software', 'application', 'app',
        'javascript', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust',
        'html', 'css', 'react', 'vue', 'angular', 'node.js', 'express',
        'database', 'sql', 'mongodb', 'postgresql', 'mysql',
        'api', 'rest', 'graphql', 'microservices', 'cloud',
        'aws', 'azure', 'google cloud', 'docker', 'kubernetes',
        'git', 'github', 'gitlab', 'version control',
        'framework', 'library', 'package', 'npm', 'yarn',
        'testing', 'debugging', 'deployment', 'ci/cd',
        'frontend', 'backend', 'fullstack', 'devops',
        'algorithm', 'data structure', 'performance', 'optimization',
        'security', 'authentication', 'authorization',
        'review', 'practices', 'standards', 'guidelines',
        'tutorial', 'documentation', 'learning', 'course',
        'interview', 'bootcamp', 'certification', 'career',
        // Design and mobile app development
        'mobile app', 'app design', 'ui', 'ux', 'user interface', 'user experience',
        'design', 'crafting', 'wireframe', 'prototype', 'mockup',
        'figma', 'sketch', 'adobe xd', 'photoshop', 'illustrator',
        'responsive', 'mobile', 'web design', 'graphic design',
        'scripting', 'code scripting', 'automation', 'workflow'
    ],
    health: [
        'health', 'medical', 'doctor', 'hospital', 'treatment', 'therapy',
        'disease', 'illness', 'condition', 'symptoms', 'diagnosis',
        'medicine', 'medication', 'drug', 'pharmaceutical',
        'cancer', 'diabetes', 'heart', 'blood', 'pressure',
        'nutrition', 'diet', 'fitness', 'exercise', 'workout',
        'wellness', 'mental health', 'psychology', 'psychiatry',
        'surgery', 'operation', 'recovery', 'rehabilitation',
        'prevention', 'screening', 'checkup', 'examination'
    ],
    business: [
        'business', 'company', 'corporation', 'startup', 'enterprise',
        'market', 'marketing', 'sales', 'revenue', 'profit', 'finance',
        'investment', 'funding', 'venture capital', 'ipo',
        'strategy', 'management', 'leadership', 'team', 'employee',
        'customer', 'client', 'service', 'product', 'brand',
        'competition', 'competitor', 'industry', 'sector',
        'growth', 'expansion', 'merger', 'acquisition',
        'partnership', 'collaboration', 'contract', 'deal',
        'office', 'workplace', 'remote work', 'meeting'
    ],
    food: [
        'food', 'cooking', 'recipe', 'ingredient', 'meal', 'dish',
        'restaurant', 'chef', 'kitchen', 'eat', 'taste', 'flavor',
        'nutrition', 'calories', 'protein', 'carbs', 'fat',
        'vegetarian', 'vegan', 'organic', 'fresh', 'frozen',
        'fish', 'meat', 'seafood', 'vegetables', 'fruits',
        'spices', 'herbs', 'sauce', 'marinade', 'grill', 'bake'
    ],
    education: [
        'education', 'school', 'university', 'college', 'student',
        'teacher', 'professor', 'course', 'class', 'lesson',
        'study', 'learn', 'knowledge', 'skill', 'training',
        'degree', 'diploma', 'certificate', 'graduation',
        'curriculum', 'syllabus', 'assignment', 'homework',
        'exam', 'test', 'quiz', 'grade', 'score'
    ],
    sports: [
        'sports', 'sport', 'team', 'player', 'athlete', 'coach',
        'game', 'season', 'championship', 'tournament', 'league',
        'football', 'basketball', 'baseball', 'soccer', 'tennis',
        'golf', 'swimming', 'running', 'cycling', 'boxing',
        'training', 'practice', 'workout', 'fitness', 'gym'
    ]
};

// Tech brand/logo mappings with correct visual representations
const TECH_LOGO_MAPPINGS = {
    'react': {
        correctLogo: 'atom symbol with orbiting electrons',
        incorrectLogos: ['infinity symbol', '∞ symbol'],
        description: 'The React logo is an atom with three orbital rings, never an infinity symbol'
    },
    'vue': {
        correctLogo: 'green V-shaped logo',
        incorrectLogos: ['react atom', 'angular shield'],
        description: 'Vue.js has a distinctive green V-shaped logo'
    },
    'angular': {
        correctLogo: 'red shield with A inside',
        incorrectLogos: ['react atom', 'vue v'],
        description: 'Angular has a red shield-shaped logo with an A'
    },
    'node.js': {
        correctLogo: 'green hexagon with node text',
        incorrectLogos: ['react atom', 'javascript logo'],
        description: 'Node.js has a distinctive green hexagonal logo'
    },
    'javascript': {
        correctLogo: 'yellow square with JS text',
        incorrectLogos: ['java logo', 'react atom'],
        description: 'JavaScript has a yellow square logo with "JS" text'
    },
    'python': {
        correctLogo: 'blue and yellow snake/python head',
        incorrectLogos: ['javascript logo', 'java logo'],
        description: 'Python has a blue and yellow snake-like logo'
    },
    'docker': {
        correctLogo: 'blue whale with containers',
        incorrectLogos: ['kubernetes helm', 'ship'],
        description: 'Docker has a blue whale carrying shipping containers'
    },
    'kubernetes': {
        correctLogo: 'blue helm wheel with 7 spokes',
        incorrectLogos: ['docker whale', 'ship wheel'],
        description: 'Kubernetes has a blue ship helm wheel with 7 spokes'
    }
};

/**
 * Analyzes the context of a user prompt to determine if ambiguous keywords
 * should be interpreted in a gaming or non-gaming context.
 * 
 * @param {string} prompt - The user's input prompt
 * @returns {Object} Analysis result with context classification and confidence
 */
export function analyzePromptContext(prompt) {
    if (!prompt || typeof prompt !== 'string') {
        return {
            primaryContext: 'general',
            confidence: 0,
            isGaming: false,
            contextScores: {},
            ambiguousKeywords: [],
            recommendations: []
        };
    }

    const normalizedPrompt = prompt.toLowerCase().trim();
    const words = normalizedPrompt.split(/[\s\-_.,;:!?()]+/).filter(word => word.length > 0);
    
    // Calculate context scores
    const contextScores = {};
    Object.keys(CONTEXT_INDICATORS).forEach(context => {
        contextScores[context] = 0;
        CONTEXT_INDICATORS[context].forEach(indicator => {
            if (normalizedPrompt.includes(indicator.toLowerCase())) {
                contextScores[context] += 1;
            }
        });
    });

    // Find the primary context
    const maxScore = Math.max(...Object.values(contextScores));
    const primaryContext = maxScore > 0 
        ? Object.keys(contextScores).find(key => contextScores[key] === maxScore)
        : 'general';

    // Identify ambiguous keywords in the prompt
    const foundAmbiguousKeywords = [];
    Object.keys(AMBIGUOUS_KEYWORDS).forEach(keyword => {
        if (normalizedPrompt.includes(keyword)) {
            foundAmbiguousKeywords.push(keyword);
        }
    });

    // Analyze each ambiguous keyword
    const keywordAnalysis = foundAmbiguousKeywords.map(keyword => {
        const config = AMBIGUOUS_KEYWORDS[keyword];
        let gamingScore = 0;
        let nonGamingScore = 0;
        let brandScore = 0;
        let generalScore = 0;

        // Special handling for "make" keyword
        if (keyword === 'make') {
            // Check for brand context indicators (Make.com)
            config.brand.forEach(indicator => {
                if (normalizedPrompt.includes(indicator.toLowerCase())) {
                    brandScore += 1;
                }
            });

            // Check for general usage indicators
            config.general.forEach(indicator => {
                if (normalizedPrompt.includes(indicator.toLowerCase())) {
                    generalScore += 1;
                }
            });

            const isBrandContext = brandScore > 0 && brandScore >= generalScore;
            const confidence = Math.max(brandScore, generalScore) / (brandScore + generalScore + 1);

            return {
                keyword,
                isGamingContext: false, // "make" is never gaming-related
                isBrandContext,
                confidence,
                brandScore,
                generalScore,
                recommendedContext: isBrandContext ? 'brand' : 'general',
                shouldGenerateIcon: isBrandContext // Only generate Make.com icon if brand context
            };
        }

        // Special handling for "react" keyword (React.js vs Reaction videos)
        if (keyword === 'react') {
            let techScore = 0;
            let reactionScore = 0;

            // PRIORITY CHECK: Strong reaction patterns that should immediately block atom logo
            const strongReactionPatterns = [
                'react to ',        // space after to avoid "reactor"
                'reacting to ',     // space after to avoid false matches
                'reacting ',        // when "reacting" is a standalone word
                ' reaction',        // space before to avoid "attraction", "interaction", etc.
                'first reaction',
                'initial reaction',
                'live reaction',
                'honest reaction',
                'trailer reaction',
                'reaction video',
                'my reaction',
                'our reaction',
                'his reaction',
                'her reaction'
            ];

            // More precise pattern matching - check if it's at word boundaries
            const hasDirectReactionPattern = strongReactionPatterns.some(pattern => {
                if (pattern.startsWith(' ') || pattern.endsWith(' ')) {
                    // For patterns with spaces, use exact match
                    return normalizedPrompt.includes(pattern);
                } else {
                    // For other patterns, check word boundaries
                    const regex = new RegExp('\\b' + pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'i');
                    return regex.test(normalizedPrompt);
                }
            });

            // If direct reaction patterns found, immediately classify as reaction
            if (hasDirectReactionPattern) {
                return {
                    keyword,
                    isGamingContext: false,
                    isTechContext: false,
                    isReactionContext: true,
                    confidence: 0.95, // High confidence for direct patterns
                    techScore: 0,
                    reactionScore: 10, // Very high score to override any tech indicators
                    recommendedContext: 'reaction',
                    shouldGenerateAtomLogo: false,
                    shouldAvoidAtomLogo: true
                };
            }

            // Check for tech context indicators (React.js)
            if (config.tech) {
                config.tech.forEach(indicator => {
                    if (normalizedPrompt.includes(indicator.toLowerCase())) {
                        techScore += 1;
                    }
                });
            }

            // Check for reaction context indicators (reaction videos)
            if (config.reaction) {
                config.reaction.forEach(indicator => {
                    if (normalizedPrompt.includes(indicator.toLowerCase())) {
                        reactionScore += 1;
                    }
                });
            }

            // Enhanced tech context detection with stricter requirements
            // ONLY consider tech context if NO reaction patterns are present
            const hasStrongTechContext = techScore >= 2 && 
                !hasDirectReactionPattern && 
                normalizedPrompt.includes('react') && (
                    normalizedPrompt.includes('hooks') ||
                    normalizedPrompt.includes('component') ||
                    normalizedPrompt.includes('jsx') ||
                    normalizedPrompt.includes('framework') ||
                    normalizedPrompt.includes('library') ||
                    normalizedPrompt.includes('development') ||
                    normalizedPrompt.includes('programming') ||
                    (normalizedPrompt.includes('tutorial') && !normalizedPrompt.includes('react to') && !normalizedPrompt.includes('reacting')) ||
                    (normalizedPrompt.includes('vs') && (normalizedPrompt.includes('vue') || normalizedPrompt.includes('angular')))
                );

            // Enhanced reaction context detection
            const hasStrongReactionContext = reactionScore >= 1 || (
                normalizedPrompt.includes('video') || 
                normalizedPrompt.includes('watching') || 
                normalizedPrompt.includes('trailer') || 
                normalizedPrompt.includes('movie') || 
                normalizedPrompt.includes('first time') ||
                normalizedPrompt.includes('shocked') ||
                normalizedPrompt.includes('surprised') ||
                normalizedPrompt.includes('emotional')
            );

            let isTechContext = false;
            let isReactionContext = false;
            let confidence = 0;
            let recommendedContext = 'unknown';

            // Stricter classification: Only tech if VERY clear tech context
            if (hasStrongTechContext && !hasStrongReactionContext) {
                isTechContext = true;
                recommendedContext = 'tech';
                confidence = techScore / (techScore + reactionScore + 1);
            } else if (hasStrongReactionContext) {
                // Prefer reaction context when in doubt
                isReactionContext = true;
                recommendedContext = 'reaction';
                confidence = Math.max(0.7, reactionScore / (techScore + reactionScore + 1));
            } else {
                // Default to unknown/neutral - avoid atom logo
                confidence = 0;
            }

            return {
                keyword,
                isGamingContext: false, // "react" is never gaming-related
                isTechContext,
                isReactionContext,
                confidence,
                techScore,
                reactionScore,
                recommendedContext,
                shouldGenerateAtomLogo: isTechContext, // Only generate React atom logo if tech context
                shouldAvoidAtomLogo: isReactionContext || confidence === 0 // Avoid React logo for reaction videos or unknown context
            };
        }

        // Standard handling for other keywords
        if (config.gaming) {
            config.gaming.forEach(indicator => {
                if (normalizedPrompt.includes(indicator.toLowerCase())) {
                    gamingScore += 1;
                }
            });
        }

        if (config.nonGaming) {
            config.nonGaming.forEach(indicator => {
                if (normalizedPrompt.includes(indicator.toLowerCase())) {
                    nonGamingScore += 1;
                }
            });
        }

        const isGamingContext = gamingScore > nonGamingScore;
        const confidence = Math.max(gamingScore, nonGamingScore) / (gamingScore + nonGamingScore + 1);

        return {
            keyword,
            isGamingContext,
            confidence,
            gamingScore,
            nonGamingScore,
            recommendedContext: isGamingContext ? 'gaming' : 'non-gaming'
        };
    });

    // Generate recommendations early to avoid reference issues
    const recommendations = [];
    
    // Check for tech logo issues
    Object.keys(TECH_LOGO_MAPPINGS).forEach(techName => {
        if (normalizedPrompt.includes(techName)) {
            const mapping = TECH_LOGO_MAPPINGS[techName];
            
            // Special handling for React: only add logo correction if it's in tech context
            if (techName === 'react') {
                const reactAnalysis = keywordAnalysis.find(k => k.keyword === 'react');
                if (reactAnalysis && reactAnalysis.isTechContext) {
                    recommendations.push({
                        type: 'logo_correction',
                        technology: techName,
                        correctLogo: mapping.correctLogo,
                        description: mapping.description,
                        context: 'tech'
                    });
                } else if (reactAnalysis && reactAnalysis.isReactionContext) {
                    recommendations.push({
                        type: 'logo_avoidance',
                        technology: techName,
                        message: 'This appears to be about reaction videos, not React.js - avoid using the React atom logo',
                        context: 'reaction'
                    });
                }
            } else {
                recommendations.push({
                    type: 'logo_correction',
                    technology: techName,
                    correctLogo: mapping.correctLogo,
                    description: mapping.description
                });
            }
        }
    });

    // Check for infinity symbol confusion with React
    if (normalizedPrompt.includes('infinity') && !normalizedPrompt.includes('react')) {
        recommendations.push({
            type: 'symbol_clarification',
            message: 'Infinity symbol detected - ensure this is not confused with React logo'
        });
    }

    // === EXPLICIT CALL OF DUTY CHECK ===
    // Special handling for Call of Duty: Only allow if explicit phrases are present
    const hasExplicitCOD = hasExplicitCallOfDutyPhrases(normalizedPrompt);
    const containsCODKeyword = normalizedPrompt.includes('cod') || normalizedPrompt.includes('call of duty');
    
    // If prompt contains COD-related keywords but no explicit phrases, block gaming logic
    if (containsCODKeyword && !hasExplicitCOD) {
        // Force non-gaming classification for ambiguous COD usage
        const forcedNonGaming = true;
        return {
            primaryContext: primaryContext === 'gaming' ? 'tech' : primaryContext, // Default to tech if was gaming
            confidence: maxScore / (words.length + 1),
            isGaming: false,
            contextScores,
            ambiguousKeywords: keywordAnalysis.map(analysis => ({
                ...analysis,
                isGamingContext: analysis.keyword === 'cod' ? false : analysis.isGamingContext,
                recommendedContext: analysis.keyword === 'cod' ? 'non-gaming' : analysis.recommendedContext
            })),
            recommendations,
            shouldApplyGamingLogic: false,
            debugInfo: {
                hasStrongNonGamingContext: true,
                hasStrongNonGamingKeywords: true,
                maxScore,
                keywordAnalysisResults: keywordAnalysis,
                explicitCODCheck: { hasExplicitCOD, containsCODKeyword, forcedNonGaming }
            }
        };
    }

    // === IMPROVED GAMING LOGIC DETERMINATION ===
    // Strong non-gaming contexts should override ambiguous keywords
    const strongNonGamingContexts = ['tech', 'business', 'health', 'education'];
    const hasStrongNonGamingContext = strongNonGamingContexts.includes(primaryContext) && contextScores[primaryContext] >= 2;
    
    // Check if ambiguous keywords have strong non-gaming indicators
    const hasStrongNonGamingKeywords = keywordAnalysis.some(analysis => 
        !analysis.isGamingContext && analysis.confidence > 0.6
    );
    
    // Only consider gaming if:
    // 1. Primary context is gaming AND has multiple indicators, OR
    // 2. Ambiguous keywords strongly suggest gaming AND no strong non-gaming context, OR
    // 3. Explicit COD phrases are present (special case)
    let isGaming = (primaryContext === 'gaming' && contextScores.gaming >= 2) ||
                   (keywordAnalysis.some(analysis => analysis.isGamingContext && analysis.confidence > 0.7) && 
                    !hasStrongNonGamingContext && !hasStrongNonGamingKeywords) ||
                   hasExplicitCOD; // Allow gaming if explicit COD phrases are present
    
    // Additional COD-specific check: if any COD keywords detected but no explicit phrases, block gaming
    if (isGaming && containsCODKeyword && !hasExplicitCOD) {
        isGaming = false;
    }



    return {
        primaryContext,
        confidence: maxScore / (words.length + 1), // Normalize by prompt length
        isGaming,
        contextScores,
        ambiguousKeywords: keywordAnalysis,
        recommendations,
        shouldApplyGamingLogic: isGaming,
        debugInfo: {
            hasStrongNonGamingContext,
            hasStrongNonGamingKeywords,
            maxScore,
            keywordAnalysisResults: keywordAnalysis,
            explicitCODCheck: { hasExplicitCOD, containsCODKeyword, forcedNonGaming: false }
        }
    };
}

/**
 * Determines if gaming-specific logic should be applied based on context analysis
 * 
 * @param {string} prompt - The user's input prompt
 * @returns {boolean} True if gaming logic should be applied
 */
export function shouldApplyGamingLogic(prompt) {
    const analysis = analyzePromptContext(prompt);
    return analysis.shouldApplyGamingLogic;
}

/**
 * Gets the correct logo description for a technology/brand
 * 
 * @param {string} techName - Name of the technology/brand
 * @returns {string|null} Correct logo description or null if not found
 */
export function getCorrectLogoDescription(techName) {
    const normalized = techName.toLowerCase();
    const mapping = TECH_LOGO_MAPPINGS[normalized];
    return mapping ? mapping.correctLogo : null;
}

/**
 * Validates and corrects icon rendering instructions based on context
 * 
 * @param {string} prompt - The user's input prompt
 * @param {Array} detectedIcons - Array of detected icon keywords
 * @returns {Object} Corrected icon instructions
 */
export function validateIconRendering(prompt, detectedIcons = []) {
    const analysis = analyzePromptContext(prompt);
    const corrections = [];

    detectedIcons.forEach(icon => {
        const logoDesc = getCorrectLogoDescription(icon);
        if (logoDesc) {
            corrections.push({
                icon,
                correctDescription: logoDesc,
                context: analysis.primaryContext
            });
        }
    });

    return {
        context: analysis.primaryContext,
        isGaming: analysis.isGaming,
        corrections,
        recommendations: analysis.recommendations
    };
}

/**
 * Determines if the word "make" in the prompt refers to the Make.com brand
 * or is just general usage (verb)
 * 
 * @param {string} prompt - The user's input prompt
 * @returns {boolean} True if "make" refers to Make.com brand, false for general usage
 */
export function shouldGenerateMakeIcon(prompt) {
    if (!prompt || typeof prompt !== 'string') return false;
    
    const normalizedPrompt = prompt.toLowerCase().trim();
    
    // If "make" isn't in the prompt, return false
    if (!normalizedPrompt.includes('make')) return false;
    
    const analysis = analyzePromptContext(prompt);
    const makeAnalysis = analysis.ambiguousKeywords.find(k => k.keyword === 'make');
    
    // If "make" was analyzed, use the analysis result
    if (makeAnalysis) {
        return makeAnalysis.shouldGenerateIcon || false;
    }
    
    // Fallback: explicit check for Make.com indicators
    const makeComIndicators = [
        'make.com', 'automation', 'workflow', 'integromat', 'no-code', 
        'zapier', 'integration', 'connector', 'scenario', 'webhook', 
        'api integration', 'automate', 'trigger', 'action'
    ];
    
    return makeComIndicators.some(indicator => normalizedPrompt.includes(indicator));
}

/**
 * Determines if "react" in the prompt refers to React.js (tech) or reaction videos
 * 
 * @param {string} prompt - The user's input prompt
 * @returns {Object} Analysis result with context classification
 */
export function analyzeReactContext(prompt) {
    if (!prompt || typeof prompt !== 'string') {
        return {
            isReactJS: false,
            isReactionVideo: false,
            confidence: 0,
            shouldUseAtomLogo: false,
            context: 'unknown'
        };
    }

    const normalizedPrompt = prompt.toLowerCase().trim();
    
    // If "react" isn't in the prompt, return neutral result
    if (!normalizedPrompt.includes('react')) {
        return {
            isReactJS: false,
            isReactionVideo: false,
            confidence: 0,
            shouldUseAtomLogo: false,
            context: 'none'
        };
    }

    const analysis = analyzePromptContext(prompt);
    const reactAnalysis = analysis.ambiguousKeywords.find(k => k.keyword === 'react');
    
    if (reactAnalysis) {
        return {
            isReactJS: reactAnalysis.isTechContext || false,
            isReactionVideo: reactAnalysis.isReactionContext || false,
            confidence: reactAnalysis.confidence || 0,
            shouldUseAtomLogo: reactAnalysis.shouldGenerateAtomLogo || false,
            shouldAvoidAtomLogo: reactAnalysis.shouldAvoidAtomLogo || false,
            context: reactAnalysis.recommendedContext || 'unknown',
            techScore: reactAnalysis.techScore || 0,
            reactionScore: reactAnalysis.reactionScore || 0
        };
    }

    // PRIORITY CHECK: Strong reaction patterns that should immediately block atom logo
    const strongReactionPatterns = [
        'react to ',        // space after to avoid "reactor"
        'reacting to ',     // space after to avoid false matches
        'reacting ',        // when "reacting" is a standalone word
        ' reaction',        // space before to avoid "attraction", "interaction", etc.
        'first reaction',
        'initial reaction',
        'live reaction',
        'honest reaction',
        'trailer reaction',
        'reaction video',
        'my reaction',
        'our reaction',
        'his reaction',
        'her reaction'
    ];

    const hasDirectReactionPattern = strongReactionPatterns.some(pattern => 
        normalizedPrompt.includes(pattern)
    );

    // If direct reaction patterns found, immediately classify as reaction
    if (hasDirectReactionPattern) {
        return {
            isReactJS: false,
            isReactionVideo: true,
            confidence: 0.95,
            shouldUseAtomLogo: false,
            shouldAvoidAtomLogo: true,
            context: 'reaction',
            techScore: 0,
            reactionScore: 10
        };
    }

    // Fallback analysis if not caught by main analysis
    const techIndicators = ['js', 'javascript', 'component', 'jsx', 'hooks', 'props', 'state', 'vue', 'angular', 'frontend', 'development', 'programming', 'vs', 'comparison', 'tutorial', 'code', 'coding', 'framework', 'library'];
    const reactionIndicators = ['trailer', 'video', 'watching', 'shocked', 'surprised', 'emotional', 'first time', 'funny', 'meme', 'movie', 'film', 'episode'];

    let techScore = 0;
    let reactionScore = 0;

    // More precise scoring - give higher weight to strong indicators
    techIndicators.forEach(indicator => {
        if (normalizedPrompt.includes(indicator)) {
            // Strong tech indicators get more weight
            if (['jsx', 'hooks', 'component', 'javascript', 'programming', 'development', 'framework', 'library'].includes(indicator)) {
                techScore += 2;
            } else {
                techScore += 1;
            }
        }
    });

    reactionIndicators.forEach(indicator => {
        if (normalizedPrompt.includes(indicator)) {
            // Strong reaction indicators get more weight
            if (['trailer', 'watching', 'first time', 'movie', 'film', 'episode'].includes(indicator)) {
                reactionScore += 2;
            } else {
                reactionScore += 1;
            }
        }
    });

    // Enhanced tech context detection with stricter requirements
    // ONLY consider tech context if NO reaction patterns are present
    const hasStrongTechContext = techScore >= 2 && 
        !hasDirectReactionPattern && 
        normalizedPrompt.includes('react') && (
            normalizedPrompt.includes('hooks') ||
            normalizedPrompt.includes('component') ||
            normalizedPrompt.includes('jsx') ||
            normalizedPrompt.includes('framework') ||
            normalizedPrompt.includes('library') ||
            normalizedPrompt.includes('development') ||
            normalizedPrompt.includes('programming') ||
            (normalizedPrompt.includes('tutorial') && !normalizedPrompt.includes('react to') && !normalizedPrompt.includes('reacting')) ||
            (normalizedPrompt.includes('vs') && (normalizedPrompt.includes('vue') || normalizedPrompt.includes('angular')))
        );

    // Enhanced reaction context detection
    const hasStrongReactionContext = reactionScore >= 1 || (
        normalizedPrompt.includes('video') || 
        normalizedPrompt.includes('watching') || 
        normalizedPrompt.includes('trailer') || 
        normalizedPrompt.includes('movie') || 
        normalizedPrompt.includes('first time') ||
        normalizedPrompt.includes('shocked') ||
        normalizedPrompt.includes('surprised') ||
        normalizedPrompt.includes('emotional')
    );

    let isTechContext = false;
    let isReactionContext = false;
    let confidence = 0;

    // Stricter classification: Only tech if VERY clear tech context
    if (hasStrongTechContext && !hasStrongReactionContext) {
        isTechContext = true;
        confidence = techScore / (techScore + reactionScore + 1);
    } else if (hasStrongReactionContext) {
        // Prefer reaction context when in doubt
        isReactionContext = true;
        confidence = Math.max(0.7, reactionScore / (techScore + reactionScore + 1));
    } else {
        // Default to unknown/neutral - avoid atom logo
        confidence = 0;
    }

    return {
        isReactJS: isTechContext,
        isReactionVideo: isReactionContext,
        confidence,
        shouldUseAtomLogo: isTechContext,
        shouldAvoidAtomLogo: isReactionContext || confidence === 0, // Avoid atom logo for reactions or unknown context
        context: isTechContext ? 'tech' : (isReactionContext ? 'reaction' : 'unknown'),
        techScore,
        reactionScore
    };
}

/**
 * Filters detected brands based on context analysis to prevent 
 * false positive icon generation
 * 
 * @param {Array} detectedBrands - Array of brand objects from detectBrandLogos
 * @param {string} prompt - The original user prompt
 * @returns {Array} Filtered array of brands that should generate icons
 */
export function filterBrandsForIconGeneration(detectedBrands, prompt) {
    if (!detectedBrands || !Array.isArray(detectedBrands)) return [];
    
    return detectedBrands.filter(brand => {
        const brandName = brand.name.toLowerCase();
        
        // Special handling for "make" brand
        if (brandName === 'make' || brandName === 'make.com') {
            return shouldGenerateMakeIcon(prompt);
        }
        
        // For other brands, use standard logic (allow by default)
        return true;
    });
}

export default {
    analyzePromptContext,
    shouldApplyGamingLogic,
    getCorrectLogoDescription,
    validateIconRendering,
    hasExplicitCallOfDutyPhrases,
    shouldGenerateMakeIcon,
    filterBrandsForIconGeneration,
    AMBIGUOUS_KEYWORDS,
    CONTEXT_INDICATORS,
    TECH_LOGO_MAPPINGS,
    EXPLICIT_COD_PHRASES
}; 