import React, { useState, useRef, useEffect } from 'react'

export const AvatarUploadModal = ({ 
    isOpen, 
    onClose, 
    onAvatarUpload, 
    avatarFile, 
    setAvatarFile, 
    avatarPreview, 
    setAvatarPreview 
}) => {
    const [isDragOver, setIsDragOver] = useState(false);
    const [isUploading, setIsUploading] = useState(false);
    const [uploadError, setUploadError] = useState(null);
    const fileInputRef = useRef(null);
    const dragCounterRef = useRef(0);

    // File validation
    const validateFile = (file) => {
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!validTypes.includes(file.type)) {
            return 'Please select a valid image file (JPG, PNG, or WebP)';
        }

        if (file.size > maxSize) {
            return 'File size must be less than 5MB';
        }

        return null;
    };

    // Handle file selection
    const handleFileSelect = (file) => {
        const error = validateFile(file);
        if (error) {
            setUploadError(error);
            return;
        }

        setUploadError(null);
        setAvatarFile(file);

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            setAvatarPreview(e.target.result);
        };
        reader.onerror = () => {
            setUploadError('Failed to read file');
        };
        reader.readAsDataURL(file);
    };

    // Handle file input change
    const handleFileInputChange = (e) => {
        const file = e.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
        // Reset input value to allow selecting the same file again
        if (e.target) {
            e.target.value = '';
        }
    };

    // Improved drag and drop handlers with proper counter management
    const handleDragEnter = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dragCounterRef.current++;
        if (e.dataTransfer?.items && e.dataTransfer.items.length > 0) {
            setIsDragOver(true);
        }
    };

    const handleDragLeave = (e) => {
        e.preventDefault();
        e.stopPropagation();
        dragCounterRef.current--;
        if (dragCounterRef.current === 0) {
            setIsDragOver(false);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        e.stopPropagation();
    };

    const handleDrop = (e) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);
        dragCounterRef.current = 0;
        
        const files = e.dataTransfer?.files;
        if (files && files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    // Handle upload
    const handleUpload = async () => {
        if (!avatarFile) return;

        setIsUploading(true);
        setUploadError(null);

        try {
            await onAvatarUpload(avatarFile);
            onClose();
        } catch (error) {
            setUploadError(error.message || 'Failed to upload avatar');
        } finally {
            setIsUploading(false);
        }
    };

    // Handle modal close
    const handleClose = () => {
        if (isUploading) return;
        setAvatarFile(null);
        setAvatarPreview(null);
        setUploadError(null);
        setIsDragOver(false);
        dragCounterRef.current = 0;
        onClose();
    };

    // Handle backdrop click
    const handleBackdropClick = (e) => {
        if (e.target === e.currentTarget) {
            handleClose();
        }
    };

    // Handle escape key and cleanup
    useEffect(() => {
        const handleEscape = (e) => {
            if (e.key === 'Escape' && isOpen) {
                handleClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
            // Reset drag counter on cleanup
            dragCounterRef.current = 0;
            setIsDragOver(false);
        };
    }, [isOpen]);

    // Reset states when modal opens
    useEffect(() => {
        if (isOpen) {
            setUploadError(null);
            setIsDragOver(false);
            dragCounterRef.current = 0;
        }
    }, [isOpen]);

    if (!isOpen) return null;

    return React.createElement('div', {
        className: 'fixed inset-0 z-50 flex items-center justify-center p-4',
        onClick: handleBackdropClick,
        style: {
            animation: 'fadeIn 300ms cubic-bezier(0.165, 0.84, 0.44, 1) forwards'
        }
    },
        // Backdrop
        React.createElement('div', {
            className: 'absolute inset-0 bg-black/60 backdrop-blur-sm',
            style: {
                animation: 'fadeIn 300ms ease-out forwards'
            }
        }),
        
        // Modal Content
        React.createElement('div', {
            className: 'relative bg-gray-800 border border-gray-700 rounded-2xl p-6 w-full max-w-md mx-4',
            style: {
                animation: 'modalSlideIn 300ms cubic-bezier(0.165, 0.84, 0.44, 1) forwards'
            }
        },
            // Header
            React.createElement('div', {
                className: 'flex items-center justify-between mb-6'
            },
                React.createElement('h2', {
                    className: 'text-xl font-bold text-white'
                }, 'Upload Avatar'),
                React.createElement('button', {
                    className: 'w-8 h-8 rounded-full flex items-center justify-center text-gray-400 hover:bg-gray-700 hover:text-white transition-colors',
                    onClick: handleClose,
                    disabled: isUploading,
                    'aria-label': 'Close modal'
                },
                    // Close X icon (static SVG)
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        viewBox: '0 0 24 24',
                        fill: 'none',
                        stroke: 'currentColor',
                        strokeWidth: '2',
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        className: 'w-5 h-5'
                    },
                        React.createElement('line', { x1: '18', y1: '6', x2: '6', y2: '18' }),
                        React.createElement('line', { x1: '6', y1: '6', x2: '18', y2: '18' })
                    )
                )
            ),

            // Upload Area
            React.createElement('div', {
                className: `border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer ${
                    isDragOver 
                        ? 'border-purple-500 bg-purple-500/10' 
                        : 'border-gray-600 hover:border-gray-500'
                }`,
                onDragEnter: handleDragEnter,
                onDragLeave: handleDragLeave,
                onDragOver: handleDragOver,
                onDrop: handleDrop,
                onClick: () => {
                    if (fileInputRef.current && !isUploading) {
                        fileInputRef.current.click();
                    }
                }
            },
                avatarPreview ? (
                    // Preview
                    React.createElement('div', {
                        className: 'space-y-4'
                    },
                        React.createElement('div', {
                            className: 'w-24 h-24 mx-auto rounded-full overflow-hidden border-4 border-purple-500'
                        },
                            React.createElement('img', {
                                src: avatarPreview,
                                alt: 'Avatar Preview',
                                className: 'w-full h-full object-cover'
                            })
                        ),
                        React.createElement('p', {
                            className: 'text-sm text-gray-300'
                        }, 'Click to change or drag a new image')
                    )
                ) : (
                    // Upload prompt
                    React.createElement('div', {
                        className: 'space-y-4'
                    },
                        React.createElement('div', {
                            className: 'w-16 h-16 mx-auto rounded-full bg-gray-700 flex items-center justify-center'
                        },
                            // Camera icon (static SVG)
                            React.createElement('svg', {
                                xmlns: 'http://www.w3.org/2000/svg',
                                viewBox: '0 0 24 24',
                                fill: 'none',
                                stroke: 'currentColor',
                                strokeWidth: '2',
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                className: 'w-6 h-6 text-gray-400'
                            },
                                React.createElement('path', { d: 'M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z' }),
                                React.createElement('circle', { cx: '12', cy: '13', r: '3' })
                            )
                        ),
                        React.createElement('div', {
                            className: 'space-y-2'
                        },
                            React.createElement('p', {
                                className: 'text-white font-medium'
                            }, 'Drop your image here'),
                            React.createElement('p', {
                                className: 'text-sm text-gray-400'
                            }, 'or click to browse'),
                            React.createElement('p', {
                                className: 'text-xs text-gray-500'
                            }, 'JPG, PNG, WebP • Max 5MB')
                        )
                    )
                ),
                
                // Hidden file input
                React.createElement('input', {
                    ref: fileInputRef,
                    type: 'file',
                    accept: 'image/jpeg,image/png,image/webp',
                    onChange: handleFileInputChange,
                    className: 'hidden',
                    disabled: isUploading
                })
            ),

            // Error message
            uploadError && React.createElement('div', {
                className: 'mt-4 p-3 bg-red-900/30 border border-red-700/50 rounded-lg'
            },
                React.createElement('div', {
                    className: 'flex items-center gap-2 text-red-400 text-sm'
                },
                    // Warning triangle icon (static SVG)
                    React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        viewBox: '0 0 24 24',
                        fill: 'none',
                        stroke: 'currentColor',
                        strokeWidth: '2',
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        className: 'w-4 h-4 flex-shrink-0'
                    },
                        React.createElement('path', { d: 'M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z' }),
                        React.createElement('line', { x1: '12', y1: '9', x2: '12', y2: '13' }),
                        React.createElement('line', { x1: '12', y1: '17', x2: '12.01', y2: '17' })
                    ),
                    uploadError
                )
            ),

            // Actions
            React.createElement('div', {
                className: 'flex gap-3 mt-6'
            },
                React.createElement('button', {
                    onClick: handleUpload,
                    disabled: !avatarFile || isUploading,
                    className: 'flex-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-700 disabled:text-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2'
                },
                    isUploading && React.createElement('svg', {
                        xmlns: 'http://www.w3.org/2000/svg',
                        viewBox: '0 0 24 24',
                        fill: 'none',
                        stroke: 'currentColor',
                        strokeWidth: '2',
                        strokeLinecap: 'round',
                        strokeLinejoin: 'round',
                        className: 'w-4 h-4 animate-spin'
                    },
                        React.createElement('path', { d: 'M21 12a9 9 0 11-6.219-8.56' })
                    ),
                    isUploading ? 'Uploading...' : 'Upload Avatar'
                ),
                React.createElement('button', {
                    onClick: handleClose,
                    disabled: isUploading,
                    className: 'px-4 py-2 bg-gray-700 hover:bg-gray-600 disabled:opacity-50 text-white rounded-lg font-medium transition-all duration-200'
                }, 'Cancel')
            )
        )
    );
};

// Add CSS animations with better error handling
const addModalStyles = () => {
    // Check if styles already exist
    if (document.head.querySelector('style[data-avatar-modal]')) {
        return;
    }

    const style = document.createElement('style');
    style.setAttribute('data-avatar-modal', 'true');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes modalSlideIn {
            from { 
                opacity: 0; 
                transform: scale(0.9) translateY(20px); 
            }
            to { 
                opacity: 1; 
                transform: scale(1) translateY(0); 
            }
        }
    `;

    try {
        document.head.appendChild(style);
    } catch (error) {
        console.warn('Failed to add modal styles:', error);
    }
};

// Initialize styles when module loads
addModalStyles(); 