---
description: 
globs: 
alwaysApply: false
---
gradient-color-picker-replacement
# 🖌️ Rule: Replace Existing Color Pickers with React Gradient Color Picker

description: Install and integrate the `react-gradient-color-picker` package (hxf31891/react-gradient-color-picker) to replace **all current color–selection UIs**. This unifies the experience for both text color selection and the "Solid Color" background option.
ruleType: always

## 📦 Dependency
- Add **react-gradient-color-picker** via npm/yarn (`npm i react-gradient-color-picker`).
- Package automatically bundles CSS, no extra stylesheet imports required.

## 🏗️ Applies To
- /src/components/ControlPanel.jsx               # Font color pickers
- /src/components/background/BackgroundControls.jsx  # Solid color picker in Background Styles modal
- /src/pages/Welcome.jsx (if any inline color pickers remain)

## 🎯 Implementation Tasks
1. **Remove** any existing third-party or custom color-selection components.
2. **Import** the new picker in components that require color input:
   ```javascript
   import GradientColorPicker from 'react-gradient-color-picker';
   ```
3. **Usage Pattern** (Functional Example):
   ```jsx
   <GradientColorPicker
       value={currentColor}            // hex or rgba string
       onChange={(val) => setColor(val)}
       hideEyeDropper                 // optional: hide eyedropper icon
       hideInputs                     // optional: hide individual RGB inputs
   />
   ```
4. **State Sync:**
   - Font color controls should bind to `primaryTextColor` and `secondaryTextColor` state.
   - Solid background picker should bind to `selectedSolidBgColor`.
5. **Accessibility & Theming:**
   - Ensure picker is wrapped in a dark-mode container (`bg-gray-800`, `rounded-lg`, `p-4`).
   - Picker must be keyboard-navigable; if default component lacks tabindex, wrap with focusable div.

## 🧪 QA Checklist
- [ ] Picker renders correctly inside font color section.
- [ ] Picker renders inside "Solid Color" modal tile.
- [ ] Selected color updates preview in real time.
- [ ] No console warnings/errors.
- [ ] Build succeeds (`npm run build`).

## ✨ References
- Demo: <https://gradient-package-demo.web.app/>
- GitHub: <https://github.com/hxf31891/react-gradient-color-picker>
- NPM: <https://www.npmjs.com/package/react-best-gradient-color-picker>

## ⏳ ETA
2–3 hrs including dependency installation, component refactor, and manual QA.


