/* Authentication Pages V2 - MacOS Liquid Glass Effect */
/* A/B Test Version with unified glass design across all auth pages */

/* Main container with liquid glass background */
.auth-glass-container {
    min-height: 100vh;
    background: 
        radial-gradient(ellipse at top left, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(16, 185, 129, 0.06) 0%, transparent 70%),
        #0f172a;
    backdrop-filter: blur(40px);
    position: relative;
    overflow: hidden;
}

/* Animated background particles */
.auth-glass-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 25% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.04) 0%, transparent 50%);
    animation: float-auth 25s ease-in-out infinite;
    pointer-events: none;
}

@keyframes float-auth {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg) scale(1); 
    }
    33% { 
        transform: translateY(-15px) rotate(0.5deg) scale(1.02); 
    }
    66% { 
        transform: translateY(8px) rotate(-0.5deg) scale(0.98); 
    }
}

/* Main glass card for auth forms */
.auth-glass-card {
    background: rgba(255, 255, 255, 0.04);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
    animation: auth-card-entrance 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

@keyframes auth-card-entrance {
    0% { 
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    100% { 
        opacity: 1;
        transform: translateY(0px) scale(1);
    }
}

.auth-glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    animation: shimmer-auth 4s ease-in-out infinite;
}

@keyframes shimmer-auth {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* Logo styling - Increased by 45% */
.auth-glass-logo {
    width: 70%; /* Increased from 150px by 45% */
    height: auto;
    margin: -1rem auto 0.3rem auto; /* Increased negative top margin to shift logo higher up */
    display: none;
    transition: all 0.3s ease;
}

.auth-glass-logo:hover {
    transform: scale(1.05);
}

/* Title and subtitle styling - Title increased by 25% */
.auth-glass-title {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 2.0rem; /* Increased from 2rem by 25% */
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    letter-spacing: -0.025em;
    text-align: center;
}

.auth-glass-subtitle {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #94a3b8;
    margin-bottom: 1.2rem; /* Reduced from 1.5rem to shift up by 20% */
    line-height: 1.5;
    text-align: center;
}

/* Sign In specific slogan */
.auth-glass-slogan {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: #64748b;
    margin-bottom: 1.2rem; /* Reduced from 1.5rem to shift up by 20% */
    text-align: center;
    letter-spacing: 0.025em;
}

/* Input fields with glass effect */
.auth-glass-input-group {
    margin-bottom: 1rem;
    position: relative;
}

.auth-glass-label {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    font-weight: 500;
    color: #e2e8f0;
    margin-bottom: 0.5rem;
    display: block;
    transition: all 0.3s ease;
}

.auth-glass-input-wrapper {
    position: relative;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.auth-glass-input-wrapper:focus-within {
    transform: translateY(-1px);
}

.auth-glass-input {
    width: 100%;
    padding: 0.85rem 0.85rem 0.85rem 2.35rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    outline: none;
}

.auth-glass-input::placeholder {
    color: #64748b;
    transition: all 0.3s ease;
}

.auth-glass-input:focus {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 
        0 0 0 3px rgba(139, 92, 246, 0.1),
        0 4px 12px rgba(139, 92, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.auth-glass-input:focus::placeholder {
    color: #94a3b8;
}

/* Input icons */
.auth-glass-input-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 10;
}

.auth-glass-input-wrapper:focus-within .auth-glass-input-icon {
    color: #8b5cf6;
    transform: translateY(-50%) scale(1.1);
}

/* Password toggle button */
.auth-glass-password-toggle {
    position: absolute;
    right: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 10;
}

.auth-glass-password-toggle:hover {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

/* Error states */
.auth-glass-input.error {
    border-color: rgba(239, 68, 68, 0.5);
    background: rgba(239, 68, 68, 0.05);
}

.auth-glass-input.error:focus {
    box-shadow: 
        0 0 0 3px rgba(239, 68, 68, 0.1),
        0 4px 12px rgba(239, 68, 68, 0.15);
}

.auth-glass-error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    color: #f87171;
    animation: error-fade-in 0.3s ease-out;
}

/* Increase danger circle icon size by 20% in error messages */
.auth-glass-error-message .iconify[data-icon="solar:danger-circle-bold"] {
    font-size: 1.2em; /* 20% larger than the default size */
    flex-shrink: 0;
}

@keyframes error-fade-in {
    0% { opacity: 0; transform: translateY(-5px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* Global error container */
.auth-glass-global-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 12px;
    padding: 0.85rem;
    margin-bottom: 1.25rem;
    backdrop-filter: blur(10px);
    animation: error-shake 0.5s ease-out;
}

@keyframes error-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.auth-glass-global-error-text {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    color: #f87171;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: normal;
    gap: 0.5rem;
}

/* Increase danger circle icon size by 20% in global error messages */
.auth-glass-global-error-text .iconify[data-icon="solar:danger-circle-bold"] {
    font-size: 1.2em; /* 20% larger than the default size */
    flex-shrink: 0;
}

/* CTA Buttons with liquid glass effect */
.auth-glass-cta-btn {
    width: 100%;
    padding: 0.85rem 1.7rem;
    margin-top: 1.5rem; /* Added margin-top to create distance from inputs */
    min-height: 48px; /* Fixed height to prevent layout shifts */
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    background: linear-gradient(135deg, #006FEE 0%, #0056C7 50%, #004BB5 100%);
    border: 1px solid rgba(0, 111, 238, 0.3);
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    box-shadow: 
        0 8px 20px -6px rgba(0, 111, 238, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    outline: none;
    
    /* Perfect centering for all content */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem; /* Consistent gap between spinner and text */
}

/* Loading spinner styling - ensure perfect alignment */
.auth-glass-cta-btn svg.animate-spin {
    width: 1.25rem; /* 20px */
    height: 1.25rem; /* 20px */
    margin: 0; /* Remove any default margins */
    flex-shrink: 0; /* Prevent shrinking */
}

/* Focus state - always visible and consistent */
.auth-glass-cta-btn:focus,
.auth-glass-cta-btn:focus-visible {
    outline: none;
    box-shadow: 
        0 8px 20px -6px rgba(0, 111, 238, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 3px rgba(0, 111, 238, 0.4); /* Focus ring */
}

.auth-glass-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.auth-glass-cta-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 
        0 12px 28px -6px rgba(0, 111, 238, 0.4),
        0 4px 12px -2px rgba(0, 111, 238, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(0, 111, 238, 0.5);
}

/* Hover state when focused - combine focus ring with hover effects */
.auth-glass-cta-btn:hover:focus,
.auth-glass-cta-btn:hover:focus-visible {
    box-shadow: 
        0 12px 28px -6px rgba(0, 111, 238, 0.4),
        0 4px 12px -2px rgba(0, 111, 238, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        0 0 0 3px rgba(0, 111, 238, 0.4); /* Maintain focus ring on hover */
}

.auth-glass-cta-btn:hover::before {
    left: 100%;
}

.auth-glass-cta-btn:active {
    transform: translateY(-1px) scale(1.01);
    transition: all 0.1s ease;
}

.auth-glass-cta-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    min-height: 48px; /* Maintain consistent height when disabled */
    display: flex; /* Ensure flexbox layout is maintained */
    align-items: center;
    justify-content: center;
    gap: 0.5rem; /* Maintain consistent gap */
}

.auth-glass-cta-btn:disabled:hover {
    transform: none;
    box-shadow: 
        0 8px 20px -6px rgba(0, 111, 238, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Loading state specific styling - prevent any layout shifts */
.auth-glass-cta-btn:disabled svg.animate-spin {
    width: 1.25rem;
    height: 1.25rem;
    margin: 0;
    flex-shrink: 0;
}

/* Google button - Updated to lighter neutral shade with shimmer */
.auth-glass-google-btn {
    width: 100%;
    padding: 0.85rem 1.7rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 500;
    color: #1e293b; /* Dark text for better contrast */
    background: #F5F7FA; /* Lighter neutral background */
    border: 1px solid #E3EAF3; /* Subtle border */
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
    position: relative;
    overflow: hidden;
}

/* Google button shimmer effect */
.auth-glass-google-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.auth-glass-google-btn:hover {
    background: #E3EAF3; /* Slightly darker on hover */
    border-color: #D1D9E6; /* Darker border on hover */
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.auth-glass-google-btn:hover::before {
    left: 100%;
}

/* A/B Test: Dark Google button - Original dark styling */
.auth-glass-google-btn-dark {
    width: 100%;
    padding: 0.85rem 1.7rem;
    min-height: 48px; /* Fixed height to prevent layout shifts */
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff; /* White text for dark background */
    background: linear-gradient(135deg, #2a2a2a 0%, #3d3d3d 50%, #252525 100%); /* Original dark gradient */
    border: 1px solid rgba(80, 80, 80, 0.3); /* Dark border */
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.25rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    outline: none;
}

/* Dark Google button shimmer effect */
.auth-glass-google-btn-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.auth-glass-google-btn-dark:hover {
    background: linear-gradient(135deg, #404040 0%, #555555 50%, #383838 100%); /* Lighter dark gradient on hover */
    border-color: rgba(100, 100, 100, 0.4);
    transform: translateY(-1px);
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(100, 100, 100, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.auth-glass-google-btn-dark:hover::before {
    left: 100%;
}

/* Dark Google button active state */
.auth-glass-google-btn-dark:active {
    transform: translateY(0px) scale(0.98);
    box-shadow: 
        0 2px 8px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.1s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Dark Google button focus state */
.auth-glass-google-btn-dark:focus,
.auth-glass-google-btn-dark:focus-visible {
    outline: none;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.5),
        0 0 0 3px rgba(80, 80, 80, 0.6);
}

/* Google button hover + focus state */
.auth-glass-google-btn-dark:hover:focus,
.auth-glass-google-btn-dark:hover:focus-visible {
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(100, 100, 100, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 3px rgba(80, 80, 80, 0.6); /* Maintain focus ring on hover */
}

/* Dark Google button disabled state */
.auth-glass-google-btn-dark:disabled {
    background: #374151;
    border-color: #4B5563;
    color: #9CA3AF;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    opacity: 0.6;
    min-height: 48px; /* Maintain consistent height when disabled */
    display: flex; /* Ensure flexbox layout is maintained */
    align-items: center;
    justify-content: center;
    gap: 0.75rem; /* Maintain consistent gap */
}

.auth-glass-google-btn-dark:disabled::before {
    display: none;
}

.auth-glass-google-btn-dark:disabled svg {
    filter: grayscale(1) opacity(0.5);
}

/* Dark Google button icon styling */
.auth-glass-google-btn-dark svg {
    filter: brightness(1.1);
    transition: filter 0.2s ease;
}

.auth-glass-google-btn-dark:hover svg {
    filter: brightness(1.2) drop-shadow(0 0 4px rgba(255, 255, 255, 0.3));
}

/* A/B Test label styling */
.auth-glass-ab-test-label {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.75rem;
    font-weight: 600;
    color: #64748b;
    text-align: center;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.8;
}

.auth-glass-ab-test-label.light {
    color: #7c3aed;
}

.auth-glass-ab-test-label.dark {
    color: #f59e0b;
}

/* Divider */
.auth-glass-divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
}

.auth-glass-divider::before,
.auth-glass-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
}

.auth-glass-divider-text {
    padding: 0 1rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    color: #64748b;
}

/* Links and secondary actions - Simple fade transition */
.auth-glass-link {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #c5c5c5;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    background: transparent;
    border: none;
    font-size: inherit;
}

.auth-glass-link:hover {
    color: #ffffff;
}

/* Remember me checkbox */
.auth-glass-checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 1rem 0;
}

.auth-glass-checkbox {
    appearance: none;
    width: 1rem;
    height: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.auth-glass-checkbox:checked {
    background: linear-gradient(135deg, #006FEE, #0056C7);
    border-color: #006FEE;
}

.auth-glass-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.auth-glass-checkbox-label {
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    color: #e2e8f0;
    cursor: pointer;
}

/* Removed newtest button styles - no longer needed */

/* Page transitions */
.auth-glass-page-enter {
    animation: page-enter 0.6s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

.auth-glass-page-exit {
    animation: page-exit 0.4s cubic-bezier(0.165, 0.84, 0.44, 1) forwards;
}

@keyframes page-enter {
    0% { 
        opacity: 0;
        transform: translateX(20px);
    }
    100% { 
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes page-exit {
    0% { 
        opacity: 1;
        transform: translateX(0);
    }
    100% { 
        opacity: 0;
        transform: translateX(-20px);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .auth-glass-card {
        margin: 1rem;
        border-radius: 20px;
        padding: 1.7rem 1.3rem;
    }
    
    .auth-glass-title {
        font-size: 2.2rem; /* Increased proportionally from 1.75rem to maintain 25% increase */
    }
    
    .auth-glass-logo {
        width: 181px; /* Increased proportionally from 125px to maintain 45% increase */
        margin: -0.8rem auto 0.4rem auto; /* Increased negative top margin to shift logo higher up on mobile */
        display: none!important;
    }
    
    /* Removed newtest button mobile styles - no longer needed */
}

/* Mobile portrait specific enhancements */
@media (max-width: 576px) and (orientation: portrait) {
    .auth-glass-cta-btn {
        font-size: 1.09375rem; /* 25% larger as per auth button standards */
        padding: 0.85rem 1.5rem;
        margin-top: 1.5rem; /* Ensure distance from inputs on mobile */
        min-height: 50px; /* Slightly larger on mobile for better touch targets */
        letter-spacing: 0.025em;
        display: flex; /* Ensure flexbox layout on mobile */
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .auth-glass-input {
        font-size: 1rem;
        padding: 0.85rem 0.85rem 0.85rem 2.35rem;
    }
    
    .auth-glass-card {
        padding: 1.3rem 0.85rem;
    }
}

/* ================= LIGHT THEME VARIANTS ================= */

/* Light theme container - Override dark theme when body has .auth-light-theme class */
.auth-light-theme .auth-glass-container {
    background: 
        radial-gradient(ellipse at top left, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse at bottom right, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse at center, rgba(16, 185, 129, 0.04) 0%, transparent 70%),
        linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
}

/* Light theme animated background */
.auth-light-theme .auth-glass-container::before {
    background: 
        radial-gradient(circle at 25% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.03) 0%, transparent 50%);
}

/* Light theme glass card */
.auth-light-theme .auth-glass-card {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Light theme shimmer */
.auth-light-theme .auth-glass-card::before {
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);
}

/* Light theme title and text */
.auth-light-theme .auth-glass-title {
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-light-theme .auth-glass-subtitle {
    color: #64748b;
}

.auth-light-theme .auth-glass-slogan {
    color: #475569;
}

/* Light theme labels */
.auth-light-theme .auth-glass-label {
    color: #374151;
}

/* Light theme inputs */
.auth-light-theme .auth-glass-input {
    color: #1f2937;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.auth-light-theme .auth-glass-input::placeholder {
    color: #9ca3af;
}

.auth-light-theme .auth-glass-input:focus {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 
        0 0 0 3px rgba(139, 92, 246, 0.1),
        0 4px 12px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.auth-light-theme .auth-glass-input:focus::placeholder {
    color: #6b7280;
}

/* Light theme input icons */
.auth-light-theme .auth-glass-input-icon {
    color: #9ca3af;
}

.auth-light-theme .auth-glass-input-wrapper:focus-within .auth-glass-input-icon {
    color: #8b5cf6;
}

/* Light theme password toggle */
.auth-light-theme .auth-glass-password-toggle {
    color: #9ca3af;
}

.auth-light-theme .auth-glass-password-toggle:hover {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.05);
}

/* Light theme error states */
.auth-light-theme .auth-glass-input.error {
    border-color: rgba(239, 68, 68, 0.3);
    background: rgba(239, 68, 68, 0.03);
}

.auth-light-theme .auth-glass-input.error:focus {
    box-shadow: 
        0 0 0 3px rgba(239, 68, 68, 0.1),
        0 4px 12px rgba(239, 68, 68, 0.1);
}

.auth-light-theme .auth-glass-error-message {
    color: #dc2626;
}

.auth-light-theme .auth-glass-global-error {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.15);
}

.auth-light-theme .auth-glass-global-error-text {
    color: #dc2626;
}

/* Light theme danger circle icon size increase */
.auth-light-theme .auth-glass-error-message .iconify[data-icon="solar:danger-circle-bold"],
.auth-light-theme .auth-glass-global-error-text .iconify[data-icon="solar:danger-circle-bold"] {
    font-size: 1.2em; /* 20% larger than the default size */
    flex-shrink: 0;
}

/* Light theme CTA button */
.auth-light-theme .auth-glass-cta-btn {
    background: linear-gradient(135deg, #006FEE 0%, #0056C7 50%, #004BB5 100%);
    border: 1px solid rgba(0, 111, 238, 0.2);
    box-shadow: 
        0 8px 20px -6px rgba(0, 111, 238, 0.25),
        0 0 0 1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.auth-light-theme .auth-glass-cta-btn:hover {
    box-shadow: 
        0 12px 28px -6px rgba(0, 111, 238, 0.35),
        0 4px 12px -2px rgba(0, 111, 238, 0.2),
        0 0 0 1px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Light theme Google button - Use the existing light button class */
.auth-light-theme .auth-glass-google-btn-dark {
    /* Override dark button to use light styling */
    color: #1e293b !important;
    background: #F5F7FA !important;
    border: 1px solid #E3EAF3 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.auth-light-theme .auth-glass-google-btn-dark:hover {
    background: #E3EAF3 !important;
    border-color: #D1D9E6 !important;
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.12),
        0 0 0 1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
}

.auth-light-theme .auth-glass-google-btn-dark svg {
    filter: brightness(0.8) !important;
}

.auth-light-theme .auth-glass-google-btn-dark:hover svg {
    filter: brightness(0.7) drop-shadow(0 0 4px rgba(0, 0, 0, 0.1)) !important;
}

/* Light theme divider */
.auth-light-theme .auth-glass-divider::before,
.auth-light-theme .auth-glass-divider::after {
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.auth-light-theme .auth-glass-divider-text {
    color: #6b7280;
}

/* Light theme links */
.auth-light-theme .auth-glass-link {
    color: #475569;
}

.auth-light-theme .auth-glass-link:hover {
    color: #1e293b; /* Darker hover color for light theme */
}

/* Light theme checkbox */
.auth-light-theme .auth-glass-checkbox {
    border: 1px solid rgba(0, 0, 0, 0.2);
    background: rgba(255, 255, 255, 0.8);
}

.auth-light-theme .auth-glass-checkbox:checked {
    background: linear-gradient(135deg, #006FEE, #0056C7);
    border-color: #006FEE;
}

.auth-light-theme .auth-glass-checkbox-label {
    color: #374151;
}

/* Theme Toggle Button */
.auth-theme-toggle {
    position: fixed;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    font-family: 'Geist', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 0.875rem;
    font-weight: 600;
    color: #ffffff;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
    z-index: 9999;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
    user-select: none;
}

.auth-theme-toggle:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.auth-theme-toggle .toggle-icon {
    font-size: 1.125rem;
    transition: transform 0.3s ease;
}

.auth-theme-toggle:hover .toggle-icon {
    transform: scale(1.1);
}

/* Light theme toggle button */
.auth-light-theme .auth-theme-toggle {
    color: #1f2937;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.auth-light-theme .auth-theme-toggle:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Smooth transitions for theme switching */
.auth-glass-container,
.auth-glass-card,
.auth-glass-input,
.auth-glass-cta-btn,
.auth-glass-google-btn-dark,
.auth-theme-toggle {
    transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

/* Mobile responsive adjustments for toggle */
@media (max-width: 768px) {
    .auth-theme-toggle {
        top: 0.75rem;
        right: 0.75rem;
        padding: 0.625rem 0.875rem;
        font-size: 0.8125rem;
    }
    
    .auth-theme-toggle .toggle-icon {
        font-size: 1rem;
    }
} 