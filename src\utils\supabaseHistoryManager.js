import { createClient } from '@supabase/supabase-js';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '../config/supabase.mjs';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

/**
 * Compresses an image blob to a WebP thumbnail.
 * @param {Blob} imageBlob - The original image blob.
 * @param {number} width - The target width.
 * @param {number} height - The target height.
 * @param {number} quality - The WebP quality (0 to 1).
 * @returns {Promise<Blob>} - The compressed WebP blob.
 */
const compressImageToWebP = (imageBlob, width, height, quality = 0.7) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const reader = new FileReader();

        reader.onload = (e) => {
            img.src = e.target.result;
        };
        reader.onerror = reject;
        reader.readAsDataURL(imageBlob);

        img.onload = () => {
            canvas.width = width;
            canvas.height = height;
            
            // Stretch image to fill entire canvas (object-fit: fill behavior)
            // No background - just stretch the image itself
            ctx.drawImage(img, 0, 0, width, height);
            
            canvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Canvas to Blob conversion failed'));
                }
            }, 'image/webp', quality);
        };
        img.onerror = reject;
    });
};


/**
 * Processes full-size image to stretch to 1280x720 without background
 * @param {Blob} imageBlob - The original image blob
 * @returns {Promise<Blob>} - The processed image blob
 */
const processFullSizeImage = (imageBlob) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const reader = new FileReader();

        reader.onload = (e) => {
            img.src = e.target.result;
        };
        reader.onerror = reject;
        reader.readAsDataURL(imageBlob);

        img.onload = () => {
            // Set canvas to exactly 1280x720
            canvas.width = 1280;
            canvas.height = 720;
            
            // Enable high-quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            
            // Stretch image to fill entire canvas (object-fit: fill behavior)
            // This will distort the image to fit exactly 1280x720 - no background needed
            ctx.drawImage(img, 0, 0, 1280, 720);
            
            canvas.toBlob((blob) => {
                if (blob) {
                    resolve(blob);
                } else {
                    reject(new Error('Canvas to Blob conversion failed'));
                }
            }, 'image/png'); // PNG to preserve transparency
        };
        img.onerror = reject;
    });
};

/**
 * Saves a generated thumbnail to Supabase.
 * @param {object} generationData - The data for the generation.
 * @param {Blob} fullImageBlob - The full-size image blob.
 * @returns {Promise<object>} - The saved data.
 */
export const saveGeneration = async (generationData, fullImageBlob) => {
    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not logged in.');

        const timestamp = Date.now();
        const userId = user.id;

        // 1. Process full-size image to stretch to 1280x720 with solid background
        const processedFullImageBlob = await processFullSizeImage(fullImageBlob);

        // 2. Create compressed thumbnail from processed image
        const thumbnailBlob = await compressImageToWebP(processedFullImageBlob, 640, 360);

        // 3. Upload images to Supabase Storage
        const fullImageFileName = `${userId}/${timestamp}_full.png`;
        const thumbImageFileName = `${userId}/${timestamp}_thumb.webp`;

        const { data: fullImageData, error: fullImageError } = await supabase.storage
            .from('thumbnails')
            .upload(fullImageFileName, processedFullImageBlob, { contentType: 'image/png' });

        if (fullImageError) throw fullImageError;

        const { data: thumbImageData, error: thumbImageError } = await supabase.storage
            .from('thumbnails')
            .upload(thumbImageFileName, thumbnailBlob, { contentType: 'image/webp' });

        if (thumbImageError) throw thumbImageError;
        
        // 3. Get public URLs
        const { data: { publicUrl: full_image_url } } = supabase.storage.from('thumbnails').getPublicUrl(fullImageFileName);
        const { data: { publicUrl: thumbnail_url } } = supabase.storage.from('thumbnails').getPublicUrl(thumbImageFileName);


        // 4. Save metadata to database
        const historyData = {
            user_id: userId,
            title: generationData.title || 'Untitled',
            prompt: generationData.prompt,
            final_prompt: generationData.final_prompt,
            quality: generationData.quality || 'HD',
            credits: generationData.credits || 1,
            thumbnail_url,
            full_image_url,
            metadata: generationData.metadata || {},
        };

        const { data, error } = await supabase
            .from('generation_history')
            .insert([historyData])
            .select();

        if (error) throw error;
        
        console.log('✅ Generation saved to Supabase successfully', data[0]);
        return data[0];

    } catch (error) {
        console.error('🚨 Error saving generation to Supabase:', error.message);
        // Fallback to localStorage could be implemented here
        return null;
    }
};

/**
 * Fetches generation history for the current user.
 * @returns {Promise<Array>} - The list of generation history items.
 */
export const getHistory = async () => {
    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) return [];

        const { data, error } = await supabase
            .from('generation_history')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

        if (error) throw error;

        console.log(`📦 Generation history loaded from Supabase: ${data.length} items`);
        return data;
    } catch (error) {
        console.error('🚨 Error loading generation history from Supabase:', error.message);
        return [];
    }
};

/**
 * Deletes a generation history item and its associated images.
 * @param {string} historyId - The ID of the history item to delete.
 * @param {string} fullImageUrl - The URL of the full image.
 * @param {string} thumbImageUrl - The URL of the thumbnail image.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
export const deleteHistoryItem = async (historyId, fullImageUrl, thumbImageUrl) => {
    try {
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new Error('User not logged in.');

        // Extract file paths from URLs
        const fullImagePath = new URL(fullImageUrl).pathname.split('/thumbnails/')[1];
        const thumbImagePath = new URL(thumbImageUrl).pathname.split('/thumbnails/')[1];

        // 1. Delete images from storage
        const { error: fullImageError } = await supabase.storage
            .from('thumbnails')
            .remove([fullImagePath]);
        
        if (fullImageError) console.error("Error deleting full image:", fullImageError.message);


        const { error: thumbImageError } = await supabase.storage
            .from('thumbnails')
            .remove([thumbImagePath]);

        if (thumbImageError) console.error("Error deleting thumbnail image:", thumbImageError.message);

        // 2. Delete item from database
        const { error: dbError } = await supabase
            .from('generation_history')
            .delete()
            .eq('id', historyId)
            .eq('user_id', user.id);

        if (dbError) throw dbError;

        console.log(`🗑️ Deleted history item ${historyId}`);
        return true;

    } catch (error) {
        console.error('🚨 Error deleting history item:', error.message);
        return false;
    }
}; 