const React = window.React;
const { useState, useEffect } = React;

// Import styles
import '../styles/user-profile-section.css';

export const UserProfileSection = ({ 
    user, 
    onSignOut, 
    onNavigateToProfile,
    onUpgradeClick 
}) => {
    const [userInfo, setUserInfo] = useState({
        name: '<PERSON>',
        email: '<EMAIL>',
        credits: 750,
        maxCredits: 1000,
        plan: 'free'
    });

    // Update user info when user prop changes
    useEffect(() => {
        if (user) {
            setUserInfo({
                name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
                email: user.email || '<EMAIL>',
                credits: user.app_metadata?.credits || 750,
                maxCredits: user.app_metadata?.max_credits || 1000,
                plan: user.app_metadata?.plan || 'free'
            });
        }
    }, [user]);

    const creditsPercentage = (userInfo.credits / userInfo.maxCredits) * 100;

    return React.createElement('div', {
        className: 'user-profile-main-container',
        id: 'user-profile-dashboard'
    },
        // Profile Card Container
        React.createElement('div', {
            className: 'user-profile-card bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl overflow-hidden max-w-sm mx-auto',
            id: 'profile-card-wrapper',
            style: { width: '320px' }
        },
            // Welcome Banner Section
            React.createElement('div', {
                className: 'profile-welcome-banner bg-gradient-to-br from-purple-600/20 via-blue-600/20 to-cyan-600/20 p-6 relative overflow-hidden',
                id: 'profile-welcome-header'
            },
                // Banner Background Pattern
                React.createElement('div', {
                    className: 'banner-pattern absolute inset-0 opacity-10',
                    style: {
                        backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.3) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.3) 0%, transparent 50%)',
                        backgroundSize: '100px 100px'
                    }
                }),
                // Banner Content
                React.createElement('div', {
                    className: 'banner-content relative z-10'
                },
                    React.createElement('div', {
                        className: 'banner-greeting flex items-center gap-3 mb-2'
                    },
                        React.createElement('span', {
                            className: 'iconify text-yellow-400',
                            'data-icon': 'solar:star-linear',
                            style: { fontSize: '20px' }
                        }),
                        React.createElement('span', {
                            className: 'text-white/90 text-sm font-medium'
                        }, 'Welcome back!')
                    ),
                    React.createElement('div', {
                        className: 'banner-user-info flex items-center gap-3'
                    },
                        // Enhanced User Avatar
                        React.createElement('div', {
                            className: 'profile-avatar-container w-14 h-14 rounded-full bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 p-0.5',
                            id: 'user-avatar-wrapper'
                        },
                            React.createElement('div', {
                                className: 'profile-avatar w-full h-full rounded-full bg-gray-800 flex items-center justify-center'
                            },
                                React.createElement('span', {
                                    className: 'iconify text-white',
                                    'data-icon': 'solar:user-linear',
                                    style: { fontSize: '22px' }
                                })
                            )
                        ),
                        // User Details
                        React.createElement('div', {
                            className: 'profile-user-details flex-1 min-w-0',
                            id: 'user-details-section'
                        },
                            React.createElement('h3', {
                                className: 'profile-user-name text-white font-bold text-lg truncate'
                            }, userInfo.name),
                            React.createElement('p', {
                                className: 'profile-user-email text-blue-200/80 text-sm truncate'
                            }, userInfo.email)
                        )
                    )
                )
            ),

            // Profile Content Section
            React.createElement('div', {
                className: 'profile-content-section p-6',
                id: 'profile-main-content'
            },
                // Plan Status Banner
                React.createElement('div', {
                    className: 'plan-status-banner bg-gradient-to-r from-emerald-900/30 to-blue-900/30 border border-emerald-500/20 rounded-xl p-4 mb-6',
                    id: 'user-plan-status'
                },
                    React.createElement('div', {
                        className: 'plan-header flex items-center justify-between mb-2'
                    },
                        React.createElement('div', {
                            className: 'plan-info flex items-center gap-2'
                        },
                            React.createElement('span', {
                                className: 'iconify text-emerald-400',
                                'data-icon': 'solar:shield-check-linear',
                                style: { fontSize: '18px' }
                            }),
                            React.createElement('span', {
                                className: 'text-emerald-400 font-semibold text-sm uppercase tracking-wider'
                            }, `${userInfo.plan} Plan`)
                        ),
                        React.createElement('span', {
                            className: 'iconify text-gray-400',
                            'data-icon': 'solar:settings-linear',
                            style: { fontSize: '16px' }
                        })
                    ),
                    React.createElement('p', {
                        className: 'plan-description text-gray-300 text-xs'
                    }, 'Your current subscription plan and benefits')
                ),

                // Upgrade to Pro Section - Only shown for 'free' users
                userInfo.plan === 'free' && React.createElement('div', {
                    className: 'upgrade-promotion-section bg-gradient-to-r from-purple-900/30 to-blue-900/30 border border-purple-500/20 rounded-xl p-4 mb-6 relative overflow-hidden',
                    id: 'upgrade-promotion-card'
                },
                    // Upgrade Background Effect
                    React.createElement('div', {
                        className: 'upgrade-glow absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full blur-xl'
                    }),
                    React.createElement('div', {
                        className: 'upgrade-content relative z-10'
                    },
                        React.createElement('div', {
                            className: 'upgrade-header flex items-center gap-2 mb-3'
                        },
                            React.createElement('span', {
                                className: 'iconify text-yellow-400',
                                'data-icon': 'solar:crown-linear',
                                style: { fontSize: '22px' }
                            }),
                            React.createElement('span', {
                                className: 'text-transparent bg-gradient-to-r from-yellow-400 to-purple-400 bg-clip-text font-bold text-base'
                            }, 'Upgrade to Pro')
                        ),
                        React.createElement('div', {
                            className: 'upgrade-benefits flex items-center gap-2 mb-3'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '16px' }
                            }),
                            React.createElement('span', {
                                className: 'text-gray-300 text-sm'
                            }, 'Unlimited AI generations')
                        ),
                        React.createElement('div', {
                            className: 'upgrade-benefits flex items-center gap-2 mb-4'
                        },
                            React.createElement('span', {
                                className: 'iconify text-green-400',
                                'data-icon': 'solar:check-circle-linear',
                                style: { fontSize: '16px' }
                            }),
                            React.createElement('span', {
                                className: 'text-gray-300 text-sm'
                            }, 'Premium templates & features')
                        ),
                        React.createElement('button', {
                            className: 'upgrade-action-btn w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white py-2.5 px-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-lg',
                            id: 'upgrade-now-button',
                            onClick: onUpgradeClick
                        }, 'Upgrade Now')
                    )
                ),

                // Available Credits Section
                React.createElement('div', {
                    className: 'credits-tracking-section mb-6',
                    id: 'user-credits-tracker'
                },
                    React.createElement('div', {
                        className: 'credits-header-section flex items-center justify-between mb-3'
                    },
                        React.createElement('div', {
                            className: 'credits-label-group flex items-center gap-2'
                        },
                            React.createElement('span', {
                                className: 'iconify text-cyan-400',
                                'data-icon': 'solar:dollar-linear',
                                style: { fontSize: '16px' }
                            }),
                            React.createElement('span', {
                                className: 'text-gray-400 text-sm font-medium'
                            }, 'Available Credits')
                        ),
                        React.createElement('span', {
                            className: 'credits-counter text-white font-bold text-lg'
                        }, `${userInfo.credits}/${userInfo.maxCredits}`)
                    ),
                    // Enhanced Credits Progress Bar
                    React.createElement('div', {
                        className: 'credits-progress-container w-full bg-gray-700/50 rounded-full h-3 overflow-hidden border border-gray-600/30',
                        id: 'credits-progress-bar-container'
                    },
                        React.createElement('div', {
                            className: 'credits-progress-fill bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-500 h-full rounded-full transition-all duration-700 ease-out relative overflow-hidden',
                            style: { width: `${creditsPercentage}%` }
                        },
                            React.createElement('div', {
                                className: 'progress-shimmer absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse'
                            })
                        )
                    ),
                    React.createElement('div', {
                        className: 'credits-status-text text-center mt-2'
                    },
                        React.createElement('span', {
                            className: `text-xs font-medium ${creditsPercentage > 20 ? 'text-green-400' : 'text-yellow-400'}`
                        }, creditsPercentage > 20 ? 'You have plenty of credits' : 'Consider upgrading for more credits')
                    )
                ),

                // Action Buttons Section
                React.createElement('div', {
                    className: 'profile-actions-section space-y-2',
                    id: 'user-action-buttons'
                },
                    // View Profile Button
                    React.createElement('button', {
                        className: 'profile-action-btn w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-700/50 transition-all duration-200 text-left group border border-gray-600/30 hover:border-gray-500/50',
                        id: 'view-profile-button',
                        onClick: onNavigateToProfile
                    },
                        React.createElement('span', {
                            className: 'iconify text-gray-400 group-hover:text-blue-400 transition-colors',
                            'data-icon': 'solar:user-circle-linear',
                            style: { fontSize: '18px' }
                        }),
                        React.createElement('span', {
                            className: 'text-gray-300 group-hover:text-white font-medium transition-colors'
                        }, 'View Profile'),
                        React.createElement('span', {
                            className: 'iconify text-gray-500 group-hover:text-gray-400 transition-colors ml-auto',
                            'data-icon': 'solar:arrow-right-linear',
                            style: { fontSize: '16px' }
                        })
                    ),
                    // Settings Button
                    React.createElement('button', {
                        className: 'profile-action-btn w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-gray-700/50 transition-all duration-200 text-left group border border-gray-600/30 hover:border-gray-500/50',
                        id: 'settings-button'
                    },
                        React.createElement('span', {
                            className: 'iconify text-gray-400 group-hover:text-purple-400 transition-colors',
                            'data-icon': 'solar:settings-linear',
                            style: { fontSize: '18px' }
                        }),
                        React.createElement('span', {
                            className: 'text-gray-300 group-hover:text-white font-medium transition-colors'
                        }, 'Settings'),
                        React.createElement('span', {
                            className: 'iconify text-gray-500 group-hover:text-gray-400 transition-colors ml-auto',
                            'data-icon': 'solar:arrow-right-linear',
                            style: { fontSize: '16px' }
                        })
                    ),
                    // Sign Out Button
                    React.createElement('button', {
                        className: 'profile-action-btn w-full flex items-center gap-3 px-4 py-3 rounded-lg hover:bg-red-900/20 transition-all duration-200 text-left group border border-gray-600/30 hover:border-red-500/30',
                        id: 'sign-out-button',
                        onClick: onSignOut
                    },
                        React.createElement('span', {
                            className: 'iconify text-gray-400 group-hover:text-red-400 transition-colors',
                            'data-icon': 'solar:logout-3-linear',
                            style: { fontSize: '18px' }
                        }),
                        React.createElement('span', {
                            className: 'text-gray-300 group-hover:text-red-400 font-medium transition-colors'
                        }, 'Sign Out'),
                        React.createElement('span', {
                            className: 'iconify text-gray-500 group-hover:text-red-400 transition-colors ml-auto',
                            'data-icon': 'solar:logout-3-linear',
                            style: { fontSize: '16px' }
                        })
                    )
                )
            )
        )
    );
}; 