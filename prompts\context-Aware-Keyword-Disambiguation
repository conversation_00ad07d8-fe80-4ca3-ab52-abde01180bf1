# Context-Aware Keyword Disambiguation for promptFormatter.js

## Background

The current promptFormatter.js logic sometimes misclassifies prompts as "gaming" or "FPS" content when they contain keywords like `code`, `cod`, `battle`, etc., even if the actual context is unrelated (e.g., "code review", "battle cancer", "call of duty" vs "battle of the bands"). This leads to inappropriate application of gaming-specific prompt enhancements and visual rules.

## Request

**Develop a robust context-aware keyword disambiguation system** in `promptFormatter.js` that can:
- Distinguish between gaming/FPS contexts and non-gaming contexts when encountering ambiguous keywords (e.g., `code`, `cod`, `battle`, `vs`, `rank`, `match`, etc.).
- Prevent false positives for gaming logic when the prompt is about tech, business, health, or other non-gaming topics.
- Apply gaming-specific enhancements only when the overall context is clearly about games, esports, or FPS content.

## Requirements

- **Phrase-level and topic-level analysis:**
  - Use surrounding words and overall prompt context to determine the true meaning of ambiguous keywords.
  - Example:
    - `"code review best practices"` → **Tech** (not gaming) - 'code' + 'review/practices' indicates software development
    - `"write clean code in JavaScript"` → **Tech** (not gaming) - 'code' + programming language context
    - `"postal code validation"` → **Tech/General** (not gaming) - 'code' as identifier/address system
    - `"dress code for office"` → **Business** (not gaming) - 'code' as rules/guidelines
    - `"coding interview preparation"` → **Tech** (not gaming) - 'coding' + career/technical context
    - `"coding bootcamp reviews"` → **Education/Tech** (not gaming) - 'coding' + learning/training context
    - `"coding standards and best practices"` → **Tech** (not gaming) - 'coding' + software development guidelines
    - `"medical coding certification"` → **Healthcare** (not gaming) - 'coding' + medical classification system
    - `"color coding for organization"` → **General/Productivity** (not gaming) - 'coding' + categorization system
    - `"coding languages comparison"` → **Tech** (not gaming) - 'coding' + programming languages context
    - `"coding challenges and solutions"` → **Tech** (not gaming) - 'coding' + programming practice context
    - `"COD tournament highlights"` → **Gaming** (Call of Duty) - gaming tournament context
    - `"COD championship finals 2024"` → **Gaming** (Call of Duty) - esports competition context
    - `"COD Warzone loadout guide"` → **Gaming** (Call of Duty) - specific game mode and equipment
    - `"COD multiplayer tips"` → **Gaming** (Call of Duty) - online gaming context
    - `"COD zombies easter eggs"` → **Gaming** (Call of Duty) - specific game mode content
    - `"COD MW2 campaign walkthrough"` → **Gaming** (Call of Duty) - specific game title and gameplay
    - `"COD Black Ops strategies"` → **Gaming** (Call of Duty) - specific game series tactics
    - `"COD killstreaks explained"` → **Gaming** (Call of Duty) - gaming mechanics terminology
    - `"COD weapon tier list"` → **Gaming** (Call of Duty) - gaming equipment rankings
    - `"COD map callouts and positions"` → **Gaming** (Call of Duty) - gaming strategy and locations
    - `"COD esports team rankings"` → **Gaming** (Call of Duty) - competitive gaming scene
    - `"COD pro player highlights"` → **Gaming** (Call of Duty) - professional gaming content
    - `"COD gameplay montage"` → **Gaming** (Call of Duty) - gaming video content
    - `"COD update patch notes"` → **Gaming** (Call of Duty) - game development updates
    - `"COD console vs PC comparison"` → **Gaming** (Call of Duty) - gaming platform discussion
    - `"cod fish recipes"` → **Cooking** (not gaming) - food preparation context
    - `"Atlantic cod population"` → **Science/Nature** (not gaming) - marine biology context
    - `"cod liver oil benefits"` → **Health/Nutrition** (not gaming) - dietary supplement context
    - `"cod fishing techniques"` → **Recreation/Fishing** (not gaming) - outdoor activity context
    - `"fresh cod market prices"` → **Business/Commerce** (not gaming) - commercial trading context
    - `"battle cancer with new treatments"` → **Health** (not gaming) - 'battle' + medical/disease context
    - `"battle of Gettysburg history"` → **History** (not gaming) - historical war context
    - `"battle for market share"` → **Business** (not gaming) - competitive business context
    - `"battle royale tips and tricks"` → **Gaming** - specific gaming genre terminology
    - `"legal battle over patents"` → **Legal** (not gaming) - legal dispute context
    - `"rank employees by performance"` → **Business/HR** (not gaming) - workplace evaluation
    - `"military rank structure"` → **Military** (not gaming) - hierarchical organization
    - `"rank higher in search results"` → **Tech/SEO** (not gaming) - search optimization
    - `"ranked competitive gameplay"` → **Gaming** - competitive gaming context
    - `"match colors for design"` → **Design** (not gaming) - color coordination
    - `"tennis match schedule"` → **Sports** (not gaming) - traditional sports
    - `"match job candidates"` → **HR/Recruiting** (not gaming) - employment matching
    - `"deathmatch strategies"` → **Gaming** - specific gaming mode terminology
    - `"vs code extensions"` → **Tech** (not gaming) - software development tool
    - `"Lakers vs Warriors game"` → **Sports** (not gaming) - traditional sports competition
    - `"democracy vs autocracy"` → **Politics** (not gaming) - political comparison
    - `"1v1 arena tips"` → **Gaming** - gaming terminology and context
    - `"level up your skills"` → **Professional Development** (could be gaming) - ambiguous, needs more context
    - `"character development in novels"` → **Literature** (not gaming) - literary analysis
    - `"create game character builds"` → **Gaming** - clear gaming context
    - `"respawn timer optimization"` → **Gaming** - gaming-specific terminology
    - `"spawn new processes"` → **Tech** (not gaming) - programming/system administration
    - `"React vs Vue comparison"` → **Tech** (not gaming) - React logo = atom symbol, Vue logo = official Vue logo
    - `"React component lifecycle"` → **Tech** (not gaming, not reaction) - React logo = atom symbol (never infinity)
    - `"React hooks tutorial"` → **Tech** (not gaming, not reaction) - React logo = atom symbol
    - `"trailer reaction to Marvel movie"` → **Reaction/Entertainment** (not tech) - avoid React atom logo, use emotional/entertainment icons
    - `"shocked reaction to plot twist"` → **Reaction/Entertainment** (not tech) - avoid React atom logo, focus on emotional response  
    - `"first time watching reaction"` → **Reaction/Entertainment** (not tech) - avoid React atom logo, use video/entertainment context
    - `"react to this funny meme"` → **Reaction/Entertainment** (not tech) - avoid React atom logo, focus on entertainment
    - `"infinity symbol meaning"` → **Math/Symbols** (not gaming, not React) - should show infinity symbol, not React logo
    - `"mathematical infinity concept"` → **Math** (not gaming, not React) - infinity symbol context, not React
    - `"React Native vs Flutter"` → **Tech** (not gaming, not reaction) - React logo = atom, Flutter logo = official Flutter logo
    - `"Angular vs React performance"` → **Tech** (not gaming) - Angular logo = official shield, React logo = atom
    - `"Vue.js best practices"` → **Tech** (not gaming) - Vue logo = official green Vue logo
    - `"Node.js server setup"` → **Tech** (not gaming) - Node.js logo = official hexagon logo
    - `"Python vs JavaScript"` → **Tech** (not gaming) - Python logo = snake, JavaScript logo = official JS logo
    - `"Docker container deployment"` → **Tech** (not gaming) - Docker logo = official whale logo
    - `"AWS cloud services"` → **Tech** (not gaming) - AWS logo = official orange cube logo
    - `"GitHub repository management"` → **Tech** (not gaming) - GitHub logo = official Octocat
    - `"MongoDB database design"` → **Tech** (not gaming) - MongoDB logo = official leaf logo
    - `"Redis caching strategies"` → **Tech** (not gaming) - Redis logo = official red cube logo
    - `"Kubernetes orchestration"` → **Tech** (not gaming) - Kubernetes logo = official helm wheel
    - `"TypeScript vs JavaScript"` → **Tech** (not gaming) - TypeScript logo = official TS logo, JavaScript = JS logo
    - `"GraphQL API design"` → **Tech** (not gaming) - GraphQL logo = official pink diamond logo
    - `"Webpack configuration"` → **Tech** (not gaming) - Webpack logo = official blue cube logo
    - `"Sass vs CSS preprocessors"` → **Tech** (not gaming) - Sass logo = official pink logo, CSS = official logo
    - `"Bootstrap responsive design"` → **Tech** (not gaming) - Bootstrap logo = official purple B logo
    - `"jQuery DOM manipulation"` → **Tech** (not gaming) - jQuery logo = official blue jQ logo
    - `"Firebase authentication"` → **Tech** (not gaming) - Firebase logo = official flame logo
    - `"Stripe payment integration"` → **Tech** (not gaming) - Stripe logo = official blue S logo
    - `"Figma design workflow"` → **Design** (not gaming) - Figma logo = official colorful logo
    - `"Adobe Photoshop tutorials"` → **Design** (not gaming) - Photoshop logo = official Ps logo
    - `"Slack team communication"` → **Business** (not gaming) - Slack logo = official hashtag logo
    - `"Zoom meeting setup"` → **Business** (not gaming) - Zoom logo = official blue camera logo
    - `"Microsoft Teams collaboration"` → **Business** (not gaming) - Teams logo = official purple logo
    - `"Google Drive file sharing"` → **Productivity** (not gaming) - Google Drive logo = official triangle logo
    - `"Notion workspace organization"` → **Productivity** (not gaming) - Notion logo = official N logo
    - `"Trello project management"` → **Productivity** (not gaming) - Trello logo = official blue board logo
- **Update the keyword detection logic** to:
  - Use a whitelist/blacklist or context phrase mapping for ambiguous terms.
  - Optionally, leverage a simple NLP or rules-based approach to infer topic.
- **Fallback:**
  - If context is unclear, default to **non-gaming** logic and do not apply gaming-specific prompt enhancements.

## Deliverables

- Updated `promptFormatter.js` with context-aware keyword disambiguation.
- Unit tests or sample prompt cases demonstrating correct classification.
- Documentation/comments explaining the new logic.