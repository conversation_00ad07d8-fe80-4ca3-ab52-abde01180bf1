# Context7MCP Face Upload by URL – Advanced Face Replacement Optimization

Design a cinematic YouTube thumbnail at 1280x720 resolution, using the following face image provided by U<PERSON> as the main subject:

**Face Image URL:** [PASTE_USER_URL_HERE]

## Context7MCP Face Upload Optimization - Critical Requirements

### FACE DETECTION & EXTRACTION:
- **Always detect and extract the most prominent face** from the provided URL image
- **If multiple faces are present** in the URL image, use the largest, most frontal face
- **PRIORITY REPLACEMENT:** The face from the URL must ALWAYS replace the original face in the thumbnail - never ignore or omit
- **URL Face Source:** Ensure proper loading and accessibility of the provided image URL

### SEAMLESS BLENDING & MATCHING:
- **Preserve EXACT facial identity** from the URL: bone structure, nose shape, eye shape and spacing, mouth shape, jawline, forehead proportions
- **Match ear shape** if visible, any unique facial features (moles, freckles, scars) from the reference image
- **EXACT skin tone and texture replication** from URL image - avoid mismatched colors or harsh edges
- **Hair color and style** must match the URL face exactly
- **Automatically harmonize lighting** between URL face and thumbnail scene for natural integration

### FACIAL ALIGNMENT & ORIENTATION:
- **Align the URL face** to match the pose, angle, and expression requirements of the thumbnail
- **Adjust orientation** as needed for perfect fit while maintaining the URL face's identity
- **Expression adaptation:** If needed, adapt the URL face to match required expression while preserving identity
- **Pose matching:** Ensure the face fits naturally with the intended body pose and camera angle

### EDGE CASE HANDLING:
- **Partial occlusion:** If URL face is partially occluded, in shadow, or at unusual angle, enhance and correct for best integration
- **Lighting compensation:** Compensate for different lighting conditions between URL source and thumbnail target
- **Quality differences:** Handle varying image quality and resolution differences between URL and target
- **URL accessibility:** Ensure proper image loading and handle potential URL access issues

### QUALITY STANDARDS:
- **Main Subject Focus:** Ensure the URL face becomes the clear focal point with sharp detail and cinematic lighting
- **No Artifacts:** Avoid ghosting, double faces, or visible seams - result should look professional and studio-quality
- **Seamless Integration:** The final result must be indistinguishable from having the person from the URL actually in the scene
- **Perfect Realism:** Think of this as placing the URL person's face onto the generated body/scene with perfect realism

## Universal Thumbnail Rules:

- **Safe Zone:** Maintain a 40px safe zone from all edges for mobile compatibility
- **Text Overlays:** Use bold, modern text overlays as needed (do not cover the face)
- **Visual Balance:** Ensure the thumbnail is visually balanced, high-contrast, and optimized for both desktop and mobile
- **Professional Quality:** Studio-quality result suitable for viral YouTube content

## Implementation Notes:

- **Priority System:** Face replacement takes absolute priority over all other visual elements
- **Quality Assurance:** Every face upload must result in visible, accurate face replacement
- **Mobile Optimization:** Ensure face clarity and recognition on mobile devices
- **Accessibility:** Handle various URL formats and image qualities gracefully

**Output:**  
A thumbnail with the uploaded face from the provided URL perfectly swapped in, seamlessly blended, and visually harmonious with the rest of the image, following all Context7MCP face upload optimization standards and OpenAI best practices.

---

## Technical Integration:

This prompt template is integrated into the application's prompt generation system (`src/utils/promptFormatter.js`) and automatically applied when users upload faces via URL. The Context7MCP optimization ensures 100% face application rate and superior blending quality.