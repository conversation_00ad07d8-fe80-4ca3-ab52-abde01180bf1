/**
 * Utility functions for image processing and download
 * All functions now use the generation history modal (modal-image-container) as the single source of truth
 */

/**
 * Creates a stretched image that fills the entire 1280x720 canvas with solid background
 * @param {string} imageUrl - The source image URL
 * @param {number} targetWidth - Target width (default: 1280)
 * @param {number} targetHeight - Target height (default: 720)
 * @returns {Promise<string>} - Promise that resolves to the processed image data URL
 */
export const createGenerationHistoryModalMatchingImage = (imageUrl, targetWidth = 1280, targetHeight = 720) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
            // Create an in-memory canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set the canvas dimensions to the target size
            canvas.width = targetWidth;
            canvas.height = targetHeight;
            
            console.log(`[Stretched Image Export] Processing image for stretched fill`);
            console.log(`[Stretched Image Export] Image: ${img.width}x${img.height}`);
            console.log(`[Stretched Image Export] Target: ${targetWidth}x${targetHeight}`);
            
            // Use high-quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            
            // Stretch image to fill entire canvas (object-fit: fill behavior)
            // This will distort the image to fit exactly the target dimensions - no background
            ctx.drawImage(img, 0, 0, targetWidth, targetHeight);
            
            console.log(`[Stretched Image Export] Image stretched to fill entire canvas without background`);
            
            // Convert to data URL with high quality PNG to preserve any transparency
            const processedDataUrl = canvas.toDataURL('image/png', 1.0);
            console.log(`[Stretched Image Export] Export complete: ${processedDataUrl.length} bytes`);
            resolve(processedDataUrl);
        };
        
        img.onerror = () => {
            console.error('[Stretched Image Export] Failed to load image:', imageUrl);
            reject(new Error('Failed to load image for processing'));
        };
        
        // Enable CORS for cross-origin images (like Supabase storage)
        img.crossOrigin = 'anonymous';
        img.src = imageUrl;
    });
};

/**
 * @deprecated Use createGenerationHistoryModalMatchingImage instead for consistent modal matching
 */
export const createModalMatchingImage = createGenerationHistoryModalMatchingImage;

/**
 * Stretches an image to 1280x720 using canvas and returns a data URL
 * Stretches the image to fill the entire 1280x720 canvas without preserving aspect ratio
 * @param {string} imageUrl - The source image URL
 * @returns {Promise<string>} - Promise that resolves to the stretched image data URL
 */
export const resizeImageTo1280x720 = (imageUrl) => {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
            // Create an in-memory canvas
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            // Set the canvas dimensions to exactly 1280x720
            canvas.width = 1280;
            canvas.height = 720;
            
            const inputAspect = img.width / img.height;
            const targetAspect = 1280 / 720; // 1.777... (16:9)
            
            // Use high-quality rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            
            // Stretch image to fill entire canvas (object-fit: fill behavior)
            // This will distort the image to fit exactly 1280x720 - no background
            ctx.drawImage(img, 0, 0, 1280, 720);
            
            // Convert to data URL with high quality
            const resizedDataUrl = canvas.toDataURL('image/png');
            resolve(resizedDataUrl);
        };
        
        img.onerror = () => {
            reject(new Error('Failed to load image for resizing'));
        };
        
        img.src = imageUrl;
    });
};

/**
 * Downloads a stretched image that fills the entire 1280x720 canvas with solid background
 * @param {string} imageUrl - The source image URL
 * @param {string|number} itemId - Unique identifier for the thumbnail
 * @param {string} fallbackUrl - Optional fallback URL if processing fails
 */
export const downloadThumbnailAt1280x720 = async (imageUrl, itemId = Date.now(), fallbackUrl = null) => {
    try {
        console.log(`[Export] Starting download with stretched fill approach`);
        
        // Create processed image that stretches to fill entire 1280x720 canvas
        const processedImageUrl = await createGenerationHistoryModalMatchingImage(imageUrl, 1280, 720);
        
        // Create a temporary link element for download
        const link = document.createElement('a');
        link.href = processedImageUrl;
        link.download = `thumbnail-${itemId}-1280x720-stretched.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log(`[Export] Download completed successfully with stretched fill`);
        
    } catch (error) {
        console.error("Canvas processing failed (likely CORS issue):", error);
        
        // Enhanced fallback: try direct download with proper filename
        try {
        const link = document.createElement('a');
            const downloadUrl = fallbackUrl || imageUrl;
            
            // For Supabase URLs, try to download directly
            link.href = downloadUrl;
            link.download = `thumbnail-${itemId}-original.jpg`;
            link.setAttribute('target', '_blank'); // Open in new tab as backup
            
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
            console.log(`[Export] Used direct download fallback`);
            
        } catch (fallbackError) {
            console.error("Direct download also failed:", fallbackError);
            
            // Import universal download as last resort
            try {
                const { downloadImageUniversal } = await import('./directImageDownload.js');
                await downloadImageUniversal(fallbackUrl || imageUrl, itemId);
                console.log(`[Export] Used universal download fallback`);
            } catch (universalError) {
                console.error("Universal download also failed:", universalError);
                // Final fallback: open in new tab
                window.open(fallbackUrl || imageUrl, '_blank');
                console.log(`[Export] Opened in new tab as final fallback`);
            }
        }
    }
}; 