/**
 * 🔧 LocalStorage Manager - Quo<PERSON> Exceeded <PERSON><PERSON><PERSON>
 * 
 * Handles localStorage quota exceeded errors and implements intelligent cleanup
 * for the ThumbSpark application, particularly for avatar uploads and auth tokens.
 */

export class LocalStorageManager {
    constructor() {
        this.QUOTA_EXCEEDED_ERROR = 'QuotaExceededError';
        this.DOM_EXCEPTION_QUOTA = 'The quota has been exceeded';
        
        // Storage keys by priority (higher number = higher priority to keep)
        this.STORAGE_PRIORITIES = {
            // Auth tokens - highest priority
            'sb-csibhnfqpwqkhpnvdakz-auth-token': 10,
            'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier': 10,
            'supabase.auth.token': 10,
            'remember_me_auth': 9,
            
            // User preferences - high priority
            'user_preferences': 8,
            'app_settings': 8,
            
            // Recent thumbnails - medium priority
            'recent_thumbnails': 6,
            'thumbnail_history': 6,
            
            // Avatar data - medium priority (but large files)
            'avatar_': 5, // prefix for avatar files
            
            // Demo data - low priority
            'demo_': 3,
            'temp_': 2,
            
            // Cache data - lowest priority
            'cache_': 1,
            'openmojiCache': 1
        };
    }

    /**
     * Safely set item in localStorage with quota management
     */
    setItem(key, value) {
        try {
            localStorage.setItem(key, value);
            return { success: true };
        } catch (error) {
            if (this.isQuotaExceededError(error)) {
                console.warn('🚨 LocalStorage quota exceeded, attempting cleanup...');
                
                // Attempt cleanup and retry
                const cleanupResult = this.performIntelligentCleanup();
                
                if (cleanupResult.freedSpace > 0) {
                    try {
                        localStorage.setItem(key, value);
                        console.log(`✅ Successfully stored ${key} after cleanup`);
                        return { success: true, cleanupPerformed: true };
                    } catch (retryError) {
                        console.error('❌ Still failed after cleanup:', retryError);
                        return { 
                            success: false, 
                            error: 'Storage quota exceeded even after cleanup',
                            cleanupPerformed: true 
                        };
                    }
                } else {
                    return { 
                        success: false, 
                        error: 'No space could be freed for storage',
                        cleanupPerformed: true 
                    };
                }
            } else {
                console.error('LocalStorage error:', error);
                return { success: false, error: error.message };
            }
        }
    }

    /**
     * Safely get item from localStorage
     */
    getItem(key) {
        try {
            return localStorage.getItem(key);
        } catch (error) {
            console.error(`Error getting ${key} from localStorage:`, error);
            return null;
        }
    }

    /**
     * Safely remove item from localStorage
     */
    removeItem(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error(`Error removing ${key} from localStorage:`, error);
            return false;
        }
    }

    /**
     * Check if error is quota exceeded
     */
    isQuotaExceededError(error) {
        return error.name === this.QUOTA_EXCEEDED_ERROR || 
               error.message.includes(this.DOM_EXCEPTION_QUOTA) ||
               error.code === 22; // QUOTA_EXCEEDED_ERR code
    }

    /**
     * Perform intelligent cleanup based on priorities
     */
    performIntelligentCleanup() {
        console.log('🧹 Starting intelligent localStorage cleanup...');
        
        let freedSpace = 0;
        let removedItems = [];
        
        // Get all localStorage keys with their sizes
        const items = this.getAllItemsWithSizes();
        
        // Sort by priority (lowest first) and size (largest first for same priority)
        const sortedItems = items.sort((a, b) => {
            const priorityA = this.getKeyPriority(a.key);
            const priorityB = this.getKeyPriority(b.key);
            
            if (priorityA !== priorityB) {
                return priorityA - priorityB; // Lower priority first
            }
            return b.size - a.size; // Larger size first for same priority
        });

        // Remove items starting with lowest priority
        for (const item of sortedItems) {
            const priority = this.getKeyPriority(item.key);
            
            // Don't remove critical auth tokens
            if (priority >= 9) {
                continue;
            }
            
            // Remove avatar files if they're large (over 100KB)
            if (item.key.startsWith('avatar_') && item.size > 100000) {
                this.removeItem(item.key);
                freedSpace += item.size;
                removedItems.push({ key: item.key, size: item.size, reason: 'Large avatar file' });
                continue;
            }
            
            // Remove low priority items
            if (priority <= 3) {
                this.removeItem(item.key);
                freedSpace += item.size;
                removedItems.push({ key: item.key, size: item.size, reason: 'Low priority' });
            }
            
            // Stop if we've freed enough space (aim for 1MB)
            if (freedSpace > 1000000) {
                break;
            }
        }

        // Clean up old avatar files specifically
        freedSpace += this.cleanupOldAvatars();

        console.log(`🎯 Cleanup complete: Freed ${this.formatBytes(freedSpace)} from ${removedItems.length} items`);
        
        return {
            freedSpace,
            removedItems,
            totalItems: items.length,
            remainingItems: items.length - removedItems.length
        };
    }

    /**
     * Clean up old avatar files specifically
     */
    cleanupOldAvatars() {
        let freedSpace = 0;
        const avatarKeys = Object.keys(localStorage).filter(key => key.startsWith('avatar_'));
        
        // Keep only the 3 most recent avatars per user
        const avatarsByUser = {};
        
        avatarKeys.forEach(key => {
            const match = key.match(/^avatar_(.+?)_(\d+)$/);
            if (match) {
                const [, userId, timestamp] = match;
                if (!avatarsByUser[userId]) {
                    avatarsByUser[userId] = [];
                }
                avatarsByUser[userId].push({ key, timestamp: parseInt(timestamp) });
            }
        });

        // Remove old avatars for each user
        Object.values(avatarsByUser).forEach(userAvatars => {
            if (userAvatars.length > 3) {
                // Sort by timestamp (newest first) and keep only 3
                userAvatars.sort((a, b) => b.timestamp - a.timestamp);
                const toRemove = userAvatars.slice(3);
                
                toRemove.forEach(avatar => {
                    const value = this.getItem(avatar.key);
                    if (value) {
                        freedSpace += value.length;
                        this.removeItem(avatar.key);
                    }
                });
            }
        });

        return freedSpace;
    }

    /**
     * Get priority for a storage key
     */
    getKeyPriority(key) {
        for (const [pattern, priority] of Object.entries(this.STORAGE_PRIORITIES)) {
            if (key.startsWith(pattern) || key.includes(pattern)) {
                return priority;
            }
        }
        return 1; // Default low priority
    }

    /**
     * Get all localStorage items with their sizes
     */
    getAllItemsWithSizes() {
        const items = [];
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            const value = localStorage.getItem(key);
            
            if (key && value) {
                items.push({
                    key,
                    size: value.length,
                    priority: this.getKeyPriority(key)
                });
            }
        }
        
        return items;
    }

    /**
     * Get localStorage usage statistics
     */
    getStorageStats() {
        const items = this.getAllItemsWithSizes();
        const totalSize = items.reduce((sum, item) => sum + item.size, 0);
        
        // Estimate quota (5MB for most browsers)
        const estimatedQuota = 5 * 1024 * 1024;
        const usagePercentage = (totalSize / estimatedQuota) * 100;
        
        return {
            totalItems: items.length,
            totalSize,
            estimatedQuota,
            usagePercentage: Math.min(usagePercentage, 100),
            formattedSize: this.formatBytes(totalSize),
            formattedQuota: this.formatBytes(estimatedQuota),
            isNearLimit: usagePercentage > 80
        };
    }

    /**
     * Format bytes for human readable display
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Clear all non-essential data
     */
    emergencyCleanup() {
        console.log('🚨 Performing emergency localStorage cleanup...');
        
        const keysToKeep = [
            'sb-csibhnfqpwqkhpnvdakz-auth-token',
            'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier',
            'supabase.auth.token',
            'remember_me_auth',
            'user_preferences'
        ];
        
        const allKeys = Object.keys(localStorage);
        let freedSpace = 0;
        
        allKeys.forEach(key => {
            if (!keysToKeep.includes(key) && !keysToKeep.some(keepKey => key.startsWith(keepKey))) {
                const value = this.getItem(key);
                if (value) {
                    freedSpace += value.length;
                    this.removeItem(key);
                }
            }
        });
        
        console.log(`🎯 Emergency cleanup freed ${this.formatBytes(freedSpace)}`);
        return freedSpace;
    }
}

// Create singleton instance
export const localStorageManager = new LocalStorageManager();

// Global functions for easy access
export const safeSetItem = (key, value) => localStorageManager.setItem(key, value);
export const safeGetItem = (key) => localStorageManager.getItem(key);
export const safeRemoveItem = (key) => localStorageManager.removeItem(key);
export const getStorageStats = () => localStorageManager.getStorageStats();
export const performCleanup = () => localStorageManager.performIntelligentCleanup(); 