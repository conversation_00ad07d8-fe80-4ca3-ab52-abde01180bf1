---
description: 
globs: 
alwaysApply: true
---
# 🎯 Smart Text & Graphics Separation System (Updated)

ruleType: always
appliesTo:
  - /src/utils/promptFormatter.js
  - /src/components/ControlPanel.jsx
  - /src/App.jsx
  - /src/utils/promptEnhancer.js
  - /src/utils/textClassifier.js # NEW: Text classification utility
  - /src/Templates/*.js

---

## 🧠 Core Concept: Intelligent Text Distinction with Context Awareness

### Purpose
Automatically distinguish between headline text (controlled by text overlay feature) and secondary/micro text (always allowed as part of graphics), while ensuring graphics are generated contextually based on the video topic, not universally applied.

---

## 📝 Text Classification System

### 🔤 Headline Text (Controlled by Text Overlay Toggle)
**Definition**: Large, attention-grabbing text that serves as the main title or hook
- Font size: Typically 60+ pixels (scaled for 1280x720)
- Position: Usually top, center, or bottom of thumbnail
- Purpose: Main message, video title, or call-to-action
- Examples: "EPIC WIN!", "TOP 10 TIPS", "SHOCKING RESULTS"
- **IMPORTANT**: Any headline text mentioned in user prompts should be ignored and must be entered via the text overlay input box

### 📊 Secondary/Micro Text (Always Allowed When Contextually Relevant)
**Definition**: Small functional text that's part of graphics, charts, or UI elements
- Font size: Typically under 30 pixels (scaled for 1280x720)
- Position: Within graphics, charts, badges, or UI elements
- Purpose: Data labels, statistics, logos, badges
- **Context-Dependent Examples**:
  - Financial/Business: Chart labels ("€0", "€10k"), statistics ("+62,320")
  - Gaming: K/D ratios, scores, rank badges
  - Tech: Version numbers, specs, model names
  - General: Small UI elements, timestamps, watermarks

---

## 🛠 Implementation Logic

### External LLM Prompt Sanitization
```javascript
// Add to /src/utils/promptFormatter.js

const sanitizeExternalLLMPrompt = (userPrompt) => {
    // Patterns that indicate headline text from external LLMs
    const headlinePatterns = [
        /bold text overlay:\s*['"]([^'"]+)['"]/gi,
        /text overlay:\s*['"]([^'"]+)['"]/gi,
        /title:\s*['"]([^'"]+)['"]/gi,
        /headline:\s*['"]([^'"]+)['"]/gi,
        /with text ['"]([^'"]+)['"]/gi,
        /displaying ['"]([^'"]+)['"] in bold/gi,
        /large text saying ['"]([^'"]+)['"]/gi
    ];
    
    let sanitizedPrompt = userPrompt;
    const detectedHeadlines = [];
    
    // Extract and remove headline references
    headlinePatterns.forEach(pattern => {
        sanitizedPrompt = sanitizedPrompt.replace(pattern, (match, text) => {
            detectedHeadlines.push(text);
            return ''; // Remove the headline instruction
        });
    });
    
    // Clean up any font/text styling instructions
    const stylePatterns = [
        /\b(bold|uppercase|large|huge|big)\s+(text|font|letters?)\b/gi,
        /\bfont[- ]?size:\s*\w+/gi,
        /\btext[- ]?size:\s*\w+/gi,
        /\b\d+px\s+font\b/gi
    ];
    
    stylePatterns.forEach(pattern => {
        sanitizedPrompt = sanitizedPrompt.replace(pattern, '');
    });
    
    // Add warning if headlines were detected
    if (detectedHeadlines.length > 0) {
        sanitizedPrompt += `\n[SYSTEM NOTE: Headline text detected in prompt has been removed. User must enter headline text via the text overlay input box if desired.]`;
    }
    
    return {
        sanitizedPrompt,
        detectedHeadlines
    };
};
```

### Context-Aware Graphics Generation
```javascript
// Determine if graphics elements are appropriate for the topic
const shouldIncludeGraphicsElements = (userPrompt, videoTopic) => {
    const promptLower = userPrompt.toLowerCase();
    
    const graphicsContexts = {
        charts: {
            keywords: ['analytics', 'growth', 'statistics', 'data', 'chart', 'graph', 'trend', 'performance', 'metrics'],
            topics: [VIDEO_TOPICS.BUSINESS_FINANCE, VIDEO_TOPICS.TECH]
        },
        dataVisualizations: {
            keywords: ['numbers', 'percentage', 'increase', 'decrease', 'comparison', 'versus', 'vs'],
            topics: [VIDEO_TOPICS.BUSINESS_FINANCE, VIDEO_TOPICS.HEALTH_NUTRITION]
        },
        uiElements: {
            keywords: ['dashboard', 'interface', 'app', 'software', 'platform', 'screen'],
            topics: [VIDEO_TOPICS.TECH]
        },
        badges: {
            keywords: ['achievement', 'rank', 'level', 'certification', 'verified', 'pro', 'premium'],
            topics: [VIDEO_TOPICS.GAMING, VIDEO_TOPICS.BUSINESS_FINANCE]
        }
    };
    
    const includedElements = {
        charts: false,
        dataVisualizations: false,
        uiElements: false,
        badges: false
    };
    
    // Check each element type for relevance
    Object.entries(graphicsContexts).forEach(([element, context]) => {
        const hasKeyword = context.keywords.some(keyword => promptLower.includes(keyword));
        const isRelevantTopic = context.topics.includes(videoTopic);
        
        includedElements[element] = hasKeyword || isRelevantTopic;
    });
    
    return includedElements;
};
```

### Modified Text Overlay Logic
```javascript
// Update in buildPrompt function

// First, sanitize the prompt if it contains external LLM instructions
const { sanitizedPrompt, detectedHeadlines } = sanitizeExternalLLMPrompt(userPrompt);
const workingPrompt = sanitizedPrompt;

// Determine what graphics elements to include based on context
const videoTopic = extractVideoTopic(workingPrompt);
const graphicsElements = shouldIncludeGraphicsElements(workingPrompt, videoTopic);

// Text Overlay Section - Smart Detection
if (textOverlay) {
    prompt += `Text Overlay (HEADLINES ONLY):\n`;
    prompt += `- This feature controls ONLY large, attention-grabbing headline text\n`;
    prompt += `- Add a bold, uppercase title that serves as the main hook\n`;
    
    if (detectedHeadlines.length > 0) {
        prompt += `- IMPORTANT: Ignore any headline text from the original prompt. Use only the text provided in the overlay input box.\n`;
    }
    // ... rest of existing text overlay logic
} else {
    prompt += `Text Overlay (HEADLINES): DISABLED\n`;
    prompt += `- Do NOT add any large headline text, main titles, or attention-grabbing text overlays\n`;
    prompt += `- Do NOT add any text that was mentioned as "bold text overlay", "title", or "headline" in the prompt\n`;
    
    // Only mention secondary text if contextually relevant
    if (Object.values(graphicsElements).some(v => v)) {
        prompt += `- CONTEXTUAL SECONDARY TEXT: The following may be included if relevant to the specific graphics:\n`;
        
        if (graphicsElements.charts) {
            prompt += `  • Chart labels, axis labels, data points (for financial/analytics content)\n`;
        }
        if (graphicsElements.dataVisualizations) {
            prompt += `  • Statistics, numbers, percentages within visualizations\n`;
        }
        if (graphicsElements.uiElements) {
            prompt += `  • UI element labels, button text (for tech/software content)\n`;
        }
        if (graphicsElements.badges) {
            prompt += `  • Badge text, achievement labels, rank indicators\n`;
        }
        
        prompt += `- These secondary text elements should be small, functional, and integrated into their respective graphics ONLY if such graphics are part of the concept\n`;
    }
    prompt += `\n`;
}

// Graphics Section - Context-Aware Generation
prompt += `Graphics & Visual Elements:\n`;
prompt += `- Generate graphics based SOLELY on what's described in the user's prompt: "${workingPrompt}"\n`;
prompt += `- Do NOT automatically add charts, UI elements, or data visualizations unless explicitly mentioned or contextually essential\n`;

if (Object.values(graphicsElements).some(v => v)) {
    prompt += `- Based on the topic, the following elements MAY be relevant (include only if naturally fitting the concept):\n`;
    Object.entries(graphicsElements).forEach(([element, include]) => {
        if (include) {
            const elementDescriptions = {
                charts: 'Charts or graphs with appropriate labels',
                dataVisualizations: 'Data visualizations with numbers',
                uiElements: 'UI elements or interface components',
                badges: 'Badges, labels, or achievement indicators'
            };
            prompt += `  • ${elementDescriptions[element]}\n`;
        }
    });
}

prompt += `- Secondary text within graphics (if any) is allowed but should be minimal and functional\n`;
prompt += `- Focus on creating visually compelling imagery that matches the prompt's intent\n\n`;
```

---

## 🎨 Context-Aware Graphics Rules

### Topic-Based Graphics Inclusion
Graphics elements are NOT universal. They should only appear when:

1. **Explicitly mentioned** in the user's prompt
2. **Contextually relevant** to the video topic/category
3. **Essential** to the concept being visualized

### Category-Specific Guidelines

#### 📊 Financial/Business Topics
- MAY include: Charts, graphs, trend lines, statistics
- Secondary text: Numbers, percentages, currency values
- Only when discussing growth, analytics, or data

#### 🎮 Gaming Topics
- MAY include: Score displays, rank badges, achievement icons
- Secondary text: K/D ratios, player stats, level numbers
- Only when relevant to competitive or progress elements

#### 💻 Tech Topics
- MAY include: UI mockups, interface elements, code snippets
- Secondary text: Version numbers, feature lists, specs
- Only when showing software or product interfaces

#### 🎬 Entertainment/Vlog Topics
- TYPICALLY exclude: Charts, data visualizations
- Focus on: People, scenes, emotional elements
- Secondary text: Minimal, only if part of the scene

---

## 🚫 External LLM Prompt Handling

### Detection Patterns
The system automatically detects and removes:
- `"bold text overlay: 'TEXT'"`
- `"with text 'TEXT'"`
- `"title: 'TEXT'"`
- Font size specifications
- Text styling instructions

### User Notification
When external LLM text is detected:
1. Remove all headline text references
2. Preserve the core visual concept
3. Require user to input headlines via text overlay box

### Example Transformation
```javascript
// Original (from external LLM):
"Design a split-screen thumbnail for 'Movie A vs Movie B'. 
Use dramatic lighting, film icons, and bold text overlay: 'MOVIE A VS MOVIE B'."

// After sanitization:
"Design a split-screen thumbnail for 'Movie A vs Movie B'. 
Use dramatic lighting, film icons."
// [Headline must be added via text overlay input]
```

---

## 📋 Updated Success Criteria

### When Text Overlay is OFF:
1. **NO headlines** regardless of prompt content
2. **Secondary text** only when contextually relevant
3. **Graphics** based solely on prompt description
4. **External LLM text** instructions ignored

### When Text Overlay is ON:
1. **Headlines** from overlay input box only
2. **Prompt headlines** ignored if present
3. **Secondary text** still context-dependent
4. **Graphics** remain prompt-driven

### Graphics Generation:
1. **Charts/Data**: Only for analytical topics
2. **UI Elements**: Only for tech/software topics
3. **Badges/Labels**: Only when achievement/rank relevant
4. **Default**: Follow prompt description exactly

---

## 🔧 Implementation Notes

1. **Prompt Sanitization**: Always run before processing
2. **Context Detection**: Analyze topic before adding graphics
3. **Text Hierarchy**: Maintain clear distinction between headline and secondary
4. **User Control**: Text overlay box is the ONLY source for headlines

---

This updated system ensures that:
- Graphics are contextually appropriate, not universally applied
- External LLM prompts are properly sanitized
- Headlines are strictly controlled via the text overlay input
- Secondary text appears only when relevant to the specific graphics