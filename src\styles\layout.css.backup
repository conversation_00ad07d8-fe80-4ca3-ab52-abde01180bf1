/* ================= NAVIGATION LAYOUT FIXES ================= */

/* Ensure main content doesn't get covered by fixed top navigation */
.app-container {
    margin-top: 62px; /* Height of top navigation */
    min-height: calc(100vh - 62px);
}

/* ================= WELCOME SCREEN BUTTON POSITIONING FIXES ================= */

/* Welcome screen button stable positioning to prevent layout shifts */
.welcome-skip-btn,
.welcome-back-btn,
.welcome-skip-demo-btn {
    /* Prevent text selection and mobile tap highlights */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    
    /* Ensure buttons maintain position during state changes */
    will-change: transform;
    transform: translateZ(0); /* Force hardware acceleration */
    
    /* Prevent double-tap zoom on mobile */
    touch-action: manipulation;
    
    /* Stable dimensions to prevent layout shifts */
    white-space: nowrap;
    flex-shrink: 0;
    
    /* Prevent event propagation issues */
    isolation: isolate;
}

/* Preview workspace header spacing fix */
.workspace-header {
    padding-top: 8px; /* Add top padding to prevent overlap */
    padding-bottom: 15px;
}

/* Mobile hamburger menu positioning */
.hamburger-menu-btn {
    position: fixed;
    top: 12px;
    left: 12px;
    z-index: 1001; /* Above sidebar */
    display: none; /* Hidden by default on desktop */
    width: 44px;
    height: 44px;
    border-radius: 8px;
    background-color: rgba(31, 41, 55, 0.9);
    border: 1px solid rgba(75, 85, 99, 0.5);
    color: #D1D5DB;
    transition: all 0.2s ease;
    backdrop-filter: blur(8px);
}

.hamburger-menu-btn:hover {
    background-color: rgba(31, 41, 55, 1);
    border-color: rgba(139, 92, 246, 0.5);
    color: #FFFFFF;
    transform: scale(1.05);
}

.hamburger-menu-btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* Show hamburger menu on tablet and mobile */
@media (max-width: 1024px) {
    .hamburger-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Adjust main content padding for mobile */
    .main-center-panel {
        padding-top: 1.5rem; /* Reduced space for hamburger button */
    }
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
    .hamburger-menu-btn {
        top: 8px;
        left: 8px;
        width: 40px;
        height: 40px;
    }
    
    .main-center-panel {
        padding-top: 1.5rem;
    }
}

/* Basic body/layout styles */
body {
    @apply bg-gray-900 text-gray-100 font-sans;
    margin: 0; 
    padding: 0;
    overflow: hidden; /* Prevent all scrolling */
}

.container {
    @apply mx-auto;
}

/* Root layout container */
#root > div {
    @apply min-h-screen;
}

/* ================= THREE-PANEL PROFESSIONAL LAYOUT ================= */

.app-container {
    display: grid;
    grid-template-columns: 368px 1fr; /* Remove right sidebar column - now just left sidebar and center panel */
    grid-template-rows: 1fr; /* Single row layout */
    min-height: calc(100vh - 62px); /* Account for top navigation height */
    max-width: 100vw;
    background-color: #111827; /* bg-gray-900 */
    height: calc(100vh - 62px); /* Adjust for top navigation height */
    overflow-x: visible; /* Allow tooltips to overflow horizontally */
    overflow-y: hidden; /* Prevent vertical scrolling */
    margin-top: 62px; /* Space for fixed navigation */
}

/* Left Sidebar - Controls */
.left-sidebar {
    grid-column: 1;
    grid-row: 1;
    background-color: #1F2937; /* bg-gray-800 */
    border-right: 1px solid #374151; /* border-gray-700 */
    overflow-x: visible; /* Changed from hidden to visible to prevent focus ring clipping */
    overflow-y: hidden !important; /* ADDED to ensure outer container does not scroll */
    position: sticky;
    top: 0;
    width: 368px;
    padding: 1rem;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 30; /* Added z-index to ensure it's below the modal */
    display: flex; /* ADDED for flex column layout */
    flex-direction: column; /* ADDED for flex column layout */
    height: calc(100vh - 62px); /* FIXED: Adjust for top navigation height */
}

/* Custom dark scrollbar for left sidebar (will apply to inner scrollable div) */
.left-sidebar::-webkit-scrollbar {
    width: 10px;
    background: #18181B; /* Very dark background */
}
.left-sidebar::-webkit-scrollbar-thumb {
    background: #27272A; /* Slightly lighter thumb */
    border-radius: 8px;
    border: 2px solid #18181B;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}
.left-sidebar::-webkit-scrollbar-thumb:hover {
    background: #3F3F46;
}
.left-sidebar::-webkit-scrollbar-corner {
    background: #18181B;
}
/* Firefox */
.left-sidebar {
    scrollbar-width: thin;
    scrollbar-color: #27272A #18181B;
}

/* Hide scrollbar for inner design controls scroller */
.left-sidebar .overflow-y-auto {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}
.left-sidebar .overflow-y-auto::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* Main Center Panel - Preview Area */
.main-center-panel {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    flex-direction: column;
    padding: 25px 20px 25px 20px; /* Back to original padding */
    background-color: #111827;
    height: calc(100vh - 62px);
    min-height: calc(100vh - 62px);
    max-height: calc(100vh - 62px);
    overflow: hidden;
    gap: 8px; /* Slightly increased gap */
}

/* Right Sidebar - Hidden for current development stage */
.right-sidebar {
    display: none; /* Hidden across all breakpoints */
}

/* ================= PREVIEW CONTAINER STYLING ================= */

.preview-container {
    width: 652px;
    height: 367px; /* 16:9 aspect ratio: 652/16*9 = 367 */
    max-width: 100%; /* Responsive on smaller screens */
    aspect-ratio: 16/9; /* Correct YouTube thumbnail aspect ratio */
    background-color: #212936; /* Slightly lighter than bg for contrast */
    /* border: 2px dashed #4B5563; */ /* Remove dashed border */
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    position: relative;
    z-index: 0;
    margin: 0 auto; /* Center horizontally */
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Responsive scaling while maintaining aspect ratio */
@media (max-width: 820px) {
    .preview-container {
        width: 100%;
        height: auto;
        max-width: 652px;
    }
}

/* Preview wrapper to contain the preview */
.preview-wrapper {
    width: 100%;
    max-width: 652px;
    margin: 0 auto;
}

/* Ensure generated images fit properly */
.preview-container img.generated-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Fill container completely, no letterboxing */
    display: block;
}

/* Preview workspace section styling */
.preview-workspace-section {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
}

/* workspace-header styles moved to top navigation fixes section */

/* Prompt controls section */
.prompt-controls-section {
    flex: 0 0 auto;
    width: 100%;
    max-width: 652px;
    margin: 40px auto 0 auto; /* Move textarea and buttons down by 40px */
    padding: 0;
}

/* Controls bottom row */
.controls-bottom-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 50 !important;
    background-color: #111827 !important;
    padding: 10px 0 !important;
    min-height: 70px !important;
    margin-top: 32px !important; /* Increased spacing between preview and controls */
    justify-content: flex-end !important; /* Align to right */
    max-width: 652px !important; /* Match preview container width */
    margin-left: auto !important;
    margin-right: auto !important;
    gap: 1rem !important; /* Reduced gap since controls are grouped */
}

/* Combined controls container */
.combined-controls {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important; /* Space between quality selector and action buttons */
}

.quality-selector {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
    min-height: 48px;
    visibility: visible !important;
}

.action-buttons {
    display: flex !important;
    align-items: center;
    gap: 0.875rem;
    flex-shrink: 0;
    min-height: 48px;
    visibility: visible !important;
}

.generate-button-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* ================= RIGHT SIDEBAR PLACEHOLDER ================= */

.coming-soon-indicator {
    text-align: center;
    padding: 2rem 1rem;
}

.coming-soon-indicator h3 {
    color: #9CA3AF; /* text-gray-400 */
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.coming-soon-indicator p {
    color: #6B7280; /* text-gray-500 */
    font-size: 0.875rem;
    line-height: 1.4;
}

.coming-soon-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    opacity: 0.5;
    color: #6B7280;
}

/* ================= RESPONSIVE DESIGN ================= */

@media (max-width: 1280px) {
    .app-container {
        grid-template-columns: 368px 1fr; /* Remove right sidebar column for this breakpoint too */
    }
}

@media (max-width: 1024px) {
    .app-container {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr;
        position: relative;
        margin-top: 62px; /* Ensure top navigation space */
    }
    
    .left-sidebar {
        position: fixed;
        top: 62px; /* Position below top navigation */
        left: 0;
        width: 350px;
        max-width: 80vw;
        height: calc(100vh - 62px); /* Account for top navigation */
        background-color: #1F2937;
        border-right: 1px solid #374151;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow-y: auto;
        padding: 1rem;
    }
    
    .left-sidebar.open {
        transform: translateX(0);
    }
    
    .main-center-panel {
        grid-column: 1;
        grid-row: 1;
        padding: 0.75rem;
        padding-top: 4rem; /* Space for hamburger button */
        gap: 0.875rem; /* Optimized gap for mobile */
        height: calc(100vh - 62px); /* Account for top navigation */
        overflow-y: auto;
    }
    
    .right-sidebar {
        display: none; /* Hide on tablet */
    }
    
    .preview-container {
        max-width: 100%;
        margin-bottom: 0.5rem;
    }
    
    /* Tablet: Apply vertical stacking for controls with improved organization */
    .controls-bottom-row {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1.25rem;
        width: 100%;
        padding: 0 0.5rem; /* Add padding for mobile */
    }
    
    /* Stack combined controls vertically on tablet */
    .combined-controls {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
        width: 100% !important;
    }
    
    .prompt-controls-section {
        padding: 0 1rem; /* Reduced padding on tablet for more space */
        max-width: 100%; /* Use full width on tablet */
    }
    
    .quality-selector {
        justify-content: center;
        width: 100%;
        max-width: 320px; /* Constrain width on mobile */
    }
    
    .action-buttons {
        justify-content: center;
        width: 100%;
        max-width: 400px; /* Provide more space for action buttons */
        gap: 0.875rem; /* Optimized gap for mobile */
        order: 2;
        margin-left: 0;
    }
    
    .generate-button-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
        justify-content: center;
    }
    
    .prompt-textarea {
        font-size: 0.8125rem; /* Slightly smaller text on mobile */
        min-height: 100px; /* Reduce height on mobile for more space */
    }
    
    /* hamburger-menu-btn styles moved to top navigation fixes section */
    
    /* Show hamburger button only on tablet and mobile */
    @media (max-width: 1024px) {
        /* hamburger button styles are in navigation fixes section */
        
        /* Show and style close button inside sidebar */
        .left-sidebar .sidebar-close-btn {
            display: flex !important;
            visibility: visible !important;
        }
        
        .left-sidebar .sidebar-close-btn:hover {
            background-color: #374151;
            color: #F9FAFB;
        }
        
        .left-sidebar .sidebar-close-btn:focus {
            outline: none;
            background-color: #374151;
            color: #F9FAFB;
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.3);
        }
    }
    
    /* Backdrop overlay */
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    
    .sidebar-backdrop.open {
        opacity: 1;
        visibility: visible;
    }
    
    /* Close button inside sidebar - hidden on desktop, visible on tablet/mobile */
    .left-sidebar .sidebar-close-btn {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background-color: transparent;
        border: none;
        color: #9CA3AF;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 6px;
        transition: all 0.2s ease-in-out;
        display: none !important; /* Hidden by default (desktop) with !important */
        visibility: hidden !important; /* Also ensure visibility is hidden */
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
    }
    
    /* Explicitly hide close button on desktop */
    @media (min-width: 1025px) {
        .left-sidebar .sidebar-close-btn {
            display: none !important;
            visibility: hidden !important;
        }
    }
    
    /* Prevent body scroll when sidebar is open */
    body.sidebar-open {
        overflow: hidden;
    }
}

@media (max-width: 768px) {
    .left-sidebar {
        width: 100vw;
        max-width: 100vw;
        top: 62px; /* Position below top navigation */
        height: calc(100vh - 62px);
    }
    
    .main-center-panel {
        padding: 0.75rem;
        padding-top: 3.5rem; /* Adjusted for smaller hamburger button */
        gap: 0.875rem; /* Optimized gap for mobile */
        height: calc(100vh - 62px);
    }
    
    .controls-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    /* Mobile: Enhanced vertical stacking with optimized organization */
    .controls-bottom-row {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1.25rem;
        width: 100%;
        padding: 0 0.5rem; /* Add padding for mobile */
    }
    
    /* Stack combined controls vertically on mobile */
    .combined-controls {
        flex-direction: column !important;
        align-items: center !important;
        gap: 1rem !important;
        width: 100% !important;
    }
    
    .quality-selector {
        justify-content: center;
        width: 100%;
        max-width: 320px; /* Constrain width on mobile */
    }
    
    .action-buttons {
        justify-content: center;
        width: 100%;
        max-width: 400px; /* Provide more space for action buttons */
        gap: 0.875rem; /* Optimized gap for mobile */
        order: 2;
        margin-left: 0;
    }
    
    .generate-button-container {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
        justify-content: center;
    }
    
    .prompt-textarea {
        font-size: 0.8125rem; /* Slightly smaller text on mobile */
        min-height: 100px; /* Reduce height on mobile for more space */
    }
    
    /* hamburger-menu-btn styles are in navigation fixes section */
    
    /* Enhanced mobile prompt controls */
    .prompt-controls-section {
        position: sticky;
        bottom: 0;
        background-color: #111827;
        padding: 1.25rem 1rem; /* Increased padding for better touch targets */
        margin-top: auto;
        max-width: 100%;
        width: 100%;
    }
    
    /* Ensure preview area doesn't get hidden by fixed controls */
    .preview-workspace-section {
        padding-bottom: 1rem;
    }
}

/* ================= ERROR BANNER (Unchanged) ================= */

.error-banner-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    z-index: 1000;
    pointer-events: none;
}

.error-banner {
    background-color: #C53030;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    opacity: 0;
    transform: translateY(-150%);
    transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;
    pointer-events: auto;
    position: relative;
}

.error-banner.show {
    opacity: 1;
    transform: translateY(0);
}

.error-banner-close-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    line-height: 1;
    cursor: pointer;
    padding: 0.25rem;
}

/* Optional: Add a simple icon if you don't use Heroicons for this */
.error-banner::before {
    /* content: '⚠️'; */ /* Example with emoji */
    /* font-size: 1.25rem; */
}

/* ================= MODAL OVERLAY & POPUP ================= */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(24, 24, 27, 0.7);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-popup {
    z-index: 11;
    position: relative;
    background: #232336;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.45);
    padding: 2rem;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
}

/* ================= INDEPENDENT TOOLTIP SYSTEM ================= */

/* Fixed positioning tooltip that ignores container boundaries */
.tooltip-fixed {
    position: fixed !important;
    z-index: 9999 !important;
    pointer-events: none;
    transform-origin: center;
    transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
}

/* Tooltip arrow for fixed tooltips */
.tooltip-arrow-fixed {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
    z-index: -1;
}

/* Arrow positioning classes */
.tooltip-arrow-top {
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-bottom {
    top: -4px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
}

.tooltip-arrow-left {
    right: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

.tooltip-arrow-right {
    left: -4px;
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
}

/* Ensures tooltips stay within viewport bounds */
.tooltip-container {
    position: relative;
    display: inline-block;
}

/* Override any container overflow that might clip tooltips */
.tooltip-no-clip {
    overflow: visible !important;
}

/* Panel preview container with responsive aspect ratio 16:9 */
.panel-preview-container {
    width: 100%;
    max-width: 652px;
    aspect-ratio: 16 / 9;
    height: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #181C23;
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.18);
    overflow: hidden;
    margin: 0 auto;
}

@media (max-width: 700px) {
  .panel-preview-container {
    width: 100vw;
    max-width: 100vw;
    aspect-ratio: 16 / 9;
    min-width: 0;
    min-height: 0;
  }
}

/* ================= Face Source Tab Styling (Upload/URL) ================= */

.face-source-tab-navigation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 260px; /* Smaller than main tabs */
    height: 45px; /* Smaller height than main tabs */
    margin: 0 auto;
    background: transparent;
}

.face-source-tab-group {
    display: flex;
    width: 100%;
    height: 100%;
    background: rgba(55, 65, 81, 0.6); /* bg-gray-700/60 */
    border-radius: 12px; /* Slightly smaller border radius */
    padding: 3px;
    position: relative;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 1px solid rgba(75, 85, 99, 0.3); /* border-gray-600/30 */
}

/* Moving indicator for face source tabs */
.face-source-tab-group::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: calc(50% - 3px);
    height: calc(100% - 6px);
    background: linear-gradient(135deg, rgb(147, 51, 234) 0%, rgb(126, 34, 206) 100%); /* Purple gradient */
    border-radius: 9px;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.face-source-tab-group.url-active::before {
    transform: translateX(calc(100% + 3px)); /* Move to second tab position */
}

.face-source-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px; /* Smaller gap than main tabs */
    width: 50%;
    height: 100%;
    font-size: 14px; /* Smaller font than main tabs */
    font-weight: 500;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: none;
    background: transparent;
    cursor: pointer;
    border-radius: 9px;
}

.face-source-tab-button.active-tab {
    background: transparent;
    color: #FFFFFF;
    position: relative;
}

.face-source-tab-button.active-tab .iconify,
.face-source-tab-button.active-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
    color: #FFFFFF;
}

.face-source-tab-button.inactive-tab {
    background: transparent;
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.face-source-tab-button.inactive-tab .iconify,
.face-source-tab-button.inactive-tab span:not(.iconify) {
    position: relative;
    z-index: 2;
}

.face-source-tab-button.inactive-tab:hover {
    color: #F3F4F6;
}

.face-source-tab-button:focus {
    outline: none;
    box-shadow: none;
}

/* Icon styling within face source tabs */
.face-source-tab-button .iconify {
    width: 16px; /* Smaller than main tabs */
    height: 16px;
    flex-shrink: 0;
}

.face-source-tab-button.active-tab .iconify {
    color: #FFFFFF;
}

.face-source-tab-button.inactive-tab .iconify {
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.face-source-tab-button.inactive-tab:hover .iconify {
    color: #F3F4F6;
}

/* Text styling for face source tab labels */
.face-source-tab-button span:not(.iconify) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 14px; /* Smaller than main tabs */
    line-height: 20px;
    letter-spacing: -0.01em;
}

/* Responsive adjustments for face source tabs */
@media (max-width: 640px) {
    .face-source-tab-navigation-container {
        max-width: 240px;
        height: 42px;
    }
    
    .face-source-tab-group {
        border-radius: 10px;
        padding: 2px;
    }
    
    .face-source-tab-group::before {
        top: 2px;
        left: 2px;
        width: calc(50% - 2px);
        height: calc(100% - 4px);
        border-radius: 8px;
    }
    
    .face-source-tab-group.url-active::before {
        transform: translateX(calc(100% + 2px));
    }
    
    .face-source-tab-button {
        font-size: 13px;
        gap: 4px;
        border-radius: 8px;
    }
    
    .face-source-tab-button .iconify {
        width: 14px;
        height: 14px;
    }
    
    .face-source-tab-button span:not(.iconify) {
        font-size: 13px;
        line-height: 18px;
    }
}

@media (max-width: 480px) {
    .face-source-tab-navigation-container {
        max-width: 220px;
        height: 40px;
    }
    
    .face-source-tab-button {
        font-size: 12px;
        gap: 3px;
    }
    
    .face-source-tab-button .iconify {
        width: 12px;
        height: 12px;
    }
    
    .face-source-tab-button span:not(.iconify) {
        font-size: 12px;
        line-height: 16px;
    }
}

/* ================= Main Tab Styling (Editor/Templates) ================= */

.main-tab-navigation-container {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    width: 324px;
    height: 57px;
    margin-left: auto;
    margin-right: auto;
}

.main-tab-group {
    width: 324px;
    height: 57px;
    background: rgb(57, 65, 80); /* #394150 - matches Figma r: 0.2235, g: 0.2549, b: 0.3137 */
    border-radius: 14px;
    padding: 0;
    display: flex;
    position: relative;
    box-shadow: none;
    border: none;
}

/* Animated highlight background that slides between tabs */
.main-tab-group::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 156px; /* Updated from 150px to 156px per Figma */
    height: 51px; /* Updated from 47px to 51px per Figma */
    background: linear-gradient(135deg, #4285F4 0%, #006FEE 100%); /* Updated gradient direction and colors */
    border-radius: 14px; /* cornerRadius from Figma JSON */
    z-index: 1;
    transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0); /* Default position for first tab */
}

/* Move highlight to second tab when templates is active */
.main-tab-group.templates-active::before {
    transform: translateX(162px); /* Move to second tab position */
}

.main-tab-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px; /* Updated from 12px to 8px per Figma */
    width: 162px; /* Updated from 192.5px to 162px per Figma */
    height: 57px;
    font-size: 16px;
    font-weight: 500;
    transition: color 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border: none;
    background: transparent;
    cursor: pointer;
}

/* Remove old ::before pseudo-elements */
.main-tab-button.active-tab {
    @apply text-white;
    position: relative;
    z-index: 2;
}

.main-tab-button.active-tab .iconify,
.main-tab-button.active-tab span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 3;
}

.main-tab-button.inactive-tab {
    @apply text-gray-300;
    position: relative;
    z-index: 1;
}

.main-tab-button.inactive-tab .iconify,
.main-tab-button.inactive-tab span:not(.iconify) {
    color: rgb(161, 161, 170) !important; /* #A1A1AA */
    position: relative;
    z-index: 2;
}

.main-tab-button.inactive-tab:hover {
    @apply text-white;
}

.main-tab-button.inactive-tab:hover .iconify,
.main-tab-button.inactive-tab:hover span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 2;
}

.main-tab-button:focus {
    outline: none;
    box-shadow: none;
}

/* Ensure active tab text stays white in all states */
.main-tab-button.active-tab,
.main-tab-button.active-tab:focus,
.main-tab-button.active-tab:hover,
.main-tab-button.active-tab:active {
    color: #FFFFFF !important;
}

.main-tab-button.active-tab .iconify,
.main-tab-button.active-tab:focus .iconify,
.main-tab-button.active-tab:hover .iconify,
.main-tab-button.active-tab:active .iconify,
.main-tab-button.active-tab span:not(.iconify),
.main-tab-button.active-tab:focus span:not(.iconify),
.main-tab-button.active-tab:hover span:not(.iconify),
.main-tab-button.active-tab:active span:not(.iconify) {
    color: #FFFFFF !important;
    position: relative;
    z-index: 3;
}

/* Icon styling within tabs */
.main-tab-button .iconify {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.main-tab-button.active-tab .iconify {
    color: #FFFFFF;
}

.main-tab-button.inactive-tab .iconify {
    color: rgb(161, 161, 170); /* #A1A1AA */
}

.main-tab-button.inactive-tab:hover .iconify {
    color: #F3F4F6;
}

/* First tab positioning */
.main-tab-button:first-child {
    border-radius: 14px 0 0 14px;
}

/* Second tab positioning */
.main-tab-button:last-child {
    border-radius: 0 14px 14px 0;
}

/* Text styling for tab labels */
.main-tab-button span:not(.iconify) {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: -0.01em;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .main-tab-navigation-container {
        width: 100%;
        max-width: 324px;
        height: 57px;
        padding: 0 4px; /* Add small padding for better spacing */
    }
    
    .main-tab-group {
        width: 100%;
        max-width: 324px;
        position: relative;
    }
    
    .main-tab-group::before {
        width: calc(50% - 6px); /* More precise calculation */
        left: 3px; /* Consistent positioning */
        transition: transform 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .main-tab-group.templates-active::before {
        transform: translateX(calc(100% + 6px)); /* More precise positioning */
    }
    
    .main-tab-button {
        width: calc(50% - 2px); /* Ensure consistent width for both tabs */
        max-width: 158px; /* Prevent overflow */
        font-size: 14px;
        gap: 6px;
        height: 57px;
        margin: 0 1px; /* Small margin for visual separation */
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 14px; /* Consistent border radius on mobile */
    }
    
    /* Override individual border radius on mobile for consistency */
    .main-tab-button:first-child,
    .main-tab-button:last-child {
        border-radius: 14px;
    }
    
    .main-tab-button .iconify {
        width: 18px;
        height: 18px;
        flex-shrink: 0;
    }
    
    .main-tab-button span:not(.iconify) {
        font-size: 14px;
        line-height: 24px;
        white-space: nowrap; /* Prevent text wrapping */
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* Additional mobile refinements for very small screens */
@media (max-width: 480px) {
    .main-tab-navigation-container {
        max-width: 300px;
        padding: 0 2px;
    }
    
    .main-tab-group {
        max-width: 300px;
    }
    
    .main-tab-button {
        font-size: 13px;
        gap: 4px;
        padding: 0 8px;
    }
    
    .main-tab-button .iconify {
        width: 16px;
        height: 16px;
    }
    
    .main-tab-button span:not(.iconify) {
        font-size: 13px;
        line-height: 22px;
    }
}

/* ================= Template Grid Layout - Figma Design Match ================= */

/* Main templates grid - responsive layout */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 2 columns by default */
    gap: 16px;
    width: 100%;
    max-width: none;
    margin: 0 auto;
    justify-content: center;
    padding: 0 4px; /* Add small padding for better mobile spacing */
}

/* Template category cards */
.template-category-card,
.add-new-card {
    position: relative;
    width: 100%;
    min-height: 140px; /* Make cards taller for better aspect ratio */
    max-width: 100%;
    border-radius: 8px; /* cornerRadius from JSON */
    overflow: hidden;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth easing */
    border: none;
    box-sizing: border-box; /* Ensure proper sizing */
    /* Drop shadows matching Figma JSON */
    box-shadow: 
        0 1px 2px -1px rgba(0, 0, 0, 0.1), /* First shadow: radius 2, offset y:1, spread -1 */
        0 1px 3px 0 rgba(0, 0, 0, 0.1);    /* Second shadow: radius 3, offset y:1, spread 0 */
}

.template-category-card:hover,
.add-new-card:hover {
    transform: translateY(-4px) scale(1.04); /* Lift and scale */
    box-shadow: 
        0 8px 24px rgba(0, 0, 0, 0.18), /* Enhanced shadow for depth */
        0 2px 8px rgba(0, 0, 0, 0.10);
    z-index: 2; /* Ensure hover card appears above others */
}

/* Active selected state */
.template-category-card.active {
    border: 2px solid #006FEE; /* 2px solid border with specified color */
    /* shadow/neutral/md */
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
}

/* Don't apply hover transform to active cards */
.template-category-card.active:hover {
    transform: none;
    /* Keep the active shadow, don't change it on hover */
    box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
}

/* Category card content - full image background */
.template-category-content {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-end;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-sizing: border-box; /* Ensure proper sizing */
    /* Fallback background color matching Figma */
    background-color: rgb(248, 72, 72); /* r: 0.9718, g: 0.2832, b: 0.2832 */
}

/* Category label overlay - matching Figma JSON with cropping fix */
.template-category-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    min-height: 32px; /* Increased from 27px to prevent cropping */
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 6px 12px; /* Increased padding for better spacing */
    box-sizing: border-box; /* Ensure proper sizing */
    /* Background matching JSON: black with 15% opacity */
    background: rgba(0, 0, 0, 0.15);
    /* Backdrop blur matching JSON: radius 8.5 */
    backdrop-filter: blur(8.5px);
    -webkit-backdrop-filter: blur(8.5px);
    /* Inner shadow effect from JSON */
    box-shadow: inset 0 1px 0 0 rgba(243, 239, 246, 0.04);
    border-radius: 0 0 8px 8px; /* Match parent border radius */
}

.template-category-label span {
    color: white; /* White text from JSON */
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    line-height: 18px; /* Increased line height for better readability */
    width: 100%; /* Use full width instead of fixed 84px */
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden; /* Hide overflow */
    text-overflow: ellipsis; /* Show ellipsis for long text */
}

/* Add New card specific styling - Matching updated template card pattern */
.add-new-card {
    /* Inherits all styles from .template-category-card, .add-new-card rule above */
    /* Remove any conflicting individual styles */
}

.add-new-card .template-category-content {
    /* Ensure the content area is properly centered */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}

.add-new-card .iconify {
    /* Consistent icon sizing across all breakpoints */
    font-size: 32px !important;
    color: rgb(113, 113, 122);
    margin-bottom: 8px;
    transition: color 0.3s ease, transform 0.3s ease;
}

.add-new-card:hover .iconify {
    /* Subtle hover effect for the icon - works with parent hover transform */
    transform: scale(1.1);
    color: rgb(139, 139, 150); /* Slightly lighter on hover */
}

/* Remove the old template-add-new-content styles since we're using inline styles now */
.template-add-new-content {
    /* This class is no longer used - styling moved to inline for Figma accuracy */
}

/* Responsive adjustments for template grid */
@media (max-width: 900px) {
    .templates-grid {
        gap: 14px; /* Slightly smaller gap for medium screens */
        padding: 0 8px;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 120px; /* Adjust height for medium screens */
    }
    
    .template-category-label {
        min-height: 28px;
        padding: 4px 10px;
    }
    
    .template-category-label span {
        font-size: 13px;
        line-height: 16px;
    }
}

@media (max-width: 640px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr); /* Keep 2 columns on mobile */
        gap: 12px;
        padding: 0 8px;
        justify-items: stretch; /* Ensure cards fill available space */
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 100px; /* Smaller height for mobile */
        max-width: 100%;
    }
    
    .template-category-label {
        min-height: 26px;
        padding: 4px 8px;
    }
    
    .template-category-label span {
        font-size: 12px;
        line-height: 14px;
    }
}

@media (max-width: 480px) {
    .templates-grid {
        grid-template-columns: repeat(2, 1fr); /* Maintain 2 columns for very small screens */
        gap: 10px;
        padding: 0 6px;
        justify-content: center;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 90px; /* Compact height for very small screens */
        border-radius: 6px; /* Slightly smaller border radius */
    }
    
    .template-category-card.active {
        border: 2px solid #006FEE;
        box-shadow: 0px 4px 6px -1px rgba(0, 0, 0, 0.10), 0px 2px 4px -2px rgba(0, 0, 0, 0.10);
    }
    
    .template-category-label {
        min-height: 24px;
        padding: 4px 6px;
        border-radius: 0 0 6px 6px; /* Match parent border radius */
    }
    
    .template-category-label span {
        font-size: 11px;
        line-height: 14px;
    }
    
    /* Optimize add-new card for small screens */
    .add-new-card .iconify {
        font-size: 24px !important;
        margin-bottom: 4px;
    }
}

/* Ultra-small screens (very small phones) */
@media (max-width: 360px) {
    .templates-grid {
        gap: 8px;
        padding: 0 4px;
    }
    
    .template-category-card,
    .add-new-card {
        min-height: 85px;
    }
    
    .template-category-label span {
        font-size: 10px;
        line-height: 13px;
    }
    
    .add-new-card .iconify {
        font-size: 20px !important;
    }
}

/* ================= USER DROPDOWN MENU STYLES ================= */

/* Dropdown animation */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* User dropdown menu enhancements */
.user-dropdown-menu {
    /* Animation is now applied via inline style */
}

/* Enhanced pricing plan cards */
.plan-card {
    position: relative;
    overflow: visible; /* Allow badges to overflow */
}

/* Plan card hover effects */
.plan-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Most Popular and Current Plan badges */
.plan-card > span:first-of-type {
    /* Position badges properly */
    z-index: 10;
}

/* Smooth transitions for all interactive elements */
.plan-button,
.menu-item,
.close-btn {
    transition: all 0.2s ease-in-out;
}

/* Focus states for accessibility */
.plan-button:focus,
.menu-item:focus,
.close-btn:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 2px;
}

/* Responsive adjustments for user dropdown */
@media (max-width: 640px) {
    .user-dropdown-menu {
        width: 100vw !important;
        max-width: 100vw !important;
        right: 0 !important;
        left: 0 !important;
        top: 100% !important;
        border-radius: 0 0 12px 12px !important;
        margin-top: 0 !important;
    }
    
    /* Adjust plan cards for mobile */
    .plans-container {
        padding: 1rem !important;
    }
    
    .plan-card {
        padding: 1rem !important;
    }
    
    .features-list {
        font-size: 0.8125rem;
    }
}

/* Custom scrollbar for dropdown menu */
.user-dropdown-menu::-webkit-scrollbar {
    width: 8px;
}

.user-dropdown-menu::-webkit-scrollbar-track {
    background: #1F2937;
}

.user-dropdown-menu::-webkit-scrollbar-thumb {
    background: #4B5563;
    border-radius: 4px;
}

.user-dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #6B7280;
}

/* Firefox scrollbar */
.user-dropdown-menu {
    scrollbar-width: thin;
    scrollbar-color: #4B5563 #1F2937;
} 
/* ================= PRICING MODAL STYLES ================= */

/* Simplified tooltip styling */
.tooltip-icon-container-simple {
    position: relative !important;
    display: inline-block !important;
    z-index: 1 !important;
}

.tooltip-simple-popup {
    position: absolute !important;
    z-index: 10000 !important;
    pointer-events: none !important;
    /* Ensure it's always visible when active */
    transform-origin: center bottom !important;
    will-change: opacity, visibility !important;
}

/* Make sure the tooltip container doesn't get clipped */
.tooltip-icon-container-simple:hover {
    z-index: 10001 !important;
}

/* Override any overflow hidden that might clip tooltips */
.face-upload-section, 
.person-settings-container,
.control-panel-main-container {
    overflow: visible !important;
}

/* Ensure tooltips are visible even in scrollable containers */
.tooltip-simple-popup {
    position: absolute !important;
    bottom: 100% !important;
    left: 50% !important;
    transform: translateX(-50%) translateY(-8px) !important;
    margin: 0 !important;
}

/* Debug version - super visible */
.tooltip-simple-popup[style*="opacity: 1"] {
    background-color: #ef4444 !important; /* Bright red */
    color: #ffffff !important;
    border: 3px solid #fbbf24 !important; /* Yellow border */
    font-size: 14px !important;
    font-weight: bold !important;
    padding: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8) !important;
    border-radius: 8px !important;
}

/* Tooltip enhancements - ensure they work properly */
.tooltip-icon-container {
    position: relative;
    display: inline-block;
}

.tooltip-fixed-content {
    z-index: 10000 !important; /* Very high z-index to ensure visibility */
    background-color: #1f2937 !important; /* Clean dark gray background */
    color: #f9fafb !important; /* White text */
    border: none !important; /* Remove border for cleaner look */
    font-size: 12px !important; /* Smaller, cleaner text */
    font-weight: 400 !important; /* Normal weight */
    line-height: 1.4 !important;
    border-radius: 0.5rem !important; /* rounded-lg */
    padding: 0.75rem !important; /* Standard padding */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; /* Reduced by 20% - lighter shadow */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    pointer-events: none !important;
    text-shadow: none !important; /* Remove text shadow for cleaner look */
    min-width: 180px !important; /* Slightly smaller minimum width */
    min-height: auto !important; /* Auto height for better text fit */
}

/* Ensure tooltips don't get clipped by parent containers */
.tooltip-icon-container,
.face-upload-section, 
.person-settings-container,
.control-panel-main-container {
    overflow: visible !important;
}

/* Force visibility for tooltip content */
.tooltip-fixed-content[style*="visibility: visible"] {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Ensure tooltip text doesn't wrap poorly */
.tooltip-fixed-content p {
    margin: 0 !important;
    padding: 0 !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    line-height: 1.4 !important;
    color: #f9fafb !important; /* Clean white text */
    font-weight: 400 !important; /* Normal weight */
}

/* Ensure modal has proper z-index */
.image-requirements-modal {
    z-index: 9999 !important;
}

/* Debug styles - temporary to check tooltip positioning */
.tooltip-fixed-content.debug {
    background: #ef4444 !important; /* Red background for debugging */
    border: 3px solid #fbbf24 !important; /* Yellow border for debugging */
    font-size: 16px !important;
    padding: 1rem !important;
}

/* ================= FOCUS RING CLIPPING FIX ================= */

/* Ensure mood expression picker and all its parents allow focus ring overflow */
.mood-expression-picker-section,
.mood-expression-picker-grid,
.person-settings-container,
.control-panel-main-container,
.left-sidebar,
.collapsible-section,
.collapsible-content {
    overflow: visible !important;
}

/* Specific fix for mood expression buttons to prevent focus ring clipping */
.mood-expression-picker-grid button:focus {
    position: relative;
    z-index: 10;
}

/* Ensure the left sidebar doesn't clip focus rings horizontally */
.left-sidebar {
    overflow-x: visible !important;
    overflow-y: auto !important; /* Keep vertical scrolling but allow horizontal overflow */
}

/* Ensure collapsible sections don't clip focus rings when expanded */
.collapsible-content {
    overflow: visible !important;
}

/* Force all containers in the chain to allow focus ring overflow */
.mood-expression-picker-section *,
.person-settings-container *,
.control-panel-main-container * {
    overflow: visible !important;
}

.tooltip-fixed-content {
    z-index: 10000 !important; /* Very high z-index to ensure visibility */
    background-color: #1f2937 !important; /* Clean dark gray background */
    color: #f9fafb !important; /* White text */
    border: none !important; /* Remove border for cleaner look */
    font-size: 12px !important; /* Smaller, cleaner text */
    font-weight: 400 !important; /* Normal weight */
    line-height: 1.4 !important;
    border-radius: 0.5rem !important; /* rounded-lg */
    padding: 0.75rem !important; /* Standard padding */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important; /* Reduced by 20% - lighter shadow */
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    pointer-events: none !important;
    text-shadow: none !important; /* Remove text shadow for cleaner look */
    min-width: 180px !important; /* Slightly smaller minimum width */
    min-height: auto !important; /* Auto height for better text fit */
}

/* ================= TOOLTIP OVERFLOW FIX ================= */
/* Ensure all parent containers allow tooltip overflow */
.premade-templates-section,
.premade-templates-section *,
.templates-tab-content,
.templates-tab-content *,
.flex-grow.overflow-y-auto,
.left-sidebar *,
.app-container *,
.main-tab-navigation-container,
.main-tab-group {
    overflow-x: visible !important;
}

/* ================= MODAL INTERFERENCE FIX ================= */
/* Hide tab navigation when any modal is open */
body:has(.template-modal-container[style*="opacity-100"]) .main-tab-navigation-container,
body:has(.show-more-modal-container[style*="opacity-100"]) .main-tab-navigation-container {
    display: none !important;
}

/* ================= TOP NAVIGATION LOGO STYLES ================= */

.top-nav-logo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.5rem 0;
}

.top-nav-logo-img {
    display: block;
    max-width: 100%;
    height: auto;
    object-fit: contain;
    vertical-align: middle;
}

.top-nav-logo-img:hover {
    filter: brightness(1.1) !important;
}

/* Ensure logo doesn't break on small screens */
@media (max-width: 640px) {
    .top-nav-logo-img {
        width: 28px;
        height: 28px;
    }
}

/* ================= END TOP NAVIGATION LOGO STYLES ================= */

/* ================= FORCE ACTION BUTTONS VISIBILITY ================= */

/* Force action buttons to be visible */
#action-buttons-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    min-height: 50px !important;
    align-items: center !important;
    gap: 1rem !important;
}

/* Ensure buttons are visible */
#action-buttons-row button {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-height: 44px !important;
}

/* Force controls bottom row to be visible */
.controls-bottom-row {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
    background-color: #111827 !important;
    padding: 5px 0 !important;
    min-height: 60px !important;
    margin-top: 5px !important;
    justify-content: center !important; /* Center everything */
    max-width: 652px !important; /* Match preview container width */
    margin-left: auto !important;
    margin-right: auto !important;
    gap: 2rem !important; /* Space between quality and buttons */
}

/* Ensure main container removes scrollbar */
.main-center-panel {
    overflow: hidden !important; /* Remove scrollbar */
    min-height: calc(100vh - 60px) !important;
    max-height: calc(100vh - 60px) !important;
}

@media (min-width: 1025px) {
    .left-sidebar .sidebar-close-btn {
        display: none !important;
    }
}
