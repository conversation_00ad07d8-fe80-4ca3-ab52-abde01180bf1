const React = window.React;
const { useEffect, useState, useCallback, useMemo, useRef } = React;
import { AdminTable } from './AdminTable.jsx';

export const UserManagementPage = () => {
    const [searchTerm, setSearchTerm] = useState('');

    // Mock user data
    const [users] = useState([
        {
            id: 'user-1',
            email: '<EMAIL>',
            registrationDate: '2024-01-10',
            lastActiveDate: '2024-01-20',
            totalApiCalls: 1247,
            status: 'Active'
        },
        {
            id: 'user-2',
            email: '<EMAIL>',
            registrationDate: '2024-01-08',
            lastActiveDate: '2024-01-19',
            totalApiCalls: 892,
            status: 'Active'
        },
        {
            id: 'user-3',
            email: '<EMAIL>',
            registrationDate: '2024-01-05',
            lastActiveDate: '2024-01-15',
            totalApiCalls: 2156,
            status: 'Active'
        },
        {
            id: 'user-4',
            email: '<EMAIL>',
            registrationDate: '2024-01-03',
            lastActiveDate: '2024-01-12',
            totalApiCalls: 456,
            status: 'Suspended'
        }
    ]);

    const filteredUsers = users.filter(user => 
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const getStatusBadge = (status) => {
        const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium';
        const statusClasses = status === 'Active' 
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800';
        
        return React.createElement('span', {
            className: `user-status-badge ${baseClasses} ${statusClasses}`
        }, status);
    };

    const tableColumns = [
        {
            key: 'email',
            label: 'User Email',
            render: (user) => React.createElement('div', {
                className: 'user-email-cell'
            },
                React.createElement('span', {
                    className: 'user-email-text font-medium text-white'
                }, user.email)
            )
        },
        {
            key: 'registrationDate',
            label: 'Registration Date',
            render: (user) => React.createElement('span', {
                className: 'user-registration-date text-gray-400 text-sm'
            }, new Date(user.registrationDate).toLocaleDateString())
        },
        {
            key: 'lastActiveDate',
            label: 'Last Active',
            render: (user) => React.createElement('span', {
                className: 'user-last-active-date text-gray-400 text-sm'
            }, new Date(user.lastActiveDate).toLocaleDateString())
        },
        {
            key: 'totalApiCalls',
            label: 'Total API Calls',
            render: (user) => React.createElement('span', {
                className: 'user-api-calls-count font-medium text-purple-400'
            }, user.totalApiCalls.toLocaleString())
        },
        {
            key: 'status',
            label: 'Status',
            render: (user) => getStatusBadge(user.status)
        },
        {
            key: 'actions',
            label: 'Actions',
            render: (user) => React.createElement('div', {
                className: 'user-actions-cell flex items-center gap-2'
            },
                React.createElement('button', {
                    className: 'user-view-btn p-2 text-blue-400 hover:text-blue-300 transition-colors',
                    onClick: () => console.log('View user details:', user.id),
                    title: 'View user details'
                },
                    React.createElement('svg', {
                        className: 'user-view-icon w-4 h-4',
                        fill: 'none',
                        stroke: 'currentColor',
                        viewBox: '0 0 24 24'
                    },
                        React.createElement('path', {
                            strokeLinecap: 'round',
                            strokeLinejoin: 'round',
                            strokeWidth: 2,
                            d: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z'
                        })
                    )
                ),
                user.status === 'Active' ? 
                    React.createElement('button', {
                        className: 'user-suspend-btn p-2 text-yellow-400 hover:text-yellow-300 transition-colors',
                        onClick: () => console.log('Suspend user:', user.id),
                        title: 'Suspend user'
                    },
                        React.createElement('svg', {
                            className: 'user-suspend-icon w-4 h-4',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728'
                            })
                        )
                    ) :
                    React.createElement('button', {
                        className: 'user-activate-btn p-2 text-green-400 hover:text-green-300 transition-colors',
                        onClick: () => console.log('Activate user:', user.id),
                        title: 'Activate user'
                    },
                        React.createElement('svg', {
                            className: 'user-activate-icon w-4 h-4',
                            fill: 'none',
                            stroke: 'currentColor',
                            viewBox: '0 0 24 24'
                        },
                            React.createElement('path', {
                                strokeLinecap: 'round',
                                strokeLinejoin: 'round',
                                strokeWidth: 2,
                                d: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
                            })
                        )
                    )
            )
        }
    ];

    return React.createElement('div', {
        className: 'admin-user-management-page',
        id: 'admin-user-management-content'
    },
        // Page Header
        React.createElement('div', {
            className: 'admin-user-management-header mb-8',
            id: 'admin-user-management-header'
        },
            React.createElement('h1', {
                className: 'admin-user-management-title text-3xl font-bold text-white mb-2'
            }, 'User Management'),
            React.createElement('p', {
                className: 'admin-user-management-subtitle text-gray-400'
            }, 'Monitor and manage user accounts and activity')
        ),

        // Search and Filters
        React.createElement('div', {
            className: 'admin-user-filters-section bg-gray-800 border border-gray-700 rounded-lg p-6 mb-6',
            id: 'admin-user-filters-section'
        },
            React.createElement('div', {
                className: 'admin-user-search-wrapper max-w-md'
            },
                React.createElement('label', {
                    className: 'admin-user-search-label block text-sm font-medium text-gray-300 mb-2'
                }, 'Search Users'),
                React.createElement('input', {
                    type: 'text',
                    className: 'admin-user-search-input w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent',
                    id: 'admin-user-search-input',
                    placeholder: 'Search by email address...',
                    value: searchTerm,
                    onChange: (e) => setSearchTerm(e.target.value)
                })
            )
        ),

        // Users Table
        React.createElement('div', {
            className: 'admin-users-table-wrapper',
            id: 'admin-users-table-section'
        },
            React.createElement(AdminTable, {
                columns: tableColumns,
                data: filteredUsers,
                emptyMessage: 'No users found matching your search criteria.',
                tableId: 'admin-users-table'
            })
        ),

        // Summary Stats
        React.createElement('div', {
            className: 'admin-user-stats-section mt-8',
            id: 'admin-user-stats-section'
        },
            React.createElement('div', {
                className: 'admin-user-stats-grid grid grid-cols-1 md:grid-cols-3 gap-6'
            },
                React.createElement('div', {
                    className: 'admin-user-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-user-stat-title text-lg font-semibold text-white mb-2'
                    }, 'Total Users'),
                    React.createElement('p', {
                        className: 'admin-user-stat-value text-3xl font-bold text-purple-400'
                    }, users.length)
                ),
                React.createElement('div', {
                    className: 'admin-user-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-user-stat-title text-lg font-semibold text-white mb-2'
                    }, 'Active Users'),
                    React.createElement('p', {
                        className: 'admin-user-stat-value text-3xl font-bold text-green-400'
                    }, users.filter(u => u.status === 'Active').length)
                ),
                React.createElement('div', {
                    className: 'admin-user-stat-card bg-gray-800 border border-gray-700 rounded-lg p-6'
                },
                    React.createElement('h3', {
                        className: 'admin-user-stat-title text-lg font-semibold text-white mb-2'
                    }, 'Total API Calls'),
                    React.createElement('p', {
                        className: 'admin-user-stat-value text-3xl font-bold text-blue-400'
                    }, users.reduce((sum, user) => sum + user.totalApiCalls, 0).toLocaleString())
                )
            )
        )
    );
}; 