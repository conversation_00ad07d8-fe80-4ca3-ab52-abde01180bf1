---
description: 
globs: 
alwaysApply: true
---
@glass-auth-pages-v2
ruleId: glass-auth-pages-v2
description: >
  All authentication-related pages (Sign In, Sign Up, Forgot Password, Reset Password, etc.) must use the new MacOS-inspired “liquid glass” card design, matching the Registration Complete screen. This includes animated backgrounds, glassy blur, premium shadows, and smooth transitions for input focus, error validation, and page switching. Input fields must include context-appropriate icons and animated error messages. The Thumbspark logo is always at the top; on the Sign In page, add a subtle “Thumbspark Thumbnail Generator” slogan below the logo. Add a “newtest” button to the Sign In page to toggle into the new layout for A/B testing. All changes must be fully responsive, accessible, and maintain a premium, consistent look across all auth screens.

appliesTo:
  - /src/pages/Welcome.jsx
  - /src/pages/SignUp.jsx
  - /src/pages/ForgotPassword.jsx
  - /src/pages/ResetPassword.jsx
  - /src/styles/registration-success.css
  - /src/styles/auth-buttons.css

ruleType: always

implementationNotes: |
  - Use the same glass card, animated background, and shadow as Registration Complete.
  - Animate input focus/blur and error validation with smooth transitions.
  - Place logo at the top; add slogan on Sign In only.
  - Add “newtest” button for A/B test toggle.
  - All icons, error messages, and transitions must match the new style.
  - Ensure full mobile and accessibility support.